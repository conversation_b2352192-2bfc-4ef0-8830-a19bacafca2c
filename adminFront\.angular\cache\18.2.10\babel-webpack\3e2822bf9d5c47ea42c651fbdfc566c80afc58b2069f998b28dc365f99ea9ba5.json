{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function every(predicate, thisArg) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      if (!predicate.call(thisArg, value, index++, source)) {\n        subscriber.next(false);\n        subscriber.complete();\n      }\n    }, () => {\n      subscriber.next(true);\n      subscriber.complete();\n    }));\n  });\n}\n//# sourceMappingURL=every.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}