{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let TimingPipe = /*#__PURE__*/(() => {\n  class TimingPipe {\n    transform(time) {\n      if (time) {\n        const minutes = Math.floor(time / 60);\n        const seconds = Math.floor(time % 60);\n        return `${this.initZero(minutes)}${minutes}:${this.initZero(seconds)}${seconds}`;\n      }\n      return '00:00';\n    }\n    initZero(time) {\n      return time < 10 ? '0' : '';\n    }\n    static {\n      this.ɵfac = function TimingPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TimingPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"timing\",\n        type: TimingPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return TimingPipe;\n})();\nexport let FormatHourPipe = /*#__PURE__*/(() => {\n  class FormatHourPipe {\n    transform(value, ...args) {\n      if (value === 24) {\n        return '00:00';\n      } else {\n        return `${value}:00`;\n      }\n    }\n    static {\n      this.ɵfac = function FormatHourPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FormatHourPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"hour\",\n        type: FormatHourPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return FormatHourPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}