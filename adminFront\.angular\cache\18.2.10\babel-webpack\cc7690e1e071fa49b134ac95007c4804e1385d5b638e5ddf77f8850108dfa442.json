{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiHouseHoldMainAddHouseHoldMainPost$Json } from '../fn/house-hold-main/api-house-hold-main-add-house-hold-main-post-json';\nimport { apiHouseHoldMainAddHouseHoldMainPost$Plain } from '../fn/house-hold-main/api-house-hold-main-add-house-hold-main-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let HouseHoldMainService = /*#__PURE__*/(() => {\n  class HouseHoldMainService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiHouseHoldMainAddHouseHoldMainPost()` */\n    static {\n      this.ApiHouseHoldMainAddHouseHoldMainPostPath = '/api/HouseHoldMain/AddHouseHoldMain';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHoldMainAddHouseHoldMainPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainAddHouseHoldMainPost$Plain$Response(params, context) {\n      return apiHouseHoldMainAddHouseHoldMainPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHoldMainAddHouseHoldMainPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainAddHouseHoldMainPost$Plain(params, context) {\n      return this.apiHouseHoldMainAddHouseHoldMainPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHoldMainAddHouseHoldMainPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainAddHouseHoldMainPost$Json$Response(params, context) {\n      return apiHouseHoldMainAddHouseHoldMainPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHoldMainAddHouseHoldMainPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainAddHouseHoldMainPost$Json(params, context) {\n      return this.apiHouseHoldMainAddHouseHoldMainPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function HouseHoldMainService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HouseHoldMainService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: HouseHoldMainService,\n        factory: HouseHoldMainService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return HouseHoldMainService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}