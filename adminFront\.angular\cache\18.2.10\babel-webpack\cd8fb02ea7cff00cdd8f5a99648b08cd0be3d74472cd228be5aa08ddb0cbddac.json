{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Input, ViewChild, ContentChild, NgModule } from '@angular/core';\nimport { Calendar } from '@fullcalendar/core';\nimport { CustomRenderingStore } from '@fullcalendar/core/internal';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"*\"];\nconst _c1 = [\"rootEl\"];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nfunction TransportContainerComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nconst _c3 = [\"dayHeaderContent\"];\nconst _c4 = [\"dayCellContent\"];\nconst _c5 = [\"weekNumberContent\"];\nconst _c6 = [\"nowIndicatorContent\"];\nconst _c7 = [\"eventContent\"];\nconst _c8 = [\"slotLaneContent\"];\nconst _c9 = [\"slotLabelContent\"];\nconst _c10 = [\"allDayContent\"];\nconst _c11 = [\"moreLinkContent\"];\nconst _c12 = [\"noEventsContent\"];\nconst _c13 = [\"resourceAreaHeaderContent\"];\nconst _c14 = [\"resourceGroupLabelContent\"];\nconst _c15 = [\"resourceLabelContent\"];\nconst _c16 = [\"resourceLaneContent\"];\nconst _c17 = [\"resourceGroupLaneContent\"];\nfunction FullCalendarComponent_transport_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"transport-container\", 1);\n  }\n  if (rf & 2) {\n    const customRendering_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"inPlaceOf\", customRendering_r1.containerEl)(\"reportEl\", customRendering_r1.reportNewContainerEl)(\"elTag\", customRendering_r1.elTag)(\"elClasses\", customRendering_r1.elClasses)(\"elStyle\", customRendering_r1.elStyle)(\"elAttrs\", customRendering_r1.elAttrs)(\"template\", ctx_r1.templateMap[customRendering_r1.generatorName])(\"renderProps\", customRendering_r1.renderProps);\n  }\n}\nconst OPTION_IS_DEEP = {\n  headerToolbar: true,\n  footerToolbar: true,\n  events: true,\n  eventSources: true,\n  resources: true\n};\n/*\nNOTE: keep synced with component\n*/\nconst OPTION_INPUT_NAMES = ['events', 'eventSources', 'resources'];\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\n/*\nReally simple clone utility. Only copies plain arrays, objects, and Dates. Transfers everything else as-is.\nWanted to use a third-party lib, but none did exactly this.\n*/\nfunction deepCopy(input) {\n  if (Array.isArray(input)) {\n    return input.map(deepCopy);\n  } else if (input instanceof Date) {\n    return new Date(input.valueOf());\n  } else if (typeof input === 'object' && input) {\n    // non-null object\n    return mapHash(input, deepCopy);\n  } else {\n    // everything else (null, function, etc)\n    return input;\n  }\n}\nfunction mapHash(input, func) {\n  const output = {};\n  for (const key in input) {\n    if (hasOwnProperty.call(input, key)) {\n      output[key] = func(input[key], key);\n    }\n  }\n  return output;\n}\n\n/*\nForked from https://github.com/epoberezkin/fast-deep-equal (also has MIT license)\nNeeded ESM support or else Angular complains about treeshaking\n(https://github.com/fullcalendar/fullcalendar-angular/issues/421)\n*/\nfunction deepEqual(a, b) {\n  if (a === b) return true;\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) if (!deepEqual(a[i], b[i])) return false;\n      return true;\n    }\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n    for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n      if (!deepEqual(a[key], b[key])) return false;\n    }\n    return true;\n  }\n  // true if both NaN, false otherwise\n  return a !== a && b !== b;\n}\nconst dummyContainer$1 = typeof document !== 'undefined' ? document.createDocumentFragment() : null;\nlet OffscreenFragmentComponent = /*#__PURE__*/(() => {\n  class OffscreenFragmentComponent {\n    constructor(element) {\n      this.element = element;\n    }\n    ngAfterViewInit() {\n      if (dummyContainer$1) {\n        dummyContainer$1.appendChild(this.element.nativeElement);\n      }\n    }\n    // invoked BEFORE component removed from DOM\n    ngOnDestroy() {\n      if (dummyContainer$1) {\n        dummyContainer$1.removeChild(this.element.nativeElement);\n      }\n    }\n  }\n  OffscreenFragmentComponent.ɵfac = function OffscreenFragmentComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OffscreenFragmentComponent)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  OffscreenFragmentComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: OffscreenFragmentComponent,\n    selectors: [[\"offscreen-fragment\"]],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function OffscreenFragmentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2\n  });\n  return OffscreenFragmentComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst dummyContainer = typeof document !== 'undefined' ? document.createDocumentFragment() : null;\nlet TransportContainerComponent = /*#__PURE__*/(() => {\n  class TransportContainerComponent {\n    ngAfterViewInit() {\n      const rootEl = this.rootElRef?.nativeElement; // assumed defined\n      replaceEl(rootEl, this.inPlaceOf);\n      applyElAttrs(rootEl, undefined, this.elAttrs);\n      // insurance for if Preact recreates and reroots inPlaceOf element\n      this.inPlaceOf.style.display = 'none';\n      this.reportEl(rootEl);\n    }\n    ngOnChanges(changes) {\n      const rootEl = this.rootElRef?.nativeElement;\n      // ngOnChanges is called before ngAfterViewInit (and before DOM initializes)\n      // so make sure rootEl is defined before doing anything\n      if (rootEl) {\n        // If the ContentContainer's tagName changed, it will create a new DOM element in its\n        // original place. Detect this and re-replace.\n        if (this.inPlaceOf.parentNode !== dummyContainer) {\n          replaceEl(rootEl, this.inPlaceOf);\n          applyElAttrs(rootEl, undefined, this.elAttrs);\n          this.reportEl(rootEl);\n        } else {\n          const elAttrsChange = changes['elAttrs'];\n          if (elAttrsChange) {\n            applyElAttrs(rootEl, elAttrsChange.previousValue, elAttrsChange.currentValue);\n          }\n        }\n      }\n    }\n    // invoked BEFORE component removed from DOM\n    ngOnDestroy() {\n      if (\n      // protect against Preact recreating and rerooting inPlaceOf element\n      this.inPlaceOf.parentNode === dummyContainer && dummyContainer) {\n        dummyContainer.removeChild(this.inPlaceOf);\n      }\n      this.reportEl(null);\n    }\n  }\n  TransportContainerComponent.ɵfac = function TransportContainerComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TransportContainerComponent)();\n  };\n  TransportContainerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TransportContainerComponent,\n    selectors: [[\"transport-container\"]],\n    viewQuery: function TransportContainerComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootElRef = _t.first);\n      }\n    },\n    inputs: {\n      inPlaceOf: \"inPlaceOf\",\n      reportEl: \"reportEl\",\n      elTag: \"elTag\",\n      elClasses: \"elClasses\",\n      elStyle: \"elStyle\",\n      elAttrs: \"elAttrs\",\n      template: \"template\",\n      renderProps: \"renderProps\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 6,\n    vars: 6,\n    consts: [[\"rootEl\", \"\"], [3, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n    template: function TransportContainerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, TransportContainerComponent_ng_template_0_Template, 3, 6, \"ng-template\", 1)(1, TransportContainerComponent_ng_template_1_Template, 3, 6, \"ng-template\", 1)(2, TransportContainerComponent_ng_template_2_Template, 3, 6, \"ng-template\", 1)(3, TransportContainerComponent_ng_template_3_Template, 3, 6, \"ng-template\", 1)(4, TransportContainerComponent_ng_template_4_Template, 3, 6, \"ng-template\", 1)(5, TransportContainerComponent_ng_template_5_Template, 3, 6, \"ng-template\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"div\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"span\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"a\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"tr\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"th\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"td\");\n      }\n    },\n    dependencies: [i1.NgIf, i1.NgClass, i1.NgStyle, i1.NgTemplateOutlet],\n    encapsulation: 2\n  });\n  return TransportContainerComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nfunction replaceEl(subject, inPlaceOf) {\n  inPlaceOf.parentNode?.insertBefore(subject, inPlaceOf.nextSibling);\n  if (dummyContainer) {\n    dummyContainer.appendChild(inPlaceOf);\n  }\n}\nfunction applyElAttrs(el, previousAttrs = {}, currentAttrs = {}) {\n  // these are called \"attributes\" but they manipulate DOM node *properties*\n  for (const attrName in previousAttrs) {\n    if (!(attrName in currentAttrs)) {\n      el[attrName] = null;\n    }\n  }\n  for (const attrName in currentAttrs) {\n    el[attrName] = currentAttrs[attrName];\n  }\n}\nlet FullCalendarComponent = /*#__PURE__*/(() => {\n  class FullCalendarComponent {\n    constructor(element, changeDetector) {\n      this.element = element;\n      this.calendar = null;\n      this.optionSnapshot = {}; // for diffing\n      this.customRenderingMap = new Map();\n      this.templateMap = {};\n      const customRenderingStore = new CustomRenderingStore();\n      customRenderingStore.subscribe(customRenderingMap => {\n        this.customRenderingMap = customRenderingMap;\n        this.customRenderingArray = undefined; // clear cache\n        changeDetector.detectChanges();\n      });\n      this.handleCustomRendering = customRenderingStore.handle.bind(customRenderingStore);\n      this.templateMap = this; // alias to this\n    }\n    ngAfterViewInit() {\n      const {\n        deepChangeDetection\n      } = this;\n      const options = {\n        ...this.options,\n        ...this.buildInputOptions()\n      };\n      // initialize snapshot\n      this.optionSnapshot = mapHash(options, (optionVal, optionName) => deepChangeDetection && OPTION_IS_DEEP[optionName] ? deepCopy(optionVal) : optionVal);\n      const calendarEl = this.element.nativeElement;\n      const calendar = this.calendar = new Calendar(calendarEl, {\n        ...options,\n        ...this.buildExtraOptions()\n      });\n      // Ionic dimensions hack\n      // https://github.com/fullcalendar/fullcalendar/issues/4976\n      const ionContent = calendarEl.closest('ion-content');\n      if (ionContent && ionContent.componentOnReady) {\n        ionContent.componentOnReady().then(() => {\n          window.requestAnimationFrame(() => {\n            calendar.render();\n          });\n        });\n      } else {\n        calendar.render();\n      }\n    }\n    /*\n    allows us to manually detect complex input changes, internal mutations to certain options.\n    called before ngOnChanges. called much more often than ngOnChanges.\n    */\n    ngDoCheck() {\n      if (this.calendar) {\n        // not the initial render\n        const {\n          deepChangeDetection,\n          optionSnapshot\n        } = this;\n        const newOptions = {\n          ...this.options,\n          ...this.buildInputOptions()\n        };\n        const newProcessedOptions = {};\n        const changedOptionNames = [];\n        // detect adds and updates (and update snapshot)\n        for (const optionName in newOptions) {\n          if (newOptions.hasOwnProperty(optionName)) {\n            let optionVal = newOptions[optionName];\n            if (deepChangeDetection && OPTION_IS_DEEP[optionName]) {\n              if (!deepEqual(optionSnapshot[optionName], optionVal)) {\n                optionSnapshot[optionName] = deepCopy(optionVal);\n                changedOptionNames.push(optionName);\n              }\n            } else {\n              if (optionSnapshot[optionName] !== optionVal) {\n                optionSnapshot[optionName] = optionVal;\n                changedOptionNames.push(optionName);\n              }\n            }\n            newProcessedOptions[optionName] = optionVal;\n          }\n        }\n        const oldOptionNames = Object.keys(optionSnapshot);\n        // detect removals (and update snapshot)\n        for (const optionName of oldOptionNames) {\n          if (!(optionName in newOptions)) {\n            // doesn't exist in new options?\n            delete optionSnapshot[optionName];\n            changedOptionNames.push(optionName);\n          }\n        }\n        if (changedOptionNames.length) {\n          this.calendar.pauseRendering();\n          this.calendar.resetOptions({\n            ...newProcessedOptions,\n            ...this.buildExtraOptions()\n          }, changedOptionNames);\n        }\n      }\n    }\n    ngAfterContentChecked() {\n      if (this.calendar) {\n        // too defensive?\n        this.calendar.resumeRendering();\n      }\n    }\n    ngOnDestroy() {\n      if (this.calendar) {\n        // too defensive?\n        this.calendar.destroy();\n        this.calendar = null;\n      }\n    }\n    get customRenderings() {\n      return this.customRenderingArray || (this.customRenderingArray = [...this.customRenderingMap.values()]);\n    }\n    getApi() {\n      return this.calendar;\n    }\n    buildInputOptions() {\n      const options = {};\n      for (const inputName of OPTION_INPUT_NAMES) {\n        const inputValue = this[inputName];\n        if (inputValue != null) {\n          // exclude both null and undefined\n          options[inputName] = inputValue;\n        }\n      }\n      return options;\n    }\n    buildExtraOptions() {\n      return {\n        handleCustomRendering: this.handleCustomRendering,\n        customRenderingMetaMap: this.templateMap,\n        customRenderingReplaces: true\n      };\n    }\n    // for `trackBy` in loop\n    trackCustomRendering(index, customRendering) {\n      return customRendering.id;\n    }\n  }\n  FullCalendarComponent.ɵfac = function FullCalendarComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FullCalendarComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  FullCalendarComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: FullCalendarComponent,\n    selectors: [[\"full-calendar\"]],\n    contentQueries: function FullCalendarComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c3, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c4, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c5, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c6, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c7, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c8, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c9, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c10, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c11, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c12, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c13, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c14, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c15, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c16, 7);\n        i0.ɵɵcontentQuery(dirIndex, _c17, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dayHeaderContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dayCellContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.weekNumberContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nowIndicatorContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.eventContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slotLaneContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slotLabelContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.allDayContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.moreLinkContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.noEventsContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceAreaHeaderContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceGroupLabelContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceLabelContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceLaneContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceGroupLaneContent = _t.first);\n      }\n    },\n    inputs: {\n      options: \"options\",\n      deepChangeDetection: \"deepChangeDetection\",\n      events: \"events\",\n      eventSources: \"eventSources\",\n      resources: \"resources\"\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[3, \"inPlaceOf\", \"reportEl\", \"elTag\", \"elClasses\", \"elStyle\", \"elAttrs\", \"template\", \"renderProps\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"inPlaceOf\", \"reportEl\", \"elTag\", \"elClasses\", \"elStyle\", \"elAttrs\", \"template\", \"renderProps\"]],\n    template: function FullCalendarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"offscreen-fragment\");\n        i0.ɵɵtemplate(1, FullCalendarComponent_transport_container_1_Template, 1, 8, \"transport-container\", 0);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.customRenderings)(\"ngForTrackBy\", ctx.trackCustomRendering);\n      }\n    },\n    dependencies: [OffscreenFragmentComponent, TransportContainerComponent, i1.NgForOf],\n    encapsulation: 2\n  });\n  return FullCalendarComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet FullCalendarModule = /*#__PURE__*/(() => {\n  class FullCalendarModule {}\n  FullCalendarModule.ɵfac = function FullCalendarModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FullCalendarModule)();\n  };\n  FullCalendarModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FullCalendarModule\n  });\n  FullCalendarModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule]]\n  });\n  return FullCalendarModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * Public API Surface of lib\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FullCalendarComponent, FullCalendarModule };\n//# sourceMappingURL=fullcalendar-angular.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}