/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetListByHouseIdRequest } from '../../models/get-list-by-house-id-request';
import { SaveDataQuotationResponseBase } from '../../models/save-data-quotation-response-base';

export interface ApiQuotationGetListByHouseIdPost$Plain$Params {
      body?: GetListByHouseIdRequest
}

export function apiQuotationGetListByHouseIdPost$Plain(http: HttpClient, rootUrl: string, params?: ApiQuotationGetListByHouseIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<SaveDataQuotationResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiQuotationGetListByHouseIdPost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<SaveDataQuotationResponseBase>;
    })
  );
}

apiQuotationGetListByHouseIdPost$Plain.PATH = '/api/Quotation/GetListByHouseID';
