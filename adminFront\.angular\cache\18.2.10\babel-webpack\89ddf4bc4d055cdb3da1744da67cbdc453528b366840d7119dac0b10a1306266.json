{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction TemplateCreatorComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.validationErrors[\"api\"], \" \");\n  }\n}\nfunction TemplateCreatorComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.validationErrors[\"name\"], \" \");\n  }\n}\nfunction TemplateCreatorComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\", 38)(2, \"input\", 39);\n    i0.ɵɵlistener(\"change\", function TemplateCreatorComponent_div_32_Template_input_change_2_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleSelectAll());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 40);\n    i0.ɵɵelementStart(4, \"span\", 41);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r0.isAllSelected())(\"indeterminate\", ctx_r0.isIndeterminate())(\"disabled\", ctx_r0.isSubmitting);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isAllSelected() ? \"\\u53D6\\u6D88\\u5168\\u9078\" : \"\\u5168\\u9078\", \" \");\n  }\n}\nfunction TemplateCreatorComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u66AB\\u7121\\u53EF\\u9078\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateCreatorComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"label\", 45);\n    i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_div_35_Template_label_click_1_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleItemSelection(item_r4));\n    });\n    i0.ɵɵelementStart(2, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateCreatorComponent_div_35_Template_input_ngModelChange_2_listener($event) {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r4.selected, $event) || (item_r4.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_div_35_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 47)(4, \"div\", 48);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"item_\", i_r5, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r4.selected);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSubmitting);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r4.CRequirement || item_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.CGroupName || item_r4.description);\n  }\n}\nfunction TemplateCreatorComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.validationErrors[\"items\"], \" \");\n  }\n}\nfunction TemplateCreatorComponent_i_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 52);\n  }\n}\nfunction TemplateCreatorComponent_i_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 53);\n  }\n}\nexport class TemplateCreatorComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.availableData = []; // 父元件傳入的可選資料\n    this.templateType = 1; // 模板類型，1=客變需求\n    this.close = new EventEmitter(); // 關閉事件\n    this.templateCreated = new EventEmitter(); // 模板創建成功事件\n    // 新增模板表單\n    this.newTemplate = {\n      name: ''\n    };\n    // 表單驗證狀態\n    this.isSubmitting = false;\n    this.validationErrors = {};\n  }\n  ngOnInit() {\n    // 初始化時重置所有選擇狀態\n    this.resetForm();\n  }\n  // 重置表單\n  resetForm() {\n    this.newTemplate = {\n      name: ''\n    };\n    this.validationErrors = {};\n    this.isSubmitting = false;\n    // 重置選擇狀態\n    if (this.availableData) {\n      this.availableData.forEach(item => item.selected = false);\n    }\n  }\n  // 驗證表單\n  validateForm() {\n    this.validationErrors = {};\n    let isValid = true;\n    // 驗證模板名稱\n    if (!this.newTemplate.name.trim()) {\n      this.validationErrors['name'] = '請輸入模板名稱';\n      isValid = false;\n    } else if (this.newTemplate.name.trim().length > 50) {\n      this.validationErrors['name'] = '模板名稱不能超過50個字符';\n      isValid = false;\n    }\n    // 驗證是否選擇了項目\n    const selectedItems = this.getSelectedItems();\n    if (selectedItems.length === 0) {\n      this.validationErrors['items'] = '請至少選擇一個項目';\n      isValid = false;\n    }\n    return isValid;\n  }\n  // 獲取選中的項目\n  getSelectedItems() {\n    if (!this.availableData) return [];\n    return this.availableData.filter(item => item.selected).map(item => ({\n      CGroupName: item.CGroupName || null,\n      CReleateName: item.CReleateName || item.CRequirement || item.name || null,\n      CReleateId: item.CReleateId || item.CRequirementID || item.ID || item.id || 0\n    }));\n  }\n  // 儲存新模板\n  saveTemplate() {\n    if (!this.validateForm()) {\n      return;\n    }\n    this.isSubmitting = true;\n    const selectedItems = this.getSelectedItems();\n    // 準備 API 請求資料\n    const saveTemplateArgs = {\n      CTemplateId: null,\n      // 新增時為 null\n      CTemplateName: this.newTemplate.name.trim(),\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      CStatus: 1,\n      // 啟用狀態\n      Details: selectedItems.map(item => ({\n        CTemplateDetailId: null,\n        // 新增時為 null\n        CReleateId: item.CReleateId,\n        // 關聯主檔ID\n        CReleateName: item.CReleateName,\n        // 關聯名稱\n        CGroupName: item.CGroupName // 群組名稱\n      }))\n    };\n    // 調用 SaveTemplate API\n    this.templateService.apiTemplateSaveTemplatePost$Json({\n      body: saveTemplateArgs\n    }).subscribe({\n      next: response => {\n        this.isSubmitting = false;\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          this.templateCreated.emit(); // 通知父組件模板創建成功\n          this.close.emit(); // 關閉對話框\n        } else {\n          // API 返回錯誤\n          this.validationErrors['api'] = response.Message || '保存失敗，請稍後重試';\n        }\n      },\n      error: error => {\n        this.isSubmitting = false;\n        this.validationErrors['api'] = '網絡錯誤，請檢查網絡連接後重試';\n        console.error('保存模板失敗:', error);\n      }\n    });\n  }\n  // 取消操作\n  cancel() {\n    this.close.emit();\n  }\n  // 獲取選中項目數量\n  getSelectedCount() {\n    return this.availableData ? this.availableData.filter(item => item.selected).length : 0;\n  }\n  // 切換項目選擇狀態\n  toggleItemSelection(item) {\n    item.selected = !item.selected;\n    // 清除項目選擇相關的驗證錯誤\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\n      delete this.validationErrors['items'];\n    }\n  }\n  // 全選/取消全選\n  toggleSelectAll() {\n    const hasUnselected = this.availableData.some(item => !item.selected);\n    this.availableData.forEach(item => item.selected = hasUnselected);\n    // 清除項目選擇相關的驗證錯誤\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\n      delete this.validationErrors['items'];\n    }\n  }\n  // 檢查是否全選\n  isAllSelected() {\n    return this.availableData && this.availableData.length > 0 && this.availableData.every(item => item.selected);\n  }\n  // 檢查是否部分選中\n  isIndeterminate() {\n    const selectedCount = this.getSelectedCount();\n    return selectedCount > 0 && selectedCount < this.availableData.length;\n  }\n  static {\n    this.ɵfac = function TemplateCreatorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateCreatorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateCreatorComponent,\n      selectors: [[\"app-template-creator\"]],\n      inputs: {\n        availableData: \"availableData\",\n        templateType: \"templateType\"\n      },\n      outputs: {\n        close: \"close\",\n        templateCreated: \"templateCreated\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 46,\n      vars: 18,\n      consts: [[2, \"width\", \"90vw\", \"max-width\", \"800px\", \"height\", \"80vh\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"fas\", \"fa-plus\", \"mr-2\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [2, \"overflow\", \"auto\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [1, \"template-form\", 3, \"ngSubmit\"], [1, \"form-section\", \"mb-4\"], [1, \"section-title\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [1, \"input-group\"], [1, \"input-label\"], [1, \"required\"], [\"type\", \"text\", \"name\", \"templateName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"maxlength\", \"50\", 1, \"input-field\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"form-section\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"section-title\", \"mb-0\"], [1, \"fas\", \"fa-list\", \"mr-2\"], [1, \"selection-summary\"], [1, \"badge\", \"badge-primary\"], [\"class\", \"select-all-control mb-3\", 4, \"ngIf\"], [1, \"items-selector\"], [\"class\", \"empty-items\", 4, \"ngIf\"], [\"class\", \"item-option\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"invalid-feedback d-block\", 4, \"ngIf\"], [1, \"form-actions\", \"d-flex\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"mr-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", 3, \"click\", \"disabled\"], [\"class\", \"fas fa-spinner fa-spin mr-1\", 4, \"ngIf\"], [\"class\", \"fas fa-save mr-1\", 4, \"ngIf\"], [1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-2\"], [1, \"invalid-feedback\"], [1, \"select-all-control\", \"mb-3\"], [1, \"select-all-label\"], [\"type\", \"checkbox\", 1, \"select-all-checkbox\", 3, \"change\", \"checked\", \"indeterminate\", \"disabled\"], [1, \"checkmark\"], [1, \"select-all-text\"], [1, \"empty-items\"], [1, \"fas\", \"fa-info-circle\"], [1, \"item-option\"], [1, \"item-label\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"item-checkbox\", 3, \"ngModelChange\", \"click\", \"ngModel\", \"name\", \"disabled\"], [1, \"item-content\"], [1, \"item-title\"], [1, \"item-desc\"], [1, \"invalid-feedback\", \"d-block\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-1\"], [1, \"fas\", \"fa-save\", \"mr-1\"]],\n      template: function TemplateCreatorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\")(2, \"div\", 1)(3, \"h5\", 2);\n          i0.ɵɵelement(4, \"i\", 3);\n          i0.ɵɵtext(5, \"\\u65B0\\u589E\\u6A21\\u677F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_Template_button_click_6_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelement(7, \"i\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"nb-card-body\", 6);\n          i0.ɵɵtemplate(9, TemplateCreatorComponent_div_9_Template, 3, 1, \"div\", 7);\n          i0.ɵɵelementStart(10, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function TemplateCreatorComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.saveTemplate();\n          });\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"h6\", 10);\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵtext(14, \"\\u6A21\\u677F\\u8CC7\\u8A0A \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"label\", 13);\n          i0.ɵɵtext(17, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n          i0.ɵɵelementStart(18, \"span\", 14);\n          i0.ɵɵtext(19, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"input\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateCreatorComponent_Template_input_ngModelChange_20_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.newTemplate.name, $event) || (ctx.newTemplate.name = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(21, TemplateCreatorComponent_div_21_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 17)(23, \"div\", 18)(24, \"h6\", 19);\n          i0.ɵɵelement(25, \"i\", 20);\n          i0.ɵɵtext(26, \"\\u9078\\u64C7\\u8981\\u52A0\\u5165\\u6A21\\u677F\\u7684\\u9805\\u76EE \");\n          i0.ɵɵelementStart(27, \"span\", 14);\n          i0.ɵɵtext(28, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 21)(30, \"span\", 22);\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(32, TemplateCreatorComponent_div_32_Template, 6, 4, \"div\", 23);\n          i0.ɵɵelementStart(33, \"div\", 24);\n          i0.ɵɵtemplate(34, TemplateCreatorComponent_div_34_Template, 4, 0, \"div\", 25)(35, TemplateCreatorComponent_div_35_Template, 8, 6, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(36, TemplateCreatorComponent_div_36_Template, 3, 1, \"div\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"nb-card-footer\")(38, \"div\", 28)(39, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_Template_button_click_39_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelement(40, \"i\", 30);\n          i0.ɵɵtext(41, \"\\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_Template_button_click_42_listener() {\n            return ctx.saveTemplate();\n          });\n          i0.ɵɵtemplate(43, TemplateCreatorComponent_i_43_Template, 1, 0, \"i\", 32)(44, TemplateCreatorComponent_i_44_Template, 1, 0, \"i\", 33);\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.validationErrors[\"api\"]);\n          i0.ɵɵadvance(11);\n          i0.ɵɵclassProp(\"is-invalid\", ctx.validationErrors[\"name\"]);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newTemplate.name);\n          i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.validationErrors[\"name\"]);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7 \", ctx.getSelectedCount(), \" \\u9805\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.availableData.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"has-error\", ctx.validationErrors[\"items\"]);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.availableData.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.validationErrors[\"items\"]);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"\\u4FDD\\u5B58\\u4E2D...\" : \"\\u5132\\u5B58\\u6A21\\u677F\", \" \");\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, NbCardModule, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardFooterComponent, i4.NbCardHeaderComponent, NbButtonModule],\n      styles: [\".template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%] {\\n  border: 1px solid #e4e9f0;\\n  border-radius: 8px;\\n  padding: 1.5rem;\\n  background-color: #fafbfc;\\n}\\n.template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n  margin-bottom: 1rem;\\n}\\n.template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3498db;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 500;\\n  color: #2c3e50;\\n  margin-bottom: 0.5rem;\\n  font-size: 0.9rem;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n  margin-left: 2px;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 0.9rem;\\n  transition: border-color 0.3s ease;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3498db;\\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #e74c3c;\\n  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:disabled {\\n  background-color: #f8f9fa;\\n  cursor: not-allowed;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n  font-size: 0.8rem;\\n  margin-top: 0.25rem;\\n  display: block;\\n}\\n\\n.selection-summary[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.4rem 0.8rem;\\n}\\n\\n.select-all-control[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 4px;\\n}\\n.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 0;\\n  cursor: pointer;\\n  font-weight: 500;\\n}\\n.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  transform: scale(1.1);\\n}\\n.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%]   .select-all-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%]:hover   .select-all-text[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.items-selector[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n  border: 1px solid #dee2e6;\\n  border-radius: 4px;\\n  background-color: white;\\n}\\n.items-selector.has-error[_ngcontent-%COMP%] {\\n  border-color: #e74c3c;\\n}\\n.items-selector[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #6c757d;\\n}\\n.items-selector[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  display: block;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f1f3f4;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem;\\n  margin: 0;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  transform: scale(1.1);\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2c3e50;\\n  margin-bottom: 0.25rem;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-desc[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #6c757d;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  gap: 0.5rem;\\n}\\n.form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n  font-weight: 500;\\n}\\n.form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n}\\n\\n@media (max-width: 768px) {\\n  .template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .items-selector[_ngcontent-%COMP%] {\\n    max-height: 250px;\\n  }\\n  .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 0.5rem;\\n  }\\n}\\n.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n.item-option[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  transition: box-shadow 0.3s ease;\\n}\\n.form-section[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvdGVtcGxhdGUtY3JlYXRvci90ZW1wbGF0ZS1jcmVhdG9yLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVFO0VBQ0UseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtBQURKO0FBR0k7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtBQUROO0FBR007RUFDRSxjQUFBO0FBRFI7QUFNRTtFQUNFLG1CQUFBO0FBSko7QUFNSTtFQUNFLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxxQkFBQTtFQUNBLGlCQUFBO0FBSk47QUFNTTtFQUNFLGNBQUE7RUFDQSxnQkFBQTtBQUpSO0FBUUk7RUFDRSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQ0FBQTtBQU5OO0FBUU07RUFDRSxhQUFBO0VBQ0EscUJBQUE7RUFDQSw2Q0FBQTtBQU5SO0FBU007RUFDRSxxQkFBQTtFQUNBLDRDQUFBO0FBUFI7QUFVTTtFQUNFLHlCQUFBO0VBQ0EsbUJBQUE7QUFSUjtBQVlJO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0FBVk47O0FBaUJFO0VBQ0UsaUJBQUE7RUFDQSxzQkFBQTtBQWRKOztBQW1CQTtFQUNFLGdCQUFBO0VBQ0EseUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0FBaEJGO0FBa0JFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtBQWhCSjtBQWtCSTtFQUNFLG9CQUFBO0VBQ0EscUJBQUE7QUFoQk47QUFtQkk7RUFDRSxjQUFBO0FBakJOO0FBcUJNO0VBQ0UsY0FBQTtBQW5CUjs7QUEwQkE7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLHVCQUFBO0FBdkJGO0FBeUJFO0VBQ0UscUJBQUE7QUF2Qko7QUEwQkU7RUFDRSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxjQUFBO0FBeEJKO0FBMEJJO0VBQ0UsZUFBQTtFQUNBLHFCQUFBO0VBQ0EsY0FBQTtBQXhCTjtBQTRCRTtFQUNFLGdDQUFBO0FBMUJKO0FBNEJJO0VBQ0UsbUJBQUE7QUExQk47QUE2Qkk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLFNBQUE7RUFDQSxlQUFBO0VBQ0Esc0NBQUE7QUEzQk47QUE2Qk07RUFDRSx5QkFBQTtBQTNCUjtBQThCTTtFQUNFLHFCQUFBO0VBQ0EscUJBQUE7QUE1QlI7QUE4QlE7RUFDRSxtQkFBQTtBQTVCVjtBQWdDTTtFQUNFLE9BQUE7QUE5QlI7QUFnQ1E7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxzQkFBQTtBQTlCVjtBQWlDUTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtBQS9CVjs7QUF1Q0E7RUFDRSxXQUFBO0FBcENGO0FBc0NFO0VBQ0UsZ0JBQUE7RUFDQSxnQkFBQTtBQXBDSjtBQXNDSTtFQUNFLHFCQUFBO0FBcENOO0FBdUNJO0VBQ0UsbUJBQUE7QUFyQ047O0FBMkNBO0VBRUk7SUFDRSxhQUFBO0VBekNKO0VBNkNBO0lBQ0UsaUJBQUE7RUEzQ0Y7RUE4Q0E7SUFDRSxzQkFBQTtFQTVDRjtFQThDRTtJQUNFLFdBQUE7SUFDQSxxQkFBQTtFQTVDSjtBQUNGO0FBa0RFO0VBQ0UsVUFBQTtBQWhESjtBQW1ERTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7QUFqREo7QUFvREU7RUFDRSxtQkFBQTtFQUNBLGtCQUFBO0FBbERKO0FBb0RJO0VBQ0UsbUJBQUE7QUFsRE47O0FBd0RBO0VBQ0UseUJBQUE7QUFyREY7O0FBd0RBO0VBQ0UsZ0NBQUE7QUFyREY7QUF1REU7RUFDRSx3Q0FBQTtBQXJESjtBQUNBLHc3UUFBdzdRIiwic291cmNlc0NvbnRlbnQiOlsiLy8gw6jCocKow6XClsKuw6bCqMKjw6XCvMKPXHJcbi50ZW1wbGF0ZS1mb3JtIHtcclxuICAuZm9ybS1zZWN0aW9uIHtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNGU5ZjA7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYmZjO1xyXG5cclxuICAgIC5zZWN0aW9uLXRpdGxlIHtcclxuICAgICAgY29sb3I6ICMyYzNlNTA7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XHJcblxyXG4gICAgICBpIHtcclxuICAgICAgICBjb2xvcjogIzM0OThkYjtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmlucHV0LWdyb3VwIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDFyZW07XHJcblxyXG4gICAgLmlucHV0LWxhYmVsIHtcclxuICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgIGNvbG9yOiAjMmMzZTUwO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG5cclxuICAgICAgLnJlcXVpcmVkIHtcclxuICAgICAgICBjb2xvcjogI2U3NGMzYztcclxuICAgICAgICBtYXJnaW4tbGVmdDogMnB4O1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLmlucHV0LWZpZWxkIHtcclxuICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgIHBhZGRpbmc6IDAuNzVyZW07XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgIHRyYW5zaXRpb246IGJvcmRlci1jb2xvciAwLjNzIGVhc2U7XHJcblxyXG4gICAgICAmOmZvY3VzIHtcclxuICAgICAgICBvdXRsaW5lOiBub25lO1xyXG4gICAgICAgIGJvcmRlci1jb2xvcjogIzM0OThkYjtcclxuICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSg1MiwgMTUyLCAyMTksIDAuMik7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuaXMtaW52YWxpZCB7XHJcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAjZTc0YzNjO1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDIzMSwgNzYsIDYwLCAwLjIpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmOmRpc2FibGVkIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuaW52YWxpZC1mZWVkYmFjayB7XHJcbiAgICAgIGNvbG9yOiAjZTc0YzNjO1xyXG4gICAgICBmb250LXNpemU6IDAuOHJlbTtcclxuICAgICAgbWFyZ2luLXRvcDogMC4yNXJlbTtcclxuICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDDqcKBwrjDpsKTwofDpsKRwpjDqMKmwoFcclxuLnNlbGVjdGlvbi1zdW1tYXJ5IHtcclxuICAuYmFkZ2Uge1xyXG4gICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICBwYWRkaW5nOiAwLjRyZW0gMC44cmVtO1xyXG4gIH1cclxufVxyXG5cclxuLy8gw6XChcKow6nCgcK4w6bCjsKnw6XCiMK2XHJcbi5zZWxlY3QtYWxsLWNvbnRyb2wge1xyXG4gIHBhZGRpbmc6IDAuNzVyZW07XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjZGVlMmU2O1xyXG4gIGJvcmRlci1yYWRpdXM6IDRweDtcclxuXHJcbiAgLnNlbGVjdC1hbGwtbGFiZWwge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBtYXJnaW46IDA7XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICBmb250LXdlaWdodDogNTAwO1xyXG5cclxuICAgIC5zZWxlY3QtYWxsLWNoZWNrYm94IHtcclxuICAgICAgbWFyZ2luLXJpZ2h0OiAwLjVyZW07XHJcbiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcclxuICAgIH1cclxuXHJcbiAgICAuc2VsZWN0LWFsbC10ZXh0IHtcclxuICAgICAgY29sb3I6ICM0OTUwNTc7XHJcbiAgICB9XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIC5zZWxlY3QtYWxsLXRleHQge1xyXG4gICAgICAgIGNvbG9yOiAjMDA3YmZmO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDDqcKgwoXDp8Kbwq7DqcKBwrjDpsKTwofDpcKZwqhcclxuLml0ZW1zLXNlbGVjdG9yIHtcclxuICBtYXgtaGVpZ2h0OiAzMDBweDtcclxuICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNkZWUyZTY7XHJcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xyXG5cclxuICAmLmhhcy1lcnJvciB7XHJcbiAgICBib3JkZXItY29sb3I6ICNlNzRjM2M7XHJcbiAgfVxyXG5cclxuICAuZW1wdHktaXRlbXMge1xyXG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgcGFkZGluZzogMnJlbTtcclxuICAgIGNvbG9yOiAjNmM3NTdkO1xyXG5cclxuICAgIGkge1xyXG4gICAgICBmb250LXNpemU6IDJyZW07XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcclxuICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuaXRlbS1vcHRpb24ge1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMWYzZjQ7XHJcblxyXG4gICAgJjpsYXN0LWNoaWxkIHtcclxuICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTtcclxuICAgIH1cclxuXHJcbiAgICAuaXRlbS1sYWJlbCB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIHBhZGRpbmc6IDAuNzVyZW07XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuMnMgZWFzZTtcclxuXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5pdGVtLWNoZWNrYm94IHtcclxuICAgICAgICBtYXJnaW4tcmlnaHQ6IDAuNzVyZW07XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG5cclxuICAgICAgICAmOmRpc2FibGVkIHtcclxuICAgICAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAuaXRlbS1jb250ZW50IHtcclxuICAgICAgICBmbGV4OiAxO1xyXG5cclxuICAgICAgICAuaXRlbS10aXRsZSB7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgY29sb3I6ICMyYzNlNTA7XHJcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLml0ZW0tZGVzYyB7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuODVyZW07XHJcbiAgICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIMOowqHCqMOlwpbCrsOmwpPCjcOkwr3CnMOmwozCicOpwojClVxyXG4uZm9ybS1hY3Rpb25zIHtcclxuICBnYXA6IDAuNXJlbTtcclxuXHJcbiAgLmJ0biB7XHJcbiAgICBtaW4td2lkdGg6IDEwMHB4O1xyXG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuXHJcbiAgICBpIHtcclxuICAgICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtO1xyXG4gICAgfVxyXG5cclxuICAgICY6ZGlzYWJsZWQge1xyXG4gICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gw6nCn8K/w6bCh8KJw6XCvMKPw6jCqMKtw6jCqMKIXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xyXG4gIC50ZW1wbGF0ZS1mb3JtIHtcclxuICAgIC5mb3JtLXNlY3Rpb24ge1xyXG4gICAgICBwYWRkaW5nOiAxcmVtO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLml0ZW1zLXNlbGVjdG9yIHtcclxuICAgIG1heC1oZWlnaHQ6IDI1MHB4O1xyXG4gIH1cclxuXHJcbiAgLmZvcm0tYWN0aW9ucyB7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG5cclxuICAgIC5idG4ge1xyXG4gICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gw6bCu8K+w6XCi8KVw6bCosKdw6bCqMKjw6XCvMKPXHJcbi5pdGVtcy1zZWxlY3RvciB7XHJcbiAgJjo6LXdlYmtpdC1zY3JvbGxiYXIge1xyXG4gICAgd2lkdGg6IDZweDtcclxuICB9XHJcblxyXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcclxuICAgIGJhY2tncm91bmQ6ICNmMWYxZjE7XHJcbiAgICBib3JkZXItcmFkaXVzOiAzcHg7XHJcbiAgfVxyXG5cclxuICAmOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XHJcbiAgICBiYWNrZ3JvdW5kOiAjYzFjMWMxO1xyXG4gICAgYm9yZGVyLXJhZGl1czogM3B4O1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjYThhOGE4O1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gw6XCi8KVw6fClcKrw6bClcKIw6bCnsKcXHJcbi5pdGVtLW9wdGlvbiB7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxufVxyXG5cclxuLmZvcm0tc2VjdGlvbiB7XHJcbiAgdHJhbnNpdGlvbjogYm94LXNoYWRvdyAwLjNzIGVhc2U7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "validationErrors", "ɵɵlistener", "TemplateCreatorComponent_div_32_Template_input_change_2_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "toggleSelectAll", "ɵɵproperty", "isAllSelected", "isIndeterminate", "isSubmitting", "TemplateCreatorComponent_div_35_Template_label_click_1_listener", "item_r4", "_r3", "$implicit", "toggleItemSelection", "ɵɵtwoWayListener", "TemplateCreatorComponent_div_35_Template_input_ngModelChange_2_listener", "$event", "ɵɵtwoWayBindingSet", "selected", "TemplateCreatorComponent_div_35_Template_input_click_2_listener", "stopPropagation", "ɵɵpropertyInterpolate1", "i_r5", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CRequirement", "name", "CGroupName", "description", "TemplateCreatorComponent", "constructor", "templateService", "availableData", "templateType", "close", "templateCreated", "newTemplate", "ngOnInit", "resetForm", "for<PERSON>ach", "item", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "trim", "length", "selectedItems", "getSelectedItems", "filter", "map", "CReleateName", "CReleateId", "CRequirementID", "ID", "id", "saveTemplate", "saveTemplateArgs", "CTemplateId", "CTemplateName", "CTemplateType", "CStatus", "Details", "CTemplateDetailId", "apiTemplateSaveTemplatePost$Json", "body", "subscribe", "next", "response", "StatusCode", "emit", "Message", "error", "console", "cancel", "getSelectedCount", "has<PERSON><PERSON><PERSON>", "some", "every", "selectedCount", "ɵɵdirectiveInject", "i1", "TemplateService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TemplateCreatorComponent_Template", "rf", "ctx", "TemplateCreatorComponent_Template_button_click_6_listener", "ɵɵtemplate", "TemplateCreatorComponent_div_9_Template", "TemplateCreatorComponent_Template_form_ngSubmit_10_listener", "TemplateCreatorComponent_Template_input_ngModelChange_20_listener", "TemplateCreatorComponent_div_21_Template", "TemplateCreatorComponent_div_32_Template", "TemplateCreatorComponent_div_34_Template", "TemplateCreatorComponent_div_35_Template", "TemplateCreatorComponent_div_36_Template", "TemplateCreatorComponent_Template_button_click_39_listener", "TemplateCreatorComponent_Template_button_click_42_listener", "TemplateCreatorComponent_i_43_Template", "TemplateCreatorComponent_i_44_Template", "ɵɵclassProp", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "CheckboxControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "MaxLengthValidator", "NgModel", "NgForm", "i4", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-creator\\template-creator.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-creator\\template-creator.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { SaveTemplateArgs, SaveTemplateDetailArgs } from 'src/services/api/models';\r\n\r\n// 選中項目的介面定義\r\ninterface SelectedItem {\r\n  CGroupName: string | null;\r\n  CReleateName: string | null;\r\n  CReleateId: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-template-creator',\r\n  templateUrl: './template-creator.component.html',\r\n  styleUrls: ['./template-creator.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateCreatorComponent implements OnInit {\r\n  @Input() availableData: any[] = []; // 父元件傳入的可選資料\r\n  @Input() templateType: number = 1; // 模板類型，1=客變需求\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n  @Output() templateCreated = new EventEmitter<void>(); // 模板創建成功事件\r\n\r\n  // 新增模板表單\r\n  newTemplate = {\r\n    name: ''\r\n  };\r\n\r\n  // 表單驗證狀態\r\n  isSubmitting = false;\r\n  validationErrors: { [key: string]: string } = {};\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    // 初始化時重置所有選擇狀態\r\n    this.resetForm();\r\n  }\r\n\r\n  // 重置表單\r\n  resetForm() {\r\n    this.newTemplate = {\r\n      name: ''\r\n    };\r\n    this.validationErrors = {};\r\n    this.isSubmitting = false;\r\n\r\n    // 重置選擇狀態\r\n    if (this.availableData) {\r\n      this.availableData.forEach(item => item.selected = false);\r\n    }\r\n  }\r\n\r\n  // 驗證表單\r\n  validateForm(): boolean {\r\n    this.validationErrors = {};\r\n    let isValid = true;\r\n\r\n    // 驗證模板名稱\r\n    if (!this.newTemplate.name.trim()) {\r\n      this.validationErrors['name'] = '請輸入模板名稱';\r\n      isValid = false;\r\n    } else if (this.newTemplate.name.trim().length > 50) {\r\n      this.validationErrors['name'] = '模板名稱不能超過50個字符';\r\n      isValid = false;\r\n    }\r\n\r\n\r\n\r\n    // 驗證是否選擇了項目\r\n    const selectedItems = this.getSelectedItems();\r\n    if (selectedItems.length === 0) {\r\n      this.validationErrors['items'] = '請至少選擇一個項目';\r\n      isValid = false;\r\n    }\r\n\r\n    return isValid;\r\n  }\r\n\r\n  // 獲取選中的項目\r\n  getSelectedItems(): SelectedItem[] {\r\n    if (!this.availableData) return [];\r\n\r\n    return this.availableData\r\n      .filter(item => item.selected)\r\n      .map(item => ({\r\n        CGroupName: item.CGroupName || null,\r\n        CReleateName: item.CReleateName || item.CRequirement || item.name || null,\r\n        CReleateId: item.CReleateId || item.CRequirementID || item.ID || item.id || 0\r\n      }));\r\n  }\r\n\r\n  // 儲存新模板\r\n  saveTemplate() {\r\n    if (!this.validateForm()) {\r\n      return;\r\n    }\r\n\r\n    this.isSubmitting = true;\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    // 準備 API 請求資料\r\n    const saveTemplateArgs: SaveTemplateArgs = {\r\n      CTemplateId: null, // 新增時為 null\r\n      CTemplateName: this.newTemplate.name.trim(),\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      CStatus: 1, // 啟用狀態\r\n      Details: selectedItems.map(item => ({\r\n        CTemplateDetailId: null, // 新增時為 null\r\n        CReleateId: item.CReleateId, // 關聯主檔ID\r\n        CReleateName: item.CReleateName, // 關聯名稱\r\n        CGroupName: item.CGroupName // 群組名稱\r\n      } as SaveTemplateDetailArgs))\r\n    };\r\n\r\n    // 調用 SaveTemplate API\r\n    this.templateService.apiTemplateSaveTemplatePost$Json({\r\n      body: saveTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        this.isSubmitting = false;\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          this.templateCreated.emit(); // 通知父組件模板創建成功\r\n          this.close.emit(); // 關閉對話框\r\n        } else {\r\n          // API 返回錯誤\r\n          this.validationErrors['api'] = response.Message || '保存失敗，請稍後重試';\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isSubmitting = false;\r\n        this.validationErrors['api'] = '網絡錯誤，請檢查網絡連接後重試';\r\n        console.error('保存模板失敗:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 取消操作\r\n  cancel() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 獲取選中項目數量\r\n  getSelectedCount(): number {\r\n    return this.availableData ? this.availableData.filter(item => item.selected).length : 0;\r\n  }\r\n\r\n  // 切換項目選擇狀態\r\n  toggleItemSelection(item: any) {\r\n    item.selected = !item.selected;\r\n    // 清除項目選擇相關的驗證錯誤\r\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\r\n      delete this.validationErrors['items'];\r\n    }\r\n  }\r\n\r\n  // 全選/取消全選\r\n  toggleSelectAll() {\r\n    const hasUnselected = this.availableData.some(item => !item.selected);\r\n    this.availableData.forEach(item => item.selected = hasUnselected);\r\n\r\n    // 清除項目選擇相關的驗證錯誤\r\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\r\n      delete this.validationErrors['items'];\r\n    }\r\n  }\r\n\r\n  // 檢查是否全選\r\n  isAllSelected(): boolean {\r\n    return this.availableData && this.availableData.length > 0 &&\r\n      this.availableData.every(item => item.selected);\r\n  }\r\n\r\n  // 檢查是否部分選中\r\n  isIndeterminate(): boolean {\r\n    const selectedCount = this.getSelectedCount();\r\n    return selectedCount > 0 && selectedCount < this.availableData.length;\r\n  }\r\n}\r\n", "<nb-card style=\"width: 90vw; max-width: 800px; height: 80vh;\">\r\n  <nb-card-header>\r\n    <div class=\"d-flex justify-content-between align-items-center\">\r\n      <h5 class=\"mb-0\">\r\n        <i class=\"fas fa-plus mr-2\"></i>新增模板\r\n      </h5>\r\n      <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"cancel()\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </div>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body style=\"overflow: auto;\">\r\n    <!-- API 錯誤提示 -->\r\n    <div class=\"alert alert-danger\" *ngIf=\"validationErrors['api']\">\r\n      <i class=\"fas fa-exclamation-triangle mr-2\"></i>\r\n      {{ validationErrors['api'] }}\r\n    </div>\r\n\r\n    <form (ngSubmit)=\"saveTemplate()\" class=\"template-form\">\r\n      <!-- 模板基本資訊 -->\r\n      <div class=\"form-section mb-4\">\r\n        <h6 class=\"section-title\">\r\n          <i class=\"fas fa-info-circle mr-2\"></i>模板資訊\r\n        </h6>\r\n\r\n        <div class=\"input-group\">\r\n          <label class=\"input-label\">\r\n            模板名稱 <span class=\"required\">*</span>\r\n          </label>\r\n          <input type=\"text\" class=\"input-field\" [class.is-invalid]=\"validationErrors['name']\"\r\n            [(ngModel)]=\"newTemplate.name\" name=\"templateName\" placeholder=\"請輸入模板名稱\" maxlength=\"50\"\r\n            [disabled]=\"isSubmitting\">\r\n          <div class=\"invalid-feedback\" *ngIf=\"validationErrors['name']\">\r\n            {{ validationErrors['name'] }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 項目選擇 -->\r\n      <div class=\"form-section\">\r\n        <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n          <h6 class=\"section-title mb-0\">\r\n            <i class=\"fas fa-list mr-2\"></i>選擇要加入模板的項目 <span class=\"required\">*</span>\r\n          </h6>\r\n          <div class=\"selection-summary\">\r\n            <span class=\"badge badge-primary\">已選擇 {{ getSelectedCount() }} 項</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 全選控制 -->\r\n        <div class=\"select-all-control mb-3\" *ngIf=\"availableData.length > 0\">\r\n          <label class=\"select-all-label\">\r\n            <input type=\"checkbox\" class=\"select-all-checkbox\" [checked]=\"isAllSelected()\"\r\n              [indeterminate]=\"isIndeterminate()\" (change)=\"toggleSelectAll()\" [disabled]=\"isSubmitting\">\r\n            <span class=\"checkmark\"></span>\r\n            <span class=\"select-all-text\">\r\n              {{ isAllSelected() ? '取消全選' : '全選' }}\r\n            </span>\r\n          </label>\r\n        </div>\r\n\r\n        <!-- 項目列表 -->\r\n        <div class=\"items-selector\" [class.has-error]=\"validationErrors['items']\">\r\n          <div *ngIf=\"availableData.length === 0\" class=\"empty-items\">\r\n            <i class=\"fas fa-info-circle\"></i>\r\n            <span>暫無可選項目</span>\r\n          </div>\r\n\r\n          <div *ngFor=\"let item of availableData; let i = index\" class=\"item-option\">\r\n            <label class=\"item-label\" (click)=\"toggleItemSelection(item)\">\r\n              <input type=\"checkbox\" class=\"item-checkbox\" [(ngModel)]=\"item.selected\" name=\"item_{{i}}\"\r\n                [disabled]=\"isSubmitting\" (click)=\"$event.stopPropagation()\">\r\n              <div class=\"item-content\">\r\n                <div class=\"item-title\">{{ item.CRequirement || item.name }}</div>\r\n                <div class=\"item-desc\">{{ item.CGroupName || item.description }}</div>\r\n              </div>\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"invalid-feedback d-block\" *ngIf=\"validationErrors['items']\">\r\n          <i class=\"fas fa-exclamation-triangle mr-1\"></i>\r\n          {{ validationErrors['items'] }}\r\n        </div>\r\n      </div>\r\n    </form>\r\n  </nb-card-body>\r\n\r\n  <nb-card-footer>\r\n    <div class=\"form-actions d-flex justify-content-end\">\r\n      <button type=\"button\" class=\"btn btn-outline-secondary mr-2\" (click)=\"cancel()\" [disabled]=\"isSubmitting\">\r\n        <i class=\"fas fa-times mr-1\"></i>取消\r\n      </button>\r\n      <button type=\"button\" class=\"btn btn-success\" (click)=\"saveTemplate()\" [disabled]=\"isSubmitting\">\r\n        <i class=\"fas fa-spinner fa-spin mr-1\" *ngIf=\"isSubmitting\"></i>\r\n        <i class=\"fas fa-save mr-1\" *ngIf=\"!isSubmitting\"></i>\r\n        {{ isSubmitting ? '保存中...' : '儲存模板' }}\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;;;;;;;;ICWzDC,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,aACF;;;;;IAgBMR,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,cACF;;;;;;IAkBER,EAFJ,CAAAC,cAAA,cAAsE,gBACpC,gBAE+D;IAAvDD,EAAA,CAAAS,UAAA,oBAAAC,iEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAUP,MAAA,CAAAQ,eAAA,EAAiB;IAAA,EAAC;IADlEf,EAAA,CAAAI,YAAA,EAC6F;IAC7FJ,EAAA,CAAAE,SAAA,eAA+B;IAC/BF,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAG,MAAA,GACF;IAEJH,EAFI,CAAAI,YAAA,EAAO,EACD,EACJ;;;;IAPiDJ,EAAA,CAAAK,SAAA,GAA2B;IACXL,EADhB,CAAAgB,UAAA,YAAAT,MAAA,CAAAU,aAAA,GAA2B,kBAAAV,MAAA,CAAAW,eAAA,GACzC,aAAAX,MAAA,CAAAY,YAAA,CAAuD;IAG1FnB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAU,aAAA,sDACF;;;;;IAMFjB,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAE,SAAA,YAAkC;IAClCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,2CAAM;IACdH,EADc,CAAAI,YAAA,EAAO,EACf;;;;;;IAGJJ,EADF,CAAAC,cAAA,cAA2E,gBACX;IAApCD,EAAA,CAAAS,UAAA,mBAAAW,gEAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAW,aAAA,CAAAW,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAiB,mBAAA,CAAAH,OAAA,CAAyB;IAAA,EAAC;IAC3DrB,EAAA,CAAAC,cAAA,gBAC+D;IADlBD,EAAA,CAAAyB,gBAAA,2BAAAC,wEAAAC,MAAA;MAAA,MAAAN,OAAA,GAAArB,EAAA,CAAAW,aAAA,CAAAW,GAAA,EAAAC,SAAA;MAAAvB,EAAA,CAAA4B,kBAAA,CAAAP,OAAA,CAAAQ,QAAA,EAAAF,MAAA,MAAAN,OAAA,CAAAQ,QAAA,GAAAF,MAAA;MAAA,OAAA3B,EAAA,CAAAc,WAAA,CAAAa,MAAA;IAAA,EAA2B;IAC5C3B,EAAA,CAAAS,UAAA,mBAAAqB,gEAAAH,MAAA;MAAA3B,EAAA,CAAAW,aAAA,CAAAW,GAAA;MAAA,OAAAtB,EAAA,CAAAc,WAAA,CAASa,MAAA,CAAAI,eAAA,EAAwB;IAAA,EAAC;IAD9D/B,EAAA,CAAAI,YAAA,EAC+D;IAE7DJ,EADF,CAAAC,cAAA,cAA0B,cACA;IAAAD,EAAA,CAAAG,MAAA,GAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAClEJ,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAG,MAAA,GAAyC;IAGtEH,EAHsE,CAAAI,YAAA,EAAM,EAClE,EACA,EACJ;;;;;;IAPuEJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAgC,sBAAA,kBAAAC,IAAA,KAAiB;IAA7CjC,EAAA,CAAAkC,gBAAA,YAAAb,OAAA,CAAAQ,QAAA,CAA2B;IACtE7B,EAAA,CAAAgB,UAAA,aAAAT,MAAA,CAAAY,YAAA,CAAyB;IAEDnB,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAmC,iBAAA,CAAAd,OAAA,CAAAe,YAAA,IAAAf,OAAA,CAAAgB,IAAA,CAAoC;IACrCrC,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAmC,iBAAA,CAAAd,OAAA,CAAAiB,UAAA,IAAAjB,OAAA,CAAAkB,WAAA,CAAyC;;;;;IAMxEvC,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,eACF;;;;;IAWAR,EAAA,CAAAE,SAAA,YAAgE;;;;;IAChEF,EAAA,CAAAE,SAAA,YAAsD;;;AD3E9D,OAAM,MAAOsC,wBAAwB;EAenCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAd1B,KAAAC,aAAa,GAAU,EAAE,CAAC,CAAC;IAC3B,KAAAC,YAAY,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAC,KAAK,GAAG,IAAIlD,YAAY,EAAQ,CAAC,CAAC;IAClC,KAAAmD,eAAe,GAAG,IAAInD,YAAY,EAAQ,CAAC,CAAC;IAEtD;IACA,KAAAoD,WAAW,GAAG;MACZV,IAAI,EAAE;KACP;IAED;IACA,KAAAlB,YAAY,GAAG,KAAK;IACpB,KAAAX,gBAAgB,GAA8B,EAAE;EAEQ;EAExDwC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;EACAA,SAASA,CAAA;IACP,IAAI,CAACF,WAAW,GAAG;MACjBV,IAAI,EAAE;KACP;IACD,IAAI,CAAC7B,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACW,YAAY,GAAG,KAAK;IAEzB;IACA,IAAI,IAAI,CAACwB,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACO,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACtB,QAAQ,GAAG,KAAK,CAAC;IAC3D;EACF;EAEA;EACAuB,YAAYA,CAAA;IACV,IAAI,CAAC5C,gBAAgB,GAAG,EAAE;IAC1B,IAAI6C,OAAO,GAAG,IAAI;IAElB;IACA,IAAI,CAAC,IAAI,CAACN,WAAW,CAACV,IAAI,CAACiB,IAAI,EAAE,EAAE;MACjC,IAAI,CAAC9C,gBAAgB,CAAC,MAAM,CAAC,GAAG,SAAS;MACzC6C,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAI,IAAI,CAACN,WAAW,CAACV,IAAI,CAACiB,IAAI,EAAE,CAACC,MAAM,GAAG,EAAE,EAAE;MACnD,IAAI,CAAC/C,gBAAgB,CAAC,MAAM,CAAC,GAAG,eAAe;MAC/C6C,OAAO,GAAG,KAAK;IACjB;IAIA;IACA,MAAMG,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAC7C,IAAID,aAAa,CAACD,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAAC/C,gBAAgB,CAAC,OAAO,CAAC,GAAG,WAAW;MAC5C6C,OAAO,GAAG,KAAK;IACjB;IAEA,OAAOA,OAAO;EAChB;EAEA;EACAI,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACd,aAAa,EAAE,OAAO,EAAE;IAElC,OAAO,IAAI,CAACA,aAAa,CACtBe,MAAM,CAACP,IAAI,IAAIA,IAAI,CAACtB,QAAQ,CAAC,CAC7B8B,GAAG,CAACR,IAAI,KAAK;MACZb,UAAU,EAAEa,IAAI,CAACb,UAAU,IAAI,IAAI;MACnCsB,YAAY,EAAET,IAAI,CAACS,YAAY,IAAIT,IAAI,CAACf,YAAY,IAAIe,IAAI,CAACd,IAAI,IAAI,IAAI;MACzEwB,UAAU,EAAEV,IAAI,CAACU,UAAU,IAAIV,IAAI,CAACW,cAAc,IAAIX,IAAI,CAACY,EAAE,IAAIZ,IAAI,CAACa,EAAE,IAAI;KAC7E,CAAC,CAAC;EACP;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACb,YAAY,EAAE,EAAE;MACxB;IACF;IAEA,IAAI,CAACjC,YAAY,GAAG,IAAI;IACxB,MAAMqC,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAE7C;IACA,MAAMS,gBAAgB,GAAqB;MACzCC,WAAW,EAAE,IAAI;MAAE;MACnBC,aAAa,EAAE,IAAI,CAACrB,WAAW,CAACV,IAAI,CAACiB,IAAI,EAAE;MAC3Ce,aAAa,EAAE,IAAI,CAACzB,YAAY;MAAE;MAClC0B,OAAO,EAAE,CAAC;MAAE;MACZC,OAAO,EAAEf,aAAa,CAACG,GAAG,CAACR,IAAI,KAAK;QAClCqB,iBAAiB,EAAE,IAAI;QAAE;QACzBX,UAAU,EAAEV,IAAI,CAACU,UAAU;QAAE;QAC7BD,YAAY,EAAET,IAAI,CAACS,YAAY;QAAE;QACjCtB,UAAU,EAAEa,IAAI,CAACb,UAAU,CAAC;OACF;KAC7B;IAED;IACA,IAAI,CAACI,eAAe,CAAC+B,gCAAgC,CAAC;MACpDC,IAAI,EAAER;KACP,CAAC,CAACS,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC1D,YAAY,GAAG,KAAK;QACzB,IAAI0D,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACA,IAAI,CAAChC,eAAe,CAACiC,IAAI,EAAE,CAAC,CAAC;UAC7B,IAAI,CAAClC,KAAK,CAACkC,IAAI,EAAE,CAAC,CAAC;QACrB,CAAC,MAAM;UACL;UACA,IAAI,CAACvE,gBAAgB,CAAC,KAAK,CAAC,GAAGqE,QAAQ,CAACG,OAAO,IAAI,YAAY;QACjE;MACF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACX,gBAAgB,CAAC,KAAK,CAAC,GAAG,iBAAiB;QAChD0E,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MACjC;KACD,CAAC;EACJ;EAEA;EACAE,MAAMA,CAAA;IACJ,IAAI,CAACtC,KAAK,CAACkC,IAAI,EAAE;EACnB;EAEA;EACAK,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACzC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACe,MAAM,CAACP,IAAI,IAAIA,IAAI,CAACtB,QAAQ,CAAC,CAAC0B,MAAM,GAAG,CAAC;EACzF;EAEA;EACA/B,mBAAmBA,CAAC2B,IAAS;IAC3BA,IAAI,CAACtB,QAAQ,GAAG,CAACsB,IAAI,CAACtB,QAAQ;IAC9B;IACA,IAAI,IAAI,CAACrB,gBAAgB,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC4E,gBAAgB,EAAE,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAAC5E,gBAAgB,CAAC,OAAO,CAAC;IACvC;EACF;EAEA;EACAO,eAAeA,CAAA;IACb,MAAMsE,aAAa,GAAG,IAAI,CAAC1C,aAAa,CAAC2C,IAAI,CAACnC,IAAI,IAAI,CAACA,IAAI,CAACtB,QAAQ,CAAC;IACrE,IAAI,CAACc,aAAa,CAACO,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACtB,QAAQ,GAAGwD,aAAa,CAAC;IAEjE;IACA,IAAI,IAAI,CAAC7E,gBAAgB,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC4E,gBAAgB,EAAE,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAAC5E,gBAAgB,CAAC,OAAO,CAAC;IACvC;EACF;EAEA;EACAS,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC0B,aAAa,IAAI,IAAI,CAACA,aAAa,CAACY,MAAM,GAAG,CAAC,IACxD,IAAI,CAACZ,aAAa,CAAC4C,KAAK,CAACpC,IAAI,IAAIA,IAAI,CAACtB,QAAQ,CAAC;EACnD;EAEA;EACAX,eAAeA,CAAA;IACb,MAAMsE,aAAa,GAAG,IAAI,CAACJ,gBAAgB,EAAE;IAC7C,OAAOI,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,IAAI,CAAC7C,aAAa,CAACY,MAAM;EACvE;;;uCAjKWf,wBAAwB,EAAAxC,EAAA,CAAAyF,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAxBnD,wBAAwB;MAAAoD,SAAA;MAAAC,MAAA;QAAAlD,aAAA;QAAAC,YAAA;MAAA;MAAAkD,OAAA;QAAAjD,KAAA;QAAAC,eAAA;MAAA;MAAAiD,UAAA;MAAAC,QAAA,GAAAhG,EAAA,CAAAiG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClB/BvG,EAHN,CAAAC,cAAA,iBAA8D,qBAC5C,aACiD,YAC5C;UACfD,EAAA,CAAAE,SAAA,WAAgC;UAAAF,EAAA,CAAAG,MAAA,gCAClC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,gBAAoE;UAAnBD,EAAA,CAAAS,UAAA,mBAAAgG,0DAAA;YAAA,OAASD,GAAA,CAAArB,MAAA,EAAQ;UAAA,EAAC;UACjEnF,EAAA,CAAAE,SAAA,WAA4B;UAGlCF,EAFI,CAAAI,YAAA,EAAS,EACL,EACS;UAEjBJ,EAAA,CAAAC,cAAA,sBAAsC;UAEpCD,EAAA,CAAA0G,UAAA,IAAAC,uCAAA,iBAAgE;UAKhE3G,EAAA,CAAAC,cAAA,eAAwD;UAAlDD,EAAA,CAAAS,UAAA,sBAAAmG,4DAAA;YAAA,OAAYJ,GAAA,CAAAvC,YAAA,EAAc;UAAA,EAAC;UAG7BjE,EADF,CAAAC,cAAA,cAA+B,cACH;UACxBD,EAAA,CAAAE,SAAA,aAAuC;UAAAF,EAAA,CAAAG,MAAA,iCACzC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGHJ,EADF,CAAAC,cAAA,eAAyB,iBACI;UACzBD,EAAA,CAAAG,MAAA,kCAAK;UAAAH,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAG,MAAA,SAAC;UAC/BH,EAD+B,CAAAI,YAAA,EAAO,EAC9B;UACRJ,EAAA,CAAAC,cAAA,iBAE4B;UAD1BD,EAAA,CAAAyB,gBAAA,2BAAAoF,kEAAAlF,MAAA;YAAA3B,EAAA,CAAA4B,kBAAA,CAAA4E,GAAA,CAAAzD,WAAA,CAAAV,IAAA,EAAAV,MAAA,MAAA6E,GAAA,CAAAzD,WAAA,CAAAV,IAAA,GAAAV,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UADhC3B,EAAA,CAAAI,YAAA,EAE4B;UAC5BJ,EAAA,CAAA0G,UAAA,KAAAI,wCAAA,kBAA+D;UAInE9G,EADE,CAAAI,YAAA,EAAM,EACF;UAKFJ,EAFJ,CAAAC,cAAA,eAA0B,eAC4C,cACnC;UAC7BD,EAAA,CAAAE,SAAA,aAAgC;UAAAF,EAAA,CAAAG,MAAA,qEAAW;UAAAH,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAG,MAAA,SAAC;UACrEH,EADqE,CAAAI,YAAA,EAAO,EACvE;UAEHJ,EADF,CAAAC,cAAA,eAA+B,gBACK;UAAAD,EAAA,CAAAG,MAAA,IAA8B;UAEpEH,EAFoE,CAAAI,YAAA,EAAO,EACnE,EACF;UAGNJ,EAAA,CAAA0G,UAAA,KAAAK,wCAAA,kBAAsE;UAYtE/G,EAAA,CAAAC,cAAA,eAA0E;UAMxED,EALA,CAAA0G,UAAA,KAAAM,wCAAA,kBAA4D,KAAAC,wCAAA,kBAKe;UAU7EjH,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAA0G,UAAA,KAAAQ,wCAAA,kBAAwE;UAM9ElH,EAFI,CAAAI,YAAA,EAAM,EACD,EACM;UAIXJ,EAFJ,CAAAC,cAAA,sBAAgB,eACuC,kBACuD;UAA7CD,EAAA,CAAAS,UAAA,mBAAA0G,2DAAA;YAAA,OAASX,GAAA,CAAArB,MAAA,EAAQ;UAAA,EAAC;UAC7EnF,EAAA,CAAAE,SAAA,aAAiC;UAAAF,EAAA,CAAAG,MAAA,qBACnC;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,kBAAiG;UAAnDD,EAAA,CAAAS,UAAA,mBAAA2G,2DAAA;YAAA,OAASZ,GAAA,CAAAvC,YAAA,EAAc;UAAA,EAAC;UAEpEjE,EADA,CAAA0G,UAAA,KAAAW,sCAAA,gBAA4D,KAAAC,sCAAA,gBACV;UAClDtH,EAAA,CAAAG,MAAA,IACF;UAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACS,EACT;;;UAvF2BJ,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAAhG,gBAAA,QAA6B;UAgBjBR,EAAA,CAAAK,SAAA,IAA6C;UAA7CL,EAAA,CAAAuH,WAAA,eAAAf,GAAA,CAAAhG,gBAAA,SAA6C;UAClFR,EAAA,CAAAkC,gBAAA,YAAAsE,GAAA,CAAAzD,WAAA,CAAAV,IAAA,CAA8B;UAC9BrC,EAAA,CAAAgB,UAAA,aAAAwF,GAAA,CAAArF,YAAA,CAAyB;UACInB,EAAA,CAAAK,SAAA,EAA8B;UAA9BL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAAhG,gBAAA,SAA8B;UAazBR,EAAA,CAAAK,SAAA,IAA8B;UAA9BL,EAAA,CAAAM,kBAAA,wBAAAkG,GAAA,CAAApB,gBAAA,cAA8B;UAK9BpF,EAAA,CAAAK,SAAA,EAA8B;UAA9BL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAA7D,aAAA,CAAAY,MAAA,KAA8B;UAYxCvD,EAAA,CAAAK,SAAA,EAA6C;UAA7CL,EAAA,CAAAuH,WAAA,cAAAf,GAAA,CAAAhG,gBAAA,UAA6C;UACjER,EAAA,CAAAK,SAAA,EAAgC;UAAhCL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAA7D,aAAA,CAAAY,MAAA,OAAgC;UAKhBvD,EAAA,CAAAK,SAAA,EAAkB;UAAlBL,EAAA,CAAAgB,UAAA,YAAAwF,GAAA,CAAA7D,aAAA,CAAkB;UAYH3C,EAAA,CAAAK,SAAA,EAA+B;UAA/BL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAAhG,gBAAA,UAA+B;UAUQR,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAgB,UAAA,aAAAwF,GAAA,CAAArF,YAAA,CAAyB;UAGlCnB,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAgB,UAAA,aAAAwF,GAAA,CAAArF,YAAA,CAAyB;UACtDnB,EAAA,CAAAK,SAAA,EAAkB;UAAlBL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAArF,YAAA,CAAkB;UAC7BnB,EAAA,CAAAK,SAAA,EAAmB;UAAnBL,EAAA,CAAAgB,UAAA,UAAAwF,GAAA,CAAArF,YAAA,CAAmB;UAChDnB,EAAA,CAAAK,SAAA,EACF;UADEL,EAAA,CAAAM,kBAAA,MAAAkG,GAAA,CAAArF,YAAA,6DACF;;;qBD/EMvB,YAAY,EAAA4H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE7H,WAAW,EAAA8H,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,4BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,kBAAA,EAAAN,EAAA,CAAAO,OAAA,EAAAP,EAAA,CAAAQ,MAAA,EAAErI,YAAY,EAAAsI,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAEzI,cAAc;MAAA0I,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}