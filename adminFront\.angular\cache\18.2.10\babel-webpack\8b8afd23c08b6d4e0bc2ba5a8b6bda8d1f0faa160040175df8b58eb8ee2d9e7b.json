{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { FormsModule } from '@angular/forms';\nimport { MomentPipe } from '../../../@theme/pipes/moment.pipe';\nimport { Ng<PERSON><PERSON>, formatDate } from '@angular/common';\nimport { NbCardModule, NbInputModule, NbDatepickerModule, NbSelectModule, NbOptionModule } from '@nebular/theme';\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"../../components/shared.observable\";\nimport * as i3 from \"src/app/shared/helper/allowHelper\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"@nebular/theme\";\nimport * as i7 from \"@angular/forms\";\nfunction LogsManagementComponent_nb_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fun_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", fun_r2.Id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", fun_r2.Name, \"\");\n  }\n}\nfunction LogsManagementComponent_tr_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 26)(1, \"td\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 29);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 29);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 29);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r3.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r3.CAccount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r3.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 6, data_r3.DateCreate, \"yyyy-MM-DD HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(data_r3.FunctionName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r3.Ip);\n  }\n}\nexport let LogsManagementComponent = /*#__PURE__*/(() => {\n  class LogsManagementComponent extends BaseComponent {\n    constructor(userService, share, allow, baseFuncService, valid, message, drestoyedRef) {\n      super(allow);\n      this.userService = userService;\n      this.share = share;\n      this.allow = allow;\n      this.baseFuncService = baseFuncService;\n      this.valid = valid;\n      this.message = message;\n      this.drestoyedRef = drestoyedRef;\n      this.userLogs = [];\n      this.functions = [];\n      this.now = new Date();\n      this.request = {\n        DateEnd: '',\n        DateStart: '',\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        FunctionId: -1\n      };\n      this.start = this.now;\n      this.end = this.now;\n      this.share.SharedUserLog.subscribe(res => {\n        this.userLogs = res;\n      });\n      this.share.SharedFunctionModel.subscribe(res => {\n        this.functions = res;\n        if (res.length === 0) {\n          this.getFunction();\n        } else {\n          this.functions = res;\n        }\n      });\n    }\n    ngOnInit() {\n      this.getUserLog();\n    }\n    validationDate() {\n      this.valid.clear();\n      this.valid.Date(new Date(this.start), new Date(this.end));\n    }\n    getUserLog() {\n      this.validationDate();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this.request.PageIndex = this.pageIndex;\n      this.request.PageSize = this.pageSize;\n      this.request.DateStart = formatDate(this.start, 'yyyy-MM-ddT00:00:00', 'en');\n      this.request.DateEnd = formatDate(this.end, 'yyyy-MM-ddT23:59:59', 'en');\n      let req = {\n        body: {\n          ...this.request\n        }\n      };\n      this.userService.apiUserGetUserLogPost$Json(req).pipe(takeUntilDestroyed(this.drestoyedRef)).subscribe(res => {\n        this.userLogs = res.Entries;\n        this.totalRecords = res.TotalItems;\n        this.share.SetUserLog(this.userLogs);\n      });\n    }\n    getFunction() {\n      this.baseFuncService.apiBaseFunctionGetFunctionPost$Json({\n        body: {}\n      }).subscribe(res => {\n        this.functions = res.Entries;\n        this.share.SetFunctionModel(this.functions);\n      });\n    }\n    static {\n      this.ɵfac = function LogsManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || LogsManagementComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.SharedObservable), i0.ɵɵdirectiveInject(i3.AllowHelper), i0.ɵɵdirectiveInject(i1.BaseFunctionService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i0.DestroyRef));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LogsManagementComponent,\n        selectors: [[\"ngx-logs-management\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 49,\n        vars: 11,\n        consts: [[\"formcontrol\", \"\"], [\"ngmodel\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-6\"], [\"for\", \"keyWord\", 1, \"label\", \"mr-2\"], [\"nbInput\", \"\", \"placeholder\", \"\\u8D77\\u65E5\", 1, \"mr-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"label\", \"mr-2\"], [\"nbInput\", \"\", \"placeholder\", \"\\u8FC4\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"d-flex\"], [\"id\", \"FunctionId\", \"name\", \"FunctionId\", 1, \"w-full\", 3, \"selectedChange\", \"selected\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"col-12\", \"col-md-2\", \"text-right\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", 1, \"col-2\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"d-flex\"], [1, \"col-1\"], [1, \"col-3\"], [1, \"col-2\"]],\n        template: function LogsManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"label\", 7);\n            i0.ɵɵtext(8, \"\\u65E5\\u671F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"input\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function LogsManagementComponent_Template_input_ngModelChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.start, $event) || (ctx.start = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(10, \"nb-datepicker\", null, 0);\n            i0.ɵɵelementStart(12, \"label\", 9);\n            i0.ɵɵtext(13, \"~\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"input\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function LogsManagementComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.end, $event) || (ctx.end = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(15, \"nb-datepicker\", null, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 11)(18, \"label\", 7);\n            i0.ɵɵtext(19, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"nb-select\", 12);\n            i0.ɵɵtwoWayListener(\"selectedChange\", function LogsManagementComponent_Template_nb_select_selectedChange_20_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.request.FunctionId, $event) || (ctx.request.FunctionId = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(21, \"nb-option\", 13);\n            i0.ɵɵtext(22, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(23, LogsManagementComponent_nb_option_23_Template, 2, 2, \"nb-option\", 14);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 15)(25, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function LogsManagementComponent_Template_button_click_25_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getUserLog());\n            });\n            i0.ɵɵelement(26, \"i\", 17);\n            i0.ɵɵtext(27, \"\\u67E5\\u8A62\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(28, \"nb-card-body\", 3)(29, \"div\", 4)(30, \"div\", 18)(31, \"table\", 19)(32, \"thead\")(33, \"tr\", 20)(34, \"th\", 21);\n            i0.ɵɵtext(35, \"\\u5E8F\\u865F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"th\", 22);\n            i0.ɵɵtext(37, \"\\u4F7F\\u7528\\u8005\\u5E33\\u865F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"th\", 23);\n            i0.ɵɵtext(39, \"\\u4F7F\\u7528\\u8005\\u59D3\\u540D\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"th\", 23);\n            i0.ɵɵtext(41, \"\\u7D00\\u9304\\u6642\\u9593\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"th\", 23);\n            i0.ɵɵtext(43, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"th\", 23);\n            i0.ɵɵtext(45, \"\\u767B\\u5165IP\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(46, \"tbody\");\n            i0.ɵɵtemplate(47, LogsManagementComponent_tr_47_Template, 14, 9, \"tr\", 24);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(48, \"ngx-pagination\", 25);\n            i0.ɵɵtwoWayListener(\"PageChange\", function LogsManagementComponent_Template_ngx_pagination_PageChange_48_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function LogsManagementComponent_Template_ngx_pagination_PageChange_48_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getUserLog());\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            const formcontrol_r4 = i0.ɵɵreference(11);\n            const ngmodel_r5 = i0.ɵɵreference(16);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"nbDatepicker\", formcontrol_r4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.start);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"nbDatepicker\", ngmodel_r5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.end);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"selected\", ctx.request.FunctionId);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", -1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.functions);\n            i0.ɵɵadvance(24);\n            i0.ɵɵproperty(\"ngForOf\", ctx.userLogs);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          }\n        },\n        dependencies: [NbCardModule, i6.NbCardComponent, i6.NbCardBodyComponent, i6.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i6.NbInputDirective, NbDatepickerModule, i6.NbDatepickerDirective, i6.NbDatepickerComponent, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, NbSelectModule, i6.NbSelectComponent, i6.NbOptionComponent, NbOptionModule, NgFor, PaginationComponent, MomentPipe]\n      });\n    }\n  }\n  return LogsManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}