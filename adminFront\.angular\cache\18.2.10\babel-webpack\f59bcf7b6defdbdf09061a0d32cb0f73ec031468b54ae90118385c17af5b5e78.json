{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild, Output, EventEmitter } from '@angular/core';\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { tap } from 'rxjs';\nlet SpaceComponent = class SpaceComponent extends BaseComponent {\n  constructor(allow, router, dialogService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.router = router;\n    this.dialogService = dialogService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.Math = Math; // 讓模板可以使用 Math 函數\n    this.submitSpace = new EventEmitter();\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.spaceList = [];\n    this.spaceDetail = {};\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.searchStatus = null;\n  }\n  ngOnInit() {\n    this.getSpaceList();\n  }\n  // 導航到模板管理頁面\n  navigateToTemplate() {\n    this.router.navigate(['/pages/template']);\n  }\n  getSpaceList() {\n    const requestData = {\n      CPart: this.searchKeyword || null,\n      CLocation: this.searchLocation || null,\n      CStatus: this.searchStatus,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: requestData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.spaceList = response.Entries || [];\n        this.totalRecords = response.TotalItems || 0;\n      } else {\n        this.message.showErrorMSG(response.Message || '載入空間列表失敗');\n      }\n    })).subscribe();\n  }\n  onSearch() {\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  pageChanged(page) {\n    this.pageIndex = page;\n    this.getSpaceList();\n  }\n  openCreateModal(modal) {\n    this.spaceDetail = {\n      CStatus: 1\n    };\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  openEditModal(modal, item) {\n    this.spaceDetail = {\n      CSpaceID: item.CSpaceID,\n      CPart: item.CPart,\n      CLocation: item.CLocation,\n      CStatus: item.CStatus\n    };\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onSubmit(ref) {\n    if (!this.validateForm()) {\n      return;\n    }\n    const action = this.spaceDetail.CSpaceID ? 'update' : 'create';\n    this.submitSpace.emit({\n      action: action,\n      data: this.spaceDetail,\n      ref: ref\n    });\n  }\n  validateForm() {\n    if (!this.spaceDetail.CPart?.trim()) {\n      this.message.showErrorMSG('請輸入項目名稱');\n      return false;\n    }\n    if (!this.spaceDetail.CLocation?.trim()) {\n      this.message.showErrorMSG('請輸入所屬區域');\n      return false;\n    }\n    if (this.spaceDetail.CStatus === undefined || this.spaceDetail.CStatus === null) {\n      this.message.showErrorMSG('請選擇狀態');\n      return false;\n    }\n    return true;\n  }\n  deleteSpace(item) {\n    if (confirm(`確定要刪除空間「${item.CPart}」嗎？`)) {\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\n        body: {\n          CSpaceID: item.CSpaceID\n        }\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('刪除空間成功');\n          this.getSpaceList();\n        } else {\n          this.message.showErrorMSG(response.Message || '刪除空間失敗');\n        }\n      })).subscribe();\n    }\n  }\n};\n__decorate([ViewChild('createModal', {\n  static: false\n})], SpaceComponent.prototype, \"createModal\", void 0);\n__decorate([ViewChild('editModal', {\n  static: false\n})], SpaceComponent.prototype, \"editModal\", void 0);\n__decorate([Output()], SpaceComponent.prototype, \"submitSpace\", void 0);\nSpaceComponent = __decorate([Component({\n  selector: 'ngx-space',\n  templateUrl: './space.component.html',\n  styleUrls: ['./space.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, BreadcrumbComponent]\n})], SpaceComponent);\nexport { SpaceComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "Output", "EventEmitter", "SharedModule", "CommonModule", "BaseComponent", "BreadcrumbComponent", "tap", "SpaceComponent", "constructor", "allow", "router", "dialogService", "_spaceService", "message", "valid", "Math", "submitSpace", "pageFirst", "pageSize", "pageIndex", "totalRecords", "spaceList", "spaceDetail", "searchKeyword", "searchLocation", "searchStatus", "ngOnInit", "getSpaceList", "navigateToTemplate", "navigate", "requestData", "<PERSON>art", "CLocation", "CStatus", "PageIndex", "PageSize", "apiSpaceGetSpaceListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "TotalItems", "showErrorMSG", "Message", "subscribe", "onSearch", "onReset", "pageChanged", "page", "openCreateModal", "modal", "open", "context", "autoFocus", "openEditModal", "item", "CSpaceID", "onClose", "ref", "close", "onSubmit", "validateForm", "action", "emit", "data", "trim", "undefined", "deleteSpace", "confirm", "apiSpaceDeleteSpacePost$Json", "showSucessMSG", "__decorate", "static", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\space\\space.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef, Output, EventEmitter } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { tap } from 'rxjs';\r\nimport { GetSpaceListResponse, SaveSpaceRequest } from 'src/services/api/models';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-space',\r\n  templateUrl: './space.component.html',\r\n  styleUrls: ['./space.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent,\r\n  ],\r\n})\r\n\r\nexport class SpaceComponent extends BaseComponent implements OnInit {\r\n  Math = Math; // 讓模板可以使用 Math 函數\r\n\r\n  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;\r\n  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;\r\n\r\n  @Output() submitSpace = new EventEmitter<{ action: 'create' | 'update', data: SaveSpaceRequest, ref: any }>();\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private router: Router,\r\n    private dialogService: NbDialogService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  spaceList: GetSpaceListResponse[] = [];\r\n  spaceDetail: SaveSpaceRequest = {};\r\n  searchKeyword: string = '';\r\n  searchLocation: string = '';\r\n  searchStatus: number | null = null;\r\n\r\n  override ngOnInit(): void {\r\n    this.getSpaceList();\r\n  }\r\n\r\n  // 導航到模板管理頁面\r\n  navigateToTemplate(): void {\r\n    this.router.navigate(['/pages/template']);\r\n  }\r\n\r\n  getSpaceList(): void {\r\n    const requestData = {\r\n      CPart: this.searchKeyword || null,\r\n      CLocation: this.searchLocation || null,\r\n      CStatus: this.searchStatus,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: requestData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.spaceList = response.Entries || [];\r\n          this.totalRecords = response.TotalItems || 0;\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入空間列表失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSearch(): void {\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  onReset(): void {\r\n    this.searchKeyword = '';\r\n    this.searchLocation = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  pageChanged(page: number): void {\r\n    this.pageIndex = page;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  openCreateModal(modal: TemplateRef<any>): void {\r\n    this.spaceDetail = { CStatus: 1 };\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  openEditModal(modal: TemplateRef<any>, item: GetSpaceListResponse): void {\r\n    this.spaceDetail = {\r\n      CSpaceID: item.CSpaceID,\r\n      CPart: item.CPart,\r\n      CLocation: item.CLocation,\r\n      CStatus: item.CStatus\r\n    };\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  onClose(ref: any): void {\r\n    ref.close();\r\n  }\r\n\r\n  onSubmit(ref: any): void {\r\n    if (!this.validateForm()) {\r\n      return;\r\n    }\r\n\r\n    const action = this.spaceDetail.CSpaceID ? 'update' : 'create';\r\n    this.submitSpace.emit({\r\n      action: action,\r\n      data: this.spaceDetail,\r\n      ref: ref\r\n    });\r\n  }\r\n\r\n  validateForm(): boolean {\r\n    if (!this.spaceDetail.CPart?.trim()) {\r\n      this.message.showErrorMSG('請輸入項目名稱');\r\n      return false;\r\n    }\r\n\r\n    if (!this.spaceDetail.CLocation?.trim()) {\r\n      this.message.showErrorMSG('請輸入所屬區域');\r\n      return false;\r\n    }\r\n\r\n    if (this.spaceDetail.CStatus === undefined || this.spaceDetail.CStatus === null) {\r\n      this.message.showErrorMSG('請選擇狀態');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  deleteSpace(item: GetSpaceListResponse): void {\r\n    if (confirm(`確定要刪除空間「${item.CPart}」嗎？`)) {\r\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\r\n        body: { CSpaceID: item.CSpaceID }\r\n      }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('刪除空間成功');\r\n            this.getSpaceList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '刪除空間失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,SAAS,EAAeC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AAE/F,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,kCAAkC;AAKhE,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,GAAG,QAAQ,MAAM;AAqBnB,IAAMC,cAAc,GAApB,MAAMA,cAAe,SAAQH,aAAa;EAQ/CI,YACqBC,KAAkB,EAC7BC,MAAc,EACdC,aAA8B,EAC9BC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAbf,KAAAC,IAAI,GAAGA,IAAI,CAAC,CAAC;IAKH,KAAAC,WAAW,GAAG,IAAIf,YAAY,EAAqE;IAapG,KAAAgB,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,SAAS,GAA2B,EAAE;IACtC,KAAAC,WAAW,GAAqB,EAAE;IAClC,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAkB,IAAI;EAXlC;EAaSC,QAAQA,CAAA;IACf,IAAI,CAACC,YAAY,EAAE;EACrB;EAEA;EACAC,kBAAkBA,CAAA;IAChB,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAF,YAAYA,CAAA;IACV,MAAMG,WAAW,GAAG;MAClBC,KAAK,EAAE,IAAI,CAACR,aAAa,IAAI,IAAI;MACjCS,SAAS,EAAE,IAAI,CAACR,cAAc,IAAI,IAAI;MACtCS,OAAO,EAAE,IAAI,CAACR,YAAY;MAC1BS,SAAS,EAAE,IAAI,CAACf,SAAS;MACzBgB,QAAQ,EAAE,IAAI,CAACjB;KAChB;IAED,IAAI,CAACN,aAAa,CAACwB,6BAA6B,CAAC;MAAEC,IAAI,EAAEP;IAAW,CAAE,CAAC,CAACQ,IAAI,CAC1EhC,GAAG,CAACiC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACnB,SAAS,GAAGkB,QAAQ,CAACE,OAAO,IAAI,EAAE;QACvC,IAAI,CAACrB,YAAY,GAAGmB,QAAQ,CAACG,UAAU,IAAI,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAAC7B,OAAO,CAAC8B,YAAY,CAACJ,QAAQ,CAACK,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC3B,SAAS,GAAG,CAAC;IAClB,IAAI,CAACQ,YAAY,EAAE;EACrB;EAEAoB,OAAOA,CAAA;IACL,IAAI,CAACxB,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACN,SAAS,GAAG,CAAC;IAClB,IAAI,CAACQ,YAAY,EAAE;EACrB;EAEAqB,WAAWA,CAACC,IAAY;IACtB,IAAI,CAAC9B,SAAS,GAAG8B,IAAI;IACrB,IAAI,CAACtB,YAAY,EAAE;EACrB;EAEAuB,eAAeA,CAACC,KAAuB;IACrC,IAAI,CAAC7B,WAAW,GAAG;MAAEW,OAAO,EAAE;IAAC,CAAE;IACjC,IAAI,CAACtB,aAAa,CAACyC,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAACJ,KAAuB,EAAEK,IAA0B;IAC/D,IAAI,CAAClC,WAAW,GAAG;MACjBmC,QAAQ,EAAED,IAAI,CAACC,QAAQ;MACvB1B,KAAK,EAAEyB,IAAI,CAACzB,KAAK;MACjBC,SAAS,EAAEwB,IAAI,CAACxB,SAAS;MACzBC,OAAO,EAAEuB,IAAI,CAACvB;KACf;IACD,IAAI,CAACtB,aAAa,CAACyC,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAI,OAAOA,CAACC,GAAQ;IACdA,GAAG,CAACC,KAAK,EAAE;EACb;EAEAC,QAAQA,CAACF,GAAQ;IACf,IAAI,CAAC,IAAI,CAACG,YAAY,EAAE,EAAE;MACxB;IACF;IAEA,MAAMC,MAAM,GAAG,IAAI,CAACzC,WAAW,CAACmC,QAAQ,GAAG,QAAQ,GAAG,QAAQ;IAC9D,IAAI,CAACzC,WAAW,CAACgD,IAAI,CAAC;MACpBD,MAAM,EAAEA,MAAM;MACdE,IAAI,EAAE,IAAI,CAAC3C,WAAW;MACtBqC,GAAG,EAAEA;KACN,CAAC;EACJ;EAEAG,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACxC,WAAW,CAACS,KAAK,EAAEmC,IAAI,EAAE,EAAE;MACnC,IAAI,CAACrD,OAAO,CAAC8B,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAACrB,WAAW,CAACU,SAAS,EAAEkC,IAAI,EAAE,EAAE;MACvC,IAAI,CAACrD,OAAO,CAAC8B,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACrB,WAAW,CAACW,OAAO,KAAKkC,SAAS,IAAI,IAAI,CAAC7C,WAAW,CAACW,OAAO,KAAK,IAAI,EAAE;MAC/E,IAAI,CAACpB,OAAO,CAAC8B,YAAY,CAAC,OAAO,CAAC;MAClC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;EAEAyB,WAAWA,CAACZ,IAA0B;IACpC,IAAIa,OAAO,CAAC,WAAWb,IAAI,CAACzB,KAAK,KAAK,CAAC,EAAE;MACvC,IAAI,CAACnB,aAAa,CAAC0D,4BAA4B,CAAC;QAC9CjC,IAAI,EAAE;UAAEoB,QAAQ,EAAED,IAAI,CAACC;QAAQ;OAChC,CAAC,CAACnB,IAAI,CACLhC,GAAG,CAACiC,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAAC3B,OAAO,CAAC0D,aAAa,CAAC,QAAQ,CAAC;UACpC,IAAI,CAAC5C,YAAY,EAAE;QACrB,CAAC,MAAM;UACL,IAAI,CAACd,OAAO,CAAC8B,YAAY,CAACJ,QAAQ,CAACK,OAAO,IAAI,QAAQ,CAAC;QACzD;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf;EACF;CACD;AApJ8C2B,UAAA,EAA5CzE,SAAS,CAAC,aAAa,EAAE;EAAE0E,MAAM,EAAE;AAAK,CAAE,CAAC,C,kDAAgC;AACjCD,UAAA,EAA1CzE,SAAS,CAAC,WAAW,EAAE;EAAE0E,MAAM,EAAE;AAAK,CAAE,CAAC,C,gDAA8B;AAE9DD,UAAA,EAATxE,MAAM,EAAE,C,kDAAqG;AANnGO,cAAc,GAAAiE,UAAA,EAZ1B1E,SAAS,CAAC;EACT4E,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE,CAAC,wBAAwB,CAAC;EACrCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP3E,YAAY,EACZD,YAAY,EACZG,mBAAmB;CAEtB,CAAC,C,EAEWE,cAAc,CAuJ1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}