{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nfunction BuildingMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction BuildingMaterialComponent_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.exportExelMaterialList());\n    });\n    i0.ɵɵtext(1, \"\\u532F\\u51FA \");\n    i0.ɵɵelement(2, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.search());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(61);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u55AE\\u7B46\\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext();\n      const inputFile_r9 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(inputFile_r9.click());\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_th_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CSelectPictureId.length);\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CPrice);\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_57_tr_1_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r7 = i0.ɵɵreference(61);\n      return i0.ɵɵresetView(ctx_r3.onSelectedMaterial(item_r10, dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_57_tr_1_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imageBinder_r13 = i0.ɵɵreference(63);\n      return i0.ɵɵresetView(ctx_r3.bindImageForMaterial(item_r10, imageBinder_r13));\n    });\n    i0.ɵɵelement(1, \"i\", 55);\n    i0.ɵɵtext(2, \" \\u7D81\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"title\", \"\\u70BA \" + item_r10.CName + \" \\u7D81\\u5B9A\\u5716\\u7247\");\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"div\", 44);\n    i0.ɵɵtemplate(13, BuildingMaterialComponent_tbody_57_tr_1_span_13_Template, 2, 1, \"span\", 45)(14, BuildingMaterialComponent_tbody_57_tr_1_span_14_Template, 2, 0, \"span\", 46);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(17, BuildingMaterialComponent_tbody_57_tr_1_td_17_Template, 2, 1, \"td\", 33);\n    i0.ɵɵelementStart(18, \"td\")(19, \"span\", 47);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"td\", 48);\n    i0.ɵɵtemplate(22, BuildingMaterialComponent_tbody_57_tr_1_button_22_Template, 2, 0, \"button\", 49)(23, BuildingMaterialComponent_tbody_57_tr_1_button_23_Template, 3, 1, \"button\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CImageCode || \"\\u5F85\\u8A2D\\u5B9A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", item_r10.CName, \" - \", item_r10.CPart, \" - \", item_r10.CLocation, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(!item_r10.CIsMapping ? \"color: red\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CDescription);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r10.CSelectPictureId && item_r10.CSelectPictureId.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r10.CSelectPictureId || item_r10.CSelectPictureId.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CSelectPictureId && item_r10.CSelectPictureId.length > 0 ? \"\\u5DF2\\u7D81\\u5B9A\" : \"\\u672A\\u7D81\\u5B9A\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.CShowPrice == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(item_r10.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getStatusLabel(item_r10.CStatus || 0), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\");\n    i0.ɵɵtemplate(1, BuildingMaterialComponent_tbody_57_tr_1_Template, 24, 18, \"tr\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.materialList);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_60_nb_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r15.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r15.label, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_60_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"i\", 78);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u8A2D\\u5B9A\\u5EFA\\u6750\\u4EE3\\u865F: \", ctx_r3.selectedMaterial.CImageCode, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 56)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u5EFA\\u6750\\u7BA1\\u7406 > \\u65B0\\u589E\\u5EFA\\u6750 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 57)(4, \"h5\", 58);\n    i0.ɵɵtext(5, \"\\u8ACB\\u8F38\\u5165\\u4E0B\\u65B9\\u5167\\u5BB9\\u65B0\\u589E\\u5EFA\\u6750\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 59)(7, \"div\", 60)(8, \"label\", 61);\n    i0.ɵɵtext(9, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 62);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CImageCode, $event) || (ctx_r3.selectedMaterial.CImageCode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 63)(12, \"label\", 61);\n    i0.ɵɵtext(13, \"\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CName, $event) || (ctx_r3.selectedMaterial.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 63)(16, \"label\", 61);\n    i0.ɵɵtext(17, \"\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPart, $event) || (ctx_r3.selectedMaterial.CPart = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 63)(20, \"label\", 61);\n    i0.ɵɵtext(21, \"\\u4F4D\\u7F6E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CLocation, $event) || (ctx_r3.selectedMaterial.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 63)(24, \"label\", 61);\n    i0.ɵɵtext(25, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CSelectName, $event) || (ctx_r3.selectedMaterial.CSelectName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 63)(28, \"label\", 66);\n    i0.ɵɵtext(29, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"textarea\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_textarea_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CDescription, $event) || (ctx_r3.selectedMaterial.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 63)(32, \"label\", 66);\n    i0.ɵɵtext(33, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"input\", 68);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPrice, $event) || (ctx_r3.selectedMaterial.CPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 63)(36, \"label\", 61);\n    i0.ɵɵtext(37, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"nb-select\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_nb_select_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CStatus, $event) || (ctx_r3.selectedMaterial.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(39, BuildingMaterialComponent_ng_template_60_nb_option_39_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 70)(41, \"label\", 66);\n    i0.ɵɵtext(42, \"\\u5716\\u7247\\u7D81\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 71)(44, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_60_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const imageBinder_r13 = i0.ɵɵreference(63);\n      return i0.ɵɵresetView(ctx_r3.openImageBinder(imageBinder_r13));\n    });\n    i0.ɵɵelement(45, \"i\", 73);\n    i0.ɵɵtext(46, \"\\u9078\\u64C7\\u5716\\u7247 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, BuildingMaterialComponent_ng_template_60_div_47_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(48, \"nb-card-footer\", 34)(49, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_60_Template_button_click_49_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClose(ref_r16));\n    });\n    i0.ɵɵtext(50, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_60_Template_button_click_51_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSubmit(ref_r16));\n    });\n    i0.ɵɵtext(52, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CImageCode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPart);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CSelectName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CDescription);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPrice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.statusOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"title\", \"\\u70BA\\u5EFA\\u6750\\u7D81\\u5B9A\\u5716\\u7247\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedMaterial.CImageCode);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r18.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r18.label, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_40_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 134);\n  }\n  if (rf & 2) {\n    const image_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", image_r20.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"alt\", image_r20.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_40_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135);\n    i0.ɵɵelement(1, \"i\", 136);\n    i0.ɵɵelementStart(2, \"div\", 137);\n    i0.ɵɵtext(3, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 123);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_40_Template_div_click_0_listener() {\n      const image_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.moveToSelected(image_r20));\n    });\n    i0.ɵɵelementStart(1, \"div\", 124);\n    i0.ɵɵtemplate(2, BuildingMaterialComponent_ng_template_62_div_40_img_2_Template, 1, 2, \"img\", 125)(3, BuildingMaterialComponent_ng_template_62_div_40_div_3_Template, 4, 0, \"div\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 127)(5, \"div\", 128);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 129)(8, \"button\", 130);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_40_Template_button_click_8_listener($event) {\n      const image_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imagePreview_r21 = i0.ɵɵreference(65);\n      return i0.ɵɵresetView(ctx_r3.previewImage(image_r20, imagePreview_r21, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 131);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_40_Template_button_click_10_listener($event) {\n      const image_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.moveToSelected(image_r20, $event));\n    });\n    i0.ɵɵelement(11, \"i\", 133);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r20 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", image_r20.thumbnailUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !image_r20.thumbnailUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r20.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r20.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 138);\n    i0.ɵɵelement(1, \"i\", 139);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u627E\\u4E0D\\u5230\\u53EF\\u9078\\u64C7\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_63_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147);\n    i0.ɵɵtext(1, \" \\u5DF2\\u7D81\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_63_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148);\n    i0.ɵɵtext(1, \" \\u65B0\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_63_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 134);\n  }\n  if (rf & 2) {\n    const image_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", image_r23.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"alt\", image_r23.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_63_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135);\n    i0.ɵɵelement(1, \"i\", 136);\n    i0.ɵɵelementStart(2, \"div\", 137);\n    i0.ɵɵtext(3, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 140)(1, \"div\", 141);\n    i0.ɵɵtemplate(2, BuildingMaterialComponent_ng_template_62_div_63_div_2_Template, 2, 0, \"div\", 142)(3, BuildingMaterialComponent_ng_template_62_div_63_div_3_Template, 2, 0, \"div\", 143);\n    i0.ɵɵelementStart(4, \"small\", 144);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 124);\n    i0.ɵɵtemplate(7, BuildingMaterialComponent_ng_template_62_div_63_img_7_Template, 1, 2, \"img\", 125)(8, BuildingMaterialComponent_ng_template_62_div_63_div_8_Template, 4, 0, \"div\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 127)(10, \"div\", 128);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 129)(13, \"button\", 130);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_63_Template_button_click_13_listener($event) {\n      const image_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imagePreview_r21 = i0.ɵɵreference(65);\n      return i0.ɵɵresetView(ctx_r3.previewImage(image_r23, imagePreview_r21, $event));\n    });\n    i0.ɵɵelement(14, \"i\", 131);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_63_Template_button_click_15_listener($event) {\n      const image_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.moveToAvailable(image_r23, $event));\n    });\n    i0.ɵɵelement(16, \"i\", 146);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r23 = ctx.$implicit;\n    const i_r24 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"border-success\", ctx_r3.isImageBound(image_r23))(\"bg-green-50\", ctx_r3.isImageBound(image_r23));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isImageBound(image_r23));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isImageBound(image_r23));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"#\", i_r24 + 1, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", image_r23.thumbnailUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !image_r23.thumbnailUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r23.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r23.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 138);\n    i0.ɵɵelement(1, \"i\", 149);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u5C1A\\u672A\\u9078\\u64C7\\u4EFB\\u4F55\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 150);\n    i0.ɵɵtext(5, \"\\u5F9E\\u5DE6\\u5074\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_span_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 151);\n    i0.ɵɵelement(1, \"i\", 152);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getBoundImagesCount(), \" \\u5F35\\u5DF2\\u7D81\\u5B9A \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_span_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 153);\n    i0.ɵɵelement(1, \"i\", 154);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getNewSelectedCount(), \" \\u5F35\\u65B0\\u9078\\u64C7 \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 79)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 80)(4, \"div\", 81)(5, \"div\", 82);\n    i0.ɵɵelement(6, \"i\", 83);\n    i0.ɵɵelementStart(7, \"div\", 84)(8, \"div\", 85);\n    i0.ɵɵtext(9, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 86)(11, \"span\", 87);\n    i0.ɵɵtext(12, \"\\u7576\\u524D\\u5EFA\\u6750\\u4EE3\\u865F\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 88);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\");\n    i0.ɵɵtext(16, \"\\u9078\\u64C7\\u5716\\u7247\\u5F8C\\uFF0C\\u5EFA\\u6750\\u4EE3\\u865F\\u5C07\\u6703\\u81EA\\u52D5\\u8A2D\\u5B9A\\u70BA\\u6240\\u9078\\u5716\\u7247\\u7684\\u6A94\\u540D\\uFF0C\\u4E26\\u5EFA\\u7ACB\\u5716\\u7247\\u8207\\u5EFA\\u6750\\u7684\\u7D81\\u5B9A\\u95DC\\u4FC2\\u3002\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 89)(18, \"div\", 90)(19, \"label\", 91);\n    i0.ɵɵtext(20, \"\\u5716\\u7247\\u985E\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"nb-select\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_62_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedCategory, $event) || (ctx_r3.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function BuildingMaterialComponent_ng_template_62_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.categoryChanged($event));\n    });\n    i0.ɵɵtemplate(22, BuildingMaterialComponent_ng_template_62_nb_option_22_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 93)(24, \"label\", 91);\n    i0.ɵɵtext(25, \"\\u641C\\u5C0B\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_62_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageSearchTerm, $event) || (ctx_r3.imageSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function BuildingMaterialComponent_ng_template_62_Template_input_input_26_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.filterAvailableImages());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 95)(28, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.loadImages());\n    });\n    i0.ɵɵtext(29, \" \\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelement(30, \"i\", 97);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 98)(32, \"div\", 99)(33, \"div\", 100)(34, \"h6\", 101);\n    i0.ɵɵtext(35, \"\\u53EF\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 102);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 103)(39, \"div\", 104);\n    i0.ɵɵtemplate(40, BuildingMaterialComponent_ng_template_62_div_40_Template, 12, 4, \"div\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(41, BuildingMaterialComponent_ng_template_62_div_41_Template, 4, 0, \"div\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 107)(43, \"ngx-pagination\", 35);\n    i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_CollectionSizeChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageTotalRecords, $event) || (ctx_r3.imageTotalRecords = $event);\n      return i0.ɵɵresetView($event);\n    })(\"PageSizeChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageSizeChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imagePageSize, $event) || (ctx_r3.imagePageSize = $event);\n      return i0.ɵɵresetView($event);\n    })(\"PageChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageCurrentPage, $event) || (ctx_r3.imageCurrentPage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.imagePageChanged($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 108)(45, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.moveAllToSelected());\n    });\n    i0.ɵɵelement(46, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.moveAllToAvailable());\n    });\n    i0.ɵɵelement(48, \"i\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(49, \"hr\", 113);\n    i0.ɵɵelementStart(50, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_50_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.clearAllSelection());\n    });\n    i0.ɵɵelement(51, \"i\", 115)(52, \"br\");\n    i0.ɵɵelementStart(53, \"small\");\n    i0.ɵɵtext(54, \"\\u6E05\\u9664\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(55, \"div\", 99)(56, \"div\", 100)(57, \"h6\", 101);\n    i0.ɵɵtext(58, \"\\u5DF2\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"div\", 102);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 103)(62, \"div\", 104);\n    i0.ɵɵtemplate(63, BuildingMaterialComponent_ng_template_62_div_63_Template, 17, 11, \"div\", 116);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(64, BuildingMaterialComponent_ng_template_62_div_64_Template, 6, 0, \"div\", 106);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(65, \"nb-card-footer\", 117)(66, \"div\", 102);\n    i0.ɵɵtemplate(67, BuildingMaterialComponent_ng_template_62_span_67_Template, 3, 1, \"span\", 118)(68, BuildingMaterialComponent_ng_template_62_span_68_Template, 3, 1, \"span\", 119);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 120)(70, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_70_listener() {\n      const ref_r25 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCloseImageBinder(ref_r25));\n    });\n    i0.ɵɵtext(71, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_72_listener() {\n      const ref_r25 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onConfirmImageSelection(ref_r25));\n    });\n    i0.ɵɵtext(73);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5716\\u7247\\u7D81\\u5B9A - \", ctx_r3.selectedMaterial.CName ? \"\\u70BA \" + ctx_r3.selectedMaterial.CName + \" \\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\" : \"\\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\", \" \");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r3.selectedMaterial.CImageCode || \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.categoryOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.imageSearchTerm);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r3.imageTotalRecords, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.availableImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.availableImages.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx_r3.imageTotalRecords)(\"PageSize\", ctx_r3.imagePageSize)(\"Page\", ctx_r3.imageCurrentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.availableImages.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u53D6: \", ctx_r3.selectedImages.length, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getBoundImagesCount() > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getNewSelectedCount() > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r3.selectedImages.length, \") \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_64_img_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 162);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.previewingImage.fullUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r3.previewingImage.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_64_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135);\n    i0.ɵɵelement(1, \"i\", 149);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u5716\\u7247\\u8F09\\u5165\\u5931\\u6557\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 155)(1, \"nb-card-header\", 117)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 120)(5, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.previousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 157);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.nextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 158);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 159);\n    i0.ɵɵtemplate(12, BuildingMaterialComponent_ng_template_64_img_12_Template, 1, 2, \"img\", 160)(13, BuildingMaterialComponent_ng_template_64_div_13_Template, 4, 0, \"div\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 117)(15, \"div\", 102);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 120)(18, \"button\", 161);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleImageSelectionInPreview());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_20_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r26).dialogRef;\n      return i0.ɵɵresetView(ref_r27.close());\n    });\n    i0.ɵɵtext(21, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r3.previewingImage == null ? null : ctx_r3.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex >= ctx_r3.allImages.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.previewingImage && ctx_r3.previewingImage.fullUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.previewingImage || !ctx_r3.previewingImage.fullUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.currentPreviewIndex + 1, \" / \", ctx_r3.allImages.length, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.previewingImage && ctx_r3.isImageSelected(ctx_r3.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 163)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3, \" \\u6AA2\\u8996 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 164)(5, \"div\", 165);\n    i0.ɵɵelement(6, \"img\", 166);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-footer\")(8, \"div\", 167)(9, \"button\", 168);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_66_Template_button_click_9_listener() {\n      const ref_r29 = i0.ɵɵrestoreView(_r28).dialogRef;\n      return i0.ɵɵresetView(ref_r29.close());\n    });\n    i0.ɵɵtext(10, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r3.currentImageShowing, i0.ɵɵsanitizeUrl);\n  }\n}\n// 圖片類別枚舉\nvar PictureCategory = /*#__PURE__*/function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n  return PictureCategory;\n}(PictureCategory || {});\nexport let BuildingMaterialComponent = /*#__PURE__*/(() => {\n  class BuildingMaterialComponent extends BaseComponent {\n    // 根據狀態值獲取狀態標籤\n    getStatusLabel(status) {\n      const option = this.statusOptions.find(opt => opt.value === status);\n      return option ? option.label : '未設定';\n    }\n    constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService, _pictureService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._buildCaseService = _buildCaseService;\n      this._materialService = _materialService;\n      this._utilityService = _utilityService;\n      this._pictureService = _pictureService;\n      this.isNew = true;\n      this.listBuildCases = [];\n      this.materialOptions = [{\n        value: null,\n        label: '全部'\n      }, {\n        value: false,\n        label: '方案'\n      }, {\n        value: true,\n        label: '選樣'\n      }];\n      this.materialOptionsId = null;\n      this.CSelectName = \"\";\n      // 移除圖片檔名相關欄位\n      // CImageCode: string = \"\"\n      // CInfoImageCode: string = \"\"\n      // 啟用建材代號欄位\n      this.CImageCode = \"\";\n      this.ShowPrice = false;\n      this.currentImageShowing = \"\";\n      this.filterMapping = false;\n      this.CIsMapping = true;\n      // 圖片綁定相關屬性 - 重構為 picklist 模式\n      this.allImages = []; // 所有圖片的原始數據\n      this.availableImages = []; // 左側可選擇的圖片\n      this.selectedImages = []; // 右側已選擇的圖片\n      this.boundImageIds = []; // 已綁定的圖片ID\n      this.imageSearchTerm = \"\";\n      this.previewingImage = null;\n      this.currentPreviewIndex = 0;\n      // 圖片綁定分頁屬性\n      this.imageCurrentPage = 1;\n      this.imagePageSize = 50;\n      this.imageTotalRecords = 0;\n      // 類別選項\n      this.categoryOptions = [{\n        value: PictureCategory.BUILDING_MATERIAL,\n        label: '建材圖片'\n      }, {\n        value: PictureCategory.SCHEMATIC,\n        label: '示意圖片'\n      }];\n      this.selectedCategory = PictureCategory.BUILDING_MATERIAL;\n      this.isCategorySelected = true; // 預設選擇建材圖片\n      // 讓模板可以使用 enum\n      this.PictureCategory = PictureCategory;\n      // 狀態選項\n      this.statusOptions = [{\n        value: 1,\n        //0停用 1啟用 9刪除\n        label: '啟用' //enable\n      }, {\n        value: 2,\n        label: '停用' //Disable\n      }];\n    }\n    ngOnInit() {\n      this.getListBuildCase();\n    }\n    getListBuildCase() {\n      this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n        body: {\n          CIsPagi: false,\n          CStatus: 1\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.listBuildCases = res.Entries?.length ? res.Entries : [];\n          this.selectedBuildCaseId = this.listBuildCases[0].cID;\n        }\n      }), mergeMap(() => this.getMaterialList())).subscribe();\n    }\n    getMaterialList(pageIndex = 1) {\n      return this._materialService.apiMaterialGetMaterialListPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CPlanUse: this.materialOptionsId,\n          CSelectName: this.CSelectName,\n          // 啟用建材代號查詢條件\n          CImageCode: this.CImageCode,\n          // CInfoImageCode: this.CInfoImageCode,\n          PageSize: this.pageSize,\n          PageIndex: pageIndex,\n          CIsMapping: this.CIsMapping\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.materialList = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n          if (this.materialList.length > 0) {\n            this.ShowPrice = this.materialList[0].CShowPrice;\n          }\n        }\n      }));\n    }\n    search() {\n      this.getMaterialList().subscribe();\n    }\n    pageChanged(pageIndex) {\n      this.getMaterialList(pageIndex).subscribe();\n    }\n    exportExelMaterialList() {\n      this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n        body: this.selectedBuildCaseId\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries.FileByte) {\n            this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n          }\n        }\n      });\n    }\n    exportExelMaterialTemplate() {\n      this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n        body: this.selectedBuildCaseId\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries.FileByte) {\n            this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n          }\n        }\n      });\n    }\n    addNew(ref) {\n      this.isNew = true;\n      this.selectedMaterial = {\n        CStatus: 1 // 預設為啟用狀態\n      };\n      this.dialogService.open(ref);\n    }\n    onSelectedMaterial(data, ref) {\n      this.isNew = false;\n      this.selectedMaterial = {\n        ...data\n      };\n      this.dialogService.open(ref);\n    }\n    bindImageForMaterial(data, ref) {\n      this.selectedMaterial = {\n        ...data\n      };\n      // 設定已綁定的圖片ID\n      this.boundImageIds = this.selectedMaterial.CSelectPictureId ? this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id) : [];\n      // 重置選擇狀態和分頁\n      this.selectedImages = [];\n      this.imageCurrentPage = 1;\n      this.imageSearchTerm = \"\";\n      this.loadImages();\n      this.dialogService.open(ref, {\n        closeOnBackdropClick: false\n      });\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[名稱]', this.selectedMaterial.CName);\n      this.valid.required('[項目]', this.selectedMaterial.CPart);\n      this.valid.required('[位置]', this.selectedMaterial.CLocation);\n      this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n      // 啟用建材代號驗證\n      this.valid.required('[建材代號]', this.selectedMaterial.CImageCode);\n      this.valid.required('[狀態]', this.selectedMaterial.CStatus);\n      this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30);\n      this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30);\n      this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30);\n      this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n      // 啟用建材代號長度驗證\n      this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CImageCode, 30);\n    }\n    onSubmit(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          // 暫時保留 CImageCode 給圖片綁定功能使用\n          CImageCode: this.selectedMaterial.CImageCode,\n          CName: this.selectedMaterial.CName,\n          CPart: this.selectedMaterial.CPart,\n          CLocation: this.selectedMaterial.CLocation,\n          CSelectName: this.selectedMaterial.CSelectName,\n          CDescription: this.selectedMaterial.CDescription,\n          CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n          CPrice: this.selectedMaterial.CPrice,\n          CStatus: this.selectedMaterial.CStatus,\n          // 加入狀態欄位\n          CPictureId: this.selectedMaterial.selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    detectFileExcel(event) {\n      const target = event.target;\n      const reader = new FileReader();\n      reader.readAsBinaryString(target.files[0]);\n      reader.onload = e => {\n        const binarystr = e.target.result;\n        const wb = XLSX.read(binarystr, {\n          type: 'binary'\n        });\n        const wsname = wb.SheetNames[0];\n        const ws = wb.Sheets[wsname];\n        let isValidFile = true;\n        const data = XLSX.utils.sheet_to_json(ws);\n        if (data && data.length > 0) {\n          data.forEach(x => {\n            if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'])) {\n              isValidFile = false;\n            }\n          });\n          if (!isValidFile) {\n            this.message.showErrorMSG(\"导入文件时出现错误\");\n          } else {\n            this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n              body: {\n                CBuildCaseId: this.selectedBuildCaseId,\n                CFile: target.files[0]\n              }\n            }).pipe(tap(res => {\n              if (res.StatusCode == 0) {\n                this.message.showSucessMSG(\"執行成功\");\n              } else {\n                this.message.showErrorMSG(res.Message);\n              }\n            }), mergeMap(() => this.getMaterialList(1))).subscribe();\n          }\n        } else {\n          this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n        }\n        event.target.value = null;\n      };\n    }\n    showImage(imageUrl, dialog) {\n      this.currentImageShowing = imageUrl;\n      this.dialogService.open(dialog);\n    }\n    changeFilter() {\n      if (this.filterMapping) {\n        this.CIsMapping = false;\n        this.getMaterialList().subscribe();\n      } else {\n        this.CIsMapping = true;\n        this.getMaterialList().subscribe();\n      }\n    }\n    // 圖片綁定功能方法\n    openImageBinder(ref) {\n      // 重置選擇狀態和分頁\n      this.selectedImages = [];\n      this.imageCurrentPage = 1;\n      this.imageSearchTerm = \"\";\n      this.loadImages();\n      this.dialogService.open(ref, {\n        closeOnBackdropClick: false\n      });\n    }\n    loadImages() {\n      // 使用 PictureService API 載入圖片列表\n      if (this.isCategorySelected && this.selectedBuildCaseId) {\n        this._pictureService.apiPictureGetPictureListPost$Json({\n          body: {\n            CBuildCaseId: this.selectedBuildCaseId,\n            cPictureType: this.selectedCategory,\n            PageIndex: this.imageCurrentPage,\n            PageSize: this.imagePageSize\n          }\n        }).subscribe(res => {\n          if (res.StatusCode === 0) {\n            // 將 API 回應轉換為 ImageItem 格式\n            this.allImages = res.Entries?.map(picture => ({\n              id: picture.CId || 0,\n              name: picture.CPictureCode || picture.CName || '',\n              size: 0,\n              thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n              fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n              lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\n            })) || [];\n            this.imageTotalRecords = res.TotalItems || 0;\n            // 只在第一次開啟對話框時初始化已選擇的圖片（已綁定的圖片）\n            // 分頁變更時不重置已選擇的圖片\n            if (this.selectedImages.length === 0 && this.boundImageIds.length > 0) {\n              this.loadAllImagesForInitialSelection();\n            } else {\n              // 更新可選擇的圖片（排除已選擇的）\n              this.updateAvailableImages();\n            }\n          } else {\n            this.message.showErrorMSG(res.Message || '載入圖片失敗');\n            this.allImages = [];\n            this.availableImages = [];\n            // 只在第一次載入錯誤時清空已選圖片\n            if (this.selectedImages.length === 0) {\n              this.selectedImages = [];\n            }\n          }\n        });\n      } else {\n        // 如果沒有選擇類別或建案，清空圖片列表\n        this.allImages = [];\n        this.availableImages = [];\n        this.selectedImages = [];\n      }\n    }\n    // 載入所有圖片以進行初始選擇（僅用於已綁定圖片的初始化）\n    loadAllImagesForInitialSelection() {\n      // 為了初始化已綁定的圖片，我們需要載入所有圖片\n      // 這裡使用一個較大的 PageSize 來獲取所有圖片\n      this._pictureService.apiPictureGetPictureListPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: this.selectedCategory,\n          PageIndex: 1,\n          PageSize: 9999 // 使用大數字獲取所有圖片\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          const allAvailableImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\n          })) || [];\n          // 從所有圖片中找出已綁定的圖片\n          const boundImages = allAvailableImages.filter(image => this.boundImageIds.includes(image.id));\n          this.selectedImages = [...boundImages];\n          // 更新可選擇的圖片\n          this.updateAvailableImages();\n        }\n      });\n    }\n    // 新增 picklist 相關方法\n    updateAvailableImages() {\n      // 使用當前 API 回傳的圖片作為可選圖片基礎\n      let currentPageImages = [...this.allImages];\n      // 根據搜尋條件篩選當前分頁圖片\n      if (this.imageSearchTerm.trim()) {\n        const searchTerm = this.imageSearchTerm.toLowerCase();\n        currentPageImages = currentPageImages.filter(image => image.name.toLowerCase().includes(searchTerm));\n      }\n      // 排除已選擇的圖片\n      const selectedIds = this.selectedImages.map(img => img.id);\n      this.availableImages = currentPageImages.filter(image => !selectedIds.includes(image.id));\n    }\n    filterAvailableImages() {\n      this.updateAvailableImages();\n    }\n    moveToSelected(image, event) {\n      if (event) {\n        event.stopPropagation();\n      }\n      // 將圖片從可選移到已選\n      const index = this.availableImages.findIndex(img => img.id === image.id);\n      if (index > -1) {\n        this.selectedImages.push(image);\n        this.updateAvailableImages();\n      }\n    }\n    moveToAvailable(image, event) {\n      if (event) {\n        event.stopPropagation();\n      }\n      // 將圖片從已選移到可選（包括已綁定的圖片也可以取消）\n      const index = this.selectedImages.findIndex(img => img.id === image.id);\n      if (index > -1) {\n        this.selectedImages.splice(index, 1);\n        this.updateAvailableImages();\n      }\n    }\n    moveAllToSelected() {\n      // 將所有可選圖片移到已選\n      this.selectedImages.push(...this.availableImages);\n      this.updateAvailableImages();\n    }\n    moveAllToAvailable() {\n      // 將所有已選圖片移到可選（包括已綁定的圖片）\n      this.selectedImages = [];\n      this.updateAvailableImages();\n    }\n    isImageBound(image) {\n      return this.boundImageIds.includes(image.id);\n    }\n    getBoundImagesCount() {\n      return this.selectedImages.filter(image => this.isImageBound(image)).length;\n    }\n    getNewSelectedCount() {\n      return this.selectedImages.filter(image => !this.isImageBound(image)).length;\n    }\n    // 清除所有選擇（包括已綁定的圖片）\n    clearAllSelection() {\n      this.selectedImages = [];\n      this.updateAvailableImages();\n    }\n    previewImage(image, imagePreviewRef, event) {\n      event.stopPropagation();\n      this.previewingImage = image;\n      // 在所有圖片中找到當前預覽圖片的索引\n      this.currentPreviewIndex = this.allImages.findIndex(img => img.id === image.id);\n      this.dialogService.open(imagePreviewRef);\n    }\n    previousImage() {\n      if (this.currentPreviewIndex > 0) {\n        this.currentPreviewIndex--;\n        this.previewingImage = this.allImages[this.currentPreviewIndex];\n      }\n    }\n    nextImage() {\n      if (this.currentPreviewIndex < this.allImages.length - 1) {\n        this.currentPreviewIndex++;\n        this.previewingImage = this.allImages[this.currentPreviewIndex];\n      }\n    }\n    toggleImageSelectionInPreview() {\n      if (this.previewingImage) {\n        const isSelected = this.selectedImages.some(img => img.id === this.previewingImage.id);\n        if (isSelected) {\n          this.moveToAvailable(this.previewingImage);\n        } else {\n          this.moveToSelected(this.previewingImage);\n        }\n      }\n    }\n    isImageSelected(image) {\n      return this.selectedImages.some(selected => selected.id === image.id);\n    }\n    onConfirmImageSelection(ref) {\n      if (this.selectedImages.length > 0) {\n        // 收集選中圖片的 ID\n        const selectedImageIds = this.selectedImages.map(img => img.id); // 如果只選取一張圖片，直接設定圖片 ID\n        if (this.selectedImages.length === 1) {\n          // 設定第一張圖片的 ID 到 CPictureId（單一數字）\n          this.selectedMaterial.CPictureId = this.selectedImages[0].id;\n        } else {\n          // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\n          const imageNames = this.selectedImages.map(img => img.name).join(', ');\n          this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\n          // 設定第一張圖片的 ID 到 CPictureId（單一數字）\n          this.selectedMaterial.CPictureId = this.selectedImages[0].id;\n        }\n        // 暫存所有選中的圖片 ID，供 API 呼叫使用\n        this.selectedMaterial.selectedImageIds = selectedImageIds;\n        // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\n        if (this.selectedMaterial.CId) {\n          this.saveImageBinding();\n        }\n      }\n      this.clearAllSelection();\n      ref.close();\n    } // 新增方法：保存圖片綁定\n    saveImageBinding() {\n      this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CImageCode: this.selectedMaterial.CImageCode,\n          CName: this.selectedMaterial.CName,\n          CPart: this.selectedMaterial.CPart,\n          CLocation: this.selectedMaterial.CLocation,\n          CSelectName: this.selectedMaterial.CSelectName,\n          CDescription: this.selectedMaterial.CDescription,\n          CMaterialId: this.selectedMaterial.CId,\n          CPrice: this.selectedMaterial.CPrice,\n          CStatus: this.selectedMaterial.CStatus,\n          // 加入狀態欄位\n          CPictureId: this.selectedMaterial.selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(`圖片綁定成功`);\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      }), mergeMap(() => this.getMaterialList()), finalize(() => {\n        // 清空選取的建材\n        this.selectedMaterial = {};\n      })).subscribe();\n    }\n    onCloseImageBinder(ref) {\n      this.clearAllSelection();\n      this.imageSearchTerm = \"\";\n      this.imageCurrentPage = 1; // 重設圖片頁碼\n      ref.close();\n    } // 類別變更處理方法\n    categoryChanged(category) {\n      this.selectedCategory = category;\n      this.isCategorySelected = true;\n      // 當類別變更時重設頁碼並重新載入圖片\n      this.imageCurrentPage = 1;\n      if (this.selectedBuildCaseId) {\n        this.loadImages();\n      }\n    }\n    // 獲取類別標籤的方法\n    getCategoryLabel(category) {\n      const option = this.categoryOptions.find(opt => opt.value === category);\n      return option ? option.label : '未知類別';\n    }\n    // 圖片分頁變更處理方法\n    imagePageChanged(page) {\n      this.imageCurrentPage = page;\n      this.loadImages();\n    }\n    static {\n      this.ɵfac = function BuildingMaterialComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BuildingMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.MaterialService), i0.ɵɵdirectiveInject(i6.UtilityService), i0.ɵɵdirectiveInject(i5.PictureService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: BuildingMaterialComponent,\n        selectors: [[\"ngx-building-material\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 68,\n        vars: 14,\n        consts: [[\"inputFile\", \"\"], [\"dialog\", \"\"], [\"imageBinder\", \"\"], [\"imagePreview\", \"\"], [\"dialogImage\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\", \"w-[22%]\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\", \"maxlength\", \"50\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u4EE3\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"status\", \"basic\", 1, \"flex\", 2, \"flex\", \"auto\", 3, \"checkedChange\", \"change\", \"checked\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mr-2 text-white ml-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 ml-2 mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".xls, .xlsx\", 1, \"hidden\", 3, \"change\"], [1, \"btn\", \"btn-success\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-file-download\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1200px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", \"class\", \"col-1\", 4, \"ngIf\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", \"mr-2\", \"text-white\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"ml-2\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"btn\", \"btn-info\", \"mx-1\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\"], [\"class\", \"badge badge-success mr-2\", 4, \"ngIf\"], [\"class\", \"badge badge-danger mr-2\", 4, \"ngIf\"], [1, \"badge\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-info btn-sm m-1\", 3, \"title\", \"click\", 4, \"ngIf\"], [1, \"badge\", \"badge-success\", \"mr-2\"], [1, \"badge\", \"badge-danger\", \"mr-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"m-1\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\"], [1, \"w-[700px]\"], [1, \"px-4\"], [1, \"text-base\"], [1, \"w-full\", \"mt-3\"], [1, \"flex\", \"items-center\"], [1, \"required-field\", \"w-[150px]\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"20\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"mt-3\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"50\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-[150px]\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [\"type\", \"number\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"mt-4\", \"pt-3\", \"border-t\", \"border-gray-200\"], [1, \"flex\", \"gap-2\", \"w-full\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\", \"mr-2\"], [\"class\", \"text-sm text-gray-600 flex items-center\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"text-sm\", \"text-gray-600\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-check-circle\", \"text-green-500\", \"mr-2\"], [1, \"w-[90vw]\", \"max-w-[1200px]\", \"h-[85vh]\"], [1, \"px-4\", \"d-flex\", \"flex-column\", 2, \"height\", \"calc(100% - 120px)\", \"overflow\", \"hidden\", \"padding-bottom\", \"0\"], [1, \"bg-blue-50\", \"border\", \"border-blue-200\", \"rounded-lg\", \"p-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"flex\", \"items-start\", \"gap-2\"], [1, \"fas\", \"fa-info-circle\", \"text-blue-500\", \"mt-1\"], [1, \"text-sm\", \"text-blue-700\"], [1, \"font-medium\", \"mb-1\"], [1, \"mb-2\"], [1, \"font-medium\"], [1, \"bg-white\", \"px-2\", \"py-1\", \"rounded\", \"border\"], [1, \"flex\", \"gap-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"w-48\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\", \"block\"], [1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"flex-1\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"w-full\", \"search-input\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"flex\", \"flex-col\", \"justify-end\"], [1, \"btn\", \"btn-info\", \"btn-image-action\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"], [1, \"flex\", \"gap-4\", \"flex-1\", 2, \"min-height\", \"0\"], [1, \"flex-1\", \"d-flex\", \"flex-column\", \"border\", \"rounded\", \"p-3\", 2, \"min-height\", \"0\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\", \"font-medium\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"image-preview-container\", \"flex-1\", 2, \"overflow-y\", \"auto\"], [1, \"grid\", \"grid-cols-3\", \"gap-2\"], [\"class\", \"image-grid-item border rounded p-2 cursor-pointer hover:bg-gray-50\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-gray-500 py-20\", 4, \"ngIf\"], [1, \"mt-3\", \"d-flex\", \"justify-content-center\"], [1, \"d-flex\", \"flex-column\", \"justify-content-center\", \"gap-2\", 2, \"width\", \"80px\"], [\"title\", \"\\u5168\\u90E8\\u79FB\\u81F3\\u5DF2\\u9078\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [\"title\", \"\\u5168\\u90E8\\u79FB\\u81F3\\u53EF\\u9078\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [1, \"my-2\"], [\"title\", \"\\u6E05\\u9664\\u6240\\u6709\\u9078\\u64C7\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-times\"], [\"class\", \"image-grid-item border rounded p-2 cursor-pointer\", 3, \"border-success\", \"bg-green-50\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"class\", \"text-success\", 4, \"ngIf\"], [\"class\", \"text-info ml-3\", 4, \"ngIf\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"image-grid-item\", \"border\", \"rounded\", \"p-2\", \"cursor-pointer\", \"hover:bg-gray-50\", 3, \"click\"], [1, \"w-full\", \"h-24\", \"bg-gray-100\", \"rounded\", \"mb-2\", \"flex\", \"items-center\", \"justify-center\", \"overflow-hidden\"], [\"class\", \"image-thumbnail max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-600\"], [1, \"font-medium\", \"truncate\", 3, \"title\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mt-2\"], [1, \"btn\", \"btn-outline-info\", \"btn-xs\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"btn\", \"btn-outline-primary\", \"btn-xs\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"image-thumbnail\", \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-xl\", \"mb-1\"], [1, \"text-xs\"], [1, \"text-center\", \"text-gray-500\", \"py-20\"], [1, \"fas\", \"fa-images\", \"text-4xl\", \"mb-3\"], [1, \"image-grid-item\", \"border\", \"rounded\", \"p-2\", \"cursor-pointer\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [\"class\", \"badge badge-success text-xs px-2 py-1\", \"title\", \"\\u6B64\\u5716\\u7247\\u5DF2\\u7D93\\u7D81\\u5B9A\\u5230\\u6B64\\u5EFA\\u6750\", 4, \"ngIf\"], [\"class\", \"badge badge-info text-xs px-2 py-1\", 4, \"ngIf\"], [1, \"text-gray-500\"], [1, \"btn\", \"btn-outline-danger\", \"btn-xs\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [\"title\", \"\\u6B64\\u5716\\u7247\\u5DF2\\u7D93\\u7D81\\u5B9A\\u5230\\u6B64\\u5EFA\\u6750\", 1, \"badge\", \"badge-success\", \"text-xs\", \"px-2\", \"py-1\"], [1, \"badge\", \"badge-info\", \"text-xs\", \"px-2\", \"py-1\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"text-sm\", \"mt-2\"], [1, \"text-success\"], [1, \"fas\", \"fa-check-circle\"], [1, \"text-info\", \"ml-3\"], [1, \"fas\", \"fa-plus-circle\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [2, \"padding\", \"1rem 2rem\"], [1, \"w-full\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"]],\n        template: function BuildingMaterialComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 5)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 6);\n            i0.ɵɵtext(5, \" \\u53EF\\u8A2D\\u5B9A\\u55AE\\u7B46\\u6216\\u6279\\u6B21\\u532F\\u5165\\u8A2D\\u5B9A\\u5404\\u5340\\u57DF\\u53CA\\u65B9\\u6848\\u5C0D\\u61C9\\u4E4B\\u5EFA\\u6750\\u3002 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9)(9, \"label\", 10);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"ngModelChange\", function BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.search());\n            });\n            i0.ɵɵtemplate(12, BuildingMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"label\", 10);\n            i0.ɵɵtext(16, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.CSelectName, $event) || (ctx.CSelectName = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(18, \"div\", 8)(19, \"div\", 9)(20, \"label\", 10);\n            i0.ɵɵtext(21, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"input\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_22_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.CImageCode, $event) || (ctx.CImageCode = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\", 16)(25, \"nb-checkbox\", 17);\n            i0.ɵɵtwoWayListener(\"checkedChange\", function BuildingMaterialComponent_Template_nb_checkbox_checkedChange_25_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.filterMapping, $event) || (ctx.filterMapping = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_nb_checkbox_change_25_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.changeFilter());\n            });\n            i0.ɵɵtext(26, \" \\u53EA\\u986F\\u793A\\u7F3A\\u5C11\\u5EFA\\u6750\\u5716\\u7247\\u6216\\u793A\\u610F\\u5716\\u7247\\u7684\\u5EFA\\u6750 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(27, BuildingMaterialComponent_button_27_Template, 3, 0, \"button\", 18)(28, BuildingMaterialComponent_button_28_Template, 3, 0, \"button\", 19)(29, BuildingMaterialComponent_button_29_Template, 3, 0, \"button\", 20)(30, BuildingMaterialComponent_button_30_Template, 2, 0, \"button\", 21);\n            i0.ɵɵelementStart(31, \"input\", 22, 0);\n            i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_input_change_31_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.detectFileExcel($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_Template_button_click_33_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.exportExelMaterialTemplate());\n            });\n            i0.ɵɵtext(34, \"\\u4E0B\\u8F09\\u7BC4\\u4F8B\\u6A94\\u6848 \");\n            i0.ɵɵelement(35, \"i\", 24);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(36, \"div\", 25)(37, \"table\", 26)(38, \"thead\")(39, \"tr\", 27)(40, \"th\", 28);\n            i0.ɵɵtext(41, \"\\u9805\\u6B21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"th\", 28);\n            i0.ɵɵtext(43, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"th\", 29);\n            i0.ɵɵtext(45, \"\\u9078\\u9805\\u984C\\u76EE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"th\", 28);\n            i0.ɵɵtext(47, \"\\u9078\\u9805\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"th\", 30);\n            i0.ɵɵtext(49, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"th\", 28);\n            i0.ɵɵtext(51, \"\\u5DF2\\u7D81\\u5B9A\\u5716\\u7247\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(52, BuildingMaterialComponent_th_52_Template, 2, 0, \"th\", 31);\n            i0.ɵɵelementStart(53, \"th\", 28);\n            i0.ɵɵtext(54, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"th\", 32);\n            i0.ɵɵtext(56, \"\\u64CD\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(57, BuildingMaterialComponent_tbody_57_Template, 2, 1, \"tbody\", 33);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(58, \"nb-card-footer\", 34)(59, \"ngx-pagination\", 35);\n            i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_59_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n              return i0.ɵɵresetView($event);\n            })(\"PageSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_59_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n              return i0.ɵɵresetView($event);\n            })(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_59_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_59_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(60, BuildingMaterialComponent_ng_template_60_Template, 53, 12, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(62, BuildingMaterialComponent_ng_template_62_Template, 74, 21, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(64, BuildingMaterialComponent_ng_template_64_Template, 22, 8, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(66, BuildingMaterialComponent_ng_template_66_Template, 11, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCases);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CSelectName);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CImageCode);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"checked\", ctx.filterMapping);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isExcelExport);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isExcelImport);\n            i0.ɵɵadvance(22);\n            i0.ɵɵproperty(\"ngIf\", ctx.ShowPrice == true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.materialList != null && ctx.materialList.length > 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n          }\n        },\n        dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.MaxLengthValidator, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent],\n        styles: [\"@charset \\\"UTF-8\\\";.image-table[_ngcontent-%COMP%]{width:50px;height:50px;cursor:pointer}.empty-image[_ngcontent-%COMP%]{color:red}.fit-size[_ngcontent-%COMP%]{width:100%;height:auto;max-width:100%;max-height:500px;object-fit:contain}.image-grid-item[_ngcontent-%COMP%]{transition:all .3s ease}.image-grid-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #0000001a}.image-grid-item.selected[_ngcontent-%COMP%]{border-color:#36f;background-color:#f0f7ff;box-shadow:0 0 0 2px #36f3}.image-checkbox[_ngcontent-%COMP%]{border-color:#ccc;background-color:#fff;transition:all .2s ease}.image-checkbox.checked[_ngcontent-%COMP%]{background-color:#36f;border-color:#36f}.image-thumbnail[_ngcontent-%COMP%]{transition:transform .2s ease}.image-thumbnail[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.image-preview-container[_ngcontent-%COMP%]{max-height:480px;overflow-y:auto!important;overflow-x:hidden;-webkit-overflow-scrolling:touch;scrollbar-width:thin;scrollbar-color:#c1c1c1 #f1f1f1}.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:8px}.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:4px}.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:4px}.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}.search-input[_ngcontent-%COMP%]{padding:8px 12px;border:1px solid #ddd;border-radius:4px;font-size:14px;transition:border-color .2s ease-in-out}.search-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#36f;box-shadow:0 0 0 2px #3366ff1a}.btn-image-action[_ngcontent-%COMP%]{padding:4px 8px;font-size:12px;border-radius:4px;transition:all .2s ease-in-out}.btn-image-action[_ngcontent-%COMP%]:hover{transform:translateY(-1px)}.btn-image-action.btn-xs[_ngcontent-%COMP%]{padding:2px 6px;font-size:10px}.d-flex.flex-column[_ngcontent-%COMP%]{display:flex;flex-direction:column}.flex-1[_ngcontent-%COMP%]{flex:1;min-height:0}nb-card.w-\\\\__ph-0__[_ngcontent-%COMP%]   .nb-card-body[_ngcontent-%COMP%]{height:580px!important;overflow:hidden!important;display:flex!important;flex-direction:column!important}.flex-shrink-0[_ngcontent-%COMP%]{flex-shrink:0}.image-preview-container.flex-1[_ngcontent-%COMP%]{flex:1 1 auto;min-height:0;height:auto;overflow-y:scroll!important;overflow-x:hidden!important}.grid.grid-cols-4[_ngcontent-%COMP%]{min-height:min-content}  nb-card-body .image-preview-container{max-height:none!important;height:100%!important}.picklist-container[_ngcontent-%COMP%]{display:flex;gap:1rem;height:100%;min-height:400px}.picklist-panel[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;border:1px solid #e0e0e0;border-radius:8px;padding:1rem;background-color:#fafafa}.picklist-panel[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin-bottom:.75rem;color:#333;font-weight:600}.picklist-controls[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;width:80px;gap:.5rem}.picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%;min-height:36px;display:flex;align-items:center;justify-content:center}.picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.picklist-controls[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%]{width:100%;margin:.5rem 0;border-color:#ddd}.image-grid-item.hover\\\\:bg-gray-50[_ngcontent-%COMP%]:hover{background-color:#f9f9f9}.image-grid-item.border-success[_ngcontent-%COMP%]{border-color:#28a745!important;background-color:#f8fff9}.image-grid-item.bg-green-50[_ngcontent-%COMP%]{background-color:#f0fff4}.badge.badge-success[_ngcontent-%COMP%]{background-color:#28a745;color:#fff}.badge.badge-info[_ngcontent-%COMP%]{background-color:#17a2b8;color:#fff}@media (max-width: 768px){.picklist-container[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}.picklist-controls[_ngcontent-%COMP%]{flex-direction:row;width:100%;height:auto}.picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:auto;min-width:60px}.grid.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}}\"]\n      });\n    }\n  }\n  return BuildingMaterialComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}