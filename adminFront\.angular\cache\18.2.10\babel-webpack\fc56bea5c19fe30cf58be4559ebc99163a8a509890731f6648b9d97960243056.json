{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let DateFormatPipe = /*#__PURE__*/(() => {\n  class DateFormatPipe {\n    transform(value) {\n      if (!value) return value;\n      const date = new Date(value);\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    }\n    static {\n      this.ɵfac = function DateFormatPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DateFormatPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"dateFormat\",\n        type: DateFormatPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return DateFormatPipe;\n})();\nexport let DateFormatHourPipe = /*#__PURE__*/(() => {\n  class DateFormatHourPipe {\n    transform(value) {\n      if (!value) return value;\n      const date = new Date(value);\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      const seconds = String(date.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    }\n    static {\n      this.ɵfac = function DateFormatHourPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DateFormatHourPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"dateFormatHour\",\n        type: DateFormatHourPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return DateFormatHourPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}