{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function filter(predicate, thisArg) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => predicate.call(thisArg, value, index++) && subscriber.next(value)));\n  });\n}\n//# sourceMappingURL=filter.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}