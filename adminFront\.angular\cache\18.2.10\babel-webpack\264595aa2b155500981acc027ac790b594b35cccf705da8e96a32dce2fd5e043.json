{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nlet TemplateViewerComponent = class TemplateViewerComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.templateType = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\n    this.fieldDisplayConfig = []; // 欄位顯示設定，由父元件傳入\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 公開Math對象供模板使用\n    this.Math = Math;\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = []; // 保留用於向後相容\n    this.selectedTemplate = null;\n    // 新的詳情資料管理\n    this.currentTemplateDetailsData = [];\n    this.detailSearchKeyword = ''; // 明細專用搜尋關鍵字\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 分頁功能 - 模板列表\n    this.templatePagination = {\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedTemplates = [];\n    // 分頁功能 - 模板詳情\n    this.detailPagination = {\n      currentPage: 1,\n      pageSize: 5,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedDetails = [];\n  }\n  ngOnInit() {\n    this.loadTemplates();\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 載入模板列表 - 使用真實的 API 調用\n  loadTemplates() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      PageIndex: 1,\n      // 暫時載入第一頁\n      PageSize: 100,\n      // 暫時載入較多資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateList API\n    this.templateService.apiTemplateGetTemplateListPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // API 調用成功\n          // 轉換 API 回應為內部格式\n          this.templates = response.Entries.map(item => ({\n            TemplateID: item.CTemplateId,\n            TemplateName: item.CTemplateName || '',\n            Description: item.CTemplateName || '',\n            CreateTime: item.CCreateDt,\n            UpdateTime: item.CUpdateDt,\n            Creator: item.CCreator || undefined,\n            Updator: item.CUpdator || undefined\n          }));\n          // 更新過濾列表\n          this.updateFilteredTemplates();\n          // 清空詳情資料，改為按需載入\n          this.templateDetails = [];\n          this.currentTemplateDetailsData = [];\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n          this.templateDetails = [];\n          this.updateFilteredTemplates();\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n        this.templates = [];\n        this.templateDetails = [];\n        this.updateFilteredTemplates();\n      }\n    });\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n    this.updateTemplatePagination();\n  }\n  // 更新模板分頁\n  updateTemplatePagination() {\n    this.templatePagination.totalItems = this.filteredTemplates.length;\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\n    // 確保當前頁面不超過總頁數\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\n    }\n    this.updatePaginatedTemplates();\n  }\n  // 更新分頁後的模板列表\n  updatePaginatedTemplates() {\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\n    const endIndex = startIndex + this.templatePagination.pageSize;\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\n  }\n  // 模板分頁導航\n  goToTemplatePage(page) {\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = page;\n      this.updatePaginatedTemplates();\n    }\n  }\n  // 獲取模板分頁頁碼數組\n  getTemplatePageNumbers() {\n    const pages = [];\n    const totalPages = this.templatePagination.totalPages;\n    const currentPage = this.templatePagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n    // 按需載入模板詳情\n    if (template.TemplateID) {\n      this.loadTemplateDetails(template.TemplateID);\n    }\n    this.updateDetailPagination();\n  }\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\n  loadTemplateDetails(templateId, pageIndex = 1, searchKeyword) {\n    const args = {\n      templateId: templateId\n    };\n    // 調用真實的 GetTemplateDetailById API\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\n          let allDetails = response.Entries.map(item => ({\n            CTemplateDetailId: item.CTemplateDetailId || 0,\n            CTemplateId: item.CTemplateId || templateId,\n            CReleateId: item.CReleateId || 0,\n            CSort: undefined,\n            CRemark: undefined,\n            CCreateDt: new Date().toISOString(),\n            CCreator: '系統',\n            CCategory: undefined,\n            CUnitPrice: undefined,\n            CQuantity: undefined,\n            CUnit: undefined\n          }));\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\n          if (searchKeyword && searchKeyword.trim()) {\n            allDetails = allDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()));\n          }\n          // 前端分頁處理 (因為 API 不支援分頁參數)\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n          const endIndex = startIndex + this.detailPagination.pageSize;\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\n          // 更新詳情資料和分頁資訊\n          this.currentTemplateDetailsData = pagedDetails;\n          this.detailPagination.totalItems = allDetails.length;\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\n          this.detailPagination.currentPage = pageIndex;\n        } else {\n          this.currentTemplateDetailsData = [];\n          this.detailPagination.totalItems = 0;\n          this.detailPagination.totalPages = 0;\n          this.detailPagination.currentPage = 1;\n        }\n      },\n      error: () => {\n        this.currentTemplateDetailsData = [];\n        this.detailPagination.totalItems = 0;\n        this.detailPagination.totalPages = 0;\n        this.detailPagination.currentPage = 1;\n      }\n    });\n  }\n  // 搜尋模板詳情 (明細專用搜尋)\n  searchTemplateDetails(keyword) {\n    this.detailSearchKeyword = keyword;\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\n    }\n  }\n  // 清除明細搜尋\n  clearDetailSearch() {\n    this.detailSearchKeyword = '';\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1);\n    }\n  }\n  // 明細搜尋輸入事件處理\n  onDetailSearchInput() {\n    // 可以在這裡添加即時搜尋邏輯，目前保持原有的 Enter 鍵搜尋方式\n  }\n  // 明細搜尋事件處理（即時搜尋）\n  onDetailSearch() {\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, this.detailSearchKeyword);\n    }\n  }\n  // 更新詳情分頁 (保留向後相容)\n  updateDetailPagination() {\n    // 如果使用新的詳情資料，直接返回\n    if (this.currentTemplateDetailsData.length > 0) {\n      return;\n    }\n    // 向後相容：使用舊的詳情資料\n    const details = this.currentTemplateDetails;\n    this.detailPagination.totalItems = details.length;\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\n    this.detailPagination.currentPage = 1; // 重置到第一頁\n    this.updatePaginatedDetails();\n  }\n  // 更新分頁後的詳情列表\n  updatePaginatedDetails() {\n    const details = this.currentTemplateDetails;\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\n    const endIndex = startIndex + this.detailPagination.pageSize;\n    this.paginatedDetails = details.slice(startIndex, endIndex);\n  }\n  // 詳情分頁導航\n  goToDetailPage(page) {\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\n      // 如果使用新的詳情資料，重新載入\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\n      } else {\n        // 向後相容：使用舊的分頁邏輯\n        this.detailPagination.currentPage = page;\n        this.updatePaginatedDetails();\n      }\n    }\n  }\n  // 獲取詳情分頁頁碼數組\n  getDetailPageNumbers() {\n    const pages = [];\n    const totalPages = this.detailPagination.totalPages;\n    const currentPage = this.detailPagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // 刪除模板 - 使用真實的 API 調用\n  deleteTemplateById(templateID) {\n    // 準備 API 請求參數\n    const deleteTemplateArgs = {\n      CTemplateId: templateID\n    };\n    // 調用 DeleteTemplate API\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\n      body: deleteTemplateArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          // 重新載入模板列表\n          this.loadTemplates();\n          // 如果當前查看的模板被刪除，關閉詳情\n          if (this.selectedTemplate?.TemplateID === templateID) {\n            this.selectedTemplate = null;\n          }\n        } else {\n          // API 返回錯誤\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n      }\n    });\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/Template/GetTemplateList\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/Template/SaveTemplate\n   *    請求體: {\n   *      CTemplateName: string,\n   *      CTemplateType: number,  // 1=客變需求\n   *      CStatus: number,\n   *      Details: [{\n   *        CReleateId: number,        // 關聯主檔ID\n   *        CReleateName: string       // 關聯名稱\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    POST /api/Template/DeleteTemplate\n   *    請求體: { CTemplateId: number }\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 欄位顯示相關輔助方法\n  hasVisibleFields(detail) {\n    return this.fieldDisplayConfig.some(config => this.getFieldValue(detail, config.fieldName));\n  }\n  getFieldValue(detail, fieldName) {\n    // 根據templateType動態取得資料\n    if (this.templateType === 1) {\n      // 當type=1時，從tblRequirement物件中取得資料\n      return detail.tblRequirement?.[fieldName];\n    } else {\n      // 其他type可能從tblMaterials或其他物件中取得資料\n      // 目前先支援直接從detail物件取得\n      return detail[fieldName];\n    }\n  }\n  getFieldClass(fieldName) {\n    const classMap = {\n      'CUnitPrice': 'price-group',\n      'CQuantity': 'quantity-group',\n      'CUnit': 'unit-group',\n      'CRemark': 'remark-group',\n      'CGroupName': 'group-group',\n      'CCategory': 'category-group'\n    };\n    return classMap[fieldName] || 'default-group';\n  }\n  getValueClass(fieldName) {\n    const classMap = {\n      'CUnitPrice': 'price-value',\n      'CQuantity': 'quantity-value',\n      'CUnit': 'unit-value',\n      'CRemark': 'remark-value'\n    };\n    return classMap[fieldName] || 'default-value';\n  }\n  formatFieldValue(detail, fieldName) {\n    const value = this.getFieldValue(detail, fieldName);\n    if (!value && value !== 0) return '';\n    switch (fieldName) {\n      case 'CUnitPrice':\n        return `NT$ ${Number(value).toLocaleString()}`;\n      case 'CQuantity':\n        const unit = this.getFieldValue(detail, 'CUnit') || '';\n        return unit ? `${value} ${unit}` : value.toString();\n      default:\n        return value.toString();\n    }\n  }\n  // 取得主要欄位值（用於標題顯示）\n  getPrimaryFieldValue(detail) {\n    // 使用第一個fieldDisplayConfig作為主要欄位\n    if (this.fieldDisplayConfig.length > 0) {\n      const primaryField = this.fieldDisplayConfig[0];\n      return this.getFieldValue(detail, primaryField.fieldName) || '';\n    }\n    // 如果沒有配置，回退到預設欄位\n    return this.getFieldValue(detail, 'CRequirement') || '';\n  }\n  // 取得用於meta顯示的欄位配置（排除第一個主要欄位）\n  getMetaFieldConfigs() {\n    // 返回除了第一個欄位以外的所有欄位，用於meta資訊顯示\n    return this.fieldDisplayConfig.slice(1);\n  }\n  // 取得meta欄位的CSS類別\n  getMetaFieldClass(fieldName) {\n    const classMap = {\n      'CGroupName': 'group-meta',\n      'CRemark': 'category-meta',\n      'CCategory': 'category-meta',\n      'CUnit': 'unit-meta',\n      'CUnitPrice': 'price-meta',\n      'CQuantity': 'quantity-meta'\n    };\n    return classMap[fieldName] || 'default-meta';\n  }\n  // 取得meta欄位的圖示\n  getMetaFieldIcon(fieldName) {\n    const iconMap = {\n      'CGroupName': 'fas fa-layer-group mr-1',\n      'CRemark': 'fas fa-tag mr-1',\n      'CCategory': 'fas fa-tag mr-1',\n      'CUnit': 'fas fa-ruler mr-1',\n      'CUnitPrice': 'fas fa-dollar-sign mr-1',\n      'CQuantity': 'fas fa-calculator mr-1'\n    };\n    return iconMap[fieldName] || 'fas fa-info-circle mr-1';\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n};\n__decorate([Input()], TemplateViewerComponent.prototype, \"templateType\", void 0);\n__decorate([Input()], TemplateViewerComponent.prototype, \"fieldDisplayConfig\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"selectTemplate\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"close\", void 0);\nTemplateViewerComponent = __decorate([Component({\n  selector: 'app-template-viewer',\n  templateUrl: './template-viewer.component.html',\n  styleUrls: ['./template-viewer.component.scss'],\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule]\n})], TemplateViewerComponent);\nexport { TemplateViewerComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "TemplateViewerComponent", "constructor", "templateService", "templateType", "fieldDisplayConfig", "selectTemplate", "close", "Math", "templates", "templateDetails", "selectedTemplate", "currentTemplateDetailsData", "detailSearchKeyword", "searchKeyword", "filteredTemplates", "templatePagination", "currentPage", "pageSize", "totalItems", "totalPages", "paginatedTemplates", "detailPagination", "paginatedDetails", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "TemplateID", "CTemplateId", "TemplateName", "Description", "CreateTime", "CCreateDt", "UpdateTime", "CUpdateDt", "Creator", "CCreator", "undefined", "Up<PERSON>tor", "CUpdator", "error", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "updateTemplatePagination", "length", "ceil", "max", "updatePaginatedTemplates", "startIndex", "endIndex", "slice", "goToTemplatePage", "page", "getTemplatePageNumbers", "pages", "startPage", "endPage", "min", "i", "push", "onSearch", "clearSearch", "onSelectTemplate", "emit", "loadTemplateDetails", "updateDetailPagination", "templateId", "pageIndex", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "allDetails", "CTemplateDetailId", "CReleateId", "CSort", "CRemark", "Date", "toISOString", "CCategory", "CUnitPrice", "CQuantity", "CUnit", "detail", "CReleateName", "CGroupName", "pagedDetails", "searchTemplateDetails", "clearDetailSearch", "onDetailSearchInput", "onDetailSearch", "details", "currentTemplateDetails", "updatePaginatedDetails", "goToDetailPage", "getDetailPageNumbers", "onClose", "onDeleteTemplate", "templateID", "confirm", "deleteTemplateById", "deleteTemplateArgs", "apiTemplateDeleteTemplatePost$Json", "closeTemplateDetail", "hasVisibleFields", "some", "config", "getFieldValue", "fieldName", "tblRequirement", "getFieldClass", "classMap", "getValueClass", "formatFieldValue", "value", "Number", "toLocaleString", "unit", "toString", "getPrimaryFieldValue", "primaryField", "getMetaFieldConfigs", "getMetaFieldClass", "getMetaFieldIcon", "iconMap", "d", "trackByTemplateId", "index", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, GetTemplateByIdArgs, GetTemplateDetailByIdArgs, TemplateDetailItem as ApiTemplateDetailItem } from 'src/services/api/models';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\r\n  @Input() fieldDisplayConfig: FieldDisplayConfig[] = []; // 欄位顯示設定，由父元件傳入\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 公開Math對象供模板使用\r\n  Math = Math;\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = []; // 保留用於向後相容\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 新的詳情資料管理\r\n  currentTemplateDetailsData: ApiTemplateDetailItem[] = [];\r\n  detailSearchKeyword = ''; // 明細專用搜尋關鍵字\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板列表\r\n  templatePagination = {\r\n    currentPage: 1,\r\n    pageSize: 10,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板詳情\r\n  detailPagination = {\r\n    currentPage: 1,\r\n    pageSize: 5,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedDetails: TemplateDetail[] = [];\r\n\r\n\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    this.loadTemplates();\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 載入模板列表 - 使用真實的 API 調用\r\n  loadTemplates() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      PageIndex: 1, // 暫時載入第一頁\r\n      PageSize: 100, // 暫時載入較多資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateList API\r\n    this.templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // API 調用成功\r\n\r\n          // 轉換 API 回應為內部格式\r\n          this.templates = response.Entries.map(item => ({\r\n            TemplateID: item.CTemplateId,\r\n            TemplateName: item.CTemplateName || '',\r\n            Description: item.CTemplateName || '',\r\n            CreateTime: item.CCreateDt,\r\n            UpdateTime: item.CUpdateDt,\r\n            Creator: item.CCreator || undefined,\r\n            Updator: item.CUpdator || undefined\r\n          }));\r\n\r\n          // 更新過濾列表\r\n          this.updateFilteredTemplates();\r\n\r\n          // 清空詳情資料，改為按需載入\r\n          this.templateDetails = [];\r\n          this.currentTemplateDetailsData = [];\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n          this.templateDetails = [];\r\n          this.updateFilteredTemplates();\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n        this.templateDetails = [];\r\n        this.updateFilteredTemplates();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n    this.updateTemplatePagination();\r\n  }\r\n\r\n  // 更新模板分頁\r\n  updateTemplatePagination() {\r\n    this.templatePagination.totalItems = this.filteredTemplates.length;\r\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\r\n\r\n    // 確保當前頁面不超過總頁數\r\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\r\n    }\r\n\r\n    this.updatePaginatedTemplates();\r\n  }\r\n\r\n  // 更新分頁後的模板列表\r\n  updatePaginatedTemplates() {\r\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\r\n    const endIndex = startIndex + this.templatePagination.pageSize;\r\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 模板分頁導航\r\n  goToTemplatePage(page: number) {\r\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = page;\r\n      this.updatePaginatedTemplates();\r\n    }\r\n  }\r\n\r\n  // 獲取模板分頁頁碼數組\r\n  getTemplatePageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.templatePagination.totalPages;\r\n    const currentPage = this.templatePagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n\r\n    // 按需載入模板詳情\r\n    if (template.TemplateID) {\r\n      this.loadTemplateDetails(template.TemplateID);\r\n    }\r\n\r\n    this.updateDetailPagination();\r\n  }\r\n\r\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\r\n  loadTemplateDetails(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    // 調用真實的 GetTemplateDetailById API\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n\r\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\r\n          let allDetails = response.Entries.map(item => ({\r\n            CTemplateDetailId: item.CTemplateDetailId || 0,\r\n            CTemplateId: item.CTemplateId || templateId,\r\n            CReleateId: item.CReleateId || 0,\r\n            CSort: undefined,\r\n            CRemark: undefined,\r\n            CCreateDt: new Date().toISOString(),\r\n            CCreator: '系統',\r\n            CCategory: undefined,\r\n            CUnitPrice: undefined,\r\n            CQuantity: undefined,\r\n            CUnit: undefined\r\n          } as TemplateDetailItem));\r\n\r\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\r\n          if (searchKeyword && searchKeyword.trim()) {\r\n            allDetails = allDetails.filter(detail =>\r\n              detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n              (detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n            );\r\n          }\r\n\r\n          // 前端分頁處理 (因為 API 不支援分頁參數)\r\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n          const endIndex = startIndex + this.detailPagination.pageSize;\r\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\r\n\r\n          // 更新詳情資料和分頁資訊\r\n          this.currentTemplateDetailsData = pagedDetails;\r\n          this.detailPagination.totalItems = allDetails.length;\r\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\r\n          this.detailPagination.currentPage = pageIndex;\r\n        } else {\r\n          this.currentTemplateDetailsData = [];\r\n          this.detailPagination.totalItems = 0;\r\n          this.detailPagination.totalPages = 0;\r\n          this.detailPagination.currentPage = 1;\r\n        }\r\n      },\r\n      error: () => {\r\n        this.currentTemplateDetailsData = [];\r\n        this.detailPagination.totalItems = 0;\r\n        this.detailPagination.totalPages = 0;\r\n        this.detailPagination.currentPage = 1;\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n\r\n  // 搜尋模板詳情 (明細專用搜尋)\r\n  searchTemplateDetails(keyword: string) {\r\n    this.detailSearchKeyword = keyword;\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\r\n    }\r\n  }\r\n\r\n  // 清除明細搜尋\r\n  clearDetailSearch() {\r\n    this.detailSearchKeyword = '';\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1);\r\n    }\r\n  }\r\n\r\n  // 明細搜尋輸入事件處理\r\n  onDetailSearchInput() {\r\n    // 可以在這裡添加即時搜尋邏輯，目前保持原有的 Enter 鍵搜尋方式\r\n  }\r\n\r\n  // 明細搜尋事件處理（即時搜尋）\r\n  onDetailSearch() {\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, this.detailSearchKeyword);\r\n    }\r\n  }\r\n\r\n  // 更新詳情分頁 (保留向後相容)\r\n  updateDetailPagination() {\r\n    // 如果使用新的詳情資料，直接返回\r\n    if (this.currentTemplateDetailsData.length > 0) {\r\n      return;\r\n    }\r\n\r\n    // 向後相容：使用舊的詳情資料\r\n    const details = this.currentTemplateDetails;\r\n    this.detailPagination.totalItems = details.length;\r\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\r\n    this.detailPagination.currentPage = 1; // 重置到第一頁\r\n    this.updatePaginatedDetails();\r\n  }\r\n\r\n  // 更新分頁後的詳情列表\r\n  updatePaginatedDetails() {\r\n    const details = this.currentTemplateDetails;\r\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\r\n    const endIndex = startIndex + this.detailPagination.pageSize;\r\n    this.paginatedDetails = details.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 詳情分頁導航\r\n  goToDetailPage(page: number) {\r\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\r\n      // 如果使用新的詳情資料，重新載入\r\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\r\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\r\n      } else {\r\n        // 向後相容：使用舊的分頁邏輯\r\n        this.detailPagination.currentPage = page;\r\n        this.updatePaginatedDetails();\r\n      }\r\n    }\r\n  }\r\n\r\n  // 獲取詳情分頁頁碼數組\r\n  getDetailPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.detailPagination.totalPages;\r\n    const currentPage = this.detailPagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // 刪除模板 - 使用真實的 API 調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 準備 API 請求參數\r\n    const deleteTemplateArgs: GetTemplateByIdArgs = {\r\n      CTemplateId: templateID\r\n    };\r\n\r\n    // 調用 DeleteTemplate API\r\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\r\n      body: deleteTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          // 重新載入模板列表\r\n          this.loadTemplates();\r\n\r\n          // 如果當前查看的模板被刪除，關閉詳情\r\n          if (this.selectedTemplate?.TemplateID === templateID) {\r\n            this.selectedTemplate = null;\r\n          }\r\n        } else {\r\n          // API 返回錯誤\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n      }\r\n    });\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/Template/GetTemplateList\r\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/Template/SaveTemplate\r\n   *    請求體: {\r\n   *      CTemplateName: string,\r\n   *      CTemplateType: number,  // 1=客變需求\r\n   *      CStatus: number,\r\n   *      Details: [{\r\n   *        CReleateId: number,        // 關聯主檔ID\r\n   *        CReleateName: string       // 關聯名稱\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    POST /api/Template/DeleteTemplate\r\n   *    請求體: { CTemplateId: number }\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\r\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\r\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\r\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 欄位顯示相關輔助方法\r\n  hasVisibleFields(detail: ApiTemplateDetailItem): boolean {\r\n    return this.fieldDisplayConfig.some(config =>\r\n      this.getFieldValue(detail, config.fieldName)\r\n    );\r\n  }\r\n\r\n  getFieldValue(detail: ApiTemplateDetailItem, fieldName: string): any {\r\n    // 根據templateType動態取得資料\r\n    if (this.templateType === 1) {\r\n      // 當type=1時，從tblRequirement物件中取得資料\r\n      return detail.tblRequirement?.[fieldName as keyof typeof detail.tblRequirement];\r\n    } else {\r\n      // 其他type可能從tblMaterials或其他物件中取得資料\r\n      // 目前先支援直接從detail物件取得\r\n      return (detail as any)[fieldName];\r\n    }\r\n  }\r\n\r\n  getFieldClass(fieldName: string): string {\r\n    const classMap: { [key: string]: string } = {\r\n      'CUnitPrice': 'price-group',\r\n      'CQuantity': 'quantity-group',\r\n      'CUnit': 'unit-group',\r\n      'CRemark': 'remark-group',\r\n      'CGroupName': 'group-group',\r\n      'CCategory': 'category-group'\r\n    };\r\n    return classMap[fieldName] || 'default-group';\r\n  }\r\n\r\n  getValueClass(fieldName: string): string {\r\n    const classMap: { [key: string]: string } = {\r\n      'CUnitPrice': 'price-value',\r\n      'CQuantity': 'quantity-value',\r\n      'CUnit': 'unit-value',\r\n      'CRemark': 'remark-value'\r\n    };\r\n    return classMap[fieldName] || 'default-value';\r\n  }\r\n\r\n  formatFieldValue(detail: ApiTemplateDetailItem, fieldName: string): string {\r\n    const value = this.getFieldValue(detail, fieldName);\r\n    if (!value && value !== 0) return '';\r\n\r\n    switch (fieldName) {\r\n      case 'CUnitPrice':\r\n        return `NT$ ${Number(value).toLocaleString()}`;\r\n      case 'CQuantity':\r\n        const unit = this.getFieldValue(detail, 'CUnit') || '';\r\n        return unit ? `${value} ${unit}` : value.toString();\r\n      default:\r\n        return value.toString();\r\n    }\r\n  }\r\n\r\n  // 取得主要欄位值（用於標題顯示）\r\n  getPrimaryFieldValue(detail: ApiTemplateDetailItem): string {\r\n    // 使用第一個fieldDisplayConfig作為主要欄位\r\n    if (this.fieldDisplayConfig.length > 0) {\r\n      const primaryField = this.fieldDisplayConfig[0];\r\n      return this.getFieldValue(detail, primaryField.fieldName) || '';\r\n    }\r\n    // 如果沒有配置，回退到預設欄位\r\n    return this.getFieldValue(detail, 'CRequirement') || '';\r\n  }\r\n\r\n  // 取得用於meta顯示的欄位配置（排除第一個主要欄位）\r\n  getMetaFieldConfigs(): FieldDisplayConfig[] {\r\n    // 返回除了第一個欄位以外的所有欄位，用於meta資訊顯示\r\n    return this.fieldDisplayConfig.slice(1);\r\n  }\r\n\r\n  // 取得meta欄位的CSS類別\r\n  getMetaFieldClass(fieldName: string): string {\r\n    const classMap: { [key: string]: string } = {\r\n      'CGroupName': 'group-meta',\r\n      'CRemark': 'category-meta',\r\n      'CCategory': 'category-meta',\r\n      'CUnit': 'unit-meta',\r\n      'CUnitPrice': 'price-meta',\r\n      'CQuantity': 'quantity-meta'\r\n    };\r\n    return classMap[fieldName] || 'default-meta';\r\n  }\r\n\r\n  // 取得meta欄位的圖示\r\n  getMetaFieldIcon(fieldName: string): string {\r\n    const iconMap: { [key: string]: string } = {\r\n      'CGroupName': 'fas fa-layer-group mr-1',\r\n      'CRemark': 'fas fa-tag mr-1',\r\n      'CCategory': 'fas fa-tag mr-1',\r\n      'CUnit': 'fas fa-ruler mr-1',\r\n      'CUnitPrice': 'fas fa-dollar-sign mr-1',\r\n      'CQuantity': 'fas fa-calculator mr-1'\r\n    };\r\n    return iconMap[fieldName] || 'fas fa-info-circle mr-1';\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n  CreateTime?: string;\r\n  UpdateTime?: string;\r\n  Creator?: string;\r\n  Updator?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  CTemplateType: number; // 模板類型，1=客變需求\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n\r\n// 擴展的詳情項目結構 (基於 API 的 TemplateDetailItem 擴展)\r\nexport interface TemplateDetailItem {\r\n  CTemplateDetailId: number;\r\n  CTemplateId: number;\r\n  CReleateId: number;            // 關聯主檔ID\r\n  CReleateName: string;          // 關聯名稱\r\n  CGroupName?: string;           // 群組名稱 (API 新增欄位)\r\n  CSort?: number;                // 排序順序\r\n  CRemark?: string;              // 備註\r\n  CCreateDt: string;             // 建立時間\r\n  CCreator: string;              // 建立者\r\n  CUpdateDt?: string;            // 更新時間\r\n  CUpdator?: string;             // 更新者\r\n\r\n  // 業務相關欄位 (依需求添加，部分由 API 提供)\r\n  CUnitPrice?: number;           // 單價\r\n  CQuantity?: number;            // 數量\r\n  CUnit?: string;                // 單位\r\n  CCategory?: string;            // 分類\r\n}\r\n\r\n// 注意：GetTemplateDetailById API 使用的是 GetTemplateDetailByIdArgs 和 TemplateDetailItemListResponseBase\r\n// 這些 interface 已經在 API models 中定義，不需要重複定義\r\n\r\n// 欄位顯示設定介面\r\nexport interface FieldDisplayConfig {\r\n  displayName: string;  // 中文名稱\r\n  fieldName: string;    // API欄位名稱\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAA2B,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;AAWtD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EA0ClCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAzC1B,KAAAC,YAAY,GAAW,CAAC,CAAC,CAAC;IAC1B,KAAAC,kBAAkB,GAAyB,EAAE,CAAC,CAAC;IAC9C,KAAAC,cAAc,GAAG,IAAIZ,YAAY,EAAY;IAC7C,KAAAa,KAAK,GAAG,IAAIb,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAAc,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE,CAAC,CAAC;IACxC,KAAAC,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAC,0BAA0B,GAA4B,EAAE;IACxD,KAAAC,mBAAmB,GAAG,EAAE,CAAC,CAAC;IAE1B;IACA,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAC,kBAAkB,GAAG;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE;KACb;IACD,KAAAC,kBAAkB,GAAe,EAAE;IAEnC;IACA,KAAAC,gBAAgB,GAAG;MACjBL,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE;KACb;IACD,KAAAG,gBAAgB,GAAqB,EAAE;EAIiB;EAExDC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACA,MAAMG,mBAAmB,GAAwB;MAC/CC,aAAa,EAAE,IAAI,CAACzB,YAAY;MAAE;MAClC0B,SAAS,EAAE,CAAC;MAAE;MACdC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAAC7B,eAAe,CAAC8B,mCAAmC,CAAC;MACvDC,IAAI,EAAEN;KACP,CAAC,CAACO,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UAEA;UACA,IAAI,CAAC9B,SAAS,GAAG4B,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7CC,UAAU,EAAED,IAAI,CAACE,WAAW;YAC5BC,YAAY,EAAEH,IAAI,CAACT,aAAa,IAAI,EAAE;YACtCa,WAAW,EAAEJ,IAAI,CAACT,aAAa,IAAI,EAAE;YACrCc,UAAU,EAAEL,IAAI,CAACM,SAAS;YAC1BC,UAAU,EAAEP,IAAI,CAACQ,SAAS;YAC1BC,OAAO,EAAET,IAAI,CAACU,QAAQ,IAAIC,SAAS;YACnCC,OAAO,EAAEZ,IAAI,CAACa,QAAQ,IAAIF;WAC3B,CAAC,CAAC;UAEH;UACA,IAAI,CAAC1B,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAAChB,eAAe,GAAG,EAAE;UACzB,IAAI,CAACE,0BAA0B,GAAG,EAAE;QACtC,CAAC,MAAM;UACL;UACA,IAAI,CAACH,SAAS,GAAG,EAAE;UACnB,IAAI,CAACC,eAAe,GAAG,EAAE;UACzB,IAAI,CAACgB,uBAAuB,EAAE;QAChC;MACF,CAAC;MACD6B,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAAC9C,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACgB,uBAAuB,EAAE;MAChC;KACD,CAAC;EACJ;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACZ,aAAa,CAAC0C,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACzC,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMgD,OAAO,GAAG,IAAI,CAAC3C,aAAa,CAAC4C,WAAW,EAAE;MAChD,IAAI,CAAC3C,iBAAiB,GAAG,IAAI,CAACN,SAAS,CAACkD,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAAChB,YAAY,CAACc,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAACf,WAAW,IAAIe,QAAQ,CAACf,WAAW,CAACa,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;IACA,IAAI,CAACK,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,IAAI,CAAC9C,kBAAkB,CAACG,UAAU,GAAG,IAAI,CAACJ,iBAAiB,CAACgD,MAAM;IAClE,IAAI,CAAC/C,kBAAkB,CAACI,UAAU,GAAGZ,IAAI,CAACwD,IAAI,CAAC,IAAI,CAAChD,kBAAkB,CAACG,UAAU,GAAG,IAAI,CAACH,kBAAkB,CAACE,QAAQ,CAAC;IAErH;IACA,IAAI,IAAI,CAACF,kBAAkB,CAACC,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACI,UAAU,EAAE;MAC5E,IAAI,CAACJ,kBAAkB,CAACC,WAAW,GAAGT,IAAI,CAACyD,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjD,kBAAkB,CAACI,UAAU,CAAC;IACvF;IAEA,IAAI,CAAC8C,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAACnD,kBAAkB,CAACC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,kBAAkB,CAACE,QAAQ;IAC/F,MAAMkD,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACnD,kBAAkB,CAACE,QAAQ;IAC9D,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAACN,iBAAiB,CAACsD,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACAE,gBAAgBA,CAACC,IAAY;IAC3B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACvD,kBAAkB,CAACI,UAAU,EAAE;MAC3D,IAAI,CAACJ,kBAAkB,CAACC,WAAW,GAAGsD,IAAI;MAC1C,IAAI,CAACL,wBAAwB,EAAE;IACjC;EACF;EAEA;EACAM,sBAAsBA,CAAA;IACpB,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMrD,UAAU,GAAG,IAAI,CAACJ,kBAAkB,CAACI,UAAU;IACrD,MAAMH,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACC,WAAW;IAEvD;IACA,MAAMyD,SAAS,GAAGlE,IAAI,CAACyD,GAAG,CAAC,CAAC,EAAEhD,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAM0D,OAAO,GAAGnE,IAAI,CAACoE,GAAG,CAACxD,UAAU,EAAEH,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAI4D,CAAC,GAAGH,SAAS,EAAEG,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCJ,KAAK,CAACK,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOJ,KAAK;EACd;EAEA;EACAM,QAAQA,CAAA;IACN,IAAI,CAACrD,uBAAuB,EAAE;EAChC;EAEA;EACAsD,WAAWA,CAAA;IACT,IAAI,CAAClE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACY,uBAAuB,EAAE;EAChC;EAQA;EACAuD,gBAAgBA,CAACrB,QAAkB;IACjC,IAAI,CAACjD,gBAAgB,GAAGiD,QAAQ;IAChC,IAAI,CAACtD,cAAc,CAAC4E,IAAI,CAACtB,QAAQ,CAAC;IAElC;IACA,IAAIA,QAAQ,CAAClB,UAAU,EAAE;MACvB,IAAI,CAACyC,mBAAmB,CAACvB,QAAQ,CAAClB,UAAU,CAAC;IAC/C;IAEA,IAAI,CAAC0C,sBAAsB,EAAE;EAC/B;EAEA;EACAD,mBAAmBA,CAACE,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAExE,aAAsB;IACnF,MAAMyE,IAAI,GAA8B;MACtCF,UAAU,EAAEA;KACb;IAED;IACA,IAAI,CAAClF,eAAe,CAACqF,yCAAyC,CAAC;MAC7DtD,IAAI,EAAEqD;KACP,CAAC,CAACpD,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UAEjD;UACA,IAAIkD,UAAU,GAAGpD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7CiD,iBAAiB,EAAEjD,IAAI,CAACiD,iBAAiB,IAAI,CAAC;YAC9C/C,WAAW,EAAEF,IAAI,CAACE,WAAW,IAAI0C,UAAU;YAC3CM,UAAU,EAAElD,IAAI,CAACkD,UAAU,IAAI,CAAC;YAChCC,KAAK,EAAExC,SAAS;YAChByC,OAAO,EAAEzC,SAAS;YAClBL,SAAS,EAAE,IAAI+C,IAAI,EAAE,CAACC,WAAW,EAAE;YACnC5C,QAAQ,EAAE,IAAI;YACd6C,SAAS,EAAE5C,SAAS;YACpB6C,UAAU,EAAE7C,SAAS;YACrB8C,SAAS,EAAE9C,SAAS;YACpB+C,KAAK,EAAE/C;WACe,EAAC;UAEzB;UACA,IAAItC,aAAa,IAAIA,aAAa,CAAC0C,IAAI,EAAE,EAAE;YACzCiC,UAAU,GAAGA,UAAU,CAAC9B,MAAM,CAACyC,MAAM,IACnCA,MAAM,CAACC,YAAY,CAAC3C,WAAW,EAAE,CAACG,QAAQ,CAAC/C,aAAa,CAAC4C,WAAW,EAAE,CAAC,IACtE0C,MAAM,CAACE,UAAU,IAAIF,MAAM,CAACE,UAAU,CAAC5C,WAAW,EAAE,CAACG,QAAQ,CAAC/C,aAAa,CAAC4C,WAAW,EAAE,CAAE,CAC7F;UACH;UAEA;UACA,MAAMS,UAAU,GAAG,CAACmB,SAAS,GAAG,CAAC,IAAI,IAAI,CAAChE,gBAAgB,CAACJ,QAAQ;UACnE,MAAMkD,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC7C,gBAAgB,CAACJ,QAAQ;UAC5D,MAAMqF,YAAY,GAAGd,UAAU,CAACpB,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;UAE3D;UACA,IAAI,CAACxD,0BAA0B,GAAG2F,YAAY;UAC9C,IAAI,CAACjF,gBAAgB,CAACH,UAAU,GAAGsE,UAAU,CAAC1B,MAAM;UACpD,IAAI,CAACzC,gBAAgB,CAACF,UAAU,GAAGZ,IAAI,CAACwD,IAAI,CAACyB,UAAU,CAAC1B,MAAM,GAAG,IAAI,CAACzC,gBAAgB,CAACJ,QAAQ,CAAC;UAChG,IAAI,CAACI,gBAAgB,CAACL,WAAW,GAAGqE,SAAS;QAC/C,CAAC,MAAM;UACL,IAAI,CAAC1E,0BAA0B,GAAG,EAAE;UACpC,IAAI,CAACU,gBAAgB,CAACH,UAAU,GAAG,CAAC;UACpC,IAAI,CAACG,gBAAgB,CAACF,UAAU,GAAG,CAAC;UACpC,IAAI,CAACE,gBAAgB,CAACL,WAAW,GAAG,CAAC;QACvC;MACF,CAAC;MACDsC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC3C,0BAA0B,GAAG,EAAE;QACpC,IAAI,CAACU,gBAAgB,CAACH,UAAU,GAAG,CAAC;QACpC,IAAI,CAACG,gBAAgB,CAACF,UAAU,GAAG,CAAC;QACpC,IAAI,CAACE,gBAAgB,CAACL,WAAW,GAAG,CAAC;MACvC;KACD,CAAC;EACJ;EAIA;EACAuF,qBAAqBA,CAAC/C,OAAe;IACnC,IAAI,CAAC5C,mBAAmB,GAAG4C,OAAO;IAClC,IAAI,IAAI,CAAC9C,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC+B,UAAU,EAAE;MAC7D,IAAI,CAACyC,mBAAmB,CAAC,IAAI,CAACxE,gBAAgB,CAAC+B,UAAU,EAAE,CAAC,EAAEe,OAAO,CAAC;IACxE;EACF;EAEA;EACAgD,iBAAiBA,CAAA;IACf,IAAI,CAAC5F,mBAAmB,GAAG,EAAE;IAC7B,IAAI,IAAI,CAACF,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC+B,UAAU,EAAE;MAC7D,IAAI,CAACyC,mBAAmB,CAAC,IAAI,CAACxE,gBAAgB,CAAC+B,UAAU,EAAE,CAAC,CAAC;IAC/D;EACF;EAEA;EACAgE,mBAAmBA,CAAA;IACjB;EAAA;EAGF;EACAC,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAChG,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC+B,UAAU,EAAE;MAC7D,IAAI,CAACyC,mBAAmB,CAAC,IAAI,CAACxE,gBAAgB,CAAC+B,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC7B,mBAAmB,CAAC;IACzF;EACF;EAEA;EACAuE,sBAAsBA,CAAA;IACpB;IACA,IAAI,IAAI,CAACxE,0BAA0B,CAACmD,MAAM,GAAG,CAAC,EAAE;MAC9C;IACF;IAEA;IACA,MAAM6C,OAAO,GAAG,IAAI,CAACC,sBAAsB;IAC3C,IAAI,CAACvF,gBAAgB,CAACH,UAAU,GAAGyF,OAAO,CAAC7C,MAAM;IACjD,IAAI,CAACzC,gBAAgB,CAACF,UAAU,GAAGZ,IAAI,CAACwD,IAAI,CAAC,IAAI,CAAC1C,gBAAgB,CAACH,UAAU,GAAG,IAAI,CAACG,gBAAgB,CAACJ,QAAQ,CAAC;IAC/G,IAAI,CAACI,gBAAgB,CAACL,WAAW,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC6F,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMF,OAAO,GAAG,IAAI,CAACC,sBAAsB;IAC3C,MAAM1C,UAAU,GAAG,CAAC,IAAI,CAAC7C,gBAAgB,CAACL,WAAW,GAAG,CAAC,IAAI,IAAI,CAACK,gBAAgB,CAACJ,QAAQ;IAC3F,MAAMkD,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC7C,gBAAgB,CAACJ,QAAQ;IAC5D,IAAI,CAACK,gBAAgB,GAAGqF,OAAO,CAACvC,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC7D;EAEA;EACA2C,cAAcA,CAACxC,IAAY;IACzB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACjD,gBAAgB,CAACF,UAAU,EAAE;MACzD;MACA,IAAI,IAAI,CAACT,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC+B,UAAU,IAAI,IAAI,CAAC9B,0BAA0B,CAACmD,MAAM,GAAG,CAAC,EAAE;QAC3G,IAAI,CAACoB,mBAAmB,CAAC,IAAI,CAACxE,gBAAgB,CAAC+B,UAAU,EAAE6B,IAAI,EAAE,IAAI,CAAC1D,mBAAmB,CAAC;MAC5F,CAAC,MAAM;QACL;QACA,IAAI,CAACS,gBAAgB,CAACL,WAAW,GAAGsD,IAAI;QACxC,IAAI,CAACuC,sBAAsB,EAAE;MAC/B;IACF;EACF;EAEA;EACAE,oBAAoBA,CAAA;IAClB,MAAMvC,KAAK,GAAa,EAAE;IAC1B,MAAMrD,UAAU,GAAG,IAAI,CAACE,gBAAgB,CAACF,UAAU;IACnD,MAAMH,WAAW,GAAG,IAAI,CAACK,gBAAgB,CAACL,WAAW;IAErD;IACA,MAAMyD,SAAS,GAAGlE,IAAI,CAACyD,GAAG,CAAC,CAAC,EAAEhD,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAM0D,OAAO,GAAGnE,IAAI,CAACoE,GAAG,CAACxD,UAAU,EAAEH,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAI4D,CAAC,GAAGH,SAAS,EAAEG,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCJ,KAAK,CAACK,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOJ,KAAK;EACd;EAEA;EACAwC,OAAOA,CAAA;IACL,IAAI,CAAC1G,KAAK,CAAC2E,IAAI,EAAE;EACnB;EAEA;EACAgC,gBAAgBA,CAACC,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACA,MAAMG,kBAAkB,GAAwB;MAC9C3E,WAAW,EAAEwE;KACd;IAED;IACA,IAAI,CAAChH,eAAe,CAACoH,kCAAkC,CAAC;MACtDrF,IAAI,EAAEoF;KACP,CAAC,CAACnF,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACA;UACA,IAAI,CAACb,aAAa,EAAE;UAEpB;UACA,IAAI,IAAI,CAACd,gBAAgB,EAAE+B,UAAU,KAAKyE,UAAU,EAAE;YACpD,IAAI,CAACxG,gBAAgB,GAAG,IAAI;UAC9B;QACF,CAAC,MAAM;UACL;QAAA;MAEJ,CAAC;MACD4C,KAAK,EAAEA,CAAA,KAAK;QACV;MAAA;KAEH,CAAC;EACJ;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA;EACAiE,mBAAmBA,CAAA;IACjB,IAAI,CAAC7G,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA8G,gBAAgBA,CAACrB,MAA6B;IAC5C,OAAO,IAAI,CAAC/F,kBAAkB,CAACqH,IAAI,CAACC,MAAM,IACxC,IAAI,CAACC,aAAa,CAACxB,MAAM,EAAEuB,MAAM,CAACE,SAAS,CAAC,CAC7C;EACH;EAEAD,aAAaA,CAACxB,MAA6B,EAAEyB,SAAiB;IAC5D;IACA,IAAI,IAAI,CAACzH,YAAY,KAAK,CAAC,EAAE;MAC3B;MACA,OAAOgG,MAAM,CAAC0B,cAAc,GAAGD,SAA+C,CAAC;IACjF,CAAC,MAAM;MACL;MACA;MACA,OAAQzB,MAAc,CAACyB,SAAS,CAAC;IACnC;EACF;EAEAE,aAAaA,CAACF,SAAiB;IAC7B,MAAMG,QAAQ,GAA8B;MAC1C,YAAY,EAAE,aAAa;MAC3B,WAAW,EAAE,gBAAgB;MAC7B,OAAO,EAAE,YAAY;MACrB,SAAS,EAAE,cAAc;MACzB,YAAY,EAAE,aAAa;MAC3B,WAAW,EAAE;KACd;IACD,OAAOA,QAAQ,CAACH,SAAS,CAAC,IAAI,eAAe;EAC/C;EAEAI,aAAaA,CAACJ,SAAiB;IAC7B,MAAMG,QAAQ,GAA8B;MAC1C,YAAY,EAAE,aAAa;MAC3B,WAAW,EAAE,gBAAgB;MAC7B,OAAO,EAAE,YAAY;MACrB,SAAS,EAAE;KACZ;IACD,OAAOA,QAAQ,CAACH,SAAS,CAAC,IAAI,eAAe;EAC/C;EAEAK,gBAAgBA,CAAC9B,MAA6B,EAAEyB,SAAiB;IAC/D,MAAMM,KAAK,GAAG,IAAI,CAACP,aAAa,CAACxB,MAAM,EAAEyB,SAAS,CAAC;IACnD,IAAI,CAACM,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,EAAE;IAEpC,QAAQN,SAAS;MACf,KAAK,YAAY;QACf,OAAO,OAAOO,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,EAAE,EAAE;MAChD,KAAK,WAAW;QACd,MAAMC,IAAI,GAAG,IAAI,CAACV,aAAa,CAACxB,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE;QACtD,OAAOkC,IAAI,GAAG,GAAGH,KAAK,IAAIG,IAAI,EAAE,GAAGH,KAAK,CAACI,QAAQ,EAAE;MACrD;QACE,OAAOJ,KAAK,CAACI,QAAQ,EAAE;IAC3B;EACF;EAEA;EACAC,oBAAoBA,CAACpC,MAA6B;IAChD;IACA,IAAI,IAAI,CAAC/F,kBAAkB,CAAC0D,MAAM,GAAG,CAAC,EAAE;MACtC,MAAM0E,YAAY,GAAG,IAAI,CAACpI,kBAAkB,CAAC,CAAC,CAAC;MAC/C,OAAO,IAAI,CAACuH,aAAa,CAACxB,MAAM,EAAEqC,YAAY,CAACZ,SAAS,CAAC,IAAI,EAAE;IACjE;IACA;IACA,OAAO,IAAI,CAACD,aAAa,CAACxB,MAAM,EAAE,cAAc,CAAC,IAAI,EAAE;EACzD;EAEA;EACAsC,mBAAmBA,CAAA;IACjB;IACA,OAAO,IAAI,CAACrI,kBAAkB,CAACgE,KAAK,CAAC,CAAC,CAAC;EACzC;EAEA;EACAsE,iBAAiBA,CAACd,SAAiB;IACjC,MAAMG,QAAQ,GAA8B;MAC1C,YAAY,EAAE,YAAY;MAC1B,SAAS,EAAE,eAAe;MAC1B,WAAW,EAAE,eAAe;MAC5B,OAAO,EAAE,WAAW;MACpB,YAAY,EAAE,YAAY;MAC1B,WAAW,EAAE;KACd;IACD,OAAOA,QAAQ,CAACH,SAAS,CAAC,IAAI,cAAc;EAC9C;EAEA;EACAe,gBAAgBA,CAACf,SAAiB;IAChC,MAAMgB,OAAO,GAA8B;MACzC,YAAY,EAAE,yBAAyB;MACvC,SAAS,EAAE,iBAAiB;MAC5B,WAAW,EAAE,iBAAiB;MAC9B,OAAO,EAAE,mBAAmB;MAC5B,YAAY,EAAE,yBAAyB;MACvC,WAAW,EAAE;KACd;IACD,OAAOA,OAAO,CAAChB,SAAS,CAAC,IAAI,yBAAyB;EACxD;EAEA;EACA,IAAIhB,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAClG,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACD,eAAe,CAACiD,MAAM,CAACmF,CAAC,IAAIA,CAAC,CAACpG,UAAU,KAAK,IAAI,CAAC/B,gBAAiB,CAAC+B,UAAU,CAAC;EAC7F;EAEA;EACAqG,iBAAiBA,CAACC,KAAa,EAAEpF,QAAkB;IACjD,OAAOA,QAAQ,CAAClB,UAAU,IAAIsG,KAAK;EACrC;CACD;AAjhBUC,UAAA,EAARtJ,KAAK,EAAE,C,4DAA0B;AACzBsJ,UAAA,EAARtJ,KAAK,EAAE,C,kEAA+C;AAC7CsJ,UAAA,EAATrJ,MAAM,EAAE,C,8DAA+C;AAC9CqJ,UAAA,EAATrJ,MAAM,EAAE,C,qDAAkC;AAJhCK,uBAAuB,GAAAgJ,UAAA,EAPnCxJ,SAAS,CAAC;EACTyJ,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC,CAAC;EAC/CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACzJ,YAAY,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc;CAClE,CAAC,C,EACWC,uBAAuB,CAkhBnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}