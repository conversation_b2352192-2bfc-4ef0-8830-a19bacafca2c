{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let SharedObservable = /*#__PURE__*/(() => {\n  class SharedObservable {\n    constructor() {\n      this.Menu = new BehaviorSubject({});\n      this.User = new BehaviorSubject([]);\n      this.UserGroup = new BehaviorSubject([]);\n      this.UserGroupFunction = new BehaviorSubject({});\n      this.UserLog = new BehaviorSubject([]);\n      this.FunctionModel = new BehaviorSubject([]);\n      this.RefBase = new BehaviorSubject([]);\n      this.Tag = new BehaviorSubject([]);\n      this.Member = new BehaviorSubject([]);\n      this.MemberTag = new BehaviorSubject([]);\n      this.Task = new BehaviorSubject({});\n      this.MenuTab = new BehaviorSubject({});\n      this.SharedMenu = this.Menu.asObservable();\n      this.SharedUser = this.User.asObservable();\n      this.SharedUserGroup = this.UserGroup.asObservable();\n      this.SharedUserGroupFunction = this.UserGroupFunction.asObservable();\n      this.SharedUserLog = this.UserLog.asObservable();\n      this.SharedFunctionModel = this.FunctionModel.asObservable();\n      this.SharedRefBase = this.RefBase.asObservable();\n      this.SharedTag = this.Tag.asObservable();\n      this.SharedMember = this.Member.asObservable();\n      this.SharedMemberTag = this.MemberTag.asObservable();\n      this.SharedTask = this.Task.asObservable();\n      this.SharedMenuTab = this.MenuTab.asObservable();\n    }\n    SetMenu(Data) {\n      this.Menu.next(Data);\n    }\n    SetUser(Data) {\n      this.User.next(Data);\n    }\n    SetUserGroup(Data) {\n      this.UserGroup.next(Data);\n    }\n    SetUserGroupFunction(Data) {\n      this.UserGroupFunction.next(Data);\n    }\n    SetUserLog(Data) {\n      this.UserLog.next(Data);\n    }\n    SetFunctionModel(Data) {\n      this.FunctionModel.next(Data);\n    }\n    SetRefBase(Data) {\n      this.RefBase.next(Data);\n    }\n    SetTag(Data) {\n      this.Tag.next(Data);\n    }\n    SetMember(Data) {\n      this.Member.next(Data);\n    }\n    SetMemberTag(Data) {\n      this.MemberTag.next(Data);\n    }\n    SetTask(Data) {\n      this.Task.next(Data);\n    }\n    SetMenuTab(Data) {\n      this.MenuTab.next(Data);\n    }\n    static {\n      this.ɵfac = function SharedObservable_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SharedObservable)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SharedObservable,\n        factory: SharedObservable.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SharedObservable;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}