{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport * as moment from 'moment';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ServiceBase = /*#__PURE__*/(() => {\n  class ServiceBase {\n    constructor(http) {\n      this.http = http;\n      this.httpOptions = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      this.apiBaseUrl = `${environment.BASE_URL_API}/api`;\n    }\n    convertUtcDate(data) {\n      return data;\n    }\n    getUtcDate() {\n      return moment().utc;\n    }\n    // 實作moment\n    convertLocalDate(data) {\n      return data;\n    }\n    formatDate(data, format) {}\n    static {\n      this.ɵfac = function ServiceBase_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ServiceBase)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ServiceBase,\n        factory: ServiceBase.ɵfac\n      });\n    }\n  }\n  return ServiceBase;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}