{"ast": null, "code": "import isSameQuarter from \"../isSameQuarter/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisQuarter\n * @category Quarter Helpers\n * @summary Is the given date in the same quarter as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same quarter as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this quarter\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this quarter?\n * const result = isThisQuarter(new Date(2014, 6, 2))\n * //=> true\n */\nexport default function isThisQuarter(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameQuarter(Date.now(), dirtyDate);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}