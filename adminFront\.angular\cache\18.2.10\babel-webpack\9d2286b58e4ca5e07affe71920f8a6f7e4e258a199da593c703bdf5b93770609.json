{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { decodeJwtPayload } from '@nebular/auth';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/utility.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"src/services/File.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../@theme/pipes/mapping.pipe\";\nfunction DetailApprovalWaitingComponent_div_2_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u7121\\u9644\\u4EF6\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_div_30_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const item_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.handleFileClick(item_r3));\n    });\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"div\", 27);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 29)(5, \"div\", 5)(6, \"span\", 30);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 31);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 32);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 33);\n    i0.ɵɵelement(13, \"i\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r3.getFileIcon(item_r3.CFileName || \"\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getDisplayFileName(item_r3), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getFileTypeText(item_r3.CFileName || \"\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u9EDE\\u64CA\\u4EE5\", ctx_r3.isImageFile(item_r3.CFileName || \"\") ? \"\\u9810\\u89BD\" : ctx_r3.isPDFString(item_r3.CFileName || \"\") ? \"\\u6AA2\\u8996\" : \"\\u4E0B\\u8F09\", \"\\u6A94\\u6848 \");\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, DetailApprovalWaitingComponent_div_2_div_30_div_1_Template, 14, 5, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.CFile && (item_r3.CFileName || item_r3.CFile));\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_51_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 41)(1, \"th\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 43);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 43);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 6, record_r5.CRecordDate ? record_r5.CRecordDate : ctx_r3.approvalWaiting.CCreateDT, \"yyyy/MM/dd HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \", record_r5.CAction === 1 ? \"\\u9001\\u51FA\\u5BE9\\u6838\" : \"\", \" \", record_r5.CAction === 2 ? \"\\u5BE9\\u6838\\u901A\\u904E\" : \"\", \" \", record_r5.CAction === 3 ? \"\\u5BE9\\u6838\\u99C1\\u56DE\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", record_r5.CCreator, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", record_r5.CRemark, \" \");\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"table\", 36)(2, \"thead\", 37)(3, \"tr\", 38)(4, \"th\", 39);\n    i0.ɵɵtext(5, \" \\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 39);\n    i0.ɵɵtext(7, \" \\u52D5\\u4F5C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 39);\n    i0.ɵɵtext(9, \" \\u4F7F\\u7528\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 39);\n    i0.ɵɵtext(11, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"tbody\");\n    i0.ɵɵtemplate(13, DetailApprovalWaitingComponent_div_2_div_51_tr_13_Template, 10, 9, \"tr\", 40);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.approvalWaiting.CApproveRecord);\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"span\", 3);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"span\", 7);\n    i0.ɵɵtext(8, \" \\u5EFA\\u6848 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 8);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 6)(13, \"span\", 7);\n    i0.ɵɵtext(14, \" \\u985E\\u5225 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"span\", 8);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"getTypeApprovalWaiting\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 6)(20, \"span\", 7);\n    i0.ɵɵtext(21, \" \\u540D\\u7A31 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"span\", 8);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 10)(25, \"div\", 6)(26, \"span\", 7);\n    i0.ɵɵtext(27, \" \\u6A94\\u6848 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 8);\n    i0.ɵɵtemplate(29, DetailApprovalWaitingComponent_div_2_div_29_Template, 4, 0, \"div\", 11)(30, DetailApprovalWaitingComponent_div_2_div_30_Template, 2, 1, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 9)(32, \"div\", 6)(33, \"span\", 7);\n    i0.ɵɵtext(34, \" \\u5BE9\\u6838\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"span\", 8);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"div\", 13)(38, \"div\", 14)(39, \"div\", 6)(40, \"span\", 7);\n    i0.ɵɵtext(41, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 15)(43, \"textarea\", 16);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailApprovalWaitingComponent_div_2_Template_textarea_ngModelChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.remark, $event) || (ctx_r3.remark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 17)(45, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.goBack());\n    });\n    i0.ɵɵtext(46, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleAction(false));\n    });\n    i0.ɵɵtext(48, \"\\u99C1\\u56DE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleAction(true));\n    });\n    i0.ɵɵtext(50, \"\\u540C\\u610F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(51, DetailApprovalWaitingComponent_div_2_div_51_Template, 14, 1, \"div\", 21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CName, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CBuildcaseName, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 12, ctx_r3.approvalWaiting.CType), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CName, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.approvalWaiting.CFileApproves || ctx_r3.approvalWaiting.CFileApproves.length === 0 || !ctx_r3.hasValidFiles());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.approvalWaiting.CFileApproves);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CApprovalRemark, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.remark);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.approvalWaiting.CIsApprove !== null && ctx_r3.approvalWaiting.CIsApprove);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.approvalWaiting.CIsApprove !== null && ctx_r3.approvalWaiting.CIsApprove);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r3.approvalWaiting);\n  }\n}\nexport let DetailApprovalWaitingComponent = /*#__PURE__*/(() => {\n  class DetailApprovalWaitingComponent {\n    constructor(_specialChangeService, _activatedRoute, _ultilityService, _location, message, _validationHelper, _eventService, fileService) {\n      this._specialChangeService = _specialChangeService;\n      this._activatedRoute = _activatedRoute;\n      this._ultilityService = _ultilityService;\n      this._location = _location;\n      this.message = message;\n      this._validationHelper = _validationHelper;\n      this._eventService = _eventService;\n      this.fileService = fileService;\n      this.CType = 1;\n      this.remark = \"\";\n      this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN));\n      this.CID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"id\"));\n      this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"buildCaseId\"));\n      this._activatedRoute.queryParams.pipe(tap(p => {\n        this.CType = p[\"type\"];\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.getApprovalWaitingById();\n    }\n    getApprovalWaitingById() {\n      this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\n        body: {\n          CID: this.CID,\n          CType: this.CType.toString()\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.approvalWaiting = res.Entries;\n        }\n      })).subscribe();\n    }\n    downloadFile(CFile, CFileName) {\n      // if (CFile && CFileName) {\n      //   this._ultilityService.downloadFileFullUrl(\n      //     CFile, CFileName\n      //   )\n      // }\n      window.open(CFile, \"_blank\");\n    }\n    handleAction(isApprove) {\n      if (!isApprove) {\n        this.validation();\n        if (this._validationHelper.errorMessages.length > 0) {\n          this.message.showErrorMSGs(this._validationHelper.errorMessages);\n          return;\n        }\n      }\n      this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\n        body: {\n          CID: this.CID,\n          CType: this.CType,\n          CIsApprove: isApprove,\n          CRemark: this.remark\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getApprovalWaitingById();\n          if (this.approvalWaiting.CApproveRecord?.length == 0) {\n            this.approvalWaiting.CApproveRecord?.push({\n              CCreator: this.decodeJWT.userName,\n              CRecordDate: new Date().toISOString(),\n              CRemark: this.remark\n            });\n          }\n          this.remark = \"\";\n        }\n        this.goBack();\n      })).subscribe();\n    }\n    goBack() {\n      this._eventService.push({\n        action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n        payload: this.buildCaseID\n      });\n      this._location.back();\n    }\n    validation() {\n      this._validationHelper.clear();\n      this._validationHelper.required(\"[備註]\", this.remark);\n    }\n    // 檢查是否有有效的檔案\n    hasValidFiles() {\n      if (!this.approvalWaiting?.CFileApproves) {\n        return false;\n      }\n      return this.approvalWaiting.CFileApproves.some(file => file.CFile && (file.CFileName || file.CFile.length > 0));\n    }\n    // 處理檔案點擊事件\n    handleFileClick(file) {\n      const fileName = file.CFileName || file.fileName || '';\n      const displayName = file.CFileName || fileName;\n      // 判斷檔案類型\n      const isImageByName = this.isImageFile(displayName);\n      const isPdfByName = this.isPDFString(displayName);\n      const isCadByName = this.isCadString(displayName);\n      // 統一使用 GetFile API 取得檔案\n      const relativePath = file.relativePath || file.CFile;\n      const serverFileName = file.fileName || file.CFileName;\n      if (relativePath && serverFileName) {\n        this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\n      } else {\n        console.error('檔案缺少必要的路徑資訊:', file);\n        this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\n      }\n    }\n    // 從後端取得檔案 blob\n    getFileFromServer(relativePath, fileName, displayName, isImage, isPdf, isCad) {\n      this.fileService.getFile(relativePath, fileName).subscribe({\n        next: blob => {\n          const url = URL.createObjectURL(blob);\n          if (isImage) {\n            this.openImagePreview(url, displayName);\n          } else if (isPdf) {\n            this.openPdfInNewWindow(url, displayName);\n          } else {\n            this.downloadBlobFile(blob, displayName);\n          }\n          // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\n          setTimeout(() => URL.revokeObjectURL(url), 10000);\n        },\n        error: error => {\n          console.error('取得檔案失敗:', error);\n          this.message.showErrorMSG('取得檔案失敗，請稍後再試');\n        }\n      });\n    }\n    // 取得檔案圖標\n    getFileIcon(fileName) {\n      if (!fileName) return 'fas fa-file text-gray-500';\n      const extension = fileName.split('.').pop()?.toLowerCase();\n      switch (extension) {\n        case 'pdf':\n          return 'fas fa-file-pdf text-red-500';\n        case 'jpg':\n        case 'jpeg':\n        case 'png':\n        case 'gif':\n        case 'bmp':\n        case 'webp':\n          return 'fas fa-file-image text-blue-500';\n        case 'dwg':\n        case 'dxf':\n          return 'fas fa-drafting-compass text-green-500';\n        case 'doc':\n        case 'docx':\n          return 'fas fa-file-word text-blue-600';\n        case 'xls':\n        case 'xlsx':\n          return 'fas fa-file-excel text-green-600';\n        default:\n          return 'fas fa-file text-gray-500';\n      }\n    }\n    // 取得顯示檔案名稱\n    getDisplayFileName(file) {\n      return file.CFileName || file.fileName || '未知檔案';\n    }\n    // 取得檔案類型文字\n    getFileTypeText(fileName) {\n      if (!fileName) return '';\n      const extension = fileName.split('.').pop()?.toLowerCase();\n      switch (extension) {\n        case 'pdf':\n          return 'PDF';\n        case 'jpg':\n        case 'jpeg':\n          return 'JPG';\n        case 'png':\n          return 'PNG';\n        case 'gif':\n          return 'GIF';\n        case 'dwg':\n          return 'DWG';\n        case 'dxf':\n          return 'DXF';\n        default:\n          return extension?.toUpperCase() || '';\n      }\n    }\n    // 判斷檔案是否為圖片\n    isImageFile(fileName) {\n      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n      const extension = fileName.split('.').pop()?.toLowerCase();\n      return imageExtensions.includes(extension || '');\n    }\n    // 判斷是否為 PDF\n    isPDFString(str) {\n      if (str) {\n        return str.toLowerCase().endsWith(\".pdf\");\n      }\n      return false;\n    }\n    // 判斷是否為 CAD 檔案\n    isCadString(str) {\n      if (str) {\n        const lowerStr = str.toLowerCase();\n        return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n      }\n      return false;\n    }\n    // 取得正確的圖片 src，統一使用 getFile API\n    getImageSrc(file) {\n      // 不再處理 base64，統一透過後端 API 取得檔案\n      // 這個方法現在主要用於檢查檔案是否可以正常顯示\n      const fileName = file.CFileName || file.fileName || '';\n      if (!fileName) {\n        return '';\n      }\n      // 返回空字串，實際圖片會透過 handleFileClick 中的 getFileFromServer 處理\n      return '';\n    }\n    // 在新視窗中開啟 PDF\n    openPdfInNewWindow(blobUrl, fileName) {\n      try {\n        const newWindow = window.open('', '_blank');\n        if (newWindow) {\n          newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { margin: 0; padding: 0; }\n                iframe { width: 100%; height: 100vh; border: none; }\n              </style>\n            </head>\n            <body>\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\n            </body>\n          </html>\n        `);\n        } else {\n          // 如果無法開啟新視窗，使用直接開啟方式\n          window.open(blobUrl, '_blank');\n        }\n      } catch (error) {\n        console.error('開啟 PDF 視窗失敗:', error);\n        // 後備方案：直接開啟 URL\n        window.open(blobUrl, '_blank');\n      }\n    }\n    // 下載 blob 檔案\n    downloadBlobFile(blob, fileName) {\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      // 清理 URL\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\n    }\n    // 在瀏覽器中打開 PDF（移除，統一使用 getFile API）\n    openPdfInBrowser(fileUrl) {\n      console.warn('openPdfInBrowser 方法已廢棄，請使用 getFileFromServer');\n    }\n    // 直接下載檔案（移除，統一使用 getFile API）\n    downloadFileDirectly(fileUrl, fileName) {\n      console.warn('downloadFileDirectly 方法已廢棄，請使用 getFileFromServer');\n    }\n    // 開啟圖片預覽\n    openImagePreview(imageUrl, fileName) {\n      try {\n        const newWindow = window.open('', '_blank');\n        if (newWindow) {\n          newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body {\n                  margin: 0;\n                  padding: 20px;\n                  background: #f0f0f0;\n                  display: flex;\n                  justify-content: center;\n                  align-items: center;\n                  min-height: 100vh;\n                }\n                img {\n                  max-width: 100%;\n                  max-height: 100%;\n                  box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n                }\n              </style>\n            </head>\n            <body>\n              <img src=\"${imageUrl}\" alt=\"${fileName}\" />\n            </body>\n          </html>\n        `);\n        } else {\n          window.open(imageUrl, '_blank');\n        }\n      } catch (error) {\n        console.error('開啟圖片預覽失敗:', error);\n        this.message.showErrorMSG('無法預覽圖片');\n      }\n    }\n    static {\n      this.ɵfac = function DetailApprovalWaitingComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DetailApprovalWaitingComponent)(i0.ɵɵdirectiveInject(i1.SpecialChangeService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.UtilityService), i0.ɵɵdirectiveInject(i4.Location), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.EventService), i0.ɵɵdirectiveInject(i8.FileService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DetailApprovalWaitingComponent,\n        selectors: [[\"app-detail-approval-waiting\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 1,\n        consts: [[\"class\", \"flex flex-col justify-items-center items-center m-auto w-[50%]\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"justify-items-center\", \"items-center\", \"m-auto\", \"w-[50%]\"], [1, \"border-b-2\", \"border-b-black\", \"w-full\"], [1, \"text-xl\", \"font-bold\"], [1, \"px-3\", \"py-4\"], [1, \"flex\", \"items-center\"], [1, \"w-[100px]\"], [1, \"font-bold\"], [1, \"w-[80%]\", \"break-words\"], [1, \"flex\", \"items-center\", \"mt-3\"], [1, \"flex\", \"items-start\", \"mt-3\"], [\"class\", \"no-files\", 4, \"ngIf\"], [\"class\", \"file-item-wrapper\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-2\", \"w-full\"], [1, \"flex\", \"px-3\", \"py-4\", \"w-full\"], [1, \"w-full\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [1, \"mt-3\", \"flex\", \"items-center\"], [\"nbButton\", \"\", 1, \"btn\", \"border-black\", \"border\", \"mr-2\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"danger\", 1, \"btn\", \"mr-2\", 3, \"click\", \"disabled\"], [\"nbButton\", \"\", \"status\", \"primary\", 1, \"btn\", \"mr-2\", 3, \"click\", \"disabled\"], [\"class\", \"table-responsive relative overflow-x-auto mt-3\", 4, \"ngIf\"], [1, \"no-files\"], [1, \"fas\", \"fa-folder-open\", \"text-gray-400\", \"text-2xl\", \"mb-2\"], [1, \"file-item-wrapper\"], [\"class\", \"file-item cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [1, \"file-item\", \"cursor-pointer\", 3, \"click\"], [1, \"file-icon\", \"mr-3\"], [1, \"text-xl\"], [1, \"file-info\"], [1, \"file-name\"], [1, \"file-type-badge\"], [1, \"file-action-hint\"], [1, \"flex-shrink-0\", \"ml-3\"], [1, \"fas\", \"fa-external-link-alt\", \"file-action-icon\"], [1, \"table-responsive\", \"relative\", \"overflow-x-auto\", \"mt-3\"], [1, \"table\", \"table-bordered\", \"w-full\", \"text-sm\", \"text-left\", \"rtl:text-right\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"text-xs\", \"text-gray-700\", \"uppercase\", \"bg-gray-50\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"px-6\", \"py-3\"], [\"class\", \"bg-white text-black\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"text-black\"], [\"scope\", \"row\", 1, \"px-6\", \"py-4\", \"font-medium\", \"whitespace-nowrap\"], [1, \"px-6\", \"py-4\"]],\n        template: function DetailApprovalWaitingComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-body\");\n            i0.ɵɵtemplate(2, DetailApprovalWaitingComponent_div_2_Template, 52, 14, \"div\", 0);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !!ctx.approvalWaiting);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbInputDirective, i10.NbButtonComponent, i11.ApprovalWaitingPipe],\n        styles: [\"@charset \\\"UTF-8\\\";.file-item[_ngcontent-%COMP%]{transition:all .3s ease;border:1px solid #e5e7eb;border-radius:8px;padding:12px;margin-bottom:12px;background:#fff}.file-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #0000001a;border-color:#3b82f6}.file-icon[_ngcontent-%COMP%]{width:48px;height:48px;display:flex;align-items:center;justify-content:center;background:#f3f4f6;border-radius:8px;transition:background-color .3s ease}.file-icon[_ngcontent-%COMP%]:hover{background:#e5e7eb}.file-info[_ngcontent-%COMP%]{flex-grow:1;min-width:0}.file-name[_ngcontent-%COMP%]{color:#1d4ed8;font-weight:500;transition:color .3s ease;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.file-name[_ngcontent-%COMP%]:hover{color:#1e40af}.file-type-badge[_ngcontent-%COMP%]{padding:4px 8px;font-size:12px;background:#dbeafe;color:#1e40af;border-radius:999px;margin-left:8px}.file-action-hint[_ngcontent-%COMP%]{font-size:14px;color:#6b7280;margin-top:4px}.file-action-icon[_ngcontent-%COMP%]{color:#9ca3af;transition:color .3s ease}.file-action-icon[_ngcontent-%COMP%]:hover{color:#6b7280}@media (max-width: 768px){.file-item[_ngcontent-%COMP%]{padding:8px;margin-bottom:8px}.file-icon[_ngcontent-%COMP%]{width:40px;height:40px}.file-name[_ngcontent-%COMP%]{font-size:14px}.file-action-hint[_ngcontent-%COMP%]{font-size:12px}}.no-files[_ngcontent-%COMP%]{color:#6b7280;font-style:italic;padding:20px;text-align:center;background:#f9fafb;border-radius:8px;border:1px dashed #d1d5db}\"]\n      });\n    }\n  }\n  return DetailApprovalWaitingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}