{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport tinymce from 'tinymce';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nexport let TinyMCEComponent = /*#__PURE__*/(() => {\n  class TinyMCEComponent {\n    constructor(host, locationStrategy) {\n      this.host = host;\n      this.locationStrategy = locationStrategy;\n      this.editorKeyup = new EventEmitter();\n    }\n    ngAfterViewInit() {\n      tinymce.init({\n        target: this.host.nativeElement,\n        plugins: ['link', 'paste', 'table'],\n        skin_url: `${this.locationStrategy.getBaseHref()}assets/skins/lightgray`,\n        setup: editor => {\n          this.editor = editor;\n          editor.on('keyup', () => {\n            this.editorKeyup.emit(editor.getContent());\n          });\n        },\n        height: '320'\n      });\n    }\n    ngOnDestroy() {\n      tinymce.remove(this.editor);\n    }\n    static {\n      this.ɵfac = function TinyMCEComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TinyMCEComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.LocationStrategy));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TinyMCEComponent,\n        selectors: [[\"ngx-tiny-mce\"]],\n        outputs: {\n          editorKeyup: \"editorKeyup\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 0,\n        vars: 0,\n        template: function TinyMCEComponent_Template(rf, ctx) {},\n        encapsulation: 2\n      });\n    }\n  }\n  return TinyMCEComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}