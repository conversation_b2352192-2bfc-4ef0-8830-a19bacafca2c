{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\nimport { TemplateCreatorComponent } from 'src/app/shared/components/template-creator/template-creator.component';\nlet RequirementManagementComponent = class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.currentTab = 0; // 追蹤當前選中的 tab\n    this.isCreatingTemplate = false; // 控制是否正在創建模板\n    this.selectedRequirementsForTemplate = []; // 用於模板創建的選中項目\n    // Tab 切換事件處理\n    this.isFirstTabChange = true;\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CGroupName = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        if (this.currentTab === 0) {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        } else {\n          this.getListRequirementRequest.CBuildCaseID = 0;\n        }\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 根據當前 tab 決定是否需要驗證建案名稱\n    if (this.currentTab === 0) {\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    }\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 根據當前 tab 決定是否需要建案ID\n    if (this.currentTab === 0) {\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    } else {\n      // 模板頁面 - 設定建案ID為0，CHouseType預設[1,2]\n      this.saveRequirement.CBuildCaseID = 0;\n      this.saveRequirement.CHouseType = [1, 2];\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        // 編輯時如果是共用tab，CHouseType強制為[1,2]\n        if (_this.currentTab === 1) {\n          _this.saveRequirement.CHouseType = [1, 2];\n        }\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\n    if (this.currentTab === 1) {\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 只在建案 tab 下且有建案時才查詢\n      if (this.currentTab === 0 && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      } else if (this.currentTab === 1) {\n        this.getListRequirementRequest.CBuildCaseID = 0;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動，CHouseType預設為[1,2]\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n      this.getListRequirementRequest.CHouseType = [1, 2];\n    } else {\n      // 建案頁面的邏輯保持不變\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          // 將 API 返回的數據轉換為 SelectableRequirement 並初始化 selected 屬性\n          this.requirementList = res.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          // 共用tab時CHouseType強制為[1,2]\n          this.saveRequirement.CHouseType = this.currentTab === 1 ? [1, 2] : res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          // TODO: 等後端API更新後啟用這行\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  onTabChange(event) {\n    // 避免頁面初始化時自動觸發重複查詢\n    if (this.isFirstTabChange) {\n      this.isFirstTabChange = false;\n      return;\n    }\n    // 根據 tabTitle 來判斷當前頁面\n    if (event.tabTitle === '共用') {\n      this.currentTab = 1;\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      this.currentTab = 0;\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    }\n    this.getList();\n  }\n  // 新增模板\n  addTemplate(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 模板設定建案ID為0，CHouseType預設[1,2]\n    this.saveRequirement.CBuildCaseID = 0;\n    this.saveRequirement.CHouseType = [1, 2];\n    this.dialogService.open(dialog);\n  }\n  // 編輯模板\n  onEditTemplate(data, dialog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this2.isNew = false;\n      try {\n        yield _this2.getData();\n        // 編輯模板時CHouseType強制為[1,2]\n        _this2.saveRequirement.CHouseType = [1, 2];\n        _this2.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get template data\", error);\n      }\n    })();\n  }\n  // 保存模板\n  saveTemplate(ref) {\n    // 模板驗證（不包含建案名稱）\n    this.valid.clear();\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    // CHouseType強制為[1,2]\n    if (this.currentTab === 1) {\n      this.saveRequirement.CHouseType = [1, 2];\n    }\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 確保模板建案ID為0\n    const templateData = {\n      ...this.saveRequirement\n    };\n    templateData.CBuildCaseID = 0;\n    // CHouseType強制為[1,2]\n    if (this.currentTab === 1) {\n      templateData.CHouseType = [1, 2];\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: templateData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  openTemplateViewer(templateViewerDialog) {\n    this.dialogService.open(templateViewerDialog);\n  }\n  onSelectTemplate(tpl) {\n    // 查看模板邏輯\n  }\n  // 獲取選中的需求項目\n  getSelectedRequirements() {\n    return this.requirementList.filter(req => req.selected);\n  }\n  // 選中狀態變更處理\n  onRequirementSelectionChange() {\n    // 可以在這裡添加額外的邏輯，比如更新選中計數等\n  }\n  // 全選功能\n  selectAllRequirements() {\n    this.requirementList.forEach(req => req.selected = true);\n  }\n  // 清除所有選擇\n  clearAllSelections() {\n    this.requirementList.forEach(req => req.selected = false);\n  }\n  // 檢查是否全選\n  isAllSelected() {\n    return this.requirementList.length > 0 && this.requirementList.every(req => req.selected);\n  }\n  // 檢查是否部分選中（用於 indeterminate 狀態）\n  isIndeterminate() {\n    const selectedCount = this.requirementList.filter(req => req.selected).length;\n    return selectedCount > 0 && selectedCount < this.requirementList.length;\n  }\n  // 切換全選狀態\n  toggleSelectAll(event) {\n    const isChecked = event.target.checked;\n    this.requirementList.forEach(req => req.selected = isChecked);\n  }\n  // 打開模板創建器\n  openTemplateCreator(templateCreatorDialog) {\n    const selectedRequirements = this.getSelectedRequirements();\n    if (selectedRequirements.length === 0) {\n      this.message.showErrorMSG('請先選擇要加入模板的項目');\n      return;\n    }\n    // 將選中的項目存儲在屬性中，確保在模板中保持引用一致性\n    this.selectedRequirementsForTemplate = [...selectedRequirements];\n    this.isCreatingTemplate = true;\n    const dialogRef = this.dialogService.open(templateCreatorDialog);\n    // 當對話框關閉時重置狀態\n    dialogRef.onClose.subscribe(() => {\n      this.isCreatingTemplate = false;\n      this.selectedRequirementsForTemplate = [];\n    });\n  }\n  // 模板創建成功回調\n  onTemplateCreated() {\n    this.message.showSucessMSG('模板創建成功');\n    // 清除選中狀態\n    this.clearAllSelections();\n  }\n};\nRequirementManagementComponent = __decorate([Component({\n  selector: 'app-requirement-management',\n  standalone: true,\n  imports: [NbCardModule, BreadcrumbComponent, NbInputModule, FormsModule, NbSelectModule, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, FormGroupComponent, NumberWithCommasPipe, NbTabsetModule, TemplateViewerComponent, TemplateCreatorComponent],\n  templateUrl: './requirement-management.component.html',\n  styleUrl: './requirement-management.component.scss'\n})], RequirementManagementComponent);\nexport { RequirementManagementComponent };", "map": {"version": 3, "names": ["Component", "NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "NbTabsetModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "TemplateViewerComponent", "TemplateCreatorComponent", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getListRequirementRequest", "getRequirementRequest", "buildCaseList", "requirementList", "saveRequirement", "CHouseType", "statusOptions", "value", "label", "houseType", "getEnumOptions", "isNew", "currentBuildCase", "currentTab", "isCreatingTemplate", "selectedRequirementsForTemplate", "isFirstTabChange", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "CStatus", "CIsShow", "CRequirement", "CGroupName", "map", "type", "resetSearch", "length", "setTimeout", "CBuildCaseID", "cID", "getList", "getHouseType", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "CSort", "CUnitPrice", "CUnit", "errorMessages", "CRemark", "add", "dialog", "open", "onEdit", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "save", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "close", "onDelete", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "item", "selected", "TotalItems", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "filter", "v", "getCIsShowText", "onTabChange", "event", "tabTitle", "addTemplate", "onEditTemplate", "_this2", "saveTemplate", "templateData", "openTemplateViewer", "templateViewerDialog", "onSelectTemplate", "tpl", "getSelectedRequirements", "req", "onRequirementSelectionChange", "selectAllRequirements", "clearAllSelections", "isAllSelected", "every", "isIndeterminate", "selectedCount", "toggleSelectAll", "isChecked", "target", "openTemplateCreator", "templateCreatorDialog", "selectedRequirements", "dialogRef", "onClose", "onTemplateCreated", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { Template, TemplateDetail, TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\r\nimport { TemplateCreatorComponent } from 'src/app/shared/components/template-creator/template-creator.component';\r\n\r\n// 擴展 GetRequirement 接口以支持選中狀態\r\ninterface SelectableRequirement extends GetRequirement {\r\n  selected?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    NbTabsetModule,\r\n    TemplateViewerComponent,\r\n    TemplateCreatorComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: SelectableRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n  currentTab = 0; // 追蹤當前選中的 tab\r\n  isCreatingTemplate = false; // 控制是否正在創建模板\r\n  selectedRequirementsForTemplate: SelectableRequirement[] = []; // 用於模板創建的選中項目\r\n\r\n\r\n\r\n  override ngOnInit(): void { }\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CGroupName = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        if (this.currentTab === 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        } else {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n        }\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    // 根據當前 tab 決定是否需要驗證建案名稱\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面需要驗證建案名稱\r\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    }\r\n\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 根據當前 tab 決定是否需要建案ID\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n      if (this.currentBuildCase != 0) {\r\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    } else {\r\n      // 模板頁面 - 設定建案ID為0，CHouseType預設[1,2]\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n    }\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: SelectableRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      // 編輯時如果是共用tab，CHouseType強制為[1,2]\r\n      if (this.currentTab === 1) {\r\n        this.saveRequirement.CHouseType = [1, 2];\r\n      }\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: SelectableRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 只在建案 tab 下且有建案時才查詢\r\n        if (this.currentTab === 0 && this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        } else if (this.currentTab === 1) {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動，CHouseType預設為[1,2]\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n      this.getListRequirementRequest.CHouseType = [1, 2];\r\n    } else {\r\n      // 建案頁面的邏輯保持不變\r\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n      }\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            // 將 API 返回的數據轉換為 SelectableRequirement 並初始化 selected 屬性\r\n            this.requirementList = res.Entries.map(item => ({\r\n              ...item,\r\n              selected: false\r\n            }));\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  } getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            // 共用tab時CHouseType強制為[1,2]\r\n            this.saveRequirement.CHouseType = this.currentTab === 1 ? [1, 2] : (res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : []);\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            // TODO: 等後端API更新後啟用這行\r\n            this.saveRequirement.CIsShow = (res.Entries as any).CIsShow || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  // Tab 切換事件處理\r\n  private isFirstTabChange = true;\r\n  onTabChange(event: any) {\r\n    // 避免頁面初始化時自動觸發重複查詢\r\n    if (this.isFirstTabChange) {\r\n      this.isFirstTabChange = false;\r\n      return;\r\n    }\r\n    // 根據 tabTitle 來判斷當前頁面\r\n    if (event.tabTitle === '共用') {\r\n      this.currentTab = 1;\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      this.currentTab = 0;\r\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\r\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    }\r\n    this.getList();\r\n  }\r\n\r\n  // 新增模板\r\n  addTemplate(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    // 模板設定建案ID為0，CHouseType預設[1,2]\r\n    this.saveRequirement.CBuildCaseID = 0;\r\n    this.saveRequirement.CHouseType = [1, 2];\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 編輯模板\r\n  async onEditTemplate(data: SelectableRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      // 編輯模板時CHouseType強制為[1,2]\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get template data\", error);\r\n    }\r\n  }\r\n\r\n  // 保存模板\r\n  saveTemplate(ref: any) {\r\n    // 模板驗證（不包含建案名稱）\r\n    this.valid.clear();\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    // CHouseType強制為[1,2]\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n    }\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 確保模板建案ID為0\r\n    const templateData = { ...this.saveRequirement };\r\n    templateData.CBuildCaseID = 0;\r\n    // CHouseType強制為[1,2]\r\n    if (this.currentTab === 1) {\r\n      templateData.CHouseType = [1, 2];\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: templateData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  openTemplateViewer(templateViewerDialog: TemplateRef<any>) {\r\n    this.dialogService.open(templateViewerDialog);\r\n  }\r\n\r\n  onSelectTemplate(tpl: Template) {\r\n    // 查看模板邏輯\r\n  }\r\n\r\n  // 獲取選中的需求項目\r\n  getSelectedRequirements(): SelectableRequirement[] {\r\n    return this.requirementList.filter(req => req.selected);\r\n  }\r\n\r\n  // 選中狀態變更處理\r\n  onRequirementSelectionChange() {\r\n    // 可以在這裡添加額外的邏輯，比如更新選中計數等\r\n  }\r\n\r\n  // 全選功能\r\n  selectAllRequirements() {\r\n    this.requirementList.forEach(req => req.selected = true);\r\n  }\r\n\r\n  // 清除所有選擇\r\n  clearAllSelections() {\r\n    this.requirementList.forEach(req => req.selected = false);\r\n  }\r\n\r\n  // 檢查是否全選\r\n  isAllSelected(): boolean {\r\n    return this.requirementList.length > 0 && this.requirementList.every(req => req.selected);\r\n  }\r\n\r\n  // 檢查是否部分選中（用於 indeterminate 狀態）\r\n  isIndeterminate(): boolean {\r\n    const selectedCount = this.requirementList.filter(req => req.selected).length;\r\n    return selectedCount > 0 && selectedCount < this.requirementList.length;\r\n  }\r\n\r\n  // 切換全選狀態\r\n  toggleSelectAll(event: any) {\r\n    const isChecked = event.target.checked;\r\n    this.requirementList.forEach(req => req.selected = isChecked);\r\n  }\r\n\r\n  // 打開模板創建器\r\n  openTemplateCreator(templateCreatorDialog: TemplateRef<any>) {\r\n    const selectedRequirements = this.getSelectedRequirements();\r\n    if (selectedRequirements.length === 0) {\r\n      this.message.showErrorMSG('請先選擇要加入模板的項目');\r\n      return;\r\n    }\r\n\r\n    // 將選中的項目存儲在屬性中，確保在模板中保持引用一致性\r\n    this.selectedRequirementsForTemplate = [...selectedRequirements];\r\n    this.isCreatingTemplate = true;\r\n    const dialogRef = this.dialogService.open(templateCreatorDialog);\r\n\r\n    // 當對話框關閉時重置狀態\r\n    dialogRef.onClose.subscribe(() => {\r\n      this.isCreatingTemplate = false;\r\n      this.selectedRequirementsForTemplate = [];\r\n    });\r\n  }\r\n\r\n  // 模板創建成功回調\r\n  onTemplateCreated() {\r\n    this.message.showSucessMSG('模板創建成功');\r\n    // 清除選中狀態\r\n    this.clearAllSelections();\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAyC,eAAe;AAE1E,SAASC,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/I,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAAmCC,uBAAuB,QAAQ,qEAAqE;AACvI,SAASC,wBAAwB,QAAQ,uEAAuE;AA8BzG,IAAMC,8BAA8B,GAApC,MAAMA,8BAA+B,SAAQb,aAAa;EAC/Dc,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAMpB;IACA,KAAAC,yBAAyB,GAAG,EAA8D;IAC1F,KAAAC,qBAAqB,GAA8B,EAAE;IACrD;IACA,KAAAC,aAAa,GAA8B,EAAE;IAC7C,KAAAC,eAAe,GAA4B,EAAE;IAC7C,KAAAC,eAAe,GAAgD;MAAEC,UAAU,EAAE;IAAE,CAAE;IAEjF,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAC,SAAS,GAAG,IAAI,CAAClB,UAAU,CAACmB,cAAc,CAACzB,aAAa,CAAC;IACzD,KAAA0B,KAAK,GAAG,KAAK;IACb,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAChB,KAAAC,kBAAkB,GAAG,KAAK,CAAC,CAAC;IAC5B,KAAAC,+BAA+B,GAA4B,EAAE,CAAC,CAAC;IAwP/D;IACQ,KAAAC,gBAAgB,GAAG,IAAI;IA7Q7B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAsBSC,QAAQA,CAAA,GAAW;EAC5B;EACAF,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACjB,yBAAyB,CAACoB,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACpB,yBAAyB,CAACqB,OAAO,GAAG,IAAI;IAC7C,IAAI,CAACrB,yBAAyB,CAACsB,YAAY,GAAG,EAAE;IAChD,IAAI,CAACtB,yBAAyB,CAACuB,UAAU,GAAG,EAAE;IAC9C;IACA,IAAI,CAACvB,yBAAyB,CAACK,UAAU,GAAG,IAAI,CAACI,SAAS,CAACe,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAClB,KAAK,CAAC;EACpF;EAEA;EACAmB,WAAWA,CAAA;IACT,IAAI,CAACT,oBAAoB,EAAE;IAC3B;IACA,IAAI,IAAI,CAACf,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyB,MAAM,GAAG,CAAC,EAAE;MACvDC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACf,UAAU,KAAK,CAAC,EAAE;UACzB,IAAI,CAACb,yBAAyB,CAAC6B,YAAY,GAAG,IAAI,CAAC3B,aAAa,CAAC,CAAC,CAAC,CAAC4B,GAAG;QACzE,CAAC,MAAM;UACL,IAAI,CAAC9B,yBAAyB,CAAC6B,YAAY,GAAG,CAAC;QACjD;QACA,IAAI,CAACE,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEAC,YAAYA,CAACC,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClC,KAAK,IAAI+B,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC/B,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAO4B,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAClD,KAAK,CAACmD,KAAK,EAAE;IAElB;IACA,IAAI,IAAI,CAAChC,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,CAACnB,KAAK,CAACoD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC1C,eAAe,CAACyB,YAAY,CAAC;IAClE;IAEA,IAAI,CAACnC,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1C,eAAe,CAACkB,YAAY,CAAC;IAC9D,IAAI,CAAC5B,KAAK,CAACoD,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC1C,eAAe,CAACC,UAAU,CAAC;IAC7D,IAAI,CAACX,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1C,eAAe,CAAC2C,KAAK,CAAC;IACvD,IAAI,CAACrD,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1C,eAAe,CAACgB,OAAO,CAAC;IACzD,IAAI,CAAC1B,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1C,eAAe,CAAC4C,UAAU,CAAC;IAC5D,IAAI,CAACtD,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1C,eAAe,CAAC6C,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAAC7C,eAAe,CAACmB,UAAU,IAAI,IAAI,CAACnB,eAAe,CAACmB,UAAU,CAACI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACjC,KAAK,CAACwD,aAAa,CAACR,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACtC,eAAe,CAAC+C,OAAO,IAAI,IAAI,CAAC/C,eAAe,CAAC+C,OAAO,CAACxB,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACjC,KAAK,CAACwD,aAAa,CAACR,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEAU,GAAGA,CAACC,MAAwB;IAC1B,IAAI,CAAC1C,KAAK,GAAG,IAAI;IACjB,IAAI,CAACP,eAAe,GAAG;MAAEC,UAAU,EAAE,EAAE;MAAEgB,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACjB,eAAe,CAACgB,OAAO,GAAG,CAAC;IAChC,IAAI,CAAChB,eAAe,CAAC4C,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAACnC,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,IAAI,CAACD,gBAAgB,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACR,eAAe,CAACyB,YAAY,GAAG,IAAI,CAACjB,gBAAgB;MAC3D,CAAC,MAAM,IAAI,IAAI,CAACV,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyB,MAAM,GAAG,CAAC,EAAE;QAC9D,IAAI,CAACvB,eAAe,CAACyB,YAAY,GAAG,IAAI,CAAC3B,aAAa,CAAC,CAAC,CAAC,CAAC4B,GAAG;MAC/D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAC1B,eAAe,CAACyB,YAAY,GAAG,CAAC;MACrC,IAAI,CAACzB,eAAe,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,CAACb,aAAa,CAAC8D,IAAI,CAACD,MAAM,CAAC;EACjC;EAEME,MAAMA,CAACC,IAA2B,EAAEH,MAAwB;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MAChED,KAAI,CAACxD,qBAAqB,CAAC0D,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAAC9C,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM8C,KAAI,CAACG,OAAO,EAAE;QACpB;QACA,IAAIH,KAAI,CAAC5C,UAAU,KAAK,CAAC,EAAE;UACzB4C,KAAI,CAACrD,eAAe,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1C;QACAoD,KAAI,CAACjE,aAAa,CAAC8D,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAG,IAAIA,CAACC,GAAQ;IACX,IAAI,CAACrB,UAAU,EAAE;IACjB,IAAI,IAAI,CAAClD,KAAK,CAACwD,aAAa,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClC,OAAO,CAACyE,aAAa,CAAC,IAAI,CAACxE,KAAK,CAACwD,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAI,IAAI,CAACrC,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACT,eAAe,CAACyB,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAACjC,kBAAkB,CAACuE,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAAChE;KACZ,CAAC,CAACiE,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC9E,OAAO,CAAC+E,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACzC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACtC,OAAO,CAACgF,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEAC,QAAQA,CAACpB,IAA2B;IAClC,IAAI,CAACpD,eAAe,CAACuD,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAAChD,KAAK,GAAG,KAAK;IAClB,IAAIkE,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACnF,kBAAkB,CAACoF,iCAAiC,CAAC;MACxDZ,IAAI,EAAE;QACJT,cAAc,EAAE,IAAI,CAACvD,eAAe,CAACuD;;KAExC,CAAC,CAACU,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAAC7E,OAAO,CAAC+E,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACzC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAb,gBAAgBA,CAAA;IACd,IAAI,CAACvB,gBAAgB,CAACsF,qCAAqC,CAAC;MAAEb,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEc,IAAI,CAAC1G,kBAAkB,CAAC,IAAI,CAACuB,UAAU,CAAC,CAAC,CAACsE,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAACpE,aAAa,GAAGoE,GAAG,CAACa,OAAQ;MACjC;MACA,IAAI,IAAI,CAACtE,UAAU,KAAK,CAAC,IAAI,IAAI,CAACX,aAAa,CAACyB,MAAM,GAAG,CAAC,EAAE;QAC1D,IAAI,CAAC3B,yBAAyB,CAAC6B,YAAY,GAAG,IAAI,CAAC3B,aAAa,CAAC,CAAC,CAAC,CAAC4B,GAAG;QACvE,IAAI,CAACC,OAAO,EAAE;MAChB,CAAC,MAAM,IAAI,IAAI,CAAClB,UAAU,KAAK,CAAC,EAAE;QAChC,IAAI,CAACb,yBAAyB,CAAC6B,YAAY,GAAG,CAAC;QAC/C,IAAI,CAACE,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAAC/B,yBAAyB,CAACoF,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAACrF,yBAAyB,CAACsF,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAACpF,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAACqF,YAAY,GAAG,CAAC;IACrB;IACA,IAAI,IAAI,CAAC3E,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACb,yBAAyB,CAAC6B,YAAY,GAAG,CAAC;MAC/C,IAAI,CAAC7B,yBAAyB,CAACK,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACpD,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACL,yBAAyB,CAAC6B,YAAY,IAAI,IAAI,CAAC7B,yBAAyB,CAAC6B,YAAY,IAAI,CAAC,EAAE;QACnG,IAAI,CAACjB,gBAAgB,GAAG,IAAI,CAACZ,yBAAyB,CAAC6B,YAAY;MACrE;IACF;IAEA,IAAI,CAACjC,kBAAkB,CAAC6F,8BAA8B,CAAC;MAAErB,IAAI,EAAE,IAAI,CAACpE;IAAyB,CAAE,CAAC,CAC7FkF,IAAI,EAAE,CACNb,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACa,OAAO,EAAE;UACf;UACA,IAAI,CAAChF,eAAe,GAAGmE,GAAG,CAACa,OAAO,CAAC3D,GAAG,CAACkE,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACPC,QAAQ,EAAE;WACX,CAAC,CAAC;UACH,IAAI,CAACH,YAAY,GAAGlB,GAAG,CAACsB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAAEhC,OAAOA,CAAA;IACP,IAAI,CAAChE,kBAAkB,CAACiG,8BAA8B,CAAC;MAAEzB,IAAI,EAAE,IAAI,CAACnE;IAAqB,CAAE,CAAC,CACzFiF,IAAI,EAAE,CACNb,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACa,OAAO,EAAE;UACf,IAAI,CAAC/E,eAAe,GAAG;YAAEC,UAAU,EAAE,EAAE;YAAEgB,OAAO,EAAE;UAAK,CAAE;UACzD,IAAI,CAACjB,eAAe,CAACyB,YAAY,GAAGyC,GAAG,CAACa,OAAO,CAACtD,YAAY;UAC5D,IAAI,CAACzB,eAAe,CAACmB,UAAU,GAAG+C,GAAG,CAACa,OAAO,CAAC5D,UAAU;UACxD;UACA,IAAI,CAACnB,eAAe,CAACC,UAAU,GAAG,IAAI,CAACQ,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAIyD,GAAG,CAACa,OAAO,CAAC9E,UAAU,GAAI6B,KAAK,CAACC,OAAO,CAACmC,GAAG,CAACa,OAAO,CAAC9E,UAAU,CAAC,GAAGiE,GAAG,CAACa,OAAO,CAAC9E,UAAU,GAAG,CAACiE,GAAG,CAACa,OAAO,CAAC9E,UAAU,CAAC,GAAI,EAAG;UAC9L,IAAI,CAACD,eAAe,CAAC+C,OAAO,GAAGmB,GAAG,CAACa,OAAO,CAAChC,OAAO;UAClD,IAAI,CAAC/C,eAAe,CAACkB,YAAY,GAAGgD,GAAG,CAACa,OAAO,CAAC7D,YAAY;UAC5D,IAAI,CAAClB,eAAe,CAACuD,cAAc,GAAGW,GAAG,CAACa,OAAO,CAACxB,cAAc;UAChE,IAAI,CAACvD,eAAe,CAAC2C,KAAK,GAAGuB,GAAG,CAACa,OAAO,CAACpC,KAAK;UAC9C,IAAI,CAAC3C,eAAe,CAACgB,OAAO,GAAGkD,GAAG,CAACa,OAAO,CAAC/D,OAAO;UAClD,IAAI,CAAChB,eAAe,CAAC4C,UAAU,GAAGsB,GAAG,CAACa,OAAO,CAACnC,UAAU;UACxD,IAAI,CAAC5C,eAAe,CAAC6C,KAAK,GAAGqB,GAAG,CAACa,OAAO,CAAClC,KAAK;UAC9C;UACA,IAAI,CAAC7C,eAAe,CAACiB,OAAO,GAAIiD,GAAG,CAACa,OAAe,CAAC9D,OAAO,IAAI,KAAK;QACtE;MACF;IACF,CAAC,CAAC;EACN;EAEAyE,iBAAiBA,CAACvF,KAAa,EAAEwF,OAAY;IAC3CjC,OAAO,CAACC,GAAG,CAACgC,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAAC3F,eAAe,CAACC,UAAU,EAAE2F,QAAQ,CAACzF,KAAK,CAAC,EAAE;QACrD,IAAI,CAACH,eAAe,CAACC,UAAU,EAAEqC,IAAI,CAACnC,KAAK,CAAC;MAC9C;MACAuD,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC3D,eAAe,CAACC,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACD,eAAe,CAACC,UAAU,GAAG,IAAI,CAACD,eAAe,CAACC,UAAU,EAAE4F,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK3F,KAAK,CAAC;IAC7F;EACF;EAEA4F,cAAcA,CAAC3C,IAAS;IACtB,OAAOA,IAAI,CAACnC,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAIA+E,WAAWA,CAACC,KAAU;IACpB;IACA,IAAI,IAAI,CAACrF,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B;IACF;IACA;IACA,IAAIqF,KAAK,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACzF,UAAU,GAAG,CAAC;MACnB,IAAI,CAACb,yBAAyB,CAAC6B,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,IAAI,CAAChB,UAAU,GAAG,CAAC;MACnB;MACA,IAAI,IAAI,CAACX,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyB,MAAM,GAAG,CAAC,EAAE;QACvD,IAAI,CAAC3B,yBAAyB,CAAC6B,YAAY,GAAG,IAAI,CAAC3B,aAAa,CAAC,CAAC,CAAC,CAAC4B,GAAG;MACzE;IACF;IACA,IAAI,CAACC,OAAO,EAAE;EAChB;EAEA;EACAwE,WAAWA,CAAClD,MAAwB;IAClC,IAAI,CAAC1C,KAAK,GAAG,IAAI;IACjB,IAAI,CAACP,eAAe,GAAG;MAAEC,UAAU,EAAE,EAAE;MAAEgB,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACjB,eAAe,CAACgB,OAAO,GAAG,CAAC;IAChC,IAAI,CAAChB,eAAe,CAAC4C,UAAU,GAAG,CAAC;IACnC;IACA,IAAI,CAAC5C,eAAe,CAACyB,YAAY,GAAG,CAAC;IACrC,IAAI,CAACzB,eAAe,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACxC,IAAI,CAACb,aAAa,CAAC8D,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACMmD,cAAcA,CAAChD,IAA2B,EAAEH,MAAwB;IAAA,IAAAoD,MAAA;IAAA,OAAA/C,iBAAA;MACxE+C,MAAI,CAACxG,qBAAqB,CAAC0D,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChE8C,MAAI,CAAC9F,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM8F,MAAI,CAAC7C,OAAO,EAAE;QACpB;QACA6C,MAAI,CAACrG,eAAe,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACxCoG,MAAI,CAACjH,aAAa,CAAC8D,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA;EACA6C,YAAYA,CAACzC,GAAQ;IACnB;IACA,IAAI,CAACvE,KAAK,CAACmD,KAAK,EAAE;IAClB,IAAI,CAACnD,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1C,eAAe,CAACkB,YAAY,CAAC;IAC9D;IACA,IAAI,IAAI,CAACT,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACT,eAAe,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,CAACX,KAAK,CAACoD,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC1C,eAAe,CAACC,UAAU,CAAC;IAC7D,IAAI,CAACX,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1C,eAAe,CAAC2C,KAAK,CAAC;IACvD,IAAI,CAACrD,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1C,eAAe,CAACgB,OAAO,CAAC;IACzD,IAAI,CAAC1B,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1C,eAAe,CAAC4C,UAAU,CAAC;IAC5D,IAAI,CAACtD,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1C,eAAe,CAAC6C,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAAC7C,eAAe,CAACmB,UAAU,IAAI,IAAI,CAACnB,eAAe,CAACmB,UAAU,CAACI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACjC,KAAK,CAACwD,aAAa,CAACR,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACtC,eAAe,CAAC+C,OAAO,IAAI,IAAI,CAAC/C,eAAe,CAAC+C,OAAO,CAACxB,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACjC,KAAK,CAACwD,aAAa,CAACR,IAAI,CAAC,kBAAkB,CAAC;IACnD;IAEA,IAAI,IAAI,CAAChD,KAAK,CAACwD,aAAa,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClC,OAAO,CAACyE,aAAa,CAAC,IAAI,CAACxE,KAAK,CAACwD,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMyD,YAAY,GAAG;MAAE,GAAG,IAAI,CAACvG;IAAe,CAAE;IAChDuG,YAAY,CAAC9E,YAAY,GAAG,CAAC;IAC7B;IACA,IAAI,IAAI,CAAChB,UAAU,KAAK,CAAC,EAAE;MACzB8F,YAAY,CAACtG,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClC;IAEA,IAAI,CAACT,kBAAkB,CAACuE,+BAA+B,CAAC;MACtDC,IAAI,EAAEuC;KACP,CAAC,CAACtC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC9E,OAAO,CAAC+E,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACzC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACtC,OAAO,CAACgF,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEAiC,kBAAkBA,CAACC,oBAAsC;IACvD,IAAI,CAACrH,aAAa,CAAC8D,IAAI,CAACuD,oBAAoB,CAAC;EAC/C;EAEAC,gBAAgBA,CAACC,GAAa;IAC5B;EAAA;EAGF;EACAC,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAAC7G,eAAe,CAAC8F,MAAM,CAACgB,GAAG,IAAIA,GAAG,CAACtB,QAAQ,CAAC;EACzD;EAEA;EACAuB,4BAA4BA,CAAA;IAC1B;EAAA;EAGF;EACAC,qBAAqBA,CAAA;IACnB,IAAI,CAAChH,eAAe,CAACkC,OAAO,CAAC4E,GAAG,IAAIA,GAAG,CAACtB,QAAQ,GAAG,IAAI,CAAC;EAC1D;EAEA;EACAyB,kBAAkBA,CAAA;IAChB,IAAI,CAACjH,eAAe,CAACkC,OAAO,CAAC4E,GAAG,IAAIA,GAAG,CAACtB,QAAQ,GAAG,KAAK,CAAC;EAC3D;EAEA;EACA0B,aAAaA,CAAA;IACX,OAAO,IAAI,CAAClH,eAAe,CAACwB,MAAM,GAAG,CAAC,IAAI,IAAI,CAACxB,eAAe,CAACmH,KAAK,CAACL,GAAG,IAAIA,GAAG,CAACtB,QAAQ,CAAC;EAC3F;EAEA;EACA4B,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG,IAAI,CAACrH,eAAe,CAAC8F,MAAM,CAACgB,GAAG,IAAIA,GAAG,CAACtB,QAAQ,CAAC,CAAChE,MAAM;IAC7E,OAAO6F,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,IAAI,CAACrH,eAAe,CAACwB,MAAM;EACzE;EAEA;EACA8F,eAAeA,CAACpB,KAAU;IACxB,MAAMqB,SAAS,GAAGrB,KAAK,CAACsB,MAAM,CAAC5B,OAAO;IACtC,IAAI,CAAC5F,eAAe,CAACkC,OAAO,CAAC4E,GAAG,IAAIA,GAAG,CAACtB,QAAQ,GAAG+B,SAAS,CAAC;EAC/D;EAEA;EACAE,mBAAmBA,CAACC,qBAAuC;IACzD,MAAMC,oBAAoB,GAAG,IAAI,CAACd,uBAAuB,EAAE;IAC3D,IAAIc,oBAAoB,CAACnG,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAClC,OAAO,CAACgF,YAAY,CAAC,cAAc,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAAC1D,+BAA+B,GAAG,CAAC,GAAG+G,oBAAoB,CAAC;IAChE,IAAI,CAAChH,kBAAkB,GAAG,IAAI;IAC9B,MAAMiH,SAAS,GAAG,IAAI,CAACvI,aAAa,CAAC8D,IAAI,CAACuE,qBAAqB,CAAC;IAEhE;IACAE,SAAS,CAACC,OAAO,CAAC3D,SAAS,CAAC,MAAK;MAC/B,IAAI,CAACvD,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACC,+BAA+B,GAAG,EAAE;IAC3C,CAAC,CAAC;EACJ;EAEA;EACAkH,iBAAiBA,CAAA;IACf,IAAI,CAACxI,OAAO,CAAC+E,aAAa,CAAC,QAAQ,CAAC;IACpC;IACA,IAAI,CAAC4C,kBAAkB,EAAE;EAC3B;CACD;AApcYhI,8BAA8B,GAAA8I,UAAA,EAvB1ClK,SAAS,CAAC;EACTmK,QAAQ,EAAE,4BAA4B;EACtCC,UAAU,EAAE,IAAI;EAAEC,OAAO,EAAE,CACzBpK,YAAY,EACZQ,mBAAmB,EACnBN,aAAa,EACbO,WAAW,EACXL,cAAc,EACdD,cAAc,EACdQ,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVZ,gBAAgB,EAChBa,kBAAkB,EAClBC,oBAAoB,EACpBV,cAAc,EACdY,uBAAuB,EACvBC,wBAAwB,CACzB;EACDmJ,WAAW,EAAE,yCAAyC;EACtDC,QAAQ,EAAE;CACX,CAAC,C,EACWnJ,8BAA8B,CAoc1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}