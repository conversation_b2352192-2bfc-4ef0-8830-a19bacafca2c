@import '../../../@theme/styles/colors';

.step-navigator {
  width: 100%;

  .step-nav {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid $border-light;
    padding-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;

    .step-wrapper {
      display: flex;
      align-items: center;
      position: relative;

      &.clickable {
        cursor: pointer;

        .step-item {
          &:hover {
            transform: translateY(-2px);
            box-shadow: $shadow-lg;
          }
        }
      }

      .step-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.25rem;
        border-radius: 0.5rem;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        min-width: 140px;
        justify-content: center;
        gap: 0.5rem;
        border: 2px solid transparent;

        .step-icon {
          font-size: 1.1rem;
          flex-shrink: 0;
        }

        .step-content {
          display: flex;
          align-items: center;
          flex: 1;

          .step-text {
            display: flex;
            align-items: center;
            gap: 0.25rem;

            .step-number {
              font-weight: 600;
              font-size: 0.9rem;
            }

            .step-label {
              white-space: nowrap;
              font-size: 0.95rem;
            }
          }
        }

        .step-check-icon {
          font-size: 1.1rem;
          margin-left: 0.25rem;
          flex-shrink: 0;
        }

        // 狀態樣式
        &.active {
          background: $gradient-primary;
          color: $text-light;
          box-shadow: $shadow-md;
          border-color: $primary-gold-light;

          .step-icon,
          .step-check-icon {
            color: $text-light;
          }
        }

        &.completed {
          background: $info-base;
          color: $text-light;
          box-shadow: 0 4px 12px rgba(23, 162, 184, 0.25);
          border-color: $info-base;

          .step-icon,
          .step-check-icon {
            color: $text-light;
          }
        }

        &.pending {
          background-color: $bg-secondary;
          color: $text-muted;
          border-color: $border-light;

          .step-icon {
            color: $text-muted;
          }

          &:hover {
            background-color: $bg-hover;
            border-color: $border-primary;
            color: $text-secondary;
          }
        }
      }

      .step-connector {
        width: 2.5rem;
        height: 3px;
        background-color: $border-light;
        margin: 0 0.5rem;
        transition: all 0.3s ease;
        border-radius: 1.5px;

        &.completed {
          background: $info-base;
        }

        &.active {
          background: $gradient-primary;
        }
      }
    }
  }
}

// 響應式設計
@media (max-width: 768px) {
  .step-navigator {
    .step-nav {
      .step-wrapper {
        .step-item {
          font-size: 0.875rem;
          padding: 0.6rem 1rem;
          min-width: 120px;

          .step-content {
            .step-text {
              .step-label {
                font-size: 0.85rem;
              }
            }
          }
        }

        .step-connector {
          width: 2rem;
          margin: 0 0.25rem;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .step-navigator {
    .step-nav {
      flex-direction: column;
      gap: 1rem;

      .step-wrapper {
        width: 100%;
        justify-content: center;

        .step-connector {
          display: none;
        }

        .step-item {
          width: 100%;
          max-width: 240px;
          padding: 0.8rem 1.5rem;
        }
      }
    }
  }
}

// 深色主題支持
:host-context(.dark-theme) {
  .step-navigator {
    .step-nav {
      border-bottom-color: $dark-border;

      .step-wrapper {
        .step-item {
          &.pending {
            background-color: $dark-bg-secondary;
            color: $dark-text-secondary;
            border-color: $dark-border;

            &:hover {
              background-color: lighten($dark-bg-secondary, 5%);
              border-color: lighten($dark-border, 10%);
              color: $dark-text-primary;
            }
          }

          &.active {
            box-shadow: $shadow-xl;
          }

          &.completed {
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
          }
        }

        .step-connector {
          background-color: $dark-border;

          &.completed {
            background: $info-base;
          }

          &.active {
            background: $gradient-primary;
          }
        }
      }
    }
  }
}

// 動畫效果增強
.step-navigator {
  .step-nav {
    .step-wrapper {
      .step-item {
        &.active {
          animation: pulse-active 2s infinite;
        }

        &.completed {
          .step-check-icon {
            animation: check-bounce 0.6s ease-out;
          }
        }
      }
    }
  }
}

@keyframes pulse-active {

  0%,
  100% {
    box-shadow: $shadow-md;
  }

  50% {
    box-shadow: $shadow-lg;
  }
}

@keyframes check-bounce {
  0% {
    transform: scale(0);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}
