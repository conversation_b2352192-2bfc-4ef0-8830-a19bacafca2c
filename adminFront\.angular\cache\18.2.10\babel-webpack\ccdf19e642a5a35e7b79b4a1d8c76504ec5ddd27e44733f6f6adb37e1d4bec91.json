{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbIconModule } from '@nebular/theme';\nimport { StepStatus } from '../../interfaces/step-config.interface';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@nebular/theme\";\nconst _c0 = (a0, a1) => ({\n  \"completed\": a0,\n  \"active\": a1\n});\nfunction StepNavigatorComponent_div_2_nb_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 12);\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"icon\", step_r2.icon);\n  }\n}\nfunction StepNavigatorComponent_div_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", step_r2.stepNumber, \".\");\n  }\n}\nfunction StepNavigatorComponent_div_2_nb_icon_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 14);\n  }\n}\nfunction StepNavigatorComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 15);\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c0, step_r2.status === ctx_r2.StepStatus.COMPLETED, step_r2.status === ctx_r2.StepStatus.ACTIVE));\n  }\n}\nfunction StepNavigatorComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function StepNavigatorComponent_div_2_Template_div_click_0_listener() {\n      const step_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onStepClick(step_r2.stepNumber, step_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4);\n    i0.ɵɵtemplate(2, StepNavigatorComponent_div_2_nb_icon_2_Template, 1, 1, \"nb-icon\", 5);\n    i0.ɵɵelementStart(3, \"div\", 6)(4, \"span\", 7);\n    i0.ɵɵtemplate(5, StepNavigatorComponent_div_2_span_5_Template, 2, 1, \"span\", 8);\n    i0.ɵɵelementStart(6, \"span\", 9);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, StepNavigatorComponent_div_2_nb_icon_8_Template, 1, 0, \"nb-icon\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, StepNavigatorComponent_div_2_div_9_Template, 1, 4, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r2 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"clickable\", ctx_r2.isStepClickable(step_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStepClasses(step_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showStepNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r2.status === ctx_r2.StepStatus.COMPLETED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r4 < ctx_r2.processedSteps.length - 1);\n  }\n}\n/**\n * 步驟導航條共用元件\n * 支持自定義步驟配置和狀態管理\n */\nexport class StepNavigatorComponent {\n  constructor() {\n    /** 步驟配置數組 */\n    this.steps = [];\n    /** 當前步驟索引（1-based） */\n    this.currentStep = 1;\n    /** 是否允許點擊導航到其他步驟 */\n    this.allowClickNavigation = false;\n    /** 是否顯示步驟編號 */\n    this.showStepNumber = true;\n    /** 自定義CSS類名 */\n    this.customClass = '';\n    /** 步驟點擊事件 */\n    this.stepClick = new EventEmitter();\n    /** 暴露枚舉給模板使用 */\n    this.StepStatus = StepStatus;\n    /** 處理後的步驟數據 */\n    this.processedSteps = [];\n  }\n  ngOnChanges(changes) {\n    if (changes['steps'] || changes['currentStep']) {\n      this.processSteps();\n    }\n  }\n  /**\n   * 處理步驟數據，計算每個步驟的狀態\n   */\n  processSteps() {\n    this.processedSteps = this.steps.map((step, index) => {\n      const stepNumber = index + 1;\n      let status;\n      if (stepNumber < this.currentStep) {\n        status = StepStatus.COMPLETED;\n      } else if (stepNumber === this.currentStep) {\n        status = StepStatus.ACTIVE;\n      } else {\n        status = StepStatus.PENDING;\n      }\n      return {\n        ...step,\n        status,\n        stepNumber\n      };\n    });\n  }\n  /**\n   * 處理步驟點擊事件\n   * @param stepNumber 步驟編號（1-based）\n   * @param step 步驟配置\n   */\n  onStepClick(stepNumber, step) {\n    // 檢查是否允許點擊導航\n    if (!this.allowClickNavigation) {\n      return;\n    }\n    // 檢查步驟是否可點擊\n    if (step.clickable === false) {\n      return;\n    }\n    // 發出步驟點擊事件\n    this.stepClick.emit(stepNumber);\n  }\n  /**\n   * 獲取步驟的CSS類名\n   * @param step 處理後的步驟數據\n   * @returns CSS類名字符串\n   */\n  getStepClasses(step) {\n    const classes = ['step-item', step.status];\n    // 添加可點擊樣式\n    if (this.allowClickNavigation && step.clickable !== false) {\n      classes.push('clickable');\n    }\n    return classes.join(' ');\n  }\n  /**\n   * 檢查步驟是否可點擊\n   * @param step 步驟配置\n   * @returns 是否可點擊\n   */\n  isStepClickable(step) {\n    return this.allowClickNavigation && step.clickable !== false;\n  }\n  static {\n    this.ɵfac = function StepNavigatorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StepNavigatorComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StepNavigatorComponent,\n      selectors: [[\"app-step-navigator\"]],\n      inputs: {\n        steps: \"steps\",\n        currentStep: \"currentStep\",\n        allowClickNavigation: \"allowClickNavigation\",\n        showStepNumber: \"showStepNumber\",\n        customClass: \"customClass\"\n      },\n      outputs: {\n        stepClick: \"stepClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"step-navigator\", 3, \"ngClass\"], [1, \"step-nav\"], [\"class\", \"step-wrapper\", 3, \"clickable\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"step-wrapper\", 3, \"click\"], [3, \"ngClass\"], [\"class\", \"step-icon\", 3, \"icon\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"step-text\"], [\"class\", \"step-number\", 4, \"ngIf\"], [1, \"step-label\"], [\"icon\", \"checkmark-outline\", \"class\", \"step-check-icon\", 4, \"ngIf\"], [\"class\", \"step-connector\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"step-icon\", 3, \"icon\"], [1, \"step-number\"], [\"icon\", \"checkmark-outline\", 1, \"step-check-icon\"], [1, \"step-connector\", 3, \"ngClass\"]],\n      template: function StepNavigatorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, StepNavigatorComponent_div_2_Template, 10, 8, \"div\", 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.customClass);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.processedSteps);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, NbIconModule, i2.NbIconComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n.step-navigator[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  padding-bottom: 1rem;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper.clickable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper.clickable[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem 1.25rem;\\n  border-radius: 0.5rem;\\n  font-weight: 500;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  min-width: 140px;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  border: 2px solid transparent;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  flex-shrink: 0;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n  font-size: 0.95rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-check-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-left: 0.25rem;\\n  flex-shrink: 0;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 100%);\\n  color: white;\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);\\n  border-color: #3B82F6;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%], \\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%]   .step-check-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #34D399 0%, #10B981 100%);\\n  color: white;\\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.25);\\n  border-color: #10B981;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%], \\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   .step-check-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #F3F4F6;\\n  color: #9CA3AF;\\n  border-color: #E5E7EB;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%] {\\n  color: #9CA3AF;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%]:hover {\\n  background-color: #F9FAFB;\\n  border-color: #D1D5DB;\\n  color: #6B7280;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 3px;\\n  background-color: #E5E7EB;\\n  margin: 0 0.5rem;\\n  transition: all 0.3s ease;\\n  border-radius: 1.5px;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #34D399 0%, #10B981 100%);\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 100%);\\n}\\n\\n@media (max-width: 768px) {\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.4rem 0.8rem;\\n    min-width: 100px;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n    width: 1.5rem;\\n    margin: 0 0.25rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 200px;\\n  }\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  border-bottom-color: #404040;\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  color: #CCCCCC;\\n  border-color: #404040;\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  background-color: #404040;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvc3RlcC1uYXZpZ2F0b3Ivc3RlcC1uYXZpZ2F0b3IuY29tcG9uZW50LnNjc3MiLCJ3ZWJwYWNrOi8vLi9zcmMvYXBwL0B0aGVtZS9zdHlsZXMvX2NvbG9ycy5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLGdCQUFnQjtBQ0FoQjs7O0VBQUE7QURnQkE7RUFDRSxXQUFBO0FBVkY7QUFZRTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQ0FBQTtFQUNBLG9CQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7QUFWSjtBQVlJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7QUFWTjtBQVlNO0VBQ0UsZUFBQTtBQVZSO0FBYVU7RUFDRSwyQkFBQTtFQUNBLCtDQUFBO0FBWFo7QUFnQk07RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx3QkFBQTtFQUNBLHFCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpREFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7RUFDQSw2QkFBQTtBQWRSO0FBZ0JRO0VBQ0UsaUJBQUE7RUFDQSxjQUFBO0FBZFY7QUFpQlE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxPQUFBO0FBZlY7QUFpQlU7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0FBZlo7QUFpQlk7RUFDRSxnQkFBQTtFQUNBLGlCQUFBO0FBZmQ7QUFrQlk7RUFDRSxtQkFBQTtFQUNBLGtCQUFBO0FBaEJkO0FBcUJRO0VBQ0UsaUJBQUE7RUFDQSxvQkFBQTtFQUNBLGNBQUE7QUFuQlY7QUF1QlE7RUFDRSw2REFoRmM7RUFpRmQsWUFBQTtFQUNBLCtDQUFBO0VBQ0EscUJBN0ZLO0FBd0VmO0FBdUJVOztFQUVFLFlBQUE7QUFyQlo7QUF5QlE7RUFDRSw2REEzRmdCO0VBNEZoQixZQUFBO0VBQ0EsK0NBQUE7RUFDQSxxQkF0R087QUErRWpCO0FBeUJVOztFQUVFLFlBQUE7QUF2Qlo7QUEyQlE7RUFDRSx5QkE3R0s7RUE4R0wsY0E3R1U7RUE4R1YscUJBN0dZO0FBb0Z0QjtBQTJCVTtFQUNFLGNBakhRO0FBd0ZwQjtBQTRCVTtFQUNFLHlCQUFBO0VBQ0EscUJBQUE7RUFDQSxjQUFBO0FBMUJaO0FBK0JNO0VBQ0UsYUFBQTtFQUNBLFdBQUE7RUFDQSx5QkE5SGM7RUErSGQsZ0JBQUE7RUFDQSx5QkFBQTtFQUNBLG9CQUFBO0FBN0JSO0FBK0JRO0VBQ0UsNkRBaElnQjtBQW1HMUI7QUFnQ1E7RUFDRSw2REFySWM7QUF1R3hCOztBQXNDQTtFQUlRO0lBQ0UsbUJBQUE7SUFDQSxzQkFBQTtJQUNBLGdCQUFBO0VBdENSO0VBMENZO0lBQ0UsaUJBQUE7RUF4Q2Q7RUE4Q007SUFDRSxhQUFBO0lBQ0EsaUJBQUE7RUE1Q1I7QUFDRjtBQWtEQTtFQUVJO0lBQ0Usc0JBQUE7SUFDQSxTQUFBO0VBakRKO0VBbURJO0lBQ0UsV0FBQTtJQUNBLHVCQUFBO0VBakROO0VBbURNO0lBQ0UsYUFBQTtFQWpEUjtFQW9ETTtJQUNFLFdBQUE7SUFDQSxnQkFBQTtFQWxEUjtBQUNGO0FBMkRJO0VBQ0UsNEJDeEJRO0FEakNkO0FBNkRVO0VBQ0UseUJDaENRO0VEaUNSLGNDL0JVO0VEZ0NWLHFCQy9CRTtBRDVCZDtBQStEUTtFQUNFLHlCQ3BDSTtBRHpCZDtBQUNBLDQza0JBQTQza0IiLCJzb3VyY2VzQ29udGVudCI6WyJAaW1wb3J0ICcuLi8uLi8uLi9AdGhlbWUvc3R5bGVzL2NvbG9ycyc7XG5cbi8vIMOmwpbCsMOnwprChMOpwoXCjcOowonCssOowq7CisOmwpXCuCAtIMOnwo/CvsOkwrvCo8OowpfCjcOowonCssOnwrPCu1xuJHN0ZXAtcHJpbWFyeTogIzNCODJGNjsgLy8gw6TCuMK7w6jCpsKBw6jCl8KNw6jCicKyXG4kc3RlcC1wcmltYXJ5LWxpZ2h0OiAjNjBBNUZBOyAvLyDDpsK3wrrDqMKXwo3DqMKJwrJcbiRzdGVwLXByaW1hcnktZGFyazogIzI1NjNFQjsgLy8gw6bCt8Kxw6jCl8KNw6jCicKyXG4kc3RlcC1jb21wbGV0ZWQ6ICMxMEI5ODE7IC8vIMOlwq7CjMOmwojCkMOnwovCgMOmwoXCiyAtIMOpwp3CksOnwrbCoMOowonCslxuJHN0ZXAtY29tcGxldGVkLWxpZ2h0OiAjMzREMzk5OyAvLyDDpsK3wrrDqcKdwpLDp8K2wqDDqMKJwrJcbiRzdGVwLXBlbmRpbmc6ICNGM0Y0RjY7IC8vIMOlwr7ChcOowpnClcOnwpDChsOowoPCjMOmwpnCr1xuJHN0ZXAtcGVuZGluZy10ZXh0OiAjOUNBM0FGOyAvLyDDpcK+woXDqMKZwpXDp8KQwobDpsKWwofDpcKtwpdcbiRzdGVwLXBlbmRpbmctYm9yZGVyOiAjRTVFN0VCOyAvLyDDpcK+woXDqMKZwpXDp8KQwobDqcKCworDpsKhwoZcblxuLy8gw6bCvMK4w6jCrsKKw6jCicKyXG4kc3RlcC1ncmFkaWVudC1wcmltYXJ5OiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAkc3RlcC1wcmltYXJ5LWxpZ2h0IDAlLCAkc3RlcC1wcmltYXJ5IDEwMCUpO1xuJHN0ZXAtZ3JhZGllbnQtY29tcGxldGVkOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAkc3RlcC1jb21wbGV0ZWQtbGlnaHQgMCUsICRzdGVwLWNvbXBsZXRlZCAxMDAlKTtcblxuLnN0ZXAtbmF2aWdhdG9yIHtcbiAgd2lkdGg6IDEwMCU7XG5cbiAgLnN0ZXAtbmF2IHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgJGJvcmRlci1saWdodDtcbiAgICBwYWRkaW5nLWJvdHRvbTogMXJlbTtcbiAgICBmbGV4LXdyYXA6IHdyYXA7XG4gICAgZ2FwOiAwLjVyZW07XG5cbiAgICAuc3RlcC13cmFwcGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuXG4gICAgICAmLmNsaWNrYWJsZSB7XG4gICAgICAgIGN1cnNvcjogcG9pbnRlcjtcblxuICAgICAgICAuc3RlcC1pdGVtIHtcbiAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSg1OSwgMTMwLCAyNDYsIDAuMTUpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAuc3RlcC1pdGVtIHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgcGFkZGluZzogMC43NXJlbSAxLjI1cmVtO1xuICAgICAgICBib3JkZXItcmFkaXVzOiAwLjVyZW07XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgICAgbWluLXdpZHRoOiAxNDBweDtcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICAgIGdhcDogMC41cmVtO1xuICAgICAgICBib3JkZXI6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcblxuICAgICAgICAuc3RlcC1pY29uIHtcbiAgICAgICAgICBmb250LXNpemU6IDEuMXJlbTtcbiAgICAgICAgICBmbGV4LXNocmluazogMDtcbiAgICAgICAgfVxuXG4gICAgICAgIC5zdGVwLWNvbnRlbnQge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBmbGV4OiAxO1xuXG4gICAgICAgICAgLnN0ZXAtdGV4dCB7XG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgIGdhcDogMC4yNXJlbTtcblxuICAgICAgICAgICAgLnN0ZXAtbnVtYmVyIHtcbiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC5zdGVwLWxhYmVsIHtcbiAgICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjk1cmVtO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC5zdGVwLWNoZWNrLWljb24ge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xuICAgICAgICAgIG1hcmdpbi1sZWZ0OiAwLjI1cmVtO1xuICAgICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gw6fCi8KAw6bChcKLw6bCqMKjw6XCvMKPXG4gICAgICAgICYuYWN0aXZlIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAkc3RlcC1ncmFkaWVudC1wcmltYXJ5O1xuICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoNTksIDEzMCwgMjQ2LCAwLjI1KTtcbiAgICAgICAgICBib3JkZXItY29sb3I6ICRzdGVwLXByaW1hcnk7XG5cbiAgICAgICAgICAuc3RlcC1pY29uLFxuICAgICAgICAgIC5zdGVwLWNoZWNrLWljb24ge1xuICAgICAgICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgICYuY29tcGxldGVkIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAkc3RlcC1ncmFkaWVudC1jb21wbGV0ZWQ7XG4gICAgICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxNiwgMTg1LCAxMjksIDAuMjUpO1xuICAgICAgICAgIGJvcmRlci1jb2xvcjogJHN0ZXAtY29tcGxldGVkO1xuXG4gICAgICAgICAgLnN0ZXAtaWNvbixcbiAgICAgICAgICAuc3RlcC1jaGVjay1pY29uIHtcbiAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAmLnBlbmRpbmcge1xuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICRzdGVwLXBlbmRpbmc7XG4gICAgICAgICAgY29sb3I6ICRzdGVwLXBlbmRpbmctdGV4dDtcbiAgICAgICAgICBib3JkZXItY29sb3I6ICRzdGVwLXBlbmRpbmctYm9yZGVyO1xuXG4gICAgICAgICAgLnN0ZXAtaWNvbiB7XG4gICAgICAgICAgICBjb2xvcjogJHN0ZXAtcGVuZGluZy10ZXh0O1xuICAgICAgICAgIH1cblxuICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI0Y5RkFGQjtcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI0QxRDVEQjtcbiAgICAgICAgICAgIGNvbG9yOiAjNkI3MjgwO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAuc3RlcC1jb25uZWN0b3Ige1xuICAgICAgICB3aWR0aDogMi41cmVtO1xuICAgICAgICBoZWlnaHQ6IDNweDtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJHN0ZXAtcGVuZGluZy1ib3JkZXI7XG4gICAgICAgIG1hcmdpbjogMCAwLjVyZW07XG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDEuNXB4O1xuXG4gICAgICAgICYuY29tcGxldGVkIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAkc3RlcC1ncmFkaWVudC1jb21wbGV0ZWQ7XG4gICAgICAgIH1cblxuICAgICAgICAmLmFjdGl2ZSB7XG4gICAgICAgICAgYmFja2dyb3VuZDogJHN0ZXAtZ3JhZGllbnQtcHJpbWFyeTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyDDqcKfwr/DpsKHwonDpcK8wo/DqMKowq3DqMKowohcbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuc3RlcC1uYXZpZ2F0b3Ige1xuICAgIC5zdGVwLW5hdiB7XG4gICAgICAuc3RlcC13cmFwcGVyIHtcbiAgICAgICAgLnN0ZXAtaXRlbSB7XG4gICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgICAgICAgICBwYWRkaW5nOiAwLjRyZW0gMC44cmVtO1xuICAgICAgICAgIG1pbi13aWR0aDogMTAwcHg7XG5cbiAgICAgICAgICAuc3RlcC1jb250ZW50IHtcbiAgICAgICAgICAgIC5zdGVwLXRleHQge1xuICAgICAgICAgICAgICAuc3RlcC1sYWJlbCB7XG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAuc3RlcC1jb25uZWN0b3Ige1xuICAgICAgICAgIHdpZHRoOiAxLjVyZW07XG4gICAgICAgICAgbWFyZ2luOiAwIDAuMjVyZW07XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7XG4gIC5zdGVwLW5hdmlnYXRvciB7XG4gICAgLnN0ZXAtbmF2IHtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBnYXA6IDFyZW07XG5cbiAgICAgIC5zdGVwLXdyYXBwZXIge1xuICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG5cbiAgICAgICAgLnN0ZXAtY29ubmVjdG9yIHtcbiAgICAgICAgICBkaXNwbGF5OiBub25lO1xuICAgICAgICB9XG5cbiAgICAgICAgLnN0ZXAtaXRlbSB7XG4gICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgbWF4LXdpZHRoOiAyMDBweDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyDDpsK3wrHDqMKJwrLDpMK4wrvDqcKhwozDpsKUwq/DpsKMwoFcbjpob3N0LWNvbnRleHQoLmRhcmstdGhlbWUpIHtcbiAgLnN0ZXAtbmF2aWdhdG9yIHtcbiAgICAuc3RlcC1uYXYge1xuICAgICAgYm9yZGVyLWJvdHRvbS1jb2xvcjogJGRhcmstYm9yZGVyO1xuXG4gICAgICAuc3RlcC13cmFwcGVyIHtcbiAgICAgICAgLnN0ZXAtaXRlbSB7XG4gICAgICAgICAgJi5wZW5kaW5nIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICRkYXJrLWJnLXNlY29uZGFyeTtcbiAgICAgICAgICAgIGNvbG9yOiAkZGFyay10ZXh0LXNlY29uZGFyeTtcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogJGRhcmstYm9yZGVyO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC5zdGVwLWNvbm5lY3RvciB7XG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJGRhcmstYm9yZGVyO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG59XG4iLCIvKipcclxuICogw6fCtcKxw6TCuMKAw6jCicKyw6XCvcKpw6fCs8K7w6fCtcKxIC0gw6nCh8KRw6jCicKyw6TCuMK7w6nCocKMXHJcbiAqIMOlwp/CusOmwpbCvMOkwrjCu8OowonCssOnwrPCuyAjQjhBNjc2IMOowqjCrcOowqjCiMOnwprChMOlwq7CjMOmwpXCtMOowonCssOlwr3CqcOpwqvClMOnwrPCu1xyXG4gKi9cclxuXHJcbi8vID09PT09IMOkwrjCu8OowqbCgcOlwpPCgcOnwonCjMOowonCssOlwr3CqSA9PT09PVxyXG4kcHJpbWFyeS1nb2xkLWxpZ2h0OiAjQjhBNjc2OyAvLyDDpMK4wrvDqMKmwoHDqcKHwpHDqMKJwrIgLSDDpsK3wrrDqMKJwrJcclxuJHByaW1hcnktZ29sZC1iYXNlOiAjQUU5QjY2OyAvLyDDpMK4wrvDqMKmwoHDqcKHwpHDqMKJwrIgLSDDpcKfwrrDp8Kkwo7DqMKJwrJcclxuJHByaW1hcnktZ29sZC1kYXJrOiAjQTY5NjYwOyAvLyDDpMK4wrvDqMKmwoHDqcKHwpHDqMKJwrIgLSDDpsK3wrHDqMKJwrJcclxuJHByaW1hcnktZ29sZC1kYXJrZXI6ICM5QjhBNUE7IC8vIMOkwrjCu8OowqbCgcOpwofCkcOowonCsiAtIMOmwpvCtMOmwrfCsVxyXG4kcHJpbWFyeS1nb2xkLWhvdmVyOiAjQzRCMzgyOyAvLyDDpsKHwrjDpcKBwpzDp8KLwoDDpsKFwotcclxuJHByaW1hcnktZ29sZC1hY3RpdmU6ICNBODk2NjA7IC8vIMOmwrTCu8OlwovClcOnwovCgMOmwoXCi1xyXG4kcHJpbWFyeS1nb2xkLWRpc2FibGVkOiAjRDRDOEE4OyAvLyDDp8KmwoHDp8KUwqjDp8KLwoDDpsKFwotcclxuXHJcbi8vID09PT09IMOowrzClMOlworCqcOpwofCkcOowonCssOowqrCv8OowonCssOmwp3CvyA9PT09PVxyXG4kZ29sZC01MDogI0ZFRkNGODsgLy8gw6bCpcK1w6bCt8Khw6nCh8KRw6jCicKyw6jCg8KMw6bCmcKvXHJcbiRnb2xkLTEwMDogI0Y4RjZGMDsgLy8gw6bCt8Khw6nCh8KRw6jCicKyw6jCg8KMw6bCmcKvXHJcbiRnb2xkLTIwMDogI0YwRURFNTsgLy8gw6bCt8K6w6nCh8KRw6jCicKyw6jCg8KMw6bCmcKvXHJcbiRnb2xkLTMwMDogI0U4RTJENTsgLy8gw6TCuMKtw6bCt8K6w6nCh8KRw6jCicKyXHJcbiRnb2xkLTQwMDogI0Q0QzhBODsgLy8gw6TCuMKtw6nCh8KRw6jCicKyXHJcbiRnb2xkLTUwMDogI0I4QTY3NjsgLy8gw6TCuMK7w6nCh8KRw6jCicKyXHJcbiRnb2xkLTYwMDogI0FFOUI2NjsgLy8gw6bCt8Kxw6nCh8KRw6jCicKyXHJcbiRnb2xkLTcwMDogIzlCOEE1QTsgLy8gw6bCm8K0w6bCt8Kxw6nCh8KRw6jCicKyXHJcbiRnb2xkLTgwMDogIzhBN0E0RjsgLy8gw6bCmsKXw6nCh8KRw6jCicKyXHJcbiRnb2xkLTkwMDogIzZCNUYzRTsgLy8gw6bCnMKAw6bCt8Kxw6nCh8KRw6jCicKyXHJcblxyXG4vLyA9PT09PSDDpsK8wrjDqMKuworDqMKJwrLDpcK9wqkgPT09PT1cclxuJGdyYWRpZW50LXByaW1hcnk6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICRwcmltYXJ5LWdvbGQtbGlnaHQgMCUsICRwcmltYXJ5LWdvbGQtZGFyayAxMDAlKTtcclxuJGdyYWRpZW50LXByaW1hcnktaG92ZXI6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICRwcmltYXJ5LWdvbGQtaG92ZXIgMCUsICRwcmltYXJ5LWdvbGQtYWN0aXZlIDEwMCUpO1xyXG4kZ3JhZGllbnQtcHJpbWFyeS1saWdodDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJGdvbGQtMjAwIDAlLCAkZ29sZC0zMDAgMTAwJSk7XHJcbiRncmFkaWVudC1zZWNvbmRhcnk6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICRnb2xkLTEwMCAwJSwgJGdvbGQtMjAwIDEwMCUpO1xyXG4kZ3JhZGllbnQtYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgJGdvbGQtNTAgMCUsICRnb2xkLTEwMCAxMDAlKTtcclxuXHJcbi8vID09PT09IMOmwpbCh8Olwq3Cl8OowonCssOlwr3CqSA9PT09PVxyXG4kdGV4dC1wcmltYXJ5OiAjMkMzRTUwOyAvLyDDpMK4wrvDqMKmwoHDpsKWwofDpcKtwpdcclxuJHRleHQtc2Vjb25kYXJ5OiAjNUE1QTVBOyAvLyDDpsKswqHDqMKmwoHDpsKWwofDpcKtwpdcclxuJHRleHQtdGVydGlhcnk6ICM2Qzc1N0Q7IC8vIMOowrzClMOlworCqcOmwpbCh8Olwq3Cl1xyXG4kdGV4dC1tdXRlZDogI0FEQjVCRDsgLy8gw6nCncKcw6nCn8Kzw6bClsKHw6XCrcKXXHJcbiR0ZXh0LWRpc2FibGVkOiAjQ0VENERBOyAvLyDDp8KmwoHDp8KUwqjDpsKWwofDpcKtwpdcclxuJHRleHQtbGlnaHQ6ICNGRkZGRkY7IC8vIMOmwrfCusOowonCssOmwpbCh8Olwq3Cl1xyXG4kdGV4dC1kYXJrOiAjMjEyNTI5OyAvLyDDpsK3wrHDqMKJwrLDpsKWwofDpcKtwpdcclxuXHJcbi8vID09PT09IMOowoPCjMOmwpnCr8OowonCssOlwr3CqSA9PT09PVxyXG4kYmctcHJpbWFyeTogI0ZGRkZGRjsgLy8gw6TCuMK7w6jCpsKBw6jCg8KMw6bCmcKvXHJcbiRiZy1zZWNvbmRhcnk6ICNGOEY5RkE7IC8vIMOmwqzCocOowqbCgcOowoPCjMOmwpnCr1xyXG4kYmctdGVydGlhcnk6ICNGNUY1RjU7IC8vIMOnwqzCrMOkwrjCicOowoPCjMOmwpnCr1xyXG4kYmctY3JlYW06ICNGRUZDRjg7IC8vIMOlwqXCtsOmwrLCucOowonCssOowoPCjMOmwpnCr1xyXG4kYmctbGlnaHQtZ29sZDogcmdiYSgxODQsIDE2NiwgMTE4LCAwLjAzKTsgLy8gw6bCpcK1w6bCt8Khw6nCh8KRw6jCicKyw6jCg8KMw6bCmcKvXHJcbiRiZy1ob3ZlcjogcmdiYSgxODQsIDE2NiwgMTE4LCAwLjA1KTsgLy8gw6bCh8K4w6XCgcKcw6jCg8KMw6bCmcKvXHJcbiRiZy1zZWxlY3RlZDogcmdiYSgxODQsIDE2NiwgMTE4LCAwLjE1KTsgLy8gw6nCgcK4w6TCuMKtw6jCg8KMw6bCmcKvXHJcblxyXG4vLyA9PT09PSDDqcKCworDpsKhwobDqMKJwrLDpcK9wqkgPT09PT1cclxuJGJvcmRlci1saWdodDogI0U5RUNFRjsgLy8gw6bCt8K6w6jCicKyw6nCgsKKw6bCocKGXHJcbiRib3JkZXItbWVkaXVtOiAjQ0RDRENEOyAvLyDDpMK4wq3Dp8KtwonDqcKCworDpsKhwoZcclxuJGJvcmRlci1kYXJrOiAjQURCNUJEOyAvLyDDpsK3wrHDqMKJwrLDqcKCworDpsKhwoZcclxuJGJvcmRlci1wcmltYXJ5OiByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMyk7IC8vIMOkwrjCu8OowonCssOpwoLCisOmwqHChlxyXG4kYm9yZGVyLWZvY3VzOiByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuNSk7IC8vIMOnwoTCpsOpwrvCnsOpwoLCisOmwqHChlxyXG5cclxuLy8gPT09PT0gw6fCi8KAw6bChcKLw6jCicKyw6XCvcKpID09PT09XHJcbi8vIMOmwojCkMOlworCn8OnwovCgMOmwoXCi1xyXG4kc3VjY2Vzcy1saWdodDogI0Q0RUREQTtcclxuJHN1Y2Nlc3MtYmFzZTogIzI4QTc0NTtcclxuJHN1Y2Nlc3MtZGFyazogIzFFN0UzNDtcclxuJHN1Y2Nlc3MtZ3JhZGllbnQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNFOEY1RTggMCUsICNENEVEREEgMTAwJSk7XHJcblxyXG4vLyDDqMKtwqbDpcKRworDp8KLwoDDpsKFwotcclxuJHdhcm5pbmctbGlnaHQ6ICNGRkYzQ0Q7XHJcbiR3YXJuaW5nLWJhc2U6ICNGRkMxMDc7XHJcbiR3YXJuaW5nLWRhcms6ICNFMEE4MDA7XHJcbiR3YXJuaW5nLWdyYWRpZW50OiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjRkZGOEUxIDAlLCAjRkZGM0NEIDEwMCUpO1xyXG5cclxuLy8gw6nCjMKvw6jCqsKkw6fCi8KAw6bChcKLXHJcbiRlcnJvci1saWdodDogI0Y4RDdEQTtcclxuJGVycm9yLWJhc2U6ICNEQzM1NDU7XHJcbiRlcnJvci1kYXJrOiAjQzgyMzMzO1xyXG4kZXJyb3ItZ3JhZGllbnQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNGRkVCRUUgMCUsICNGOEQ3REEgMTAwJSk7XHJcblxyXG4vLyDDqMKzwofDqMKoworDp8KLwoDDpsKFwotcclxuJGluZm8tbGlnaHQ6ICNEMUVDRjE7XHJcbiRpbmZvLWJhc2U6ICMxN0EyQjg7XHJcbiRpbmZvLWRhcms6ICMxMzg0OTY7XHJcbiRpbmZvLWdyYWRpZW50OiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjRTNGMkZEIDAlLCAjRDFFQ0YxIDEwMCUpO1xyXG5cclxuLy8gw6nCocKNw6XCpMKWw6bCvMK4w6jCrsKKw6jCicKyw6XCvcKpw6/CvMKIw6XCnMKow6fCi8KAw6bChcKLw6jCicKyw6XCvcKpw6XCrsKaw6fCvsKpw6TCucKLw6XCvsKMw6/CvMKJXHJcbiRncmFkaWVudC1zdWNjZXNzOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAkc3VjY2Vzcy1saWdodCAwJSwgJHN1Y2Nlc3MtYmFzZSAxMDAlKTtcclxuXHJcbi8vID09PT09IMOpwpnCsMOlwr3CscOnwrPCu8OnwrXCsSA9PT09PVxyXG4kc2hhZG93LXNtOiAwIDFweCAzcHggcmdiYSgxODQsIDE2NiwgMTE4LCAwLjEpO1xyXG4kc2hhZG93LW1kOiAwIDJweCA4cHggcmdiYSgxODQsIDE2NiwgMTE4LCAwLjE1KTtcclxuJHNoYWRvdy1sZzogMCA0cHggMTJweCByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMik7XHJcbiRzaGFkb3cteGw6IDAgOHB4IDI0cHggcmdiYSgxODQsIDE2NiwgMTE4LCAwLjI1KTtcclxuXHJcbi8vIMOnwonCucOmwq7CisOpwpnCsMOlwr3CsVxyXG4kc2hhZG93LWZvY3VzOiAwIDAgMCAwLjJyZW0gcmdiYSgxODQsIDE2NiwgMTE4LCAwLjI1KTtcclxuJHNoYWRvdy1pbnNldDogaW5zZXQgMCAxcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyksIGluc2V0IDAgLTFweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuXHJcbi8vID09PT09IMOlwovClcOnwpXCq8OlwpLCjMOpwoHCjsOmwrjCoSA9PT09PVxyXG4kdHJhbnNpdGlvbi1mYXN0OiAwLjE1cyBlYXNlO1xyXG4kdHJhbnNpdGlvbi1ub3JtYWw6IDAuM3MgZWFzZTtcclxuJHRyYW5zaXRpb24tc2xvdzogMC41cyBlYXNlO1xyXG4kdHJhbnNpdGlvbi1ib3VuY2U6IDAuM3MgY3ViaWMtYmV6aWVyKDAuNjgsIC0wLjU1LCAwLjI2NSwgMS41NSk7XHJcblxyXG4vLyA9PT09PSDDqcKCworDpsKhwobDpcKcwpPDqMKnwpLDp8KzwrvDp8K1wrEgPT09PT1cclxuJGJvcmRlci1yYWRpdXMtc206IDAuMjVyZW07IC8vIMOlwrDCj8OlwpzCk8OowqfCklxyXG4kYm9yZGVyLXJhZGl1cy1tZDogMC4zNzVyZW07IC8vIMOkwrjCrcOnwq3CicOlwpzCk8OowqfCklxyXG4kYm9yZGVyLXJhZGl1cy1sZzogMC41cmVtOyAvLyDDpcKkwqfDpcKcwpPDqMKnwpJcclxuJGJvcmRlci1yYWRpdXMteGw6IDAuNzVyZW07IC8vIMOnwonCucOlwqTCp8OlwpzCk8OowqfCklxyXG4kYm9yZGVyLXJhZGl1cy0yeGw6IDFyZW07IC8vIMOowrbChcOlwqTCp8OlwpzCk8OowqfCklxyXG4kYm9yZGVyLXJhZGl1cy1mdWxsOiA1MCU7IC8vIMOlwq7CjMOlwoXCqMOlwpzCk8Olwr3ColxyXG5cclxuLy8gPT09PT0gw6bClsKHw6XCrcKXw6nCmcKww6XCvcKxw6fCs8K7w6fCtcKxID09PT09XHJcbiR0ZXh0LXNoYWRvdy1zbTogMCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuJHRleHQtc2hhZG93LW1kOiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcclxuJHRleHQtc2hhZG93LWxnOiAwIDRweCA4cHggcmdiYSgwLCAwLCAwLCAwLjIpO1xyXG5cclxuLy8gPT09PT0gTmVidWxhciDDpMK4wrvDqcKhwozDqMKJwrLDpcK9wqnDpsKYwqDDpcKwwoQgPT09PT1cclxuLy8gw6TCuMK7w6jCpsKBw6jCicKyw6XCvcKpXHJcbiRuYi1wcmltYXJ5OiAkcHJpbWFyeS1nb2xkLWxpZ2h0O1xyXG4kbmItcHJpbWFyeS1saWdodDogJHByaW1hcnktZ29sZC1ob3ZlcjtcclxuJG5iLXByaW1hcnktZGFyazogJHByaW1hcnktZ29sZC1kYXJrO1xyXG5cclxuLy8gw6bCrMKhw6jCpsKBw6jCicKyw6XCvcKpXHJcbiRuYi1zdWNjZXNzOiAkc3VjY2Vzcy1iYXNlO1xyXG4kbmItc3VjY2Vzcy1saWdodDogJHN1Y2Nlc3MtbGlnaHQ7XHJcbiRuYi1zdWNjZXNzLWRhcms6ICRzdWNjZXNzLWRhcms7XHJcblxyXG4kbmItd2FybmluZzogJHdhcm5pbmctYmFzZTtcclxuJG5iLXdhcm5pbmctbGlnaHQ6ICR3YXJuaW5nLWxpZ2h0O1xyXG4kbmItd2FybmluZy1kYXJrOiAkd2FybmluZy1kYXJrO1xyXG5cclxuJG5iLWRhbmdlcjogJGVycm9yLWJhc2U7XHJcbiRuYi1kYW5nZXItbGlnaHQ6ICRlcnJvci1saWdodDtcclxuJG5iLWRhbmdlci1kYXJrOiAkZXJyb3ItZGFyaztcclxuXHJcbiRuYi1pbmZvOiAkaW5mby1iYXNlO1xyXG4kbmItaW5mby1saWdodDogJGluZm8tbGlnaHQ7XHJcbiRuYi1pbmZvLWRhcms6ICRpbmZvLWRhcms7XHJcblxyXG4vLyDDqMKDwozDpsKZwq/DpcKSwozDpsKWwofDpcKtwpdcclxuJG5iLWJnLXByaW1hcnk6ICRiZy1wcmltYXJ5O1xyXG4kbmItYmctc2Vjb25kYXJ5OiAkYmctc2Vjb25kYXJ5O1xyXG4kbmItdGV4dC1wcmltYXJ5OiAkdGV4dC1wcmltYXJ5O1xyXG4kbmItdGV4dC1zZWNvbmRhcnk6ICR0ZXh0LXNlY29uZGFyeTtcclxuJG5iLXRleHQtaGludDogJHRleHQtbXV0ZWQ7XHJcblxyXG4vLyDDqcKCworDpsKhwoZcclxuJG5iLWJvcmRlci1iYXNpYzogJGJvcmRlci1saWdodDtcclxuJG5iLWJvcmRlci1hbHRlcm5hdGl2ZTogJGJvcmRlci1tZWRpdW07XHJcblxyXG4vLyA9PT09PSDDp8KJwrnDpsKuworDp8KUwqjDqcKAwpTDqMKJwrLDpcK9wqkgPT09PT1cclxuLy8gUmFkaW8gQnV0dG9uIMOlwrDCiMOnwpTCqFxyXG4kcmFkaW8tYmctaG92ZXI6IHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgY2VudGVyLCByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMSkgMCUsIHRyYW5zcGFyZW50IDcwJSk7XHJcbiRyYWRpby1iZy1zZWxlY3RlZDogJGdyYWRpZW50LXByaW1hcnk7XHJcbiRyYWRpby1iZy1zZWxlY3RlZC1ob3ZlcjogJGdyYWRpZW50LXByaW1hcnktaG92ZXI7XHJcbiRyYWRpby1pbm5lci1kb3Q6IHJhZGlhbC1ncmFkaWVudChjaXJjbGUsICR0ZXh0LWxpZ2h0IDAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSkgMTAwJSk7XHJcblxyXG4vLyDDqMKhwqjDpsKgwrzDpcKwwojDp8KUwqhcclxuJHRhYmxlLWhlYWRlci1iZzogJGdyYWRpZW50LXByaW1hcnktbGlnaHQ7XHJcbiR0YWJsZS1yb3ctaG92ZXI6ICRiZy1ob3ZlcjtcclxuJHRhYmxlLXJvdy1zZWxlY3RlZDogJGJnLXNlbGVjdGVkO1xyXG4kdGFibGUtYm9yZGVyOiAkYm9yZGVyLXByaW1hcnk7XHJcblxyXG4vLyDDpcKNwqHDp8KJwofDpcKwwojDp8KUwqhcclxuJGNhcmQtYmc6ICRiZy1wcmltYXJ5O1xyXG4kY2FyZC1oZWFkZXItYmc6ICRncmFkaWVudC1wcmltYXJ5O1xyXG4kY2FyZC1ib3JkZXI6ICRib3JkZXItcHJpbWFyeTtcclxuJGNhcmQtc2hhZG93OiAkc2hhZG93LW1kO1xyXG5cclxuLy8gw6bCjMKJw6nCiMKVw6XCsMKIw6fClMKoXHJcbiRidG4tcHJpbWFyeS1iZzogJGdyYWRpZW50LXByaW1hcnk7XHJcbiRidG4tcHJpbWFyeS1ob3ZlcjogJGdyYWRpZW50LXByaW1hcnktaG92ZXI7XHJcbiRidG4tc2Vjb25kYXJ5LWJnOiAkYmctc2Vjb25kYXJ5O1xyXG4kYnRuLXNlY29uZGFyeS1ob3ZlcjogJGJnLWhvdmVyO1xyXG5cclxuLy8gPT09PT0gw6nCn8K/w6bCh8KJw6XCvMKPw6bClsK3w6nCu8Kew6jCicKyw6XCvcKpw6jCqsK/w6bClcK0ID09PT09XHJcbi8vIMOlwpzCqMOlwrDCj8Oowp7CosOlwrnClcOkwrjCisOkwr3Cv8OnwpTCqMOmwpvCtMOmwp/ClMOlwpLCjMOnwprChMOowonCssOlwr3CqVxyXG4kbW9iaWxlLXByaW1hcnk6IGxpZ2h0ZW4oJHByaW1hcnktZ29sZC1saWdodCwgNSUpO1xyXG4kbW9iaWxlLXNoYWRvdzogcmdiYSgxODQsIDE2NiwgMTE4LCAwLjA4KTtcclxuXHJcbi8vID09PT09IMOmwrfCscOowonCssOkwrjCu8OpwqHCjMOmwpTCr8Omwo/CtCA9PT09PVxyXG4kZGFyay1iZy1wcmltYXJ5OiAjMUExQTFBO1xyXG4kZGFyay1iZy1zZWNvbmRhcnk6ICMyRDJEMkQ7XHJcbiRkYXJrLXRleHQtcHJpbWFyeTogI0ZGRkZGRjtcclxuJGRhcmstdGV4dC1zZWNvbmRhcnk6ICNDQ0NDQ0M7XHJcbiRkYXJrLWJvcmRlcjogIzQwNDA0MDtcclxuXHJcbi8vID09PT09IMOowrzClMOlworCqcOlwofCvcOmwpXCuMOowonCssOlwr3CqSA9PT09PVxyXG4vLyDDqcKAwo/DpsKYwo7DpcK6wqbDqMKuworDpcKMwpZcclxuQGZ1bmN0aW9uIGFscGhhLWdvbGQoJGFscGhhKSB7XHJcbiAgQHJldHVybiByZ2JhKDE4NCwgMTY2LCAxMTgsICRhbHBoYSk7XHJcbn1cclxuXHJcbi8vIMOkwrrCrsOlwrrCpsOowqrCv8OmwpXCtFxyXG5AZnVuY3Rpb24gbGlnaHRlbi1nb2xkKCRhbW91bnQpIHtcclxuICBAcmV0dXJuIGxpZ2h0ZW4oJHByaW1hcnktZ29sZC1saWdodCwgJGFtb3VudCk7XHJcbn1cclxuXHJcbkBmdW5jdGlvbiBkYXJrZW4tZ29sZCgkYW1vdW50KSB7XHJcbiAgQHJldHVybiBkYXJrZW4oJHByaW1hcnktZ29sZC1saWdodCwgJGFtb3VudCk7XHJcbn1cclxuXHJcbi8vID09PT09IMOowonCssOlwr3CqcOpwqnCl8Oowq3CiSA9PT09PVxyXG4vLyDDp8KiwrrDpMK/wp3DqMKJwrLDpcK9wqnDpcKwwo3DpsKvwpTDpcK6wqbDp8KswqbDpcKQwoggV0NBRyDDpsKowpnDpsK6wpZcclxuJGNvbnRyYXN0LXJhdGlvLWFhOiA0LjU7XHJcbiRjb250cmFzdC1yYXRpby1hYWE6IDc7XHJcblxyXG4vLyA9PT09PSDDqMKIworDp8KJwojDp8KbwrjDpcKuwrnDpsKAwqcgPT09PT1cclxuLy8gw6TCv8Kdw6bCjMKBw6XCkMKRw6XCvsKMw6fCm8K4w6XCrsK5w6bCgMKnw6fCmsKEw6jCrsKKw6bClcK4w6bCmMKgw6XCsMKEXHJcbiRtYWluQ29sb3JCOiAkcHJpbWFyeS1nb2xkLWxpZ2h0O1xyXG4kbWFpbkNvbG9yRzogJHN1Y2Nlc3MtYmFzZTtcclxuJG1haW5Db2xvckdyYXk6ICR0ZXh0LXNlY29uZGFyeTtcclxuJHRleHRDb2xvcjogJHRleHQtcHJpbWFyeTtcclxuJG1haW5Db2xvclk6ICR3YXJuaW5nLWJhc2U7XHJcbiRjb2xvci1kcm9wOiAkc2hhZG93LW1kOyJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "NbIconModule", "StepStatus", "i0", "ɵɵelement", "ɵɵproperty", "step_r2", "icon", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpureFunction2", "_c0", "status", "ctx_r2", "COMPLETED", "ACTIVE", "ɵɵlistener", "StepNavigatorComponent_div_2_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onStepClick", "ɵɵtemplate", "StepNavigatorComponent_div_2_nb_icon_2_Template", "StepNavigatorComponent_div_2_span_5_Template", "StepNavigatorComponent_div_2_nb_icon_8_Template", "StepNavigatorComponent_div_2_div_9_Template", "ɵɵclassProp", "isStepClickable", "getStepClasses", "showStepNumber", "ɵɵtextInterpolate", "label", "i_r4", "processedSteps", "length", "StepNavigatorComponent", "constructor", "steps", "currentStep", "allowClickNavigation", "customClass", "step<PERSON>lick", "ngOnChanges", "changes", "processSteps", "map", "step", "index", "PENDING", "clickable", "emit", "classes", "push", "join", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "StepNavigatorComponent_Template", "rf", "ctx", "StepNavigatorComponent_div_2_Template", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "NbIconComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\step-navigator\\step-navigator.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\step-navigator\\step-navigator.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbIconModule } from '@nebular/theme';\nimport { StepConfig, StepStatus } from '../../interfaces/step-config.interface';\n\n/**\n * 步驟導航條共用元件\n * 支持自定義步驟配置和狀態管理\n */\n@Component({\n  selector: 'app-step-navigator',\n  standalone: true,\n  imports: [\n    CommonModule,\n    NbIconModule\n  ],\n  templateUrl: './step-navigator.component.html',\n  styleUrls: ['./step-navigator.component.scss']\n})\nexport class StepNavigatorComponent implements OnChanges {\n  /** 步驟配置數組 */\n  @Input() steps: StepConfig[] = [];\n  \n  /** 當前步驟索引（1-based） */\n  @Input() currentStep: number = 1;\n  \n  /** 是否允許點擊導航到其他步驟 */\n  @Input() allowClickNavigation: boolean = false;\n  \n  /** 是否顯示步驟編號 */\n  @Input() showStepNumber: boolean = true;\n  \n  /** 自定義CSS類名 */\n  @Input() customClass: string = '';\n  \n  /** 步驟點擊事件 */\n  @Output() stepClick = new EventEmitter<number>();\n  \n  /** 暴露枚舉給模板使用 */\n  StepStatus = StepStatus;\n  \n  /** 處理後的步驟數據 */\n  processedSteps: Array<StepConfig & { status: StepStatus; stepNumber: number }> = [];\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes['steps'] || changes['currentStep']) {\n      this.processSteps();\n    }\n  }\n\n  /**\n   * 處理步驟數據，計算每個步驟的狀態\n   */\n  private processSteps(): void {\n    this.processedSteps = this.steps.map((step, index) => {\n      const stepNumber = index + 1;\n      let status: StepStatus;\n      \n      if (stepNumber < this.currentStep) {\n        status = StepStatus.COMPLETED;\n      } else if (stepNumber === this.currentStep) {\n        status = StepStatus.ACTIVE;\n      } else {\n        status = StepStatus.PENDING;\n      }\n      \n      return {\n        ...step,\n        status,\n        stepNumber\n      };\n    });\n  }\n\n  /**\n   * 處理步驟點擊事件\n   * @param stepNumber 步驟編號（1-based）\n   * @param step 步驟配置\n   */\n  onStepClick(stepNumber: number, step: StepConfig): void {\n    // 檢查是否允許點擊導航\n    if (!this.allowClickNavigation) {\n      return;\n    }\n    \n    // 檢查步驟是否可點擊\n    if (step.clickable === false) {\n      return;\n    }\n    \n    // 發出步驟點擊事件\n    this.stepClick.emit(stepNumber);\n  }\n\n  /**\n   * 獲取步驟的CSS類名\n   * @param step 處理後的步驟數據\n   * @returns CSS類名字符串\n   */\n  getStepClasses(step: StepConfig & { status: StepStatus; stepNumber: number }): string {\n    const classes = ['step-item', step.status];\n    \n    // 添加可點擊樣式\n    if (this.allowClickNavigation && step.clickable !== false) {\n      classes.push('clickable');\n    }\n    \n    return classes.join(' ');\n  }\n\n  /**\n   * 檢查步驟是否可點擊\n   * @param step 步驟配置\n   * @returns 是否可點擊\n   */\n  isStepClickable(step: StepConfig): boolean {\n    return this.allowClickNavigation && step.clickable !== false;\n  }\n}\n", "<!-- 步驟導航條 -->\n<div class=\"step-navigator\" [ngClass]=\"customClass\">\n  <div class=\"step-nav\">\n    <div \n      *ngFor=\"let step of processedSteps; let i = index\"\n      class=\"step-wrapper\"\n      [class.clickable]=\"isStepClickable(step)\"\n      (click)=\"onStepClick(step.stepNumber, step)\">\n      \n      <!-- 步驟項目 -->\n      <div [ngClass]=\"getStepClasses(step)\">\n        <!-- 步驟圖標（如果有） -->\n        <nb-icon \n          *ngIf=\"step.icon\" \n          [icon]=\"step.icon\" \n          class=\"step-icon\">\n        </nb-icon>\n        \n        <!-- 步驟內容 -->\n        <div class=\"step-content\">\n          <!-- 步驟編號和標籤 -->\n          <span class=\"step-text\">\n            <span *ngIf=\"showStepNumber\" class=\"step-number\">{{ step.stepNumber }}.</span>\n            <span class=\"step-label\">{{ step.label }}</span>\n          </span>\n        </div>\n        \n        <!-- 完成狀態圖標 -->\n        <nb-icon \n          *ngIf=\"step.status === StepStatus.COMPLETED\" \n          icon=\"checkmark-outline\" \n          class=\"step-check-icon\">\n        </nb-icon>\n      </div>\n      \n      <!-- 步驟連接線（除了最後一個步驟） -->\n      <div \n        *ngIf=\"i < processedSteps.length - 1\" \n        class=\"step-connector\"\n        [ngClass]=\"{\n          'completed': step.status === StepStatus.COMPLETED,\n          'active': step.status === StepStatus.ACTIVE\n        }\">\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAkC,eAAe;AAChG,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAAqBC,UAAU,QAAQ,wCAAwC;;;;;;;;;;ICSvEC,EAAA,CAAAC,SAAA,kBAIU;;;;IAFRD,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAC,IAAA,CAAkB;;;;;IAQhBJ,EAAA,CAAAK,cAAA,eAAiD;IAAAL,EAAA,CAAAM,MAAA,GAAsB;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;IAA7BP,EAAA,CAAAQ,SAAA,EAAsB;IAAtBR,EAAA,CAAAS,kBAAA,KAAAN,OAAA,CAAAO,UAAA,MAAsB;;;;;IAM3EV,EAAA,CAAAC,SAAA,kBAIU;;;;;IAIZD,EAAA,CAAAC,SAAA,cAOM;;;;;IAJJD,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAW,eAAA,IAAAC,GAAA,EAAAT,OAAA,CAAAU,MAAA,KAAAC,MAAA,CAAAf,UAAA,CAAAgB,SAAA,EAAAZ,OAAA,CAAAU,MAAA,KAAAC,MAAA,CAAAf,UAAA,CAAAiB,MAAA,EAGE;;;;;;IAvCNhB,EAAA,CAAAK,cAAA,aAI+C;IAA7CL,EAAA,CAAAiB,UAAA,mBAAAC,2DAAA;MAAA,MAAAf,OAAA,GAAAH,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAU,WAAA,CAAArB,OAAA,CAAAO,UAAA,EAAAP,OAAA,CAAkC;IAAA,EAAC;IAG5CH,EAAA,CAAAK,cAAA,aAAsC;IAEpCL,EAAA,CAAAyB,UAAA,IAAAC,+CAAA,qBAGoB;IAMlB1B,EAFF,CAAAK,cAAA,aAA0B,cAEA;IACtBL,EAAA,CAAAyB,UAAA,IAAAE,4CAAA,kBAAiD;IACjD3B,EAAA,CAAAK,cAAA,cAAyB;IAAAL,EAAA,CAAAM,MAAA,GAAgB;IAE7CN,EAF6C,CAAAO,YAAA,EAAO,EAC3C,EACH;IAGNP,EAAA,CAAAyB,UAAA,IAAAG,+CAAA,sBAG0B;IAE5B5B,EAAA,CAAAO,YAAA,EAAM;IAGNP,EAAA,CAAAyB,UAAA,IAAAI,2CAAA,kBAMK;IAEP7B,EAAA,CAAAO,YAAA,EAAM;;;;;;IAtCJP,EAAA,CAAA8B,WAAA,cAAAhB,MAAA,CAAAiB,eAAA,CAAA5B,OAAA,EAAyC;IAIpCH,EAAA,CAAAQ,SAAA,EAAgC;IAAhCR,EAAA,CAAAE,UAAA,YAAAY,MAAA,CAAAkB,cAAA,CAAA7B,OAAA,EAAgC;IAGhCH,EAAA,CAAAQ,SAAA,EAAe;IAAfR,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAC,IAAA,CAAe;IASPJ,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAE,UAAA,SAAAY,MAAA,CAAAmB,cAAA,CAAoB;IACFjC,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAkC,iBAAA,CAAA/B,OAAA,CAAAgC,KAAA,CAAgB;IAM1CnC,EAAA,CAAAQ,SAAA,EAA0C;IAA1CR,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAU,MAAA,KAAAC,MAAA,CAAAf,UAAA,CAAAgB,SAAA,CAA0C;IAQ5Cf,EAAA,CAAAQ,SAAA,EAAmC;IAAnCR,EAAA,CAAAE,UAAA,SAAAkC,IAAA,GAAAtB,MAAA,CAAAuB,cAAA,CAAAC,MAAA,KAAmC;;;ADhC5C;;;;AAcA,OAAM,MAAOC,sBAAsB;EAVnCC,YAAA;IAWE;IACS,KAAAC,KAAK,GAAiB,EAAE;IAEjC;IACS,KAAAC,WAAW,GAAW,CAAC;IAEhC;IACS,KAAAC,oBAAoB,GAAY,KAAK;IAE9C;IACS,KAAAV,cAAc,GAAY,IAAI;IAEvC;IACS,KAAAW,WAAW,GAAW,EAAE;IAEjC;IACU,KAAAC,SAAS,GAAG,IAAIjD,YAAY,EAAU;IAEhD;IACA,KAAAG,UAAU,GAAGA,UAAU;IAEvB;IACA,KAAAsC,cAAc,GAAmE,EAAE;;EAEnFS,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,OAAO,CAAC,IAAIA,OAAO,CAAC,aAAa,CAAC,EAAE;MAC9C,IAAI,CAACC,YAAY,EAAE;IACrB;EACF;EAEA;;;EAGQA,YAAYA,CAAA;IAClB,IAAI,CAACX,cAAc,GAAG,IAAI,CAACI,KAAK,CAACQ,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;MACnD,MAAMzC,UAAU,GAAGyC,KAAK,GAAG,CAAC;MAC5B,IAAItC,MAAkB;MAEtB,IAAIH,UAAU,GAAG,IAAI,CAACgC,WAAW,EAAE;QACjC7B,MAAM,GAAGd,UAAU,CAACgB,SAAS;MAC/B,CAAC,MAAM,IAAIL,UAAU,KAAK,IAAI,CAACgC,WAAW,EAAE;QAC1C7B,MAAM,GAAGd,UAAU,CAACiB,MAAM;MAC5B,CAAC,MAAM;QACLH,MAAM,GAAGd,UAAU,CAACqD,OAAO;MAC7B;MAEA,OAAO;QACL,GAAGF,IAAI;QACPrC,MAAM;QACNH;OACD;IACH,CAAC,CAAC;EACJ;EAEA;;;;;EAKAc,WAAWA,CAACd,UAAkB,EAAEwC,IAAgB;IAC9C;IACA,IAAI,CAAC,IAAI,CAACP,oBAAoB,EAAE;MAC9B;IACF;IAEA;IACA,IAAIO,IAAI,CAACG,SAAS,KAAK,KAAK,EAAE;MAC5B;IACF;IAEA;IACA,IAAI,CAACR,SAAS,CAACS,IAAI,CAAC5C,UAAU,CAAC;EACjC;EAEA;;;;;EAKAsB,cAAcA,CAACkB,IAA6D;IAC1E,MAAMK,OAAO,GAAG,CAAC,WAAW,EAAEL,IAAI,CAACrC,MAAM,CAAC;IAE1C;IACA,IAAI,IAAI,CAAC8B,oBAAoB,IAAIO,IAAI,CAACG,SAAS,KAAK,KAAK,EAAE;MACzDE,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;IAC3B;IAEA,OAAOD,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;EAC1B;EAEA;;;;;EAKA1B,eAAeA,CAACmB,IAAgB;IAC9B,OAAO,IAAI,CAACP,oBAAoB,IAAIO,IAAI,CAACG,SAAS,KAAK,KAAK;EAC9D;;;uCAlGWd,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAmB,SAAA;MAAAC,MAAA;QAAAlB,KAAA;QAAAC,WAAA;QAAAC,oBAAA;QAAAV,cAAA;QAAAW,WAAA;MAAA;MAAAgB,OAAA;QAAAf,SAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GAAA9D,EAAA,CAAA+D,oBAAA,EAAA/D,EAAA,CAAAgE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjCtE,EADF,CAAAK,cAAA,aAAoD,aAC5B;UACpBL,EAAA,CAAAyB,UAAA,IAAA+C,qCAAA,kBAI+C;UAuCnDxE,EADE,CAAAO,YAAA,EAAM,EACF;;;UA7CsBP,EAAA,CAAAE,UAAA,YAAAqE,GAAA,CAAA3B,WAAA,CAAuB;UAG5B5C,EAAA,CAAAQ,SAAA,GAAmB;UAAnBR,EAAA,CAAAE,UAAA,YAAAqE,GAAA,CAAAlC,cAAA,CAAmB;;;qBDStCxC,YAAY,EAAA4E,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZ9E,YAAY,EAAA+E,EAAA,CAAAC,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}