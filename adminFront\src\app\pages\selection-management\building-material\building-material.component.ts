import { Component, OnInit, TemplateRef, } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbDialogService } from '@nebular/theme';
import { MessageService } from 'src/app/shared/services/message.service';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';
import { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse, GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';
import { finalize, mergeMap, tap } from 'rxjs';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { UtilityService } from 'src/app/shared/services/utility.service';
import * as XLSX from 'xlsx';
import { SharedModule } from '../../components/shared.module';
import { BaseComponent } from '../../components/base/baseComponent';

// 圖片類別枚舉
enum PictureCategory {
  NONE = 0,           // 未選擇
  BUILDING_MATERIAL = 1,  // 建材圖片
  SCHEMATIC = 2       // 示意圖片
}

// 圖片項目介面
interface ImageItem {
  id: number;
  name: string;
  size: number;
  thumbnailUrl?: string;
  fullUrl?: string;
  lastModified?: Date;
}

@Component({
  selector: 'ngx-building-material',
  templateUrl: './building-material.component.html',
  styleUrls: ['./building-material.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule],
})

export class BuildingMaterialComponent extends BaseComponent implements OnInit {
  isNew = true

  materialList: GetMaterialListResponse[]
  selectedMaterial: GetMaterialListResponse

  listBuildCases: BuildCaseGetListReponse[] = []
  selectedBuildCaseId: number

  materialOptions = [
    {
      value: null,
      label: '全部',
    },
    {
      value: false,
      label: '方案',
    },
    {
      value: true,
      label: '選樣',
    }]; materialOptionsId = null;
  CSelectName: string = ""
  // 移除圖片檔名相關欄位
  // CImageCode: string = ""
  // CInfoImageCode: string = ""
  // 啟用建材代號欄位
  CImageCode: string = ""
  ShowPrice: boolean = false
  currentImageShowing: string = ""
  filterMapping: boolean = false
  CIsMapping: boolean = true
  // 圖片綁定相關屬性 - 重構為 picklist 模式
  allImages: ImageItem[] = [] // 所有圖片的原始數據
  availableImages: ImageItem[] = [] // 左側可選擇的圖片
  selectedImages: ImageItem[] = [] // 右側已選擇的圖片
  boundImageIds: number[] = [] // 已綁定的圖片ID
  imageSearchTerm: string = ""
  previewingImage: ImageItem | null = null
  currentPreviewIndex: number = 0

  // 圖片綁定分頁屬性
  imageCurrentPage: number = 1
  imagePageSize: number = 50
  imageTotalRecords: number = 0

  // 類別選項
  categoryOptions = [
    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },
    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }
  ]
  selectedCategory: PictureCategory = PictureCategory.BUILDING_MATERIAL
  isCategorySelected: boolean = true // 預設選擇建材圖片
  // 讓模板可以使用 enum
  PictureCategory = PictureCategory;

  // 狀態選項
  statusOptions = [{
    value: 1, //0停用 1啟用 9刪除
    label: '啟用' //enable
  }, {
    value: 2,
    label: '停用' //Disable
  }];
  // 根據狀態值獲取狀態標籤
  getStatusLabel(status: number): string {
    const option = this.statusOptions.find(opt => opt.value === status);
    return option ? option.label : '未設定';
  }

  constructor(
    private _allow: AllowHelper,
    private dialogService: NbDialogService,
    private message: MessageService,
    private valid: ValidationHelper,
    private _buildCaseService: BuildCaseService,
    private _materialService: MaterialService,
    private _utilityService: UtilityService,
    private _pictureService: PictureService
  ) {
    super(_allow)
  }

  override ngOnInit(): void {
    this.getListBuildCase()
  }

  getListBuildCase() {
    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({
      body: {
        CIsPagi: false,
        CStatus: 1,
      }
    })
      .pipe(
        tap(res => {
          if (res.StatusCode == 0) {
            this.listBuildCases = res.Entries?.length ? res.Entries : []
            this.selectedBuildCaseId = this.listBuildCases[0].cID!
          }
        }),
        mergeMap(() => this.getMaterialList())
      ).subscribe()
  } getMaterialList(pageIndex: number = 1) {
    return this._materialService.apiMaterialGetMaterialListPost$Json({
      body: {
        CBuildCaseId: this.selectedBuildCaseId,
        CPlanUse: this.materialOptionsId,
        CSelectName: this.CSelectName,
        // 啟用建材代號查詢條件
        CImageCode: this.CImageCode,
        // CInfoImageCode: this.CInfoImageCode,
        PageSize: this.pageSize,
        PageIndex: pageIndex,
        CIsMapping: this.CIsMapping
      }
    }).pipe(
      tap(res => {
        if (res.StatusCode == 0) {
          this.materialList = res.Entries! ?? []
          this.totalRecords = res.TotalItems!

          if (this.materialList.length > 0) {
            this.ShowPrice = this.materialList[0].CShowPrice!;
          }
        }
      })
    )
  }

  search() {
    this.getMaterialList().subscribe()
  }

  pageChanged(pageIndex: number) {
    this.getMaterialList(pageIndex).subscribe()
  }

  exportExelMaterialList() {
    this._materialService.apiMaterialExportExcelMaterialListPost$Json({
      body: this.selectedBuildCaseId
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        if (res.Entries!.FileByte) {
          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')
        }
      }
    })
  }

  exportExelMaterialTemplate() {
    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({
      body: this.selectedBuildCaseId
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        if (res.Entries!.FileByte) {
          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')
        }
      }
    })
  }
  addNew(ref: any) {
    this.isNew = true
    this.selectedMaterial = {
      CStatus: 1, // 預設為啟用狀態
      CPrice: 0   // 預設價格為0
    }
    this.dialogService.open(ref)
  }
  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {
    this.isNew = false
    this.selectedMaterial = { ...data }
    this.dialogService.open(ref)
  }
  bindImageForMaterial(data: GetMaterialListResponse, ref: TemplateRef<any>) {
    this.selectedMaterial = { ...data }
    // 設定已綁定的圖片ID
    this.boundImageIds = this.selectedMaterial.CSelectPictureId ?
      this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id) : []

    // 重置選擇狀態和分頁
    this.selectedImages = []
    this.imageCurrentPage = 1
    this.imageSearchTerm = ""

    this.loadImages()
    this.dialogService.open(ref, { closeOnBackdropClick: false })
  } validation() {
    this.valid.clear();
    this.valid.required('[名稱]', this.selectedMaterial.CName)
    this.valid.required('[項目]', this.selectedMaterial.CPart)
    this.valid.required('[位置]', this.selectedMaterial.CLocation)
    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)
    // 啟用建材代號驗證
    this.valid.required('[建材代號]', this.selectedMaterial.CImageCode)
    this.valid.required('[狀態]', this.selectedMaterial.CStatus)
    this.valid.required('[價格]', this.selectedMaterial.CPrice)
    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30)
    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30)
    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30)
    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)
    // 啟用建材代號長度驗證
    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CImageCode, 30)
    // 價格驗證：必須為數字且大於等於0
    if (this.selectedMaterial.CPrice !== undefined && this.selectedMaterial.CPrice !== null) {
      if (this.selectedMaterial.CPrice < 0) {
        this.valid.errorMessages.push('[價格] 不能小於0')
      }
    }
  }

  onSubmit(ref: any) {
    this.validation()
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return
    } this._materialService.apiMaterialSaveMaterialAdminPost$Json({
      body: {
        CBuildCaseId: this.selectedBuildCaseId,
        // 暫時保留 CImageCode 給圖片綁定功能使用
        CImageCode: this.selectedMaterial.CImageCode,
        CName: this.selectedMaterial.CName,
        CPart: this.selectedMaterial.CPart,
        CLocation: this.selectedMaterial.CLocation,
        CSelectName: this.selectedMaterial.CSelectName,
        CDescription: this.selectedMaterial.CDescription,
        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,
        CPrice: this.selectedMaterial.CPrice,
        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位
        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列
      }
    })
      .pipe(
        tap(res => {
          if (res.StatusCode === 0) {
            this.message.showSucessMSG("執行成功");
          } else {
            this.message.showErrorMSG(res.Message!);
          }
        }),
        mergeMap(() => this.getMaterialList()),
        finalize(() => ref.close())
      ).subscribe()
  }

  onClose(ref: any) {
    ref.close();
  }

  detectFileExcel(event: any) {
    const target: DataTransfer = <DataTransfer>(event.target);
    const reader: FileReader = new FileReader();
    reader.readAsBinaryString(target.files[0]);
    reader.onload = (e: any) => {
      const binarystr: string = e.target.result;
      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });

      const wsname: string = wb.SheetNames[0];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];

      let isValidFile: boolean = true;
      const data = XLSX.utils.sheet_to_json(ws);
      if (data && data.length > 0) {
        data.forEach((x: any) => {
          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'])) {
            isValidFile = false;
          }
        })

        if (!isValidFile) {
          this.message.showErrorMSG("导入文件时出现错误")
        } else {
          this._materialService.apiMaterialImportExcelMaterialListPost$Json({
            body: {
              CBuildCaseId: this.selectedBuildCaseId,
              CFile: target.files[0]
            }
          }).pipe(
            tap(res => {
              if (res.StatusCode == 0) {
                this.message.showSucessMSG("執行成功")
              } else {
                this.message.showErrorMSG(res.Message!)
              }
            }),
            mergeMap(() => this.getMaterialList(1))
          ).subscribe();
        }
      } else {
        this.message.showErrorMSG("匯入的檔案內容為空，請檢查檔案並重新上傳。")
      }
      event.target.value = null;
    };
  }

  showImage(imageUrl: string, dialog: TemplateRef<any>) {
    this.currentImageShowing = imageUrl;
    this.dialogService.open(dialog);
  }
  changeFilter() {
    if (this.filterMapping) {
      this.CIsMapping = false;
      this.getMaterialList().subscribe();
    }
    else {
      this.CIsMapping = true;
      this.getMaterialList().subscribe();
    }
  }
  // 圖片綁定功能方法
  openImageBinder(ref: TemplateRef<any>) {
    // 重置選擇狀態和分頁
    this.selectedImages = []
    this.imageCurrentPage = 1
    this.imageSearchTerm = ""

    this.loadImages();
    this.dialogService.open(ref, { closeOnBackdropClick: false });
  }

  loadImages() {
    // 使用 PictureService API 載入圖片列表
    if (this.isCategorySelected && this.selectedBuildCaseId) {
      this._pictureService.apiPictureGetPictureListPost$Json({
        body: {
          CBuildCaseId: this.selectedBuildCaseId,
          cPictureType: this.selectedCategory,
          PageIndex: this.imageCurrentPage,
          PageSize: this.imagePageSize
        }
      }).subscribe((res: GetPictureListResponseListResponseBase) => {
        if (res.StatusCode === 0) {
          // 將 API 回應轉換為 ImageItem 格式
          this.allImages = res.Entries?.map((picture: GetPictureListResponse) => ({
            id: picture.CId || 0,
            name: picture.CPictureCode || picture.CName || '',
            size: 0,
            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',
            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',
            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()
          })) || [];

          this.imageTotalRecords = res.TotalItems || 0;

          // 只在第一次開啟對話框時初始化已選擇的圖片（已綁定的圖片）
          // 分頁變更時不重置已選擇的圖片
          if (this.selectedImages.length === 0 && this.boundImageIds.length > 0) {
            this.loadAllImagesForInitialSelection();
          } else {
            // 更新可選擇的圖片（排除已選擇的）
            this.updateAvailableImages();
          }
        } else {
          this.message.showErrorMSG(res.Message || '載入圖片失敗');
          this.allImages = [];
          this.availableImages = [];
          // 只在第一次載入錯誤時清空已選圖片
          if (this.selectedImages.length === 0) {
            this.selectedImages = [];
          }
        }
      });
    } else {
      // 如果沒有選擇類別或建案，清空圖片列表
      this.allImages = [];
      this.availableImages = [];
      this.selectedImages = [];
    }
  }



  // 載入所有圖片以進行初始選擇（僅用於已綁定圖片的初始化）
  loadAllImagesForInitialSelection() {
    // 為了初始化已綁定的圖片，我們需要載入所有圖片
    // 這裡使用一個較大的 PageSize 來獲取所有圖片
    this._pictureService.apiPictureGetPictureListPost$Json({
      body: {
        CBuildCaseId: this.selectedBuildCaseId,
        cPictureType: this.selectedCategory,
        PageIndex: 1,
        PageSize: 9999 // 使用大數字獲取所有圖片
      }
    }).subscribe((res: GetPictureListResponseListResponseBase) => {
      if (res.StatusCode === 0) {
        const allAvailableImages = res.Entries?.map((picture: GetPictureListResponse) => ({
          id: picture.CId || 0,
          name: picture.CPictureCode || picture.CName || '',
          size: 0,
          thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',
          fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',
          lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()
        })) || [];

        // 從所有圖片中找出已綁定的圖片
        const boundImages = allAvailableImages.filter(image => this.boundImageIds.includes(image.id));
        this.selectedImages = [...boundImages];

        // 更新可選擇的圖片
        this.updateAvailableImages();
      }
    });
  }

  // 新增 picklist 相關方法
  updateAvailableImages() {
    // 使用當前 API 回傳的圖片作為可選圖片基礎
    let currentPageImages = [...this.allImages];

    // 根據搜尋條件篩選當前分頁圖片
    if (this.imageSearchTerm.trim()) {
      const searchTerm = this.imageSearchTerm.toLowerCase();
      currentPageImages = currentPageImages.filter(image =>
        image.name.toLowerCase().includes(searchTerm)
      );
    }

    // 排除已選擇的圖片
    const selectedIds = this.selectedImages.map(img => img.id);
    this.availableImages = currentPageImages.filter(image => !selectedIds.includes(image.id));
  }

  filterAvailableImages() {
    this.updateAvailableImages();
  }

  moveToSelected(image: ImageItem, event?: Event) {
    if (event) {
      event.stopPropagation();
    }

    // 將圖片從可選移到已選
    const index = this.availableImages.findIndex(img => img.id === image.id);
    if (index > -1) {
      this.selectedImages.push(image);
      this.updateAvailableImages();
    }
  }

  moveToAvailable(image: ImageItem, event?: Event) {
    if (event) {
      event.stopPropagation();
    }

    // 將圖片從已選移到可選（包括已綁定的圖片也可以取消）
    const index = this.selectedImages.findIndex(img => img.id === image.id);
    if (index > -1) {
      this.selectedImages.splice(index, 1);
      this.updateAvailableImages();
    }
  }

  moveAllToSelected() {
    // 將所有可選圖片移到已選
    this.selectedImages.push(...this.availableImages);
    this.updateAvailableImages();
  }

  moveAllToAvailable() {
    // 將所有已選圖片移到可選（包括已綁定的圖片）
    this.selectedImages = [];
    this.updateAvailableImages();
  }

  isImageBound(image: ImageItem): boolean {
    return this.boundImageIds.includes(image.id);
  }

  getBoundImagesCount(): number {
    return this.selectedImages.filter(image => this.isImageBound(image)).length;
  }

  getNewSelectedCount(): number {
    return this.selectedImages.filter(image => !this.isImageBound(image)).length;
  }

  // 清除所有選擇（包括已綁定的圖片）
  clearAllSelection() {
    this.selectedImages = [];
    this.updateAvailableImages();
  }

  previewImage(image: ImageItem, imagePreviewRef: TemplateRef<any>, event: Event) {
    event.stopPropagation();
    this.previewingImage = image;
    // 在所有圖片中找到當前預覽圖片的索引
    this.currentPreviewIndex = this.allImages.findIndex((img: ImageItem) => img.id === image.id);
    this.dialogService.open(imagePreviewRef);
  }

  previousImage() {
    if (this.currentPreviewIndex > 0) {
      this.currentPreviewIndex--;
      this.previewingImage = this.allImages[this.currentPreviewIndex];
    }
  }

  nextImage() {
    if (this.currentPreviewIndex < this.allImages.length - 1) {
      this.currentPreviewIndex++;
      this.previewingImage = this.allImages[this.currentPreviewIndex];
    }
  }

  toggleImageSelectionInPreview() {
    if (this.previewingImage) {
      const isSelected = this.selectedImages.some(img => img.id === this.previewingImage!.id);
      if (isSelected) {
        this.moveToAvailable(this.previewingImage);
      } else {
        this.moveToSelected(this.previewingImage);
      }
    }
  }

  isImageSelected(image: ImageItem): boolean {
    return this.selectedImages.some(selected => selected.id === image.id);
  }
  onConfirmImageSelection(ref: any) {
    if (this.selectedImages.length > 0) {
      // 收集選中圖片的 ID
      const selectedImageIds = this.selectedImages.map(img => img.id);      // 如果只選取一張圖片，直接設定圖片 ID
      if (this.selectedImages.length === 1) {
        // 設定第一張圖片的 ID 到 CPictureId（單一數字）
        this.selectedMaterial.CPictureId = this.selectedImages[0].id;
      } else {
        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片
        const imageNames = this.selectedImages.map(img => img.name).join(', ');
        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);
        // 設定第一張圖片的 ID 到 CPictureId（單一數字）
        this.selectedMaterial.CPictureId = this.selectedImages[0].id;
      }

      // 暫存所有選中的圖片 ID，供 API 呼叫使用
      (this.selectedMaterial as any).selectedImageIds = selectedImageIds;

      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫
      if (this.selectedMaterial.CId) {
        this.saveImageBinding();
      }
    }

    this.clearAllSelection();
    ref.close();
  }  // 新增方法：保存圖片綁定
  saveImageBinding() {
    this._materialService.apiMaterialSaveMaterialAdminPost$Json({
      body: {
        CBuildCaseId: this.selectedBuildCaseId,
        CImageCode: this.selectedMaterial.CImageCode,
        CName: this.selectedMaterial.CName,
        CPart: this.selectedMaterial.CPart,
        CLocation: this.selectedMaterial.CLocation,
        CSelectName: this.selectedMaterial.CSelectName,
        CDescription: this.selectedMaterial.CDescription,
        CMaterialId: this.selectedMaterial.CId!,
        CPrice: this.selectedMaterial.CPrice,
        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位
        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列
      }
    }).pipe(
      tap(res => {
        if (res.StatusCode === 0) {
          this.message.showSucessMSG(`圖片綁定成功`);
        } else {
          this.message.showErrorMSG(res.Message!);
        }
      }),
      mergeMap(() => this.getMaterialList()),
      finalize(() => {
        // 清空選取的建材
        this.selectedMaterial = {};
      })
    ).subscribe()
  }
  onCloseImageBinder(ref: any) {
    this.clearAllSelection();
    this.imageSearchTerm = "";
    this.imageCurrentPage = 1; // 重設圖片頁碼
    ref.close();
  }// 類別變更處理方法
  categoryChanged(category: PictureCategory) {
    this.selectedCategory = category;
    this.isCategorySelected = true;
    // 當類別變更時重設頁碼並重新載入圖片
    this.imageCurrentPage = 1;
    if (this.selectedBuildCaseId) {
      this.loadImages();
    }
  }

  // 獲取類別標籤的方法
  getCategoryLabel(category: number): string {
    const option = this.categoryOptions.find(opt => opt.value === category);
    return option ? option.label : '未知類別';
  }

  // 圖片分頁變更處理方法
  imagePageChanged(page: number) {
    this.imageCurrentPage = page;
    this.loadImages();
  }
}
