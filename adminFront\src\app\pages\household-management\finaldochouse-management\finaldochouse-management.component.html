<nb-card accent="success">
  <nb-card-header>
    <!-- <ngx-breadcrumb></ngx-breadcrumb> -->
    <div style="font-size: 32px;">戶別管理 / 簽署文件歷程</div>
  </nb-card-header>
  <nb-card-body class="bg-white">
    <div class="col-12">
      <div class="row">
        <div class="flex form-group col-12 col-md-9 text-right">
          <span for="date-select1" class="mr-3 mt-2">
            建立時間
          </span>
          <p-calendar [appendTo]="'body'" placeholder='年/月/日' dateFormat="yy/mm/dd"
            [(ngModel)]="getListFinalDocRequest.CDateStart" [maxDate]="maxDate"></p-calendar>
          <span for="date-select1" class="mr-1 ml-1 mt-2">~</span>
          <p-calendar [appendTo]="'body'" placeholder='年/月/日' dateFormat="yy/mm/dd"
            [(ngModel)]="getListFinalDocRequest.CDateEnd" [maxDate]="maxDate"></p-calendar>
        </div>
        <div class="form-group col-12 col-md-3 text-right">
          <button class="btn btn-info mr-2" (click)="getList()"><i class="fas fa-search mr-1"></i>查詢</button>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <div class="d-flex justify-content-end w-full">
            <button class="btn btn-info" (click)="addNew(dialogUploadFinaldoc)">
              新增文檔
            </button>
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>
  <nb-card-body class="bg-white">
    <div class="col-12">
      <div class="table-responsive">
        <table class="table" style="min-width: 800px;">
          <thead class="table-header">
            <tr class="d-flex">
              <th scope="col" class="col-5">文件名稱</th>
              <th scope="col" class="col-4">客戶簽名時間</th>
              <th scope="col" class="col-3">連結</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of listFinalDoc; let i = index" class="d-flex">
              <td class="col-5">{{ data.CDocumentName }}</td>
              <td class="col-4">{{ data.CSignDate }}</td>
              <td class="col-3">
                <button class="btn btn-outline-primary btn-sm m-1" (click)="openPdfInNewTab(data)">連結</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <ngx-pagination [CollectionSize]="totalRecords" [(Page)]="pageIndex" [PageSize]="pageSize"
        (PageChange)="getList()">
      </ngx-pagination>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="d-flex justify-content-center">
      <button class="btn btn-secondary btn-sm" (click)="goBack()">
        返回上一頁
      </button>
    </div>
  </nb-card-footer>
</nb-card>

<ng-template #dialogUploadFinaldoc let-dialog let-ref="dialogRef">
  <nb-card style="width:1000px; max-height: 95vh">
    <nb-card-header>
      戶別管理 > 簽署文件歷程 > {{houseByID.CHousehold}} {{houseByID.CFloor}}F
    </nb-card-header>
    <nb-card-body class="px-4">
      <div class="form-group d-flex align-items-baseline">
        <div class="d-flex flex-col col-3">
          <label for="file" class="required-field align-self-start" style="min-width:100px; position: static;"
            baseLabel>文件
          </label>
        </div>
        <div class="flex flex-col items-start space-y-4">
          <input type="file" id="fileInput" accept="image/jpeg, image/jpg, application/pdf" class="hidden"
            style="display: none" (change)="onFileSelected($event)">
          <label for="fileInput"
            class="cursor-pointer bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            <i class="fa-solid fa-cloud-arrow-up mr-2"></i> 上傳
          </label>
          <div class="flex items-center space-x-2" *ngIf="fileName">
            <span class="text-gray-600">{{ fileName }}</span>
            <button type="button" (click)="clearFile()" class="text-red-500 hover:text-red-700">
              <i class="fa-solid fa-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="CDocumentName" class="required-field align-self-start col-3" style="min-width:75px" baseLabel>
          文件名稱
        </label>
        <input type="text" class="w-full" nbInput placeholder="文件名稱" [(ngModel)]="CDocumentName" />
      </div>

      <div class="form-group d-flex align-items-center">
        <label for="remark" baseLabel class="required-field align-self-start col-3">送審資訊
          <p style="color: red">內部審核人員查看</p>
        </label>

        <textarea name="remark" id="remark" rows="5" nbInput style="resize: none; max-width: none" class="w-full"
          [(ngModel)]="CApproveRemark">
        </textarea>
      </div>

      <div class="form-group d-flex align-items-center">
        <label for="CNote" baseLabel class="required-field align-self-start col-3">摘要註記
          <p style="color: red">客戶於文件中查看</p>
        </label>
        <textarea name="CNote" id="CNote" rows="5" nbInput style="resize: none; max-width: none" class="w-full"
          [(ngModel)]="CNote">
        </textarea>
      </div>
    </nb-card-body>
    <nb-card-footer class="d-flex justify-content-center">
      <button class="btn btn-outline-secondary m-2" (click)="ref.close()">取消</button>
      <button class="btn btn-success m-2" (click)="onCreateFinalDoc(ref)">確認送出審核</button>
    </nb-card-footer>
  </nb-card>
</ng-template>