{"ast": null, "code": "import { of as observableOf, BehaviorSubject } from 'rxjs';\nimport { takeWhile } from 'rxjs/operators';\nimport { NbLayoutDirection } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nexport let StateService = /*#__PURE__*/(() => {\n  class StateService {\n    constructor(directionService) {\n      this.layouts = [{\n        name: 'One Column',\n        icon: 'nb-layout-default',\n        id: 'one-column',\n        selected: true\n      }, {\n        name: 'Two Column',\n        icon: 'nb-layout-two-column',\n        id: 'two-column'\n      }, {\n        name: 'Center Column',\n        icon: 'nb-layout-centre',\n        id: 'center-column'\n      }];\n      this.sidebars = [{\n        name: 'Sidebar at layout start',\n        icon: 'nb-layout-sidebar-left',\n        id: 'start',\n        selected: true\n      }, {\n        name: 'Sidebar at layout end',\n        icon: 'nb-layout-sidebar-right',\n        id: 'end'\n      }];\n      this.layoutState$ = new BehaviorSubject(this.layouts[0]);\n      this.sidebarState$ = new BehaviorSubject(this.sidebars[0]);\n      this.alive = true;\n      directionService.onDirectionChange().pipe(takeWhile(() => this.alive)).subscribe(direction => this.updateSidebarIcons(direction));\n      this.updateSidebarIcons(directionService.getDirection());\n    }\n    ngOnDestroy() {\n      this.alive = false;\n    }\n    updateSidebarIcons(direction) {\n      const [startSidebar, endSidebar] = this.sidebars;\n      const isLtr = direction === NbLayoutDirection.LTR;\n      const startIconClass = isLtr ? 'nb-layout-sidebar-left' : 'nb-layout-sidebar-right';\n      const endIconClass = isLtr ? 'nb-layout-sidebar-right' : 'nb-layout-sidebar-left';\n      startSidebar.icon = startIconClass;\n      endSidebar.icon = endIconClass;\n    }\n    setLayoutState(state) {\n      this.layoutState$.next(state);\n    }\n    getLayoutStates() {\n      return observableOf(this.layouts);\n    }\n    onLayoutState() {\n      return this.layoutState$.asObservable();\n    }\n    setSidebarState(state) {\n      this.sidebarState$.next(state);\n    }\n    getSidebarStates() {\n      return observableOf(this.sidebars);\n    }\n    onSidebarState() {\n      return this.sidebarState$.asObservable();\n    }\n    static {\n      this.ɵfac = function StateService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || StateService)(i0.ɵɵinject(i1.NbLayoutDirectionService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: StateService,\n        factory: StateService.ɵfac\n      });\n    }\n  }\n  return StateService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}