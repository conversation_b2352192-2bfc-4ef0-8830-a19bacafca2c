{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nlet TemplateViewerComponent = class TemplateViewerComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.availableData = []; // 父元件傳入的可選資料 (包含 CGroupName, CReleateName, CReleateId)\n    this.templateType = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\n    this.showOnlyAddForm = false; // 是否只顯示新增模板表單\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 公開Math對象供模板使用\n    this.Math = Math;\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = []; // 保留用於向後相容\n    this.selectedTemplate = null;\n    // 新的詳情資料管理\n    this.currentTemplateDetailsData = [];\n    this.detailSearchKeyword = '';\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 分頁功能 - 模板列表\n    this.templatePagination = {\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedTemplates = [];\n    // 分頁功能 - 模板詳情\n    this.detailPagination = {\n      currentPage: 1,\n      pageSize: 5,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedDetails = [];\n    // 新增模板表單\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      groupName: '',\n      // 新增群組名稱欄位\n      selectedItems: []\n    };\n  }\n  ngOnInit() {\n    // 只有在非純新增模式下才載入模板列表\n    if (!this.showOnlyAddForm) {\n      this.loadTemplates();\n      this.updateFilteredTemplates();\n    }\n    // 如果只顯示新增表單，自動打開新增模板表單\n    if (this.showOnlyAddForm) {\n      this.onAddTemplate();\n    }\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 載入模板列表 - 使用真實的 API 調用\n  loadTemplates() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      PageIndex: 1,\n      // 暫時載入第一頁\n      PageSize: 100,\n      // 暫時載入較多資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateList API\n    this.templateService.apiTemplateGetTemplateListPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // API 調用成功\n          // 轉換 API 回應為內部格式\n          this.templates = response.Entries.map(item => ({\n            TemplateID: item.CTemplateId,\n            TemplateName: item.CTemplateName || '',\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\n          }));\n          // 更新過濾列表\n          this.updateFilteredTemplates();\n          // 清空詳情資料，改為按需載入\n          this.templateDetails = [];\n          this.currentTemplateDetailsData = [];\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n          this.templateDetails = [];\n          this.updateFilteredTemplates();\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        this.templates = [];\n        this.templateDetails = [];\n        this.updateFilteredTemplates();\n      }\n    });\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n    this.updateTemplatePagination();\n  }\n  // 更新模板分頁\n  updateTemplatePagination() {\n    this.templatePagination.totalItems = this.filteredTemplates.length;\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\n    // 確保當前頁面不超過總頁數\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\n    }\n    this.updatePaginatedTemplates();\n  }\n  // 更新分頁後的模板列表\n  updatePaginatedTemplates() {\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\n    const endIndex = startIndex + this.templatePagination.pageSize;\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\n  }\n  // 模板分頁導航\n  goToTemplatePage(page) {\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = page;\n      this.updatePaginatedTemplates();\n    }\n  }\n  // 獲取模板分頁頁碼數組\n  getTemplatePageNumbers() {\n    const pages = [];\n    const totalPages = this.templatePagination.totalPages;\n    const currentPage = this.templatePagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 顯示新增模板表單\n  onAddTemplate() {\n    this.showAddForm = true;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      groupName: '',\n      selectedItems: []\n    };\n    // 如果不是只顯示新增表單模式，才重置選擇狀態\n    // 在只顯示新增表單模式下，保持父元件傳入的選中狀態\n    if (!this.showOnlyAddForm) {\n      this.availableData.forEach(item => item.selected = false);\n    }\n  }\n  // 取消新增模板\n  cancelAddTemplate() {\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      groupName: '',\n      selectedItems: []\n    };\n  }\n  // 儲存新模板\n  saveNewTemplate() {\n    if (!this.newTemplate.name.trim()) {\n      return;\n    }\n    const selectedItems = this.availableData.filter(item => item.selected).map(item => ({\n      CGroupName: item.CGroupName || null,\n      CReleateName: item.CReleateName || item.CRequirement || item.name || null,\n      CReleateId: item.CReleateId || item.CRequirementID || item.ID || item.id || 0\n    }));\n    if (selectedItems.length === 0) {\n      return;\n    }\n    // TODO: 替換為實際的API調用\n    this.createTemplate(this.newTemplate.name, selectedItems);\n  }\n  // 創建模板 - 使用真實的 API 調用\n  createTemplate(name, selectedItems) {\n    // 準備 API 請求資料\n    const saveTemplateArgs = {\n      CTemplateId: null,\n      // 新增時為 null\n      CTemplateName: name.trim(),\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      CStatus: 1,\n      // 啟用狀態\n      Details: selectedItems.map(item => ({\n        CTemplateDetailId: null,\n        // 新增時為 null\n        CReleateId: item.CReleateId,\n        // 關聯主檔ID\n        CReleateName: item.CReleateName,\n        // 關聯名稱\n        CGroupName: item.CGroupName // 群組名稱\n      }))\n    };\n    // 調用 SaveTemplate API\n    this.templateService.apiTemplateSaveTemplatePost$Json({\n      body: saveTemplateArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          // 只有在非純新增模式下才重新載入模板列表\n          if (!this.showOnlyAddForm) {\n            this.loadTemplates();\n          }\n          // 關閉表單\n          this.showAddForm = false;\n          // 如果是純新增模式，發出關閉事件通知父組件關閉 dialog\n          if (this.showOnlyAddForm) {\n            this.close.emit();\n          }\n        } else {\n          // API 返回錯誤\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n      }\n    });\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n    // 按需載入模板詳情\n    if (template.TemplateID) {\n      this.loadTemplateDetails(template.TemplateID);\n    }\n    this.updateDetailPagination();\n  }\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\n  loadTemplateDetails(templateId, pageIndex = 1, searchKeyword) {\n    const args = {\n      templateId: templateId\n    };\n    console.log('GetTemplateDetailById API 調用:', args);\n    // 調用真實的 GetTemplateDetailById API\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          console.log('模板詳情載入成功:', response);\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\n          let allDetails = response.Entries.map(item => ({\n            CTemplateDetailId: item.CTemplateDetailId || 0,\n            CTemplateId: item.CTemplateId || templateId,\n            CReleateId: item.CReleateId || 0,\n            CReleateName: item.CReleateName || '',\n            CGroupName: item.CGroupName || '',\n            // 新增群組名稱欄位\n            CSort: undefined,\n            CRemark: undefined,\n            CCreateDt: new Date().toISOString(),\n            CCreator: '系統',\n            CCategory: undefined,\n            CUnitPrice: undefined,\n            CQuantity: undefined,\n            CUnit: undefined\n          }));\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\n          if (searchKeyword && searchKeyword.trim()) {\n            allDetails = allDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()));\n          }\n          // 前端分頁處理 (因為 API 不支援分頁參數)\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n          const endIndex = startIndex + this.detailPagination.pageSize;\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\n          // 更新詳情資料和分頁資訊\n          this.currentTemplateDetailsData = pagedDetails;\n          this.detailPagination.totalItems = allDetails.length;\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\n          this.detailPagination.currentPage = pageIndex;\n        } else {\n          console.error('模板詳情載入失敗:', response.Message);\n          this.currentTemplateDetailsData = [];\n          this.detailPagination.totalItems = 0;\n          this.detailPagination.totalPages = 0;\n          this.detailPagination.currentPage = 1;\n        }\n      },\n      error: error => {\n        console.error('GetTemplateDetailById API 調用失敗:', error);\n        this.currentTemplateDetailsData = [];\n        this.detailPagination.totalItems = 0;\n        this.detailPagination.totalPages = 0;\n        this.detailPagination.currentPage = 1;\n        // 如果 API 失敗，回退到模擬資料\n        console.log('回退到模擬資料');\n        this.loadTemplateDetailsMock(templateId, pageIndex, searchKeyword);\n      }\n    });\n  }\n  // 模擬載入模板詳情 (暫時使用，等待後端 API)\n  loadTemplateDetailsMock(templateId, pageIndex = 1, searchKeyword) {\n    // 模擬 API 延遲\n    setTimeout(() => {\n      // 生成模擬詳情資料\n      const mockDetails = [];\n      const itemsPerTemplate = 8; // 每個模板8個項目\n      for (let i = 1; i <= itemsPerTemplate; i++) {\n        const detail = {\n          CTemplateDetailId: templateId * 100 + i,\n          CTemplateId: templateId,\n          CReleateId: templateId * 1000 + i,\n          CReleateName: `工程項目${String.fromCharCode(64 + templateId % 26 + 1)}-${i}`,\n          CGroupName: i % 4 === 0 ? '結構工程' : i % 4 === 1 ? '機電工程' : i % 4 === 2 ? '裝修工程' : '其他工程',\n          // 新增群組名稱\n          CSort: i,\n          CRemark: i % 3 === 0 ? `備註說明 ${i}` : undefined,\n          CCreateDt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\n          CCreator: '系統管理員',\n          CCategory: i % 2 === 0 ? '基礎工程' : '進階工程',\n          CUnitPrice: Math.floor(Math.random() * 10000) + 1000,\n          CQuantity: Math.floor(Math.random() * 10) + 1,\n          CUnit: i % 3 === 0 ? '式' : i % 3 === 1 ? '個' : 'm²'\n        };\n        mockDetails.push(detail);\n      }\n      // 搜尋篩選\n      let filteredDetails = mockDetails;\n      if (searchKeyword && searchKeyword.trim()) {\n        filteredDetails = mockDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CRemark && detail.CRemark.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CCategory && detail.CCategory.toLowerCase().includes(searchKeyword.toLowerCase()));\n      }\n      // 分頁處理\n      const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n      const endIndex = startIndex + this.detailPagination.pageSize;\n      const pagedDetails = filteredDetails.slice(startIndex, endIndex);\n      // 更新資料\n      this.currentTemplateDetailsData = pagedDetails;\n      this.detailPagination.totalItems = filteredDetails.length;\n      this.detailPagination.totalPages = Math.ceil(filteredDetails.length / this.detailPagination.pageSize);\n      this.detailPagination.currentPage = pageIndex;\n      console.log('模擬詳情載入完成:', {\n        templateId,\n        totalItems: filteredDetails.length,\n        currentPage: pageIndex,\n        details: pagedDetails\n      });\n    }, 300);\n  }\n  // 搜尋模板詳情\n  searchTemplateDetails(keyword) {\n    this.detailSearchKeyword = keyword;\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\n    }\n  }\n  // 更新詳情分頁 (保留向後相容)\n  updateDetailPagination() {\n    // 如果使用新的詳情資料，直接返回\n    if (this.currentTemplateDetailsData.length > 0) {\n      return;\n    }\n    // 向後相容：使用舊的詳情資料\n    const details = this.currentTemplateDetails;\n    this.detailPagination.totalItems = details.length;\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\n    this.detailPagination.currentPage = 1; // 重置到第一頁\n    this.updatePaginatedDetails();\n  }\n  // 更新分頁後的詳情列表\n  updatePaginatedDetails() {\n    const details = this.currentTemplateDetails;\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\n    const endIndex = startIndex + this.detailPagination.pageSize;\n    this.paginatedDetails = details.slice(startIndex, endIndex);\n  }\n  // 詳情分頁導航\n  goToDetailPage(page) {\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\n      // 如果使用新的詳情資料，重新載入\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\n      } else {\n        // 向後相容：使用舊的分頁邏輯\n        this.detailPagination.currentPage = page;\n        this.updatePaginatedDetails();\n      }\n    }\n  }\n  // 獲取詳情分頁頁碼數組\n  getDetailPageNumbers() {\n    const pages = [];\n    const totalPages = this.detailPagination.totalPages;\n    const currentPage = this.detailPagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // 刪除模板 - 使用真實的 API 調用\n  deleteTemplateById(templateID) {\n    // 準備 API 請求參數\n    const deleteTemplateArgs = {\n      CTemplateId: templateID\n    };\n    console.log('DeleteTemplate API 調用:', deleteTemplateArgs);\n    // 調用 DeleteTemplate API\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\n      body: deleteTemplateArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          console.log('模板刪除成功:', response);\n          // 重新載入模板列表\n          this.loadTemplates();\n          // 如果當前查看的模板被刪除，關閉詳情\n          if (this.selectedTemplate?.TemplateID === templateID) {\n            this.selectedTemplate = null;\n          }\n          console.log('模板已刪除');\n        } else {\n          // API 返回錯誤\n          console.error('模板刪除失敗:', response.Message);\n          console.error(`模板刪除失敗：${response.Message || '未知錯誤'}`);\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        console.error('DeleteTemplate API 調用失敗:', error);\n        console.error('模板刪除失敗，請檢查網路連線或聯繫系統管理員');\n      }\n    });\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/Template/GetTemplateList\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/Template/SaveTemplate\n   *    請求體: {\n   *      CTemplateName: string,\n   *      CTemplateType: number,  // 1=客變需求\n   *      CStatus: number,\n   *      Details: [{\n   *        CReleateId: number,        // 關聯主檔ID\n   *        CReleateName: string       // 關聯名稱\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    POST /api/Template/DeleteTemplate\n   *    請求體: { CTemplateId: number }\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n};\n__decorate([Input()], TemplateViewerComponent.prototype, \"availableData\", void 0);\n__decorate([Input()], TemplateViewerComponent.prototype, \"templateType\", void 0);\n__decorate([Input()], TemplateViewerComponent.prototype, \"showOnlyAddForm\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"selectTemplate\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"close\", void 0);\nTemplateViewerComponent = __decorate([Component({\n  selector: 'app-template-viewer',\n  templateUrl: './template-viewer.component.html',\n  styleUrls: ['./template-viewer.component.scss'],\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule]\n})], TemplateViewerComponent);\nexport { TemplateViewerComponent };\n// 注意：GetTemplateDetailById API 使用的是 GetTemplateDetailByIdArgs 和 TemplateDetailItemListResponseBase\n// 這些 interface 已經在 API models 中定義，不需要重複定義", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "TemplateViewerComponent", "constructor", "templateService", "availableData", "templateType", "showOnlyAddForm", "selectTemplate", "close", "Math", "templates", "templateDetails", "selectedTemplate", "currentTemplateDetailsData", "detailSearchKeyword", "searchKeyword", "filteredTemplates", "templatePagination", "currentPage", "pageSize", "totalItems", "totalPages", "paginatedTemplates", "detailPagination", "paginatedDetails", "showAddForm", "newTemplate", "name", "description", "groupName", "selectedItems", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "onAddTemplate", "ngOnChanges", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "TemplateID", "CTemplateId", "TemplateName", "Description", "CCreateDt", "Date", "toLocaleDateString", "error", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "updateTemplatePagination", "length", "ceil", "max", "updatePaginatedTemplates", "startIndex", "endIndex", "slice", "goToTemplatePage", "page", "getTemplatePageNumbers", "pages", "startPage", "endPage", "min", "i", "push", "onSearch", "clearSearch", "for<PERSON>ach", "selected", "cancelAddTemplate", "saveNewTemplate", "CGroupName", "CReleateName", "CRequirement", "CReleateId", "CRequirementID", "ID", "id", "createTemplate", "saveTemplateArgs", "CStatus", "Details", "CTemplateDetailId", "apiTemplateSaveTemplatePost$Json", "emit", "onSelectTemplate", "loadTemplateDetails", "updateDetailPagination", "templateId", "pageIndex", "args", "console", "log", "apiTemplateGetTemplateDetailByIdPost$Json", "allDetails", "CSort", "undefined", "CRemark", "toISOString", "CCreator", "CCategory", "CUnitPrice", "CQuantity", "CUnit", "detail", "pagedDetails", "Message", "loadTemplateDetailsMock", "setTimeout", "mockDetails", "itemsPerTemplate", "String", "fromCharCode", "now", "random", "floor", "filteredDetails", "details", "searchTemplateDetails", "currentTemplateDetails", "updatePaginatedDetails", "goToDetailPage", "getDetailPageNumbers", "onClose", "onDeleteTemplate", "templateID", "confirm", "deleteTemplateById", "deleteTemplateArgs", "apiTemplateDeleteTemplatePost$Json", "closeTemplateDetail", "d", "trackByTemplateId", "index", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { SaveTemplateArgs, SaveTemplateDetailArgs, TemplateGetListArgs, TemplateGetListResponse, GetTemplateByIdArgs, GetTemplateDetailByIdArgs } from 'src/services/api/models';\r\n\r\n// 選中項目的介面定義\r\ninterface SelectedItem {\r\n  CGroupName: string | null;\r\n  CReleateName: string | null;\r\n  CReleateId: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() availableData: any[] = []; // 父元件傳入的可選資料 (包含 CGroupName, CReleateName, CReleateId)\r\n  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\r\n  @Input() showOnlyAddForm: boolean = false; // 是否只顯示新增模板表單\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 公開Math對象供模板使用\r\n  Math = Math;\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = []; // 保留用於向後相容\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 新的詳情資料管理\r\n  currentTemplateDetailsData: TemplateDetailItem[] = [];\r\n  detailSearchKeyword = '';\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板列表\r\n  templatePagination = {\r\n    currentPage: 1,\r\n    pageSize: 10,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板詳情\r\n  detailPagination = {\r\n    currentPage: 1,\r\n    pageSize: 5,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedDetails: TemplateDetail[] = [];\r\n\r\n  // 新增模板表單\r\n  showAddForm = false;\r\n  newTemplate = {\r\n    name: '',\r\n    description: '',\r\n    groupName: '', // 新增群組名稱欄位\r\n    selectedItems: [] as any[]\r\n  };\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    // 只有在非純新增模式下才載入模板列表\r\n    if (!this.showOnlyAddForm) {\r\n      this.loadTemplates();\r\n      this.updateFilteredTemplates();\r\n    }\r\n\r\n    // 如果只顯示新增表單，自動打開新增模板表單\r\n    if (this.showOnlyAddForm) {\r\n      this.onAddTemplate();\r\n    }\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 載入模板列表 - 使用真實的 API 調用\r\n  loadTemplates() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      PageIndex: 1, // 暫時載入第一頁\r\n      PageSize: 100, // 暫時載入較多資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateList API\r\n    this.templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // API 調用成功\r\n\r\n          // 轉換 API 回應為內部格式\r\n          this.templates = response.Entries.map(item => ({\r\n            TemplateID: item.CTemplateId,\r\n            TemplateName: item.CTemplateName || '',\r\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\r\n          }));\r\n\r\n          // 更新過濾列表\r\n          this.updateFilteredTemplates();\r\n\r\n          // 清空詳情資料，改為按需載入\r\n          this.templateDetails = [];\r\n          this.currentTemplateDetailsData = [];\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n          this.templateDetails = [];\r\n          this.updateFilteredTemplates();\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n        this.templateDetails = [];\r\n        this.updateFilteredTemplates();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n    this.updateTemplatePagination();\r\n  }\r\n\r\n  // 更新模板分頁\r\n  updateTemplatePagination() {\r\n    this.templatePagination.totalItems = this.filteredTemplates.length;\r\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\r\n\r\n    // 確保當前頁面不超過總頁數\r\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\r\n    }\r\n\r\n    this.updatePaginatedTemplates();\r\n  }\r\n\r\n  // 更新分頁後的模板列表\r\n  updatePaginatedTemplates() {\r\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\r\n    const endIndex = startIndex + this.templatePagination.pageSize;\r\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 模板分頁導航\r\n  goToTemplatePage(page: number) {\r\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = page;\r\n      this.updatePaginatedTemplates();\r\n    }\r\n  }\r\n\r\n  // 獲取模板分頁頁碼數組\r\n  getTemplatePageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.templatePagination.totalPages;\r\n    const currentPage = this.templatePagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n  // 顯示新增模板表單\r\n  onAddTemplate() {\r\n    this.showAddForm = true;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      groupName: '',\r\n      selectedItems: []\r\n    };\r\n    // 如果不是只顯示新增表單模式，才重置選擇狀態\r\n    // 在只顯示新增表單模式下，保持父元件傳入的選中狀態\r\n    if (!this.showOnlyAddForm) {\r\n      this.availableData.forEach(item => item.selected = false);\r\n    }\r\n  }\r\n\r\n  // 取消新增模板\r\n  cancelAddTemplate() {\r\n    this.showAddForm = false;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      groupName: '',\r\n      selectedItems: []\r\n    };\r\n  }\r\n\r\n  // 儲存新模板\r\n  saveNewTemplate() {\r\n    if (!this.newTemplate.name.trim()) {\r\n      return;\r\n    }\r\n\r\n    const selectedItems: SelectedItem[] = this.availableData\r\n      .filter(item => item.selected)\r\n      .map(item => ({\r\n        CGroupName: item.CGroupName || null,\r\n        CReleateName: item.CReleateName || item.CRequirement || item.name || null,\r\n        CReleateId: item.CReleateId || item.CRequirementID || item.ID || item.id || 0\r\n      }));\r\n\r\n    if (selectedItems.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // TODO: 替換為實際的API調用\r\n    this.createTemplate(this.newTemplate.name, selectedItems);\r\n  }\r\n\r\n  // 創建模板 - 使用真實的 API 調用\r\n  createTemplate(name: string, selectedItems: SelectedItem[]) {\r\n    // 準備 API 請求資料\r\n    const saveTemplateArgs: SaveTemplateArgs = {\r\n      CTemplateId: null, // 新增時為 null\r\n      CTemplateName: name.trim(),\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      CStatus: 1, // 啟用狀態\r\n      Details: selectedItems.map(item => ({\r\n        CTemplateDetailId: null, // 新增時為 null\r\n        CReleateId: item.CReleateId, // 關聯主檔ID\r\n        CReleateName: item.CReleateName, // 關聯名稱\r\n        CGroupName: item.CGroupName // 群組名稱\r\n      } as SaveTemplateDetailArgs))\r\n    };\r\n\r\n    // 調用 SaveTemplate API\r\n    this.templateService.apiTemplateSaveTemplatePost$Json({\r\n      body: saveTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n\r\n          // 只有在非純新增模式下才重新載入模板列表\r\n          if (!this.showOnlyAddForm) {\r\n            this.loadTemplates();\r\n          }\r\n\r\n          // 關閉表單\r\n          this.showAddForm = false;\r\n\r\n          // 如果是純新增模式，發出關閉事件通知父組件關閉 dialog\r\n          if (this.showOnlyAddForm) {\r\n            this.close.emit();\r\n          }\r\n        } else {\r\n          // API 返回錯誤\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n\r\n    // 按需載入模板詳情\r\n    if (template.TemplateID) {\r\n      this.loadTemplateDetails(template.TemplateID);\r\n    }\r\n\r\n    this.updateDetailPagination();\r\n  }\r\n\r\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\r\n  loadTemplateDetails(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    console.log('GetTemplateDetailById API 調用:', args);\r\n\r\n    // 調用真實的 GetTemplateDetailById API\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          console.log('模板詳情載入成功:', response);\r\n\r\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\r\n          let allDetails = response.Entries.map(item => ({\r\n            CTemplateDetailId: item.CTemplateDetailId || 0,\r\n            CTemplateId: item.CTemplateId || templateId,\r\n            CReleateId: item.CReleateId || 0,\r\n            CReleateName: item.CReleateName || '',\r\n            CGroupName: item.CGroupName || '', // 新增群組名稱欄位\r\n            CSort: undefined,\r\n            CRemark: undefined,\r\n            CCreateDt: new Date().toISOString(),\r\n            CCreator: '系統',\r\n            CCategory: undefined,\r\n            CUnitPrice: undefined,\r\n            CQuantity: undefined,\r\n            CUnit: undefined\r\n          } as TemplateDetailItem));\r\n\r\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\r\n          if (searchKeyword && searchKeyword.trim()) {\r\n            allDetails = allDetails.filter(detail =>\r\n              detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n              (detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n            );\r\n          }\r\n\r\n          // 前端分頁處理 (因為 API 不支援分頁參數)\r\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n          const endIndex = startIndex + this.detailPagination.pageSize;\r\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\r\n\r\n          // 更新詳情資料和分頁資訊\r\n          this.currentTemplateDetailsData = pagedDetails;\r\n          this.detailPagination.totalItems = allDetails.length;\r\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\r\n          this.detailPagination.currentPage = pageIndex;\r\n        } else {\r\n          console.error('模板詳情載入失敗:', response.Message);\r\n          this.currentTemplateDetailsData = [];\r\n          this.detailPagination.totalItems = 0;\r\n          this.detailPagination.totalPages = 0;\r\n          this.detailPagination.currentPage = 1;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('GetTemplateDetailById API 調用失敗:', error);\r\n        this.currentTemplateDetailsData = [];\r\n        this.detailPagination.totalItems = 0;\r\n        this.detailPagination.totalPages = 0;\r\n        this.detailPagination.currentPage = 1;\r\n\r\n        // 如果 API 失敗，回退到模擬資料\r\n        console.log('回退到模擬資料');\r\n        this.loadTemplateDetailsMock(templateId, pageIndex, searchKeyword);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 模擬載入模板詳情 (暫時使用，等待後端 API)\r\n  private loadTemplateDetailsMock(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    // 模擬 API 延遲\r\n    setTimeout(() => {\r\n      // 生成模擬詳情資料\r\n      const mockDetails: TemplateDetailItem[] = [];\r\n      const itemsPerTemplate = 8; // 每個模板8個項目\r\n\r\n      for (let i = 1; i <= itemsPerTemplate; i++) {\r\n        const detail: TemplateDetailItem = {\r\n          CTemplateDetailId: templateId * 100 + i,\r\n          CTemplateId: templateId,\r\n          CReleateId: templateId * 1000 + i,\r\n          CReleateName: `工程項目${String.fromCharCode(64 + (templateId % 26) + 1)}-${i}`,\r\n          CGroupName: i % 4 === 0 ? '結構工程' : (i % 4 === 1 ? '機電工程' : (i % 4 === 2 ? '裝修工程' : '其他工程')), // 新增群組名稱\r\n          CSort: i,\r\n          CRemark: i % 3 === 0 ? `備註說明 ${i}` : undefined,\r\n          CCreateDt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\r\n          CCreator: '系統管理員',\r\n          CCategory: i % 2 === 0 ? '基礎工程' : '進階工程',\r\n          CUnitPrice: Math.floor(Math.random() * 10000) + 1000,\r\n          CQuantity: Math.floor(Math.random() * 10) + 1,\r\n          CUnit: i % 3 === 0 ? '式' : (i % 3 === 1 ? '個' : 'm²')\r\n        };\r\n        mockDetails.push(detail);\r\n      }\r\n\r\n      // 搜尋篩選\r\n      let filteredDetails = mockDetails;\r\n      if (searchKeyword && searchKeyword.trim()) {\r\n        filteredDetails = mockDetails.filter(detail =>\r\n          detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n          (detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase())) ||\r\n          (detail.CRemark && detail.CRemark.toLowerCase().includes(searchKeyword.toLowerCase())) ||\r\n          (detail.CCategory && detail.CCategory.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n        );\r\n      }\r\n\r\n      // 分頁處理\r\n      const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n      const endIndex = startIndex + this.detailPagination.pageSize;\r\n      const pagedDetails = filteredDetails.slice(startIndex, endIndex);\r\n\r\n      // 更新資料\r\n      this.currentTemplateDetailsData = pagedDetails;\r\n      this.detailPagination.totalItems = filteredDetails.length;\r\n      this.detailPagination.totalPages = Math.ceil(filteredDetails.length / this.detailPagination.pageSize);\r\n      this.detailPagination.currentPage = pageIndex;\r\n\r\n      console.log('模擬詳情載入完成:', {\r\n        templateId,\r\n        totalItems: filteredDetails.length,\r\n        currentPage: pageIndex,\r\n        details: pagedDetails\r\n      });\r\n    }, 300);\r\n  }\r\n\r\n  // 搜尋模板詳情\r\n  searchTemplateDetails(keyword: string) {\r\n    this.detailSearchKeyword = keyword;\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\r\n    }\r\n  }\r\n\r\n  // 更新詳情分頁 (保留向後相容)\r\n  updateDetailPagination() {\r\n    // 如果使用新的詳情資料，直接返回\r\n    if (this.currentTemplateDetailsData.length > 0) {\r\n      return;\r\n    }\r\n\r\n    // 向後相容：使用舊的詳情資料\r\n    const details = this.currentTemplateDetails;\r\n    this.detailPagination.totalItems = details.length;\r\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\r\n    this.detailPagination.currentPage = 1; // 重置到第一頁\r\n    this.updatePaginatedDetails();\r\n  }\r\n\r\n  // 更新分頁後的詳情列表\r\n  updatePaginatedDetails() {\r\n    const details = this.currentTemplateDetails;\r\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\r\n    const endIndex = startIndex + this.detailPagination.pageSize;\r\n    this.paginatedDetails = details.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 詳情分頁導航\r\n  goToDetailPage(page: number) {\r\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\r\n      // 如果使用新的詳情資料，重新載入\r\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\r\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\r\n      } else {\r\n        // 向後相容：使用舊的分頁邏輯\r\n        this.detailPagination.currentPage = page;\r\n        this.updatePaginatedDetails();\r\n      }\r\n    }\r\n  }\r\n\r\n  // 獲取詳情分頁頁碼數組\r\n  getDetailPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.detailPagination.totalPages;\r\n    const currentPage = this.detailPagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // 刪除模板 - 使用真實的 API 調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 準備 API 請求參數\r\n    const deleteTemplateArgs: GetTemplateByIdArgs = {\r\n      CTemplateId: templateID\r\n    };\r\n\r\n    console.log('DeleteTemplate API 調用:', deleteTemplateArgs);\r\n\r\n    // 調用 DeleteTemplate API\r\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\r\n      body: deleteTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          console.log('模板刪除成功:', response);\r\n\r\n          // 重新載入模板列表\r\n          this.loadTemplates();\r\n\r\n          // 如果當前查看的模板被刪除，關閉詳情\r\n          if (this.selectedTemplate?.TemplateID === templateID) {\r\n            this.selectedTemplate = null;\r\n          }\r\n\r\n          console.log('模板已刪除');\r\n        } else {\r\n          // API 返回錯誤\r\n          console.error('模板刪除失敗:', response.Message);\r\n          console.error(`模板刪除失敗：${response.Message || '未知錯誤'}`);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        console.error('DeleteTemplate API 調用失敗:', error);\r\n        console.error('模板刪除失敗，請檢查網路連線或聯繫系統管理員');\r\n      }\r\n    });\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/Template/GetTemplateList\r\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/Template/SaveTemplate\r\n   *    請求體: {\r\n   *      CTemplateName: string,\r\n   *      CTemplateType: number,  // 1=客變需求\r\n   *      CStatus: number,\r\n   *      Details: [{\r\n   *        CReleateId: number,        // 關聯主檔ID\r\n   *        CReleateName: string       // 關聯名稱\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    POST /api/Template/DeleteTemplate\r\n   *    請求體: { CTemplateId: number }\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\r\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\r\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\r\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  CTemplateType: number; // 模板類型，1=客變需求\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n\r\n// 擴展的詳情項目結構 (基於 API 的 TemplateDetailItem 擴展)\r\nexport interface TemplateDetailItem {\r\n  CTemplateDetailId: number;\r\n  CTemplateId: number;\r\n  CReleateId: number;            // 關聯主檔ID\r\n  CReleateName: string;          // 關聯名稱\r\n  CGroupName?: string;           // 群組名稱 (API 新增欄位)\r\n  CSort?: number;                // 排序順序\r\n  CRemark?: string;              // 備註\r\n  CCreateDt: string;             // 建立時間\r\n  CCreator: string;              // 建立者\r\n  CUpdateDt?: string;            // 更新時間\r\n  CUpdator?: string;             // 更新者\r\n\r\n  // 業務相關欄位 (依需求添加，部分由 API 提供)\r\n  CUnitPrice?: number;           // 單價\r\n  CQuantity?: number;            // 數量\r\n  CUnit?: string;                // 單位\r\n  CCategory?: string;            // 分類\r\n}\r\n\r\n// 注意：GetTemplateDetailById API 使用的是 GetTemplateDetailByIdArgs 和 TemplateDetailItemListResponseBase\r\n// 這些 interface 已經在 API models 中定義，不需要重複定義\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAA2B,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;AAkBtD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAkDlCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAjD1B,KAAAC,aAAa,GAAU,EAAE,CAAC,CAAC;IAC3B,KAAAC,YAAY,GAAW,CAAC,CAAC,CAAC;IAC1B,KAAAC,eAAe,GAAY,KAAK,CAAC,CAAC;IACjC,KAAAC,cAAc,GAAG,IAAIb,YAAY,EAAY;IAC7C,KAAAc,KAAK,GAAG,IAAId,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAAe,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE,CAAC,CAAC;IACxC,KAAAC,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAC,0BAA0B,GAAyB,EAAE;IACrD,KAAAC,mBAAmB,GAAG,EAAE;IAExB;IACA,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAC,kBAAkB,GAAG;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE;KACb;IACD,KAAAC,kBAAkB,GAAe,EAAE;IAEnC;IACA,KAAAC,gBAAgB,GAAG;MACjBL,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE;KACb;IACD,KAAAG,gBAAgB,GAAqB,EAAE;IAEvC;IACA,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,WAAW,GAAG;MACZC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MAAE;MACfC,aAAa,EAAE;KAChB;EAEuD;EAExDC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACzB,eAAe,EAAE;MACzB,IAAI,CAAC0B,aAAa,EAAE;MACpB,IAAI,CAACC,uBAAuB,EAAE;IAChC;IAEA;IACA,IAAI,IAAI,CAAC3B,eAAe,EAAE;MACxB,IAAI,CAAC4B,aAAa,EAAE;IACtB;EACF;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACF,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACA,MAAMI,mBAAmB,GAAwB;MAC/CC,aAAa,EAAE,IAAI,CAAChC,YAAY;MAAE;MAClCiC,SAAS,EAAE,CAAC;MAAE;MACdC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAACrC,eAAe,CAACsC,mCAAmC,CAAC;MACvDC,IAAI,EAAEN;KACP,CAAC,CAACO,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UAEA;UACA,IAAI,CAACrC,SAAS,GAAGmC,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7CC,UAAU,EAAED,IAAI,CAACE,WAAW;YAC5BC,YAAY,EAAEH,IAAI,CAACT,aAAa,IAAI,EAAE;YACtCa,WAAW,EAAE,SAASJ,IAAI,CAACK,SAAS,GAAG,IAAIC,IAAI,CAACN,IAAI,CAACK,SAAS,CAAC,CAACE,kBAAkB,EAAE,GAAG,IAAI;WAC5F,CAAC,CAAC;UAEH;UACA,IAAI,CAACvB,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAACtB,eAAe,GAAG,EAAE;UACzB,IAAI,CAACE,0BAA0B,GAAG,EAAE;QACtC,CAAC,MAAM;UACL;UACA,IAAI,CAACH,SAAS,GAAG,EAAE;UACnB,IAAI,CAACC,eAAe,GAAG,EAAE;UACzB,IAAI,CAACsB,uBAAuB,EAAE;QAChC;MACF,CAAC;MACDwB,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAAC/C,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACsB,uBAAuB,EAAE;MAChC;KACD,CAAC;EACJ;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAClB,aAAa,CAAC2C,IAAI,EAAE,EAAE;MAC9B,IAAI,CAAC1C,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMiD,OAAO,GAAG,IAAI,CAAC5C,aAAa,CAAC6C,WAAW,EAAE;MAChD,IAAI,CAAC5C,iBAAiB,GAAG,IAAI,CAACN,SAAS,CAACmD,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAACV,YAAY,CAACQ,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAACT,WAAW,IAAIS,QAAQ,CAACT,WAAW,CAACO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;IACA,IAAI,CAACK,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,IAAI,CAAC/C,kBAAkB,CAACG,UAAU,GAAG,IAAI,CAACJ,iBAAiB,CAACiD,MAAM;IAClE,IAAI,CAAChD,kBAAkB,CAACI,UAAU,GAAGZ,IAAI,CAACyD,IAAI,CAAC,IAAI,CAACjD,kBAAkB,CAACG,UAAU,GAAG,IAAI,CAACH,kBAAkB,CAACE,QAAQ,CAAC;IAErH;IACA,IAAI,IAAI,CAACF,kBAAkB,CAACC,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACI,UAAU,EAAE;MAC5E,IAAI,CAACJ,kBAAkB,CAACC,WAAW,GAAGT,IAAI,CAAC0D,GAAG,CAAC,CAAC,EAAE,IAAI,CAAClD,kBAAkB,CAACI,UAAU,CAAC;IACvF;IAEA,IAAI,CAAC+C,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAACpD,kBAAkB,CAACC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,kBAAkB,CAACE,QAAQ;IAC/F,MAAMmD,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACpD,kBAAkB,CAACE,QAAQ;IAC9D,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAACN,iBAAiB,CAACuD,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACAE,gBAAgBA,CAACC,IAAY;IAC3B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACxD,kBAAkB,CAACI,UAAU,EAAE;MAC3D,IAAI,CAACJ,kBAAkB,CAACC,WAAW,GAAGuD,IAAI;MAC1C,IAAI,CAACL,wBAAwB,EAAE;IACjC;EACF;EAEA;EACAM,sBAAsBA,CAAA;IACpB,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMtD,UAAU,GAAG,IAAI,CAACJ,kBAAkB,CAACI,UAAU;IACrD,MAAMH,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACC,WAAW;IAEvD;IACA,MAAM0D,SAAS,GAAGnE,IAAI,CAAC0D,GAAG,CAAC,CAAC,EAAEjD,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAM2D,OAAO,GAAGpE,IAAI,CAACqE,GAAG,CAACzD,UAAU,EAAEH,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAI6D,CAAC,GAAGH,SAAS,EAAEG,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCJ,KAAK,CAACK,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOJ,KAAK;EACd;EAEA;EACAM,QAAQA,CAAA;IACN,IAAI,CAAChD,uBAAuB,EAAE;EAChC;EAEA;EACAiD,WAAWA,CAAA;IACT,IAAI,CAACnE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACkB,uBAAuB,EAAE;EAChC;EAIA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACT,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,WAAW,GAAG;MACjBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE;KAChB;IACD;IACA;IACA,IAAI,CAAC,IAAI,CAACxB,eAAe,EAAE;MACzB,IAAI,CAACF,aAAa,CAAC+E,OAAO,CAAClC,IAAI,IAAIA,IAAI,CAACmC,QAAQ,GAAG,KAAK,CAAC;IAC3D;EACF;EAEA;EACAC,iBAAiBA,CAAA;IACf,IAAI,CAAC5D,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,WAAW,GAAG;MACjBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE;KAChB;EACH;EAEA;EACAwD,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC5D,WAAW,CAACC,IAAI,CAAC+B,IAAI,EAAE,EAAE;MACjC;IACF;IAEA,MAAM5B,aAAa,GAAmB,IAAI,CAAC1B,aAAa,CACrDyD,MAAM,CAACZ,IAAI,IAAIA,IAAI,CAACmC,QAAQ,CAAC,CAC7BpC,GAAG,CAACC,IAAI,KAAK;MACZsC,UAAU,EAAEtC,IAAI,CAACsC,UAAU,IAAI,IAAI;MACnCC,YAAY,EAAEvC,IAAI,CAACuC,YAAY,IAAIvC,IAAI,CAACwC,YAAY,IAAIxC,IAAI,CAACtB,IAAI,IAAI,IAAI;MACzE+D,UAAU,EAAEzC,IAAI,CAACyC,UAAU,IAAIzC,IAAI,CAAC0C,cAAc,IAAI1C,IAAI,CAAC2C,EAAE,IAAI3C,IAAI,CAAC4C,EAAE,IAAI;KAC7E,CAAC,CAAC;IAEL,IAAI/D,aAAa,CAACmC,MAAM,KAAK,CAAC,EAAE;MAC9B;IACF;IAEA;IACA,IAAI,CAAC6B,cAAc,CAAC,IAAI,CAACpE,WAAW,CAACC,IAAI,EAAEG,aAAa,CAAC;EAC3D;EAEA;EACAgE,cAAcA,CAACnE,IAAY,EAAEG,aAA6B;IACxD;IACA,MAAMiE,gBAAgB,GAAqB;MACzC5C,WAAW,EAAE,IAAI;MAAE;MACnBX,aAAa,EAAEb,IAAI,CAAC+B,IAAI,EAAE;MAC1BrB,aAAa,EAAE,IAAI,CAAChC,YAAY;MAAE;MAClC2F,OAAO,EAAE,CAAC;MAAE;MACZC,OAAO,EAAEnE,aAAa,CAACkB,GAAG,CAACC,IAAI,KAAK;QAClCiD,iBAAiB,EAAE,IAAI;QAAE;QACzBR,UAAU,EAAEzC,IAAI,CAACyC,UAAU;QAAE;QAC7BF,YAAY,EAAEvC,IAAI,CAACuC,YAAY;QAAE;QACjCD,UAAU,EAAEtC,IAAI,CAACsC,UAAU,CAAC;OACF;KAC7B;IAED;IACA,IAAI,CAACpF,eAAe,CAACgG,gCAAgC,CAAC;MACpDzD,IAAI,EAAEqD;KACP,CAAC,CAACpD,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UAEA;UACA,IAAI,CAAC,IAAI,CAACxC,eAAe,EAAE;YACzB,IAAI,CAAC0B,aAAa,EAAE;UACtB;UAEA;UACA,IAAI,CAACP,WAAW,GAAG,KAAK;UAExB;UACA,IAAI,IAAI,CAACnB,eAAe,EAAE;YACxB,IAAI,CAACE,KAAK,CAAC4F,IAAI,EAAE;UACnB;QACF,CAAC,MAAM;UACL;QAAA;MAEJ,CAAC;MACD3C,KAAK,EAAGA,KAAK,IAAI;QACf;MAAA;KAEH,CAAC;EACJ;EAIA;EACA4C,gBAAgBA,CAACvC,QAAkB;IACjC,IAAI,CAAClD,gBAAgB,GAAGkD,QAAQ;IAChC,IAAI,CAACvD,cAAc,CAAC6F,IAAI,CAACtC,QAAQ,CAAC;IAElC;IACA,IAAIA,QAAQ,CAACZ,UAAU,EAAE;MACvB,IAAI,CAACoD,mBAAmB,CAACxC,QAAQ,CAACZ,UAAU,CAAC;IAC/C;IAEA,IAAI,CAACqD,sBAAsB,EAAE;EAC/B;EAEA;EACAD,mBAAmBA,CAACE,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAE1F,aAAsB;IACnF,MAAM2F,IAAI,GAA8B;MACtCF,UAAU,EAAEA;KACb;IAEDG,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,IAAI,CAAC;IAElD;IACA,IAAI,CAACvG,eAAe,CAAC0G,yCAAyC,CAAC;MAC7DnE,IAAI,EAAEgE;KACP,CAAC,CAAC/D,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD4D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE/D,QAAQ,CAAC;UAElC;UACA,IAAIiE,UAAU,GAAGjE,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7CiD,iBAAiB,EAAEjD,IAAI,CAACiD,iBAAiB,IAAI,CAAC;YAC9C/C,WAAW,EAAEF,IAAI,CAACE,WAAW,IAAIqD,UAAU;YAC3Cd,UAAU,EAAEzC,IAAI,CAACyC,UAAU,IAAI,CAAC;YAChCF,YAAY,EAAEvC,IAAI,CAACuC,YAAY,IAAI,EAAE;YACrCD,UAAU,EAAEtC,IAAI,CAACsC,UAAU,IAAI,EAAE;YAAE;YACnCwB,KAAK,EAAEC,SAAS;YAChBC,OAAO,EAAED,SAAS;YAClB1D,SAAS,EAAE,IAAIC,IAAI,EAAE,CAAC2D,WAAW,EAAE;YACnCC,QAAQ,EAAE,IAAI;YACdC,SAAS,EAAEJ,SAAS;YACpBK,UAAU,EAAEL,SAAS;YACrBM,SAAS,EAAEN,SAAS;YACpBO,KAAK,EAAEP;WACe,EAAC;UAEzB;UACA,IAAIjG,aAAa,IAAIA,aAAa,CAAC2C,IAAI,EAAE,EAAE;YACzCoD,UAAU,GAAGA,UAAU,CAACjD,MAAM,CAAC2D,MAAM,IACnCA,MAAM,CAAChC,YAAY,CAAC5B,WAAW,EAAE,CAACG,QAAQ,CAAChD,aAAa,CAAC6C,WAAW,EAAE,CAAC,IACtE4D,MAAM,CAACjC,UAAU,IAAIiC,MAAM,CAACjC,UAAU,CAAC3B,WAAW,EAAE,CAACG,QAAQ,CAAChD,aAAa,CAAC6C,WAAW,EAAE,CAAE,CAC7F;UACH;UAEA;UACA,MAAMS,UAAU,GAAG,CAACoC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAClF,gBAAgB,CAACJ,QAAQ;UACnE,MAAMmD,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC9C,gBAAgB,CAACJ,QAAQ;UAC5D,MAAMsG,YAAY,GAAGX,UAAU,CAACvC,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;UAE3D;UACA,IAAI,CAACzD,0BAA0B,GAAG4G,YAAY;UAC9C,IAAI,CAAClG,gBAAgB,CAACH,UAAU,GAAG0F,UAAU,CAAC7C,MAAM;UACpD,IAAI,CAAC1C,gBAAgB,CAACF,UAAU,GAAGZ,IAAI,CAACyD,IAAI,CAAC4C,UAAU,CAAC7C,MAAM,GAAG,IAAI,CAAC1C,gBAAgB,CAACJ,QAAQ,CAAC;UAChG,IAAI,CAACI,gBAAgB,CAACL,WAAW,GAAGuF,SAAS;QAC/C,CAAC,MAAM;UACLE,OAAO,CAAClD,KAAK,CAAC,WAAW,EAAEZ,QAAQ,CAAC6E,OAAO,CAAC;UAC5C,IAAI,CAAC7G,0BAA0B,GAAG,EAAE;UACpC,IAAI,CAACU,gBAAgB,CAACH,UAAU,GAAG,CAAC;UACpC,IAAI,CAACG,gBAAgB,CAACF,UAAU,GAAG,CAAC;UACpC,IAAI,CAACE,gBAAgB,CAACL,WAAW,GAAG,CAAC;QACvC;MACF,CAAC;MACDuC,KAAK,EAAGA,KAAK,IAAI;QACfkD,OAAO,CAAClD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAAC5C,0BAA0B,GAAG,EAAE;QACpC,IAAI,CAACU,gBAAgB,CAACH,UAAU,GAAG,CAAC;QACpC,IAAI,CAACG,gBAAgB,CAACF,UAAU,GAAG,CAAC;QACpC,IAAI,CAACE,gBAAgB,CAACL,WAAW,GAAG,CAAC;QAErC;QACAyF,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QACtB,IAAI,CAACe,uBAAuB,CAACnB,UAAU,EAAEC,SAAS,EAAE1F,aAAa,CAAC;MACpE;KACD,CAAC;EACJ;EAEA;EACQ4G,uBAAuBA,CAACnB,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAE1F,aAAsB;IAC/F;IACA6G,UAAU,CAAC,MAAK;MACd;MACA,MAAMC,WAAW,GAAyB,EAAE;MAC5C,MAAMC,gBAAgB,GAAG,CAAC,CAAC,CAAC;MAE5B,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI+C,gBAAgB,EAAE/C,CAAC,EAAE,EAAE;QAC1C,MAAMyC,MAAM,GAAuB;UACjCtB,iBAAiB,EAAEM,UAAU,GAAG,GAAG,GAAGzB,CAAC;UACvC5B,WAAW,EAAEqD,UAAU;UACvBd,UAAU,EAAEc,UAAU,GAAG,IAAI,GAAGzB,CAAC;UACjCS,YAAY,EAAE,OAAOuC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAIxB,UAAU,GAAG,EAAG,GAAG,CAAC,CAAC,IAAIzB,CAAC,EAAE;UAC3EQ,UAAU,EAAER,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,MAAQ;UAAE;UAC7FgC,KAAK,EAAEhC,CAAC;UACRkC,OAAO,EAAElC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQA,CAAC,EAAE,GAAGiC,SAAS;UAC9C1D,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAAC0E,GAAG,EAAE,GAAGxH,IAAI,CAACyH,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAChB,WAAW,EAAE;UACxFC,QAAQ,EAAE,OAAO;UACjBC,SAAS,EAAErC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM;UACxCsC,UAAU,EAAE5G,IAAI,CAAC0H,KAAK,CAAC1H,IAAI,CAACyH,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI;UACpDZ,SAAS,EAAE7G,IAAI,CAAC0H,KAAK,CAAC1H,IAAI,CAACyH,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;UAC7CX,KAAK,EAAExC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG;SACjD;QACD8C,WAAW,CAAC7C,IAAI,CAACwC,MAAM,CAAC;MAC1B;MAEA;MACA,IAAIY,eAAe,GAAGP,WAAW;MACjC,IAAI9G,aAAa,IAAIA,aAAa,CAAC2C,IAAI,EAAE,EAAE;QACzC0E,eAAe,GAAGP,WAAW,CAAChE,MAAM,CAAC2D,MAAM,IACzCA,MAAM,CAAChC,YAAY,CAAC5B,WAAW,EAAE,CAACG,QAAQ,CAAChD,aAAa,CAAC6C,WAAW,EAAE,CAAC,IACtE4D,MAAM,CAACjC,UAAU,IAAIiC,MAAM,CAACjC,UAAU,CAAC3B,WAAW,EAAE,CAACG,QAAQ,CAAChD,aAAa,CAAC6C,WAAW,EAAE,CAAE,IAC3F4D,MAAM,CAACP,OAAO,IAAIO,MAAM,CAACP,OAAO,CAACrD,WAAW,EAAE,CAACG,QAAQ,CAAChD,aAAa,CAAC6C,WAAW,EAAE,CAAE,IACrF4D,MAAM,CAACJ,SAAS,IAAII,MAAM,CAACJ,SAAS,CAACxD,WAAW,EAAE,CAACG,QAAQ,CAAChD,aAAa,CAAC6C,WAAW,EAAE,CAAE,CAC3F;MACH;MAEA;MACA,MAAMS,UAAU,GAAG,CAACoC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAClF,gBAAgB,CAACJ,QAAQ;MACnE,MAAMmD,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC9C,gBAAgB,CAACJ,QAAQ;MAC5D,MAAMsG,YAAY,GAAGW,eAAe,CAAC7D,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;MAEhE;MACA,IAAI,CAACzD,0BAA0B,GAAG4G,YAAY;MAC9C,IAAI,CAAClG,gBAAgB,CAACH,UAAU,GAAGgH,eAAe,CAACnE,MAAM;MACzD,IAAI,CAAC1C,gBAAgB,CAACF,UAAU,GAAGZ,IAAI,CAACyD,IAAI,CAACkE,eAAe,CAACnE,MAAM,GAAG,IAAI,CAAC1C,gBAAgB,CAACJ,QAAQ,CAAC;MACrG,IAAI,CAACI,gBAAgB,CAACL,WAAW,GAAGuF,SAAS;MAE7CE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBJ,UAAU;QACVpF,UAAU,EAAEgH,eAAe,CAACnE,MAAM;QAClC/C,WAAW,EAAEuF,SAAS;QACtB4B,OAAO,EAAEZ;OACV,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAa,qBAAqBA,CAAC3E,OAAe;IACnC,IAAI,CAAC7C,mBAAmB,GAAG6C,OAAO;IAClC,IAAI,IAAI,CAAC/C,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACsC,UAAU,EAAE;MAC7D,IAAI,CAACoD,mBAAmB,CAAC,IAAI,CAAC1F,gBAAgB,CAACsC,UAAU,EAAE,CAAC,EAAES,OAAO,CAAC;IACxE;EACF;EAEA;EACA4C,sBAAsBA,CAAA;IACpB;IACA,IAAI,IAAI,CAAC1F,0BAA0B,CAACoD,MAAM,GAAG,CAAC,EAAE;MAC9C;IACF;IAEA;IACA,MAAMoE,OAAO,GAAG,IAAI,CAACE,sBAAsB;IAC3C,IAAI,CAAChH,gBAAgB,CAACH,UAAU,GAAGiH,OAAO,CAACpE,MAAM;IACjD,IAAI,CAAC1C,gBAAgB,CAACF,UAAU,GAAGZ,IAAI,CAACyD,IAAI,CAAC,IAAI,CAAC3C,gBAAgB,CAACH,UAAU,GAAG,IAAI,CAACG,gBAAgB,CAACJ,QAAQ,CAAC;IAC/G,IAAI,CAACI,gBAAgB,CAACL,WAAW,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,CAACsH,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMH,OAAO,GAAG,IAAI,CAACE,sBAAsB;IAC3C,MAAMlE,UAAU,GAAG,CAAC,IAAI,CAAC9C,gBAAgB,CAACL,WAAW,GAAG,CAAC,IAAI,IAAI,CAACK,gBAAgB,CAACJ,QAAQ;IAC3F,MAAMmD,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC9C,gBAAgB,CAACJ,QAAQ;IAC5D,IAAI,CAACK,gBAAgB,GAAG6G,OAAO,CAAC9D,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC7D;EAEA;EACAmE,cAAcA,CAAChE,IAAY;IACzB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAClD,gBAAgB,CAACF,UAAU,EAAE;MACzD;MACA,IAAI,IAAI,CAACT,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACsC,UAAU,IAAI,IAAI,CAACrC,0BAA0B,CAACoD,MAAM,GAAG,CAAC,EAAE;QAC3G,IAAI,CAACqC,mBAAmB,CAAC,IAAI,CAAC1F,gBAAgB,CAACsC,UAAU,EAAEuB,IAAI,EAAE,IAAI,CAAC3D,mBAAmB,CAAC;MAC5F,CAAC,MAAM;QACL;QACA,IAAI,CAACS,gBAAgB,CAACL,WAAW,GAAGuD,IAAI;QACxC,IAAI,CAAC+D,sBAAsB,EAAE;MAC/B;IACF;EACF;EAEA;EACAE,oBAAoBA,CAAA;IAClB,MAAM/D,KAAK,GAAa,EAAE;IAC1B,MAAMtD,UAAU,GAAG,IAAI,CAACE,gBAAgB,CAACF,UAAU;IACnD,MAAMH,WAAW,GAAG,IAAI,CAACK,gBAAgB,CAACL,WAAW;IAErD;IACA,MAAM0D,SAAS,GAAGnE,IAAI,CAAC0D,GAAG,CAAC,CAAC,EAAEjD,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAM2D,OAAO,GAAGpE,IAAI,CAACqE,GAAG,CAACzD,UAAU,EAAEH,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAI6D,CAAC,GAAGH,SAAS,EAAEG,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCJ,KAAK,CAACK,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOJ,KAAK;EACd;EAEA;EACAgE,OAAOA,CAAA;IACL,IAAI,CAACnI,KAAK,CAAC4F,IAAI,EAAE;EACnB;EAEA;EACAwC,gBAAgBA,CAACC,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACA,MAAMG,kBAAkB,GAAwB;MAC9C7F,WAAW,EAAE0F;KACd;IAEDlC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoC,kBAAkB,CAAC;IAEzD;IACA,IAAI,CAAC7I,eAAe,CAAC8I,kCAAkC,CAAC;MACtDvG,IAAI,EAAEsG;KACP,CAAC,CAACrG,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACA6D,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE/D,QAAQ,CAAC;UAEhC;UACA,IAAI,CAACb,aAAa,EAAE;UAEpB;UACA,IAAI,IAAI,CAACpB,gBAAgB,EAAEsC,UAAU,KAAK2F,UAAU,EAAE;YACpD,IAAI,CAACjI,gBAAgB,GAAG,IAAI;UAC9B;UAEA+F,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;QACtB,CAAC,MAAM;UACL;UACAD,OAAO,CAAClD,KAAK,CAAC,SAAS,EAAEZ,QAAQ,CAAC6E,OAAO,CAAC;UAC1Cf,OAAO,CAAClD,KAAK,CAAC,UAAUZ,QAAQ,CAAC6E,OAAO,IAAI,MAAM,EAAE,CAAC;QACvD;MACF,CAAC;MACDjE,KAAK,EAAGA,KAAK,IAAI;QACf;QACAkD,OAAO,CAAClD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDkD,OAAO,CAAClD,KAAK,CAAC,wBAAwB,CAAC;MACzC;KACD,CAAC;EACJ;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA;EACAyF,mBAAmBA,CAAA;IACjB,IAAI,CAACtI,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAI2H,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC3H,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACD,eAAe,CAACkD,MAAM,CAACsF,CAAC,IAAIA,CAAC,CAACjG,UAAU,KAAK,IAAI,CAACtC,gBAAiB,CAACsC,UAAU,CAAC;EAC7F;EAEA;EACAkG,iBAAiBA,CAACC,KAAa,EAAEvF,QAAkB;IACjD,OAAOA,QAAQ,CAACZ,UAAU,IAAImG,KAAK;EACrC;CACD;AAhlBUC,UAAA,EAAR3J,KAAK,EAAE,C,6DAA2B;AAC1B2J,UAAA,EAAR3J,KAAK,EAAE,C,4DAA0B;AACzB2J,UAAA,EAAR3J,KAAK,EAAE,C,+DAAkC;AAChC2J,UAAA,EAAT1J,MAAM,EAAE,C,8DAA+C;AAC9C0J,UAAA,EAAT1J,MAAM,EAAE,C,qDAAkC;AALhCK,uBAAuB,GAAAqJ,UAAA,EAPnC7J,SAAS,CAAC;EACT8J,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC,CAAC;EAC/CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC9J,YAAY,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc;CAClE,CAAC,C,EACWC,uBAAuB,CAilBnC;;AAsCD;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}