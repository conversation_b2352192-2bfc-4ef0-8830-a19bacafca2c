{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiUserGroupAddDataPost$Json } from '../fn/user-group/api-user-group-add-data-post-json';\nimport { apiUserGroupAddDataPost$Plain } from '../fn/user-group/api-user-group-add-data-post-plain';\nimport { apiUserGroupGetDataPost$Json } from '../fn/user-group/api-user-group-get-data-post-json';\nimport { apiUserGroupGetDataPost$Plain } from '../fn/user-group/api-user-group-get-data-post-plain';\nimport { apiUserGroupGetListPost$Json } from '../fn/user-group/api-user-group-get-list-post-json';\nimport { apiUserGroupGetListPost$Plain } from '../fn/user-group/api-user-group-get-list-post-plain';\nimport { apiUserGroupRemoveDataPost$Json } from '../fn/user-group/api-user-group-remove-data-post-json';\nimport { apiUserGroupRemoveDataPost$Plain } from '../fn/user-group/api-user-group-remove-data-post-plain';\nimport { apiUserGroupSaveDataPost$Json } from '../fn/user-group/api-user-group-save-data-post-json';\nimport { apiUserGroupSaveDataPost$Plain } from '../fn/user-group/api-user-group-save-data-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let UserGroupService = /*#__PURE__*/(() => {\n  class UserGroupService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiUserGroupGetListPost()` */\n    static {\n      this.ApiUserGroupGetListPostPath = '/api/UserGroup/GetList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGroupGetListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupGetListPost$Plain$Response(params, context) {\n      return apiUserGroupGetListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGroupGetListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupGetListPost$Plain(params, context) {\n      return this.apiUserGroupGetListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGroupGetListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupGetListPost$Json$Response(params, context) {\n      return apiUserGroupGetListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGroupGetListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupGetListPost$Json(params, context) {\n      return this.apiUserGroupGetListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiUserGroupGetDataPost()` */\n    static {\n      this.ApiUserGroupGetDataPostPath = '/api/UserGroup/GetData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGroupGetDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupGetDataPost$Plain$Response(params, context) {\n      return apiUserGroupGetDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGroupGetDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupGetDataPost$Plain(params, context) {\n      return this.apiUserGroupGetDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGroupGetDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupGetDataPost$Json$Response(params, context) {\n      return apiUserGroupGetDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGroupGetDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupGetDataPost$Json(params, context) {\n      return this.apiUserGroupGetDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiUserGroupAddDataPost()` */\n    static {\n      this.ApiUserGroupAddDataPostPath = '/api/UserGroup/AddData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGroupAddDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupAddDataPost$Plain$Response(params, context) {\n      return apiUserGroupAddDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGroupAddDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupAddDataPost$Plain(params, context) {\n      return this.apiUserGroupAddDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGroupAddDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupAddDataPost$Json$Response(params, context) {\n      return apiUserGroupAddDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGroupAddDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupAddDataPost$Json(params, context) {\n      return this.apiUserGroupAddDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiUserGroupSaveDataPost()` */\n    static {\n      this.ApiUserGroupSaveDataPostPath = '/api/UserGroup/SaveData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGroupSaveDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupSaveDataPost$Plain$Response(params, context) {\n      return apiUserGroupSaveDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGroupSaveDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupSaveDataPost$Plain(params, context) {\n      return this.apiUserGroupSaveDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGroupSaveDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupSaveDataPost$Json$Response(params, context) {\n      return apiUserGroupSaveDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGroupSaveDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupSaveDataPost$Json(params, context) {\n      return this.apiUserGroupSaveDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiUserGroupRemoveDataPost()` */\n    static {\n      this.ApiUserGroupRemoveDataPostPath = '/api/UserGroup/RemoveData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGroupRemoveDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupRemoveDataPost$Plain$Response(params, context) {\n      return apiUserGroupRemoveDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGroupRemoveDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupRemoveDataPost$Plain(params, context) {\n      return this.apiUserGroupRemoveDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGroupRemoveDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupRemoveDataPost$Json$Response(params, context) {\n      return apiUserGroupRemoveDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGroupRemoveDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGroupRemoveDataPost$Json(params, context) {\n      return this.apiUserGroupRemoveDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function UserGroupService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || UserGroupService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: UserGroupService,\n        factory: UserGroupService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return UserGroupService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}