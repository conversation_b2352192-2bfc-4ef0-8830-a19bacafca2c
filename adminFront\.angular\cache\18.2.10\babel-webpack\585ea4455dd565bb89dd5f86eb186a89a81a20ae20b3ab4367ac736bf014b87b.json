{"ast": null, "code": "import isSameHour from \"../isSameHour/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisHour\n * @category Hour Helpers\n * @summary Is the given date in the same hour as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same hour as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this hour\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:00:00 in this hour?\n * const result = isThisHour(new Date(2014, 8, 25, 18))\n * //=> true\n */\nexport default function isThisHour(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameHour(Date.now(), dirtyDate);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}