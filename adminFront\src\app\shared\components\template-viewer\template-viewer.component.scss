// 色彩變數 - 金色主題
$primary-gold-light: #B8A676;
$primary-gold-dark: #AE9B66;
$primary-gold-darker: #9B8A5A;
$primary-gold-hover: #C4B382;
$primary-gold-active: #A89660;

$gradient-primary: linear-gradient(135deg, $primary-gold-light 0%, $primary-gold-dark 100%);
$gradient-primary-hover: linear-gradient(135deg, $primary-gold-hover 0%, $primary-gold-active 100%);

$text-primary: #2C3E50;
$text-secondary: #6C757D;
$text-tertiary: #ADB5BD;
$text-light: #FFFFFF;

$bg-primary: #FFFFFF;
$bg-secondary: #F8F9FA;
$bg-cream: #FEFCF8;

$border-light: #E9ECEF;
$border-medium: #CDCDCD;

$shadow-sm: 0 1px 3px rgba(174, 155, 102, 0.1);
$shadow-md: 0 2px 8px rgba(174, 155, 102, 0.15);
$shadow-lg: 0 4px 16px rgba(174, 155, 102, 0.2);
$shadow-xl: 0 8px 32px rgba(174, 155, 102, 0.25);

$transition-normal: 0.3s ease;

// 優化的模板查看器樣式
.template-viewer-card {
  width: 90vw;
  max-width: 1200px;
  height: 80vh;
  border-radius: 12px;
  box-shadow: $shadow-xl;
  border: none;
  overflow: hidden;
  background: $bg-primary;
}

.template-viewer-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  color: #2c3e50;
  border-bottom: 2px solid #e9ecef;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .header-title {
    h5 {
      font-weight: 700;
      margin-bottom: 0.25rem;
      color: #2c3e50;
      font-size: 1.25rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      i {
        color: #007bff;
        font-size: 1.1rem;
      }
    }

    small {
      color: #6c757d;
      font-weight: 500;
      font-size: 0.875rem;
    }
  }

  .header-actions {
    .badge {
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      color: white;
      font-size: 0.875rem;
      font-weight: 600;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
      border: none;
    }
  }
}

.template-viewer-body {
  overflow: auto;
  padding: 1.5rem;
  background: $bg-primary;
}

// 增強的搜尋功能樣式
.enhanced-search-container {
  .search-wrapper {
    background: $bg-primary;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: $shadow-md;

    .search-input-group {
      position: relative;
      display: flex;
      align-items: center;
      background: $bg-secondary;
      border-radius: 8px;
      padding: 0.75rem 1rem;
      border: 2px solid transparent;
      transition: $transition-normal;

      &:focus-within {
        border-color: $primary-gold-light;
        background: $bg-primary;
        box-shadow: 0 0 0 3px rgba(184, 166, 118, 0.1);
      }

      .search-icon {
        color: $text-secondary;
        margin-right: 0.75rem;
        font-size: 1rem;
      }

      .search-input {
        flex: 1;
        border: none;
        background: transparent;
        outline: none;
        font-size: 1rem;
        color: $text-primary;

        &::placeholder {
          color: $text-tertiary;
        }
      }

      .search-actions {
        display: flex;
        gap: 0.5rem;

        .clear-search-btn {
          background: #dc3545;
          color: $text-light;
          border: none;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.75rem;
          cursor: pointer;
          transition: $transition-normal;

          &:hover {
            background: #c82333;
            transform: scale(1.1);
          }
        }
      }
    }

    .search-suggestions,
    .search-no-results {
      margin-top: 0.75rem;
      padding: 0.5rem 0;
    }
  }
}

// 模板列表容器樣式
.template-list-container {
  .list-controls {
    background: white;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .list-info {
      .info-text {
        color: #6c757d;
        font-size: 0.9rem;
      }
    }
  }
}


// 簡潔模板列表樣式
.template-list-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  .template-item {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    padding: 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
      border-color: #007bff;

      &::before {
        opacity: 1;
      }
    }

    .template-main-info {
      flex: 1;
      margin-right: 1.5rem;

      .template-header {
        margin-bottom: 0.75rem;

        .template-label {
          display: inline-block;
          background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 12px;
          font-size: 0.7rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
        }

        .template-name {
          color: #2c3e50;
          font-weight: 600;
          margin: 0;
          font-size: 1.1rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          line-height: 1.4;

          i {
            color: #007bff;
            font-size: 1rem;
          }
        }
      }

      .template-meta {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        .meta-row {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;
          padding: 0.375rem 0;

          .meta-label {
            color: #6c757d;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            min-width: 90px;

            i {
              color: #007bff;
              font-size: 0.8rem;
            }
          }

          .meta-value {
            color: #495057;
            font-weight: 500;
            background: #f8f9fa;
            padding: 0.25rem 0.75rem;
            border-radius: 6px;
            font-size: 0.85rem;
            border: 1px solid #e9ecef;
          }
        }
      }
    }

    .template-actions {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      min-width: 110px;

      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.625rem 0.875rem;
        border: none;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
        text-decoration: none;

        &.view-btn {
          background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
          color: white;
          box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);

          &:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
          }

          i {
            font-size: 0.75rem;
          }
        }

        &.delete-btn {
          background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
          color: white;
          box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);

          &:hover {
            background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
          }

          i {
            font-size: 0.75rem;
          }
        }
      }
    }
  }
}

// 空狀態卡片
.empty-state-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 3rem 2rem;
  text-align: center;
  border: 2px dashed #dee2e6;

  .empty-content {
    .empty-icon {
      font-size: 3rem;
      color: #adb5bd;
      margin-bottom: 1rem;
    }

    .empty-title {
      color: #495057;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .empty-description {
      color: #6c757d;
      margin: 0;

      .clear-link {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// 增強的分頁控制器樣式
.enhanced-pagination-container {
  background: $bg-primary;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: $shadow-md;

  .pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .pagination-info {
      .page-info {
        color: $text-secondary;
        font-size: 0.9rem;
        font-weight: 500;
      }
    }

    .pagination-nav {
      .enhanced-pagination {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 0.5rem;

        .page-item {
          .page-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            background: $bg-secondary;
            color: $text-secondary;
            font-weight: 500;
            cursor: pointer;
            transition: $transition-normal;

            &:hover:not(:disabled) {
              background: $gradient-primary;
              color: $text-light;
              transform: translateY(-1px);
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }

            &.page-number {
              font-size: 0.9rem;
            }

            &.first-page,
            &.last-page,
            &.prev-page,
            &.next-page {
              font-size: 0.8rem;
            }
          }

          &.active .page-btn {
            background: $gradient-primary;
            color: $text-light;
            box-shadow: 0 2px 8px rgba(184, 166, 118, 0.3);
          }
        }
      }
    }
  }
}

// 模板詳情視圖樣式
.template-detail-view {
  .detail-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;

    .detail-title-section {
      display: flex;
      align-items: flex-start;
      margin-bottom: 1rem;

      .back-button {
        margin-right: 1rem;

        .back-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 42px;
          height: 42px;
          border: none;
          border-radius: 8px;
          background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
          color: white;
          cursor: pointer;
          transition: all 0.2s ease;
          box-shadow: 0 2px 4px rgba(108, 117, 125, 0.2);

          &:hover {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
          }

          i {
            font-size: 0.9rem;
          }
        }
      }

      .detail-title-info {
        flex: 1;

        .detail-title {
          color: #2c3e50;
          font-weight: 700;
          margin-bottom: 0.5rem;
          font-size: 1.2rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;

          i {
            color: #007bff;
            font-size: 1.1rem;
          }
        }

        .detail-subtitle {
          color: #6c757d;
          margin: 0;
          font-size: 0.9rem;
          font-weight: 500;
        }
      }
    }

    .detail-stats {
      display: flex;
      gap: 1.5rem;

      .stat-item {
        display: flex;
        flex-direction: column;
        background: #f8f9fa;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        min-width: 80px;

        .stat-label {
          font-size: 0.75rem;
          color: #6c757d;
          margin-bottom: 0.25rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .stat-value {
          font-size: 1.1rem;
          font-weight: 700;
          color: #007bff;
        }
      }
    }
  }


}

// 增強的詳情項目列表樣式
.enhanced-detail-list {
  .enhanced-detail-item {
    margin-bottom: 1rem;

    .detail-item-card {
      background: $bg-primary;
      border-radius: 12px;
      box-shadow: $shadow-md;
      border: 1px solid $border-light;
      overflow: hidden;
      transition: $transition-normal;

      &:hover {
        transform: translateY(-1px);
        box-shadow: $shadow-lg;
        border-color: $primary-gold-light;
      }

      .detail-item-header {
        padding: 1.25rem;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        border-bottom: 1px solid $bg-secondary;

        .item-index {
          .index-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: $gradient-primary;
            color: $text-light;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
          }
        }

        .item-main-info {
          flex: 1;

          .item-name {
            color: $text-primary;
            font-weight: 600;
            margin-bottom: 0.75rem;
            font-size: 1.1rem;
          }

          .item-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;

            .meta-item {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              padding: 0.375rem 0.75rem;
              border-radius: 6px;
              font-size: 0.875rem;

              &.id-meta {
                background: rgba(184, 166, 118, 0.1);
                color: $primary-gold-darker;
              }

              &.group-meta {
                background: rgba(184, 166, 118, 0.15);
                color: $primary-gold-dark;
              }

              &.category-meta {
                background: rgba(184, 166, 118, 0.2);
                color: $primary-gold-darker;
              }

              i {
                font-size: 0.75rem;
              }
            }
          }
        }

        .item-actions {
          .create-date {
            color: $text-secondary;
            font-size: 0.875rem;
            background: $bg-secondary;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
          }
        }
      }

      .detail-item-body {
        padding: 1.25rem;

        .item-details-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1rem;
          margin-bottom: 1rem;

          .detail-group {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;

            .detail-label {
              font-size: 0.8rem;
              color: $text-secondary;
              font-weight: 500;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }

            .detail-value {
              font-weight: 600;

              &.price-value {
                color: #28a745;
                font-size: 1.1rem;
              }

              &.quantity-value {
                color: #007bff;
                font-size: 1rem;
              }
            }
          }
        }

        .item-remark {
          .remark-content {
            background: $bg-secondary;
            border-left: 4px solid $primary-gold-light;
            padding: 1rem;
            border-radius: 0 8px 8px 0;

            .remark-text {
              color: $text-primary;
              font-style: italic;
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
}

// 模板查看器底部樣式
.template-viewer-footer {
  background: $bg-secondary;
  border-top: 1px solid $border-light;
  padding: 1rem 1.5rem;

  .footer-actions {
    display: flex;
    justify-content: center;

    .close-btn {
      background: $text-secondary;
      color: $text-light;
      border: none;
      border-radius: 8px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      cursor: pointer;
      transition: $transition-normal;

      &:hover {
        background: $primary-gold-dark;
        transform: translateY(-1px);
      }
    }
  }
}

// 使用 nb-card 結構，移除不必要的包裝樣式

// nb-card-header 按鈕樣式
nb-card-header {
  .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }
  }
}

// 搜尋容器樣式
.search-container {
  .input-group {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;

    .form-control {
      border: none;
      padding: 0.75rem 1rem;
      font-size: 0.95rem;

      &:focus {
        box-shadow: none;
        border-color: transparent;
      }

      &::placeholder {
        color: #999;
        font-style: italic;
      }
    }

    .input-group-append {
      .btn {
        border: none;
        background: #f8f9fa;
        color: #6c757d;
        padding: 0.75rem 1rem;
        transition: all 0.2s ease;

        &:hover {
          background: #e9ecef;
          color: #495057;
        }

        &:focus {
          box-shadow: none;
        }
      }
    }
  }
}

// 搜尋結果資訊
.search-results-info {
  padding: 0.5rem 0;
  border-left: 3px solid #007bff;
  padding-left: 0.75rem;
  background: #f8f9ff;
  border-radius: 4px;
}

// 分頁資訊
.pagination-info {
  padding: 0.5rem 0;
  border-left: 3px solid #28a745;
  padding-left: 0.75rem;
  background: #f8fff8;
  border-radius: 4px;
}

// 表格樣式
.table-responsive {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table {
  margin-bottom: 0;

  thead.thead-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    th {
      border: none;
      font-weight: 600;
      color: #495057;
      padding: 1rem 0.75rem;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  tbody {
    tr {
      transition: all 0.2s ease;

      &:hover {
        background-color: #f8f9ff;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      td {
        padding: 1rem 0.75rem;
        border-color: #f0f0f0;
        vertical-align: middle;

        strong {
          color: #333;
          font-weight: 600;
        }

        .text-muted {
          font-size: 0.9rem;
        }
      }
    }
  }
}

// 按鈕組樣式
.btn-group-sm {
  .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s ease;

    &.btn-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      border: none;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
      }
    }

    &.btn-danger {
      background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
      border: none;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
      }
    }

    i {
      margin-right: 0.25rem;
    }
  }
}

// 空狀態樣式
.empty-state {
  padding: 2rem;

  i {
    display: block;
    margin: 0 auto 1rem;
    opacity: 0.5;
  }

  p {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  a {
    color: #007bff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 模板詳情模態框
.template-detail-modal {
  margin-top: 1.5rem;
  background: #fff;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.template-detail-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 1.25rem 1.5rem;
  border-bottom: 2px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  h6 {
    color: #2c3e50;
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    i {
      color: #007bff;
      font-size: 1rem;
    }
  }

  .btn {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.2s ease;

    &.btn-secondary {
      background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
      border: none;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
      }
    }
  }
}

.template-detail-content {
  padding: 1.5rem;
  max-height: 400px;
  overflow-y: auto;

  .template-description {
    background: #f8f9ff;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #007bff;

    strong {
      color: #495057;
    }
  }

  .template-items {
    h6 {
      color: #495057;
      font-weight: 600;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid #e9ecef;

      i {
        color: #6c757d;
      }
    }
  }
}

.detail-list {
  .detail-item {
    transition: all 0.2s ease;

    &:hover {
      background: #f8f9ff;
      border-radius: 6px;
      margin: 0 -0.5rem;
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }

    &:last-child {
      border-bottom: none;
    }

    .detail-index {
      .badge {
        background: #e9ecef;
        color: #6c757d;
        font-weight: 500;
        padding: 0.375rem 0.5rem;
        border-radius: 50%;
        min-width: 2rem;
        text-align: center;
      }
    }



    .detail-content {

      .detail-field {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;

        strong {
          color: #495057;
        }
      }

      .detail-value {
        font-size: 0.875rem;
        line-height: 1.4;
        word-break: break-word;
      }
    }
  }
}

// 新增模板表單樣式 - 簡潔版
.add-template-form {
  margin-bottom: 1.5rem;

  .template-form {
    background: #ffffff;
    border: 1px solid #e8ecef;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    }
  }

  .input-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .input-group {
    display: flex;
    flex-direction: column;

    &.full-width {
      grid-column: 1 / -1;
    }
  }

  .input-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;

    .required {
      color: #ef4444;
      margin-left: 0.125rem;
    }
  }

  .input-field {
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: #ffffff;

    &:focus {
      outline: none;
      border-color: #28a745;
      box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
    }

    &::placeholder {
      color: #9ca3af;
    }
  }

  .items-selector {
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: #f9fafb;
    max-height: 200px;
    overflow-y: auto;
  }

  .empty-items {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 2rem;
    color: #6b7280;
    font-size: 0.875rem;

    i {
      color: #9ca3af;
    }
  }

  .item-option {
    border-bottom: 1px solid #e5e7eb;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: #f3f4f6;
    }
  }

  .item-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    cursor: pointer;
    margin: 0;
    width: 100%;
  }

  .item-checkbox {
    margin: 0;
    margin-top: 0.125rem;
    width: 1rem;
    height: 1rem;
    accent-color: #28a745;
  }

  .item-content {
    flex: 1;
    min-width: 0;
  }

  .item-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
    line-height: 1.4;
  }

  .item-desc {
    font-size: 0.8rem;
    color: #6b7280;
    line-height: 1.3;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  .btn-cancel,
  .btn-save {
    padding: 0.625rem 1.25rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
  }

  .btn-cancel {
    background: #f3f4f6;
    color: #374151;

    &:hover {
      background: #e5e7eb;
      transform: translateY(-1px);
    }
  }

  .btn-save {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(40, 167, 69, 0.25);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  }
}

// 分頁控制器樣式
.pagination-container {
  .pagination {
    .page-item {
      .page-link {
        border: 1px solid #dee2e6;
        color: #6c757d;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        border-radius: 4px;
        margin: 0 0.125rem;

        &:hover {
          background-color: #e9ecef;
          border-color: #adb5bd;
          color: #495057;
          transform: translateY(-1px);
        }

        &:focus {
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        i {
          font-size: 0.75rem;
        }
      }

      &.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
      }

      &.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          transform: none;
        }
      }
    }
  }
}

// 詳情分頁樣式
.detail-pagination {
  .pagination {
    .page-item {
      .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        border-radius: 3px;
        margin: 0 0.0625rem;

        &:hover {
          background-color: #f8f9fa;
        }
      }

      &.active .page-link {
        background-color: #28a745;
        border-color: #28a745;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
      }
    }
  }
}

// 新的詳情項目樣式
.detail-item {
  transition: background-color 0.2s;

  &:hover {
    background-color: #f8f9fa;
  }

  .detail-header {
    .detail-main {
      .detail-name {
        color: #495057;
        font-size: 0.95rem;
      }

      .detail-info {
        .badge {
          font-size: 0.75rem;
          margin-bottom: 0.25rem;
        }
      }

      .detail-remark {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        border-left: 3px solid #dee2e6;
      }
    }
  }
}

// 詳情搜尋框樣式
.template-detail-search {
  .input-group {
    .form-control {
      border-radius: 0.25rem 0 0 0.25rem;

      &:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }

    .input-group-append {
      .btn {
        border-radius: 0;

        &:last-child {
          border-radius: 0 0.25rem 0.25rem 0;
        }

        &.btn-outline-secondary {
          &:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
          }
        }
      }
    }
  }
}

// 響應式設計 - 模板列表優化
@media (max-width: 768px) {
  .template-list-container {
    .template-item {
      flex-direction: column;
      gap: 1rem;
      padding: 1rem;

      .template-main-info {
        margin-right: 0;

        .template-header {
          .template-name {
            font-size: 1rem;
          }
        }

        .template-meta {
          .meta-row {
            .meta-label {
              min-width: 80px;
              font-size: 0.8rem;
            }

            .meta-value {
              font-size: 0.8rem;
              padding: 0.2rem 0.5rem;
            }
          }
        }
      }

      .template-actions {
        flex-direction: row;
        justify-content: center;
        min-width: auto;
        gap: 0.75rem;

        .action-btn {
          flex: 1;
          min-width: 80px;
          padding: 0.5rem 0.75rem;
          font-size: 0.75rem;
        }
      }
    }
  }

  .list-controls {
    padding: 0.75rem 1rem !important;

    .list-info .info-text {
      font-size: 0.8rem;
    }
  }
}