{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction TemplateViewerComponent_div_13_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_13_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(2, \"i\", 30);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_13_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\", 32);\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u627E\\u5230 \", ctx_r1.filteredTemplates.length, \" \\u500B\\u76F8\\u95DC\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_div_13_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"small\", 35);\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵtext(3, \"\\u672A\\u627E\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"div\", 21)(3, \"div\", 22);\n    i0.ɵɵelement(4, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_13_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchKeyword, $event) || (ctx_r1.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function TemplateViewerComponent_div_13_Template_input_input_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    })(\"keyup.enter\", function TemplateViewerComponent_div_13_Template_input_keyup_enter_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_13_div_6_Template, 3, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, TemplateViewerComponent_div_13_div_7_Template, 4, 1, \"div\", 26)(8, TemplateViewerComponent_div_13_div_8_Template, 4, 0, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword && ctx_r1.filteredTemplates.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword && ctx_r1.filteredTemplates.length === 0);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 4)(2, \"div\", 43)(3, \"span\", 44);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 45)(6, \"small\", 8);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A\\u7B2C \", (ctx_r1.templatePagination.currentPage - 1) * ctx_r1.templatePagination.pageSize + 1, \" - \", ctx_r1.Math.min(ctx_r1.templatePagination.currentPage * ctx_r1.templatePagination.pageSize, ctx_r1.templatePagination.totalItems), \" \\u9805\\uFF0C \\u5171 \", ctx_r1.templatePagination.totalItems, \" \\u9805\\u6A21\\u677F \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\\u7B2C \", ctx_r1.templatePagination.currentPage, \" / \", ctx_r1.templatePagination.totalPages, \" \\u9801\");\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"span\", 59);\n    i0.ɵɵelement(2, \"i\", 60);\n    i0.ɵɵtext(3, \"\\u5EFA\\u7ACB\\u65E5\\u671F\\uFF1A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 61);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 1, tpl_r5.CreateTime, \"yyyy/MM/dd HH:mm\"));\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"span\", 59);\n    i0.ɵɵelement(2, \"i\", 62);\n    i0.ɵɵtext(3, \"\\u66F4\\u65B0\\u6642\\u9593\\uFF1A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 61);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 1, tpl_r5.UpdateTime, \"yyyy/MM/dd HH:mm\"));\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_3_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const tpl_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(tpl_r5.TemplateID && ctx_r1.onDeleteTemplate(tpl_r5.TemplateID));\n    });\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 48)(3, \"span\", 49);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\", 50);\n    i0.ɵɵelement(6, \"i\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 52);\n    i0.ɵɵtemplate(9, TemplateViewerComponent_div_14_div_3_div_9_Template, 7, 4, \"div\", 53)(10, TemplateViewerComponent_div_14_div_3_div_10_Template, 7, 4, \"div\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 54)(12, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_3_Template_button_click_12_listener() {\n      const tpl_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r5));\n    });\n    i0.ɵɵelement(13, \"i\", 56);\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, TemplateViewerComponent_div_14_div_3_button_16_Template, 4, 0, \"button\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r5 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", tpl_r5.TemplateName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", tpl_r5.CreateTime);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", tpl_r5.UpdateTime);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", tpl_r5.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 23);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 72);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p\", 73);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5617\\u8A66\\u5176\\u4ED6\\u95DC\\u9375\\u5B57\\u6216 \");\n    i0.ɵɵelementStart(2, \"a\", 74);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_4_p_7_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(3, \"\\u6E05\\u9664\\u641C\\u5C0B\\u689D\\u4EF6\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 73);\n    i0.ɵɵtext(1, \" \\u76EE\\u524D\\u9084\\u6C92\\u6709\\u5EFA\\u7ACB\\u4EFB\\u4F55\\u6A21\\u677F\\uFF0C\\u8ACB\\u5148\\u5EFA\\u7ACB\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66)(2, \"div\", 67);\n    i0.ɵɵtemplate(3, TemplateViewerComponent_div_14_div_4_i_3_Template, 1, 0, \"i\", 68)(4, TemplateViewerComponent_div_14_div_4_i_4_Template, 1, 0, \"i\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\", 70);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, TemplateViewerComponent_div_14_div_4_p_7_Template, 4, 0, \"p\", 71)(8, TemplateViewerComponent_div_14_div_4_p_8_Template, 2, 0, \"p\", 71);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u66AB\\u7121\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_5_li_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 81)(1, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_li_13_Template_button_click_1_listener() {\n      const page_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(page_r10));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r10 === ctx_r1.templatePagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r10);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"div\", 77)(3, \"span\", 78);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"nav\", 79)(6, \"ul\", 80)(7, \"li\", 81)(8, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(1));\n    });\n    i0.ɵɵelement(9, \"i\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"li\", 81)(11, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage - 1));\n    });\n    i0.ɵɵelement(12, \"i\", 85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, TemplateViewerComponent_div_14_div_5_li_13_Template, 3, 3, \"li\", 86);\n    i0.ɵɵelementStart(14, \"li\", 81)(15, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage + 1));\n    });\n    i0.ɵɵelement(16, \"i\", 88);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"li\", 81)(18, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.totalPages));\n    });\n    i0.ɵɵelement(19, \"i\", 90);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.templatePagination.currentPage, \" \\u9801\\uFF0C\\u5171 \", ctx_r1.templatePagination.totalPages, \" \\u9801 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getTemplatePageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_14_div_1_Template, 8, 5, \"div\", 38);\n    i0.ɵɵelementStart(2, \"div\", 37);\n    i0.ɵɵtemplate(3, TemplateViewerComponent_div_14_div_3_Template, 17, 4, \"div\", 39)(4, TemplateViewerComponent_div_14_div_4_Template, 9, 5, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TemplateViewerComponent_div_14_div_5_Template, 20, 15, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalItems > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedTemplates)(\"ngForTrackBy\", ctx_r1.trackByTemplateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.paginatedTemplates || ctx_r1.paginatedTemplates.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalPages > 1);\n  }\n}\nfunction TemplateViewerComponent_div_15_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 111);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedTemplate.Description, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"span\", 104);\n    i0.ɵɵtext(2, \"\\u9801\\u6578\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 105);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.detailPagination.currentPage, \" / \", ctx_r1.detailPagination.totalPages, \"\");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_div_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearDetailSearch());\n    });\n    i0.ɵɵelement(2, \"i\", 30);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_15_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\", 32);\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u627E\\u5230 \", ctx_r1.detailPagination.totalItems, \" \\u500B\\u76F8\\u95DC\\u9805\\u76EE \");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"small\", 35);\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵtext(3, \"\\u672A\\u627E\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 130);\n    i0.ɵɵelement(1, \"i\", 131);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r13.tblRequirement == null ? null : detail_r13.tblRequirement.CGroupName);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 132);\n    i0.ɵɵelement(1, \"i\", 133);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r13.tblRequirement == null ? null : detail_r13.tblRequirement.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_div_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137)(1, \"span\", 138);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 139);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const config_r14 = ctx.$implicit;\n    const detail_r13 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getFieldClass(config_r14.fieldName))(\"hidden\", !ctx_r1.getFieldValue(detail_r13, config_r14.fieldName));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(config_r14.displayName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getValueClass(config_r14.fieldName));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatFieldValue(detail_r13, config_r14.fieldName), \" \");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 134)(1, \"div\", 135);\n    i0.ɵɵtemplate(2, TemplateViewerComponent_div_15_div_28_div_1_div_21_div_2_Template, 5, 5, \"div\", 136);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.fieldDisplayConfig);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114)(1, \"div\", 115)(2, \"div\", 116)(3, \"div\", 117)(4, \"span\", 118);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 119)(7, \"h6\", 120);\n    i0.ɵɵelement(8, \"i\", 121);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 122)(11, \"span\", 123);\n    i0.ɵɵelement(12, \"i\", 124);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, TemplateViewerComponent_div_15_div_28_div_1_span_15_Template, 4, 1, \"span\", 125)(16, TemplateViewerComponent_div_15_div_28_div_1_span_16_Template, 4, 1, \"span\", 126);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 127)(18, \"span\", 128);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, TemplateViewerComponent_div_15_div_28_div_1_div_21_Template, 3, 1, \"div\", 129);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = ctx.$implicit;\n    const i_r15 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r15 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (detail_r13.tblRequirement == null ? null : detail_r13.tblRequirement.CRequirement) || \"\\u672A\\u547D\\u540D\\u9805\\u76EE\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(detail_r13.CReleateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r13.tblRequirement == null ? null : detail_r13.tblRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r13.tblRequirement == null ? null : detail_r13.tblRequirement.CRemark);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 7, detail_r13.tblRequirement == null ? null : detail_r13.tblRequirement.CCreateDt, \"MM/dd\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasVisibleFields(detail_r13));\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_15_div_28_div_1_Template, 22, 10, \"div\", 113);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetailsData);\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_29_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 143)(1, \"div\", 144)(2, \"span\", 145);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 146)(5, \"div\", 147)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 148);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r16 = ctx.$implicit;\n    const i_r17 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r17 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", detail_r16.FieldName, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", detail_r16.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_29_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_15_ng_template_29_div_0_div_1_Template, 10, 3, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedDetails);\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TemplateViewerComponent_div_15_ng_template_29_div_0_Template, 2, 1, \"div\", 140);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const noDetails_r18 = i0.ɵɵreference(33);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetails.length > 0)(\"ngIfElse\", noDetails_r18);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_31_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 81)(1, \"button\", 153);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_div_31_li_6_Template_button_click_1_listener() {\n      const page_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(page_r21));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r21 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r21 === ctx_r1.detailPagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r21);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 149)(1, \"nav\", 150)(2, \"ul\", 151)(3, \"li\", 81)(4, \"button\", 152);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_div_31_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_15_div_31_li_6_Template, 3, 3, \"li\", 86);\n    i0.ɵɵelementStart(7, \"li\", 81)(8, \"button\", 152);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_div_31_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 88);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getDetailPageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 154);\n    i0.ɵɵelement(1, \"i\", 155);\n    i0.ɵɵelementStart(2, \"p\", 156);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 93)(2, \"div\", 94)(3, \"div\", 95)(4, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵelement(5, \"i\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 98)(7, \"h5\", 99);\n    i0.ɵɵelement(8, \"i\", 100);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, TemplateViewerComponent_div_15_p_10_Template, 2, 1, \"p\", 101);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 102)(12, \"div\", 103)(13, \"span\", 104);\n    i0.ɵɵtext(14, \"\\u9805\\u76EE\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 105);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, TemplateViewerComponent_div_15_div_17_Template, 5, 2, \"div\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 19)(19, \"div\", 20)(20, \"div\", 21)(21, \"div\", 22);\n    i0.ɵɵelement(22, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"input\", 107);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_15_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.detailSearchKeyword, $event) || (ctx_r1.detailSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function TemplateViewerComponent_div_15_Template_input_input_23_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDetailSearch());\n    })(\"keyup.enter\", function TemplateViewerComponent_div_15_Template_input_keyup_enter_23_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDetailSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, TemplateViewerComponent_div_15_div_24_Template, 3, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, TemplateViewerComponent_div_15_div_25_Template, 4, 1, \"div\", 26)(26, TemplateViewerComponent_div_15_div_26_Template, 4, 0, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 108);\n    i0.ɵɵtemplate(28, TemplateViewerComponent_div_15_div_28_Template, 2, 1, \"div\", 109)(29, TemplateViewerComponent_div_15_ng_template_29_Template, 1, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(31, TemplateViewerComponent_div_15_div_31_Template, 10, 7, \"div\", 110)(32, TemplateViewerComponent_div_15_ng_template_32_Template, 4, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const checkOldDetails_r22 = i0.ɵɵreference(30);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedTemplate.TemplateName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTemplate.Description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.currentTemplateDetailsData.length > 0 ? ctx_r1.detailPagination.totalItems : ctx_r1.currentTemplateDetails.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailSearchKeyword && ctx_r1.detailPagination.totalItems > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailSearchKeyword && ctx_r1.detailPagination.totalItems === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetailsData.length > 0)(\"ngIfElse\", checkOldDetails_r22);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.templateType = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\n    this.fieldDisplayConfig = [{\n      displayName: '工程項目',\n      fieldName: 'CRequirement'\n    }, {\n      displayName: '群組類別',\n      fieldName: 'CGroupName'\n    }, {\n      displayName: '單價',\n      fieldName: 'CUnitPrice'\n    }, {\n      displayName: '單位',\n      fieldName: 'CUnit'\n    }, {\n      displayName: '備註',\n      fieldName: 'CRemark'\n    }]; // 欄位顯示設定，由父元件傳入\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 公開Math對象供模板使用\n    this.Math = Math;\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = []; // 保留用於向後相容\n    this.selectedTemplate = null;\n    // 新的詳情資料管理\n    this.currentTemplateDetailsData = [];\n    this.detailSearchKeyword = ''; // 明細專用搜尋關鍵字\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 分頁功能 - 模板列表\n    this.templatePagination = {\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedTemplates = [];\n    // 分頁功能 - 模板詳情\n    this.detailPagination = {\n      currentPage: 1,\n      pageSize: 5,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedDetails = [];\n  }\n  ngOnInit() {\n    this.loadTemplates();\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 載入模板列表 - 使用真實的 API 調用\n  loadTemplates() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      PageIndex: 1,\n      // 暫時載入第一頁\n      PageSize: 100,\n      // 暫時載入較多資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateList API\n    this.templateService.apiTemplateGetTemplateListPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // API 調用成功\n          // 轉換 API 回應為內部格式\n          this.templates = response.Entries.map(item => ({\n            TemplateID: item.CTemplateId,\n            TemplateName: item.CTemplateName || '',\n            Description: item.CTemplateName || '',\n            CreateTime: item.CCreateDt,\n            UpdateTime: item.CUpdateDt,\n            Creator: item.CCreator || undefined,\n            Updator: item.CUpdator || undefined\n          }));\n          // 更新過濾列表\n          this.updateFilteredTemplates();\n          // 清空詳情資料，改為按需載入\n          this.templateDetails = [];\n          this.currentTemplateDetailsData = [];\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n          this.templateDetails = [];\n          this.updateFilteredTemplates();\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n        this.templates = [];\n        this.templateDetails = [];\n        this.updateFilteredTemplates();\n      }\n    });\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n    this.updateTemplatePagination();\n  }\n  // 更新模板分頁\n  updateTemplatePagination() {\n    this.templatePagination.totalItems = this.filteredTemplates.length;\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\n    // 確保當前頁面不超過總頁數\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\n    }\n    this.updatePaginatedTemplates();\n  }\n  // 更新分頁後的模板列表\n  updatePaginatedTemplates() {\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\n    const endIndex = startIndex + this.templatePagination.pageSize;\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\n  }\n  // 模板分頁導航\n  goToTemplatePage(page) {\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = page;\n      this.updatePaginatedTemplates();\n    }\n  }\n  // 獲取模板分頁頁碼數組\n  getTemplatePageNumbers() {\n    const pages = [];\n    const totalPages = this.templatePagination.totalPages;\n    const currentPage = this.templatePagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n    // 按需載入模板詳情\n    if (template.TemplateID) {\n      this.loadTemplateDetails(template.TemplateID);\n    }\n    this.updateDetailPagination();\n  }\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\n  loadTemplateDetails(templateId, pageIndex = 1, searchKeyword) {\n    const args = {\n      templateId: templateId\n    };\n    // 調用真實的 GetTemplateDetailById API\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\n          let allDetails = response.Entries.map(item => ({\n            CTemplateDetailId: item.CTemplateDetailId || 0,\n            CTemplateId: item.CTemplateId || templateId,\n            CReleateId: item.CReleateId || 0,\n            CSort: undefined,\n            CRemark: undefined,\n            CCreateDt: new Date().toISOString(),\n            CCreator: '系統',\n            CCategory: undefined,\n            CUnitPrice: undefined,\n            CQuantity: undefined,\n            CUnit: undefined\n          }));\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\n          if (searchKeyword && searchKeyword.trim()) {\n            allDetails = allDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()));\n          }\n          // 前端分頁處理 (因為 API 不支援分頁參數)\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n          const endIndex = startIndex + this.detailPagination.pageSize;\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\n          // 更新詳情資料和分頁資訊\n          this.currentTemplateDetailsData = pagedDetails;\n          this.detailPagination.totalItems = allDetails.length;\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\n          this.detailPagination.currentPage = pageIndex;\n        } else {\n          this.currentTemplateDetailsData = [];\n          this.detailPagination.totalItems = 0;\n          this.detailPagination.totalPages = 0;\n          this.detailPagination.currentPage = 1;\n        }\n      },\n      error: () => {\n        this.currentTemplateDetailsData = [];\n        this.detailPagination.totalItems = 0;\n        this.detailPagination.totalPages = 0;\n        this.detailPagination.currentPage = 1;\n      }\n    });\n  }\n  // 搜尋模板詳情 (明細專用搜尋)\n  searchTemplateDetails(keyword) {\n    this.detailSearchKeyword = keyword;\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\n    }\n  }\n  // 清除明細搜尋\n  clearDetailSearch() {\n    this.detailSearchKeyword = '';\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1);\n    }\n  }\n  // 明細搜尋輸入事件處理\n  onDetailSearchInput() {\n    // 可以在這裡添加即時搜尋邏輯，目前保持原有的 Enter 鍵搜尋方式\n  }\n  // 明細搜尋事件處理（即時搜尋）\n  onDetailSearch() {\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, this.detailSearchKeyword);\n    }\n  }\n  // 更新詳情分頁 (保留向後相容)\n  updateDetailPagination() {\n    // 如果使用新的詳情資料，直接返回\n    if (this.currentTemplateDetailsData.length > 0) {\n      return;\n    }\n    // 向後相容：使用舊的詳情資料\n    const details = this.currentTemplateDetails;\n    this.detailPagination.totalItems = details.length;\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\n    this.detailPagination.currentPage = 1; // 重置到第一頁\n    this.updatePaginatedDetails();\n  }\n  // 更新分頁後的詳情列表\n  updatePaginatedDetails() {\n    const details = this.currentTemplateDetails;\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\n    const endIndex = startIndex + this.detailPagination.pageSize;\n    this.paginatedDetails = details.slice(startIndex, endIndex);\n  }\n  // 詳情分頁導航\n  goToDetailPage(page) {\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\n      // 如果使用新的詳情資料，重新載入\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\n      } else {\n        // 向後相容：使用舊的分頁邏輯\n        this.detailPagination.currentPage = page;\n        this.updatePaginatedDetails();\n      }\n    }\n  }\n  // 獲取詳情分頁頁碼數組\n  getDetailPageNumbers() {\n    const pages = [];\n    const totalPages = this.detailPagination.totalPages;\n    const currentPage = this.detailPagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // 刪除模板 - 使用真實的 API 調用\n  deleteTemplateById(templateID) {\n    // 準備 API 請求參數\n    const deleteTemplateArgs = {\n      CTemplateId: templateID\n    };\n    // 調用 DeleteTemplate API\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\n      body: deleteTemplateArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          // 重新載入模板列表\n          this.loadTemplates();\n          // 如果當前查看的模板被刪除，關閉詳情\n          if (this.selectedTemplate?.TemplateID === templateID) {\n            this.selectedTemplate = null;\n          }\n        } else {\n          // API 返回錯誤\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n      }\n    });\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/Template/GetTemplateList\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/Template/SaveTemplate\n   *    請求體: {\n   *      CTemplateName: string,\n   *      CTemplateType: number,  // 1=客變需求\n   *      CStatus: number,\n   *      Details: [{\n   *        CReleateId: number,        // 關聯主檔ID\n   *        CReleateName: string       // 關聯名稱\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    POST /api/Template/DeleteTemplate\n   *    請求體: { CTemplateId: number }\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 欄位顯示相關輔助方法\n  hasVisibleFields(detail) {\n    return this.fieldDisplayConfig.some(config => this.getFieldValue(detail, config.fieldName));\n  }\n  getFieldValue(detail, fieldName) {\n    // 根據templateType動態取得資料\n    if (this.templateType === 1) {\n      // 當type=1時，從tblRequirement物件中取得資料\n      return detail.tblRequirement?.[fieldName];\n    } else {\n      // 其他type可能從tblMaterials或其他物件中取得資料\n      // 目前先支援直接從detail物件取得\n      return detail[fieldName];\n    }\n  }\n  getFieldClass(fieldName) {\n    const classMap = {\n      'CUnitPrice': 'price-group',\n      'CQuantity': 'quantity-group',\n      'CUnit': 'unit-group',\n      'CRemark': 'remark-group',\n      'CGroupName': 'group-group',\n      'CCategory': 'category-group'\n    };\n    return classMap[fieldName] || 'default-group';\n  }\n  getValueClass(fieldName) {\n    const classMap = {\n      'CUnitPrice': 'price-value',\n      'CQuantity': 'quantity-value',\n      'CUnit': 'unit-value',\n      'CRemark': 'remark-value'\n    };\n    return classMap[fieldName] || 'default-value';\n  }\n  formatFieldValue(detail, fieldName) {\n    const value = this.getFieldValue(detail, fieldName);\n    if (!value && value !== 0) return '';\n    switch (fieldName) {\n      case 'CUnitPrice':\n        return `NT$ ${Number(value).toLocaleString()}`;\n      case 'CQuantity':\n        const unit = detail.CUnit || '';\n        return unit ? `${value} ${unit}` : value.toString();\n      default:\n        return value.toString();\n    }\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        templateType: \"templateType\",\n        fieldDisplayConfig: \"fieldDisplayConfig\"\n      },\n      outputs: {\n        selectTemplate: \"selectTemplate\",\n        close: \"close\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 21,\n      vars: 4,\n      consts: [[\"checkOldDetails\", \"\"], [\"noDetails\", \"\"], [1, \"template-viewer-card\"], [1, \"template-viewer-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"header-title\"], [1, \"mb-0\"], [1, \"fas\", \"fa-layer-group\", \"mr-2\", \"text-primary\"], [1, \"text-muted\"], [1, \"header-actions\"], [1, \"badge\", \"badge-info\"], [1, \"template-viewer-body\"], [\"class\", \"enhanced-search-container mb-4\", 4, \"ngIf\"], [\"class\", \"template-list-container\", 4, \"ngIf\"], [\"class\", \"template-detail-view\", 4, \"ngIf\"], [1, \"template-viewer-footer\"], [1, \"footer-actions\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [1, \"enhanced-search-container\", \"mb-4\"], [1, \"search-wrapper\"], [1, \"search-input-group\"], [1, \"search-icon\"], [1, \"fas\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u63CF\\u8FF0\\u6216\\u95DC\\u9375\\u5B57...\", 1, \"search-input\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [\"class\", \"search-actions\", 4, \"ngIf\"], [\"class\", \"search-suggestions\", 4, \"ngIf\"], [\"class\", \"search-no-results\", 4, \"ngIf\"], [1, \"search-actions\"], [\"type\", \"button\", \"title\", \"\\u6E05\\u9664\\u641C\\u5C0B\", 1, \"clear-search-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"search-suggestions\"], [1, \"text-success\"], [1, \"fas\", \"fa-check-circle\", \"mr-1\"], [1, \"search-no-results\"], [1, \"text-warning\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"template-list-container\"], [\"class\", \"list-controls mb-3\", 4, \"ngIf\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"empty-state-card\", 4, \"ngIf\"], [\"class\", \"enhanced-pagination-container mt-4\", 4, \"ngIf\"], [1, \"list-controls\", \"mb-3\"], [1, \"list-info\"], [1, \"info-text\"], [1, \"view-options\"], [1, \"template-item\"], [1, \"template-main-info\"], [1, \"template-header\"], [1, \"template-label\"], [1, \"template-name\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\"], [1, \"template-meta\"], [\"class\", \"meta-row\", 4, \"ngIf\"], [1, \"template-actions\"], [\"title\", \"\\u67E5\\u770B\\u8A73\\u60C5\", 1, \"action-btn\", \"view-btn\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"action-btn delete-btn\", \"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 3, \"click\", 4, \"ngIf\"], [1, \"meta-row\"], [1, \"meta-label\"], [1, \"fas\", \"fa-calendar-plus\", \"mr-1\"], [1, \"meta-value\"], [1, \"fas\", \"fa-clock\", \"mr-1\"], [\"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 1, \"action-btn\", \"delete-btn\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"empty-state-card\"], [1, \"empty-content\"], [1, \"empty-icon\"], [\"class\", \"fas fa-search\", 4, \"ngIf\"], [\"class\", \"fas fa-folder-open\", 4, \"ngIf\"], [1, \"empty-title\"], [\"class\", \"empty-description\", 4, \"ngIf\"], [1, \"fas\", \"fa-folder-open\"], [1, \"empty-description\"], [\"href\", \"javascript:void(0)\", 1, \"clear-link\", 3, \"click\"], [1, \"enhanced-pagination-container\", \"mt-4\"], [1, \"pagination-wrapper\"], [1, \"pagination-info\"], [1, \"page-info\"], [\"aria-label\", \"\\u6A21\\u677F\\u5217\\u8868\\u5206\\u9801\", 1, \"pagination-nav\"], [1, \"enhanced-pagination\"], [1, \"page-item\"], [\"title\", \"\\u7B2C\\u4E00\\u9801\", 1, \"page-btn\", \"first-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [\"title\", \"\\u4E0A\\u4E00\\u9801\", 1, \"page-btn\", \"prev-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"\\u4E0B\\u4E00\\u9801\", 1, \"page-btn\", \"next-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [\"title\", \"\\u6700\\u5F8C\\u4E00\\u9801\", 1, \"page-btn\", \"last-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"page-btn\", \"page-number\", 3, \"click\"], [1, \"template-detail-view\"], [1, \"detail-header\"], [1, \"detail-title-section\"], [1, \"back-button\"], [\"title\", \"\\u8FD4\\u56DE\\u6A21\\u677F\\u5217\\u8868\", 1, \"back-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"detail-title-info\"], [1, \"detail-title\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\", \"text-primary\"], [\"class\", \"detail-subtitle\", 4, \"ngIf\"], [1, \"detail-stats\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u660E\\u7D30\\u9805\\u76EE\\u540D\\u7A31\\u3001\\u7FA4\\u7D44...\", 1, \"search-input\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [1, \"detail-content\"], [\"class\", \"enhanced-detail-list\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"detail-pagination mt-3\", 4, \"ngIf\"], [1, \"detail-subtitle\"], [1, \"enhanced-detail-list\"], [\"class\", \"enhanced-detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"enhanced-detail-item\"], [1, \"detail-item-card\"], [1, \"detail-item-header\"], [1, \"item-index\"], [1, \"index-badge\"], [1, \"item-main-info\"], [1, \"item-name\"], [1, \"fas\", \"fa-cog\", \"mr-2\", \"text-secondary\"], [1, \"item-meta\"], [1, \"meta-item\", \"id-meta\"], [1, \"fas\", \"fa-hashtag\"], [\"class\", \"meta-item group-meta\", 4, \"ngIf\"], [\"class\", \"meta-item category-meta\", 4, \"ngIf\"], [1, \"item-actions\"], [1, \"create-date\"], [\"class\", \"detail-item-body\", 4, \"ngIf\"], [1, \"meta-item\", \"group-meta\"], [1, \"fas\", \"fa-layer-group\"], [1, \"meta-item\", \"category-meta\"], [1, \"fas\", \"fa-tag\"], [1, \"detail-item-body\"], [1, \"item-details-grid\"], [\"class\", \"detail-group\", 3, \"ngClass\", \"hidden\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-group\", 3, \"ngClass\", \"hidden\"], [1, \"detail-label\"], [1, \"detail-value\", 3, \"ngClass\"], [\"class\", \"detail-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-list\"], [\"class\", \"detail-item d-flex align-items-center py-2 border-bottom\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\", \"d-flex\", \"align-items-center\", \"py-2\", \"border-bottom\"], [1, \"detail-index\"], [1, \"badge\", \"badge-light\"], [1, \"detail-content\", \"flex-grow-1\", \"ml-2\"], [1, \"detail-field\"], [1, \"detail-value\", \"text-muted\"], [1, \"detail-pagination\", \"mt-3\"], [\"aria-label\", \"\\u6A21\\u677F\\u8A73\\u60C5\\u5206\\u9801\"], [1, \"pagination\", \"pagination-sm\", \"justify-content-center\", \"mb-0\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"py-3\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"h5\", 6);\n          i0.ɵɵelement(5, \"i\", 7);\n          i0.ɵɵtext(6, \"\\u6A21\\u677F\\u7BA1\\u7406 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"small\", 8);\n          i0.ɵɵtext(8, \"\\u7BA1\\u7406\\u548C\\u67E5\\u770B\\u5BA2\\u8B8A\\u9700\\u6C42\\u6A21\\u677F\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"span\", 10);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"nb-card-body\", 11);\n          i0.ɵɵtemplate(13, TemplateViewerComponent_div_13_Template, 9, 4, \"div\", 12)(14, TemplateViewerComponent_div_14_Template, 6, 5, \"div\", 13)(15, TemplateViewerComponent_div_15_Template, 34, 11, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"nb-card-footer\", 15)(17, \"div\", 16)(18, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_18_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelement(19, \"i\", 18);\n          i0.ɵɵtext(20, \"\\u95DC\\u9589 \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\"\", ctx.templatePagination.totalItems, \" \\u500B\\u6A21\\u677F\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n        }\n      },\n      dependencies: [CommonModule, i2.NgClass, i2.NgForOf, i2.NgIf, i2.DatePipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, NbCardModule, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardFooterComponent, i4.NbCardHeaderComponent, NbButtonModule],\n      styles: [\".template-viewer-card[_ngcontent-%COMP%] {\\n  width: 90vw;\\n  max-width: 1200px;\\n  height: 80vh;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(174, 155, 102, 0.25);\\n  border: none;\\n  overflow: hidden;\\n  background: #FFFFFF;\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n  color: #2c3e50;\\n  border-bottom: 2px solid #e9ecef;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  margin-bottom: 0.25rem;\\n  color: #2c3e50;\\n  font-size: 1.25rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 1.1rem;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-weight: 500;\\n  font-size: 0.875rem;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  color: white;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  padding: 0.5rem 1rem;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);\\n  border: none;\\n}\\n\\n.template-viewer-body[_ngcontent-%COMP%] {\\n  overflow: auto;\\n  padding: 1.5rem;\\n  background: #FFFFFF;\\n}\\n\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  background: #F8F9FA;\\n  border-radius: 8px;\\n  padding: 0.75rem 1rem;\\n  border: 2px solid transparent;\\n  transition: 0.3s ease;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]:focus-within {\\n  border-color: #B8A676;\\n  background: #FFFFFF;\\n  box-shadow: 0 0 0 3px rgba(184, 166, 118, 0.1);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  margin-right: 0.75rem;\\n  font-size: 1rem;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  background: transparent;\\n  outline: none;\\n  font-size: 1rem;\\n  color: #2C3E50;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder {\\n  color: #ADB5BD;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-search-btn[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  color: #FFFFFF;\\n  border: none;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.75rem;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-search-btn[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n  transform: scale(1.1);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-suggestions[_ngcontent-%COMP%], \\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-no-results[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  padding: 0.5rem 0;\\n}\\n\\n.template-list-container[_ngcontent-%COMP%]   .list-controls[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 1rem 1.5rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n}\\n.template-list-container[_ngcontent-%COMP%]   .list-controls[_ngcontent-%COMP%]   .list-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n\\n.template-list-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n  border-radius: 10px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  border: 1px solid #e9ecef;\\n  padding: 1.25rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);\\n  transform: translateY(-2px);\\n  border-color: #007bff;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 1.5rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]   .template-label[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  color: white;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 12px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n  margin: 0;\\n  font-size: 1.1rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  line-height: 1.4;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 1rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  min-width: 90px;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 0.8rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 500;\\n  background: #f8f9fa;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 6px;\\n  font-size: 0.85rem;\\n  border: 1px solid #e9ecef;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  min-width: 110px;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  padding: 0.625rem 0.875rem;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  white-space: nowrap;\\n  text-decoration: none;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n  color: white;\\n  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n.empty-state-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  padding: 3rem 2rem;\\n  text-align: center;\\n  border: 2px dashed #dee2e6;\\n}\\n.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #adb5bd;\\n  margin-bottom: 1rem;\\n}\\n.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n}\\n.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin: 0;\\n}\\n.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]   .clear-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]   .clear-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.enhanced-pagination-container[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]   .page-info[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n  gap: 0.5rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 40px;\\n  height: 40px;\\n  border: none;\\n  border-radius: 8px;\\n  background: #F8F9FA;\\n  color: #6C757D;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  transform: translateY(-1px);\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.page-number[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.first-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.last-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.prev-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.next-page[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.3);\\n}\\n\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n  border-radius: 10px;\\n  padding: 1.5rem;\\n  margin-bottom: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n  border: 1px solid #e9ecef;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  margin-bottom: 1rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 42px;\\n  height: 42px;\\n  border: none;\\n  border-radius: 8px;\\n  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.2);\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 700;\\n  margin-bottom: 0.5rem;\\n  font-size: 1.2rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 1.1rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-subtitle[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin: 0;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1.5rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  background: #f8f9fa;\\n  padding: 0.75rem 1rem;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n  min-width: 80px;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  margin-bottom: 0.25rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 700;\\n  color: #007bff;\\n}\\n\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n  border: 1px solid #E9ECEF;\\n  overflow: hidden;\\n  transition: 0.3s ease;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 16px rgba(174, 155, 102, 0.2);\\n  border-color: #B8A676;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%] {\\n  padding: 1.25rem;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  border-bottom: 1px solid #F8F9FA;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-index[_ngcontent-%COMP%]   .index-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 600;\\n  margin-bottom: 0.75rem;\\n  font-size: 1.1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.75rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 6px;\\n  font-size: 0.875rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.id-meta[_ngcontent-%COMP%] {\\n  background: rgba(184, 166, 118, 0.1);\\n  color: #9B8A5A;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.group-meta[_ngcontent-%COMP%] {\\n  background: rgba(184, 166, 118, 0.15);\\n  color: #AE9B66;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.category-meta[_ngcontent-%COMP%] {\\n  background: rgba(184, 166, 118, 0.2);\\n  color: #9B8A5A;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-actions[_ngcontent-%COMP%]   .create-date[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  font-size: 0.875rem;\\n  background: #F8F9FA;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 6px;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%] {\\n  padding: 1.25rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6C757D;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value.price-value[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1.1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value.quantity-value[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%]   .remark-content[_ngcontent-%COMP%] {\\n  background: #F8F9FA;\\n  border-left: 4px solid #B8A676;\\n  padding: 1rem;\\n  border-radius: 0 8px 8px 0;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%]   .remark-content[_ngcontent-%COMP%]   .remark-text[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-style: italic;\\n  line-height: 1.5;\\n}\\n\\n.template-viewer-footer[_ngcontent-%COMP%] {\\n  background: #F8F9FA;\\n  border-top: 1px solid #E9ECEF;\\n  padding: 1rem 1.5rem;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  background: #6C757D;\\n  color: #FFFFFF;\\n  border: none;\\n  border-radius: 8px;\\n  padding: 0.75rem 2rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: #AE9B66;\\n  transform: translateY(-1px);\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  border: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: none;\\n  padding: 0.75rem 1rem;\\n  font-size: 0.95rem;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-style: italic;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  padding: 0.75rem 1rem;\\n  transition: all 0.2s ease;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n.search-results-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #007bff;\\n  padding-left: 0.75rem;\\n  background: #f8f9ff;\\n  border-radius: 4px;\\n}\\n\\n.pagination-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #28a745;\\n  padding-left: 0.75rem;\\n  background: #f8fff8;\\n  border-radius: 4px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  font-size: 0.9rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9ff;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  border-color: #f0f0f0;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto 1rem;\\n  opacity: 0.5;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: 0.5rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.template-detail-modal[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  background: #fff;\\n  border: 2px solid #e9ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n  padding: 1.25rem 1.5rem;\\n  border-bottom: 2px solid #e9ecef;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 700;\\n  font-size: 1.1rem;\\n  margin-bottom: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 1rem;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  padding: 0.5rem 1rem;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);\\n  border: none;\\n  color: white;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);\\n}\\n\\n.template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  background: #f8f9ff;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  border-left: 4px solid #007bff;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9ff;\\n  border-radius: 6px;\\n  margin: 0 -0.5rem;\\n  padding-left: 0.5rem !important;\\n  padding-right: 0.5rem !important;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0.375rem 0.5rem;\\n  border-radius: 50%;\\n  min-width: 2rem;\\n  text-align: center;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n  word-break: break-word;\\n}\\n\\n.add-template-form[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .template-form[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e8ecef;\\n  border-radius: 12px;\\n  padding: 2rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .template-form[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  margin-left: 0.125rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  background: #ffffff;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .items-selector[_ngcontent-%COMP%] {\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  background: #f9fafb;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  padding: 2rem;\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n  padding: 0.875rem 1rem;\\n  cursor: pointer;\\n  margin: 0;\\n  width: 100%;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-top: 0.125rem;\\n  width: 1rem;\\n  height: 1rem;\\n  accent-color: #28a745;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.25rem;\\n  line-height: 1.4;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-desc[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 0.75rem;\\n  margin-top: 1.5rem;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%], \\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  padding: 0.625rem 1.25rem;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border: none;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  min-width: 80px;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%] {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover {\\n  background: #e5e7eb;\\n  transform: translateY(-1px);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.25);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n  color: #6c757d;\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  border-radius: 4px;\\n  margin: 0 0.125rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  color: #495057;\\n  transform: translateY(-1px);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  background-color: #fff;\\n  border-color: #dee2e6;\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n}\\n\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.8rem;\\n  border-radius: 3px;\\n  margin: 0 0.0625rem;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  border-color: #28a745;\\n  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  transition: background-color 0.2s;\\n}\\n.detail-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-name[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.95rem;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-remark[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n  border-left: 3px solid #dee2e6;\\n}\\n\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-radius: 0.25rem 0 0 0.25rem;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n  border-radius: 0 0.25rem 0.25rem 0;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n\\n@media (max-width: 768px) {\\n  .template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    padding: 1rem;\\n  }\\n  .template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%] {\\n    margin-right: 0;\\n  }\\n  .template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%] {\\n    min-width: 80px;\\n    font-size: 0.8rem;\\n  }\\n  .template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    padding: 0.2rem 0.5rem;\\n  }\\n  .template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: center;\\n    min-width: auto;\\n    gap: 0.75rem;\\n  }\\n  .template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-width: 80px;\\n    padding: 0.5rem 0.75rem;\\n    font-size: 0.75rem;\\n  }\\n  .list-controls[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem !important;\\n  }\\n  .list-controls[_ngcontent-%COMP%]   .list-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvdGVtcGxhdGUtdmlld2VyL3RlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUE4QkE7RUFDRSxXQUFBO0VBQ0EsaUJBQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxnREFWVTtFQVdWLFlBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQXZCVztBQU5iOztBQWdDQTtFQUNFLDZEQUFBO0VBQ0EsY0FBQTtFQUNBLGdDQUFBO0VBQ0EsZUFBQTtFQUNBLHlDQUFBO0FBN0JGO0FBZ0NJO0VBQ0UsZ0JBQUE7RUFDQSxzQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUE5Qk47QUFnQ007RUFDRSxjQUFBO0VBQ0EsaUJBQUE7QUE5QlI7QUFrQ0k7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtBQWhDTjtBQXFDSTtFQUNFLDZEQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQkFBQTtFQUNBLGtCQUFBO0VBQ0EsNENBQUE7RUFDQSxZQUFBO0FBbkNOOztBQXdDQTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsbUJBekVXO0FBb0NiOztBQTBDRTtFQUNFLG1CQS9FUztFQWdGVCxtQkFBQTtFQUNBLGVBQUE7RUFDQSwrQ0ExRVE7QUFtQ1o7QUF5Q0k7RUFDRSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQXZGUztFQXdGVCxrQkFBQTtFQUNBLHFCQUFBO0VBQ0EsNkJBQUE7RUFDQSxxQkFoRmM7QUF5Q3BCO0FBeUNNO0VBQ0UscUJBN0dhO0VBOEdiLG1CQWhHSztFQWlHTCw4Q0FBQTtBQXZDUjtBQTBDTTtFQUNFLGNBekdTO0VBMEdULHFCQUFBO0VBQ0EsZUFBQTtBQXhDUjtBQTJDTTtFQUNFLE9BQUE7RUFDQSxZQUFBO0VBQ0EsdUJBQUE7RUFDQSxhQUFBO0VBQ0EsZUFBQTtFQUNBLGNBckhPO0FBNEVmO0FBMkNRO0VBQ0UsY0F0SE07QUE2RWhCO0FBNkNNO0VBQ0UsYUFBQTtFQUNBLFdBQUE7QUEzQ1I7QUE2Q1E7RUFDRSxtQkFBQTtFQUNBLGNBL0hHO0VBZ0lILFlBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxxQkEzSFU7QUFnRnBCO0FBNkNVO0VBQ0UsbUJBQUE7RUFDQSxxQkFBQTtBQTNDWjtBQWlESTs7RUFFRSxtQkFBQTtFQUNBLGlCQUFBO0FBL0NOOztBQXNERTtFQUNFLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxvQkFBQTtFQUNBLHlDQUFBO0FBbkRKO0FBc0RNO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0FBcERSOztBQTREQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFlBQUE7QUF6REY7QUEyREU7RUFDRSw2REFBQTtFQUNBLG1CQUFBO0VBQ0EseUNBQUE7RUFDQSx5QkFBQTtFQUNBLHlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSx1QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUF6REo7QUEyREk7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFVBQUE7RUFDQSxZQUFBO0VBQ0EsNkRBQUE7RUFDQSxVQUFBO0VBQ0EsNkJBQUE7QUF6RE47QUE0REk7RUFDRSwwQ0FBQTtFQUNBLDJCQUFBO0VBQ0EscUJBQUE7QUExRE47QUE0RE07RUFDRSxVQUFBO0FBMURSO0FBOERJO0VBQ0UsT0FBQTtFQUNBLG9CQUFBO0FBNUROO0FBOERNO0VBQ0Usc0JBQUE7QUE1RFI7QUE4RFE7RUFDRSxxQkFBQTtFQUNBLDZEQUFBO0VBQ0EsWUFBQTtFQUNBLHdCQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7RUFDQSx5QkFBQTtFQUNBLHFCQUFBO0VBQ0EsNENBQUE7QUE1RFY7QUErRFE7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxTQUFBO0VBQ0EsaUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7QUE3RFY7QUErRFU7RUFDRSxjQUFBO0VBQ0EsZUFBQTtBQTdEWjtBQWtFTTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFdBQUE7QUFoRVI7QUFrRVE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtBQWhFVjtBQWtFVTtFQUNFLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0FBaEVaO0FBa0VZO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0FBaEVkO0FBb0VVO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7RUFDQSx3QkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtBQWxFWjtBQXdFSTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtBQXRFTjtBQXdFTTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsV0FBQTtFQUNBLDBCQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLG1CQUFBO0VBQ0EscUJBQUE7QUF0RVI7QUF3RVE7RUFDRSw2REFBQTtFQUNBLFlBQUE7RUFDQSw0Q0FBQTtBQXRFVjtBQXdFVTtFQUNFLDZEQUFBO0VBQ0EsMkJBQUE7RUFDQSw0Q0FBQTtBQXRFWjtBQXlFVTtFQUNFLGtCQUFBO0FBdkVaO0FBMkVRO0VBQ0UsNkRBQUE7RUFDQSxZQUFBO0VBQ0EsNENBQUE7QUF6RVY7QUEyRVU7RUFDRSw2REFBQTtFQUNBLDJCQUFBO0VBQ0EsNENBQUE7QUF6RVo7QUE0RVU7RUFDRSxrQkFBQTtBQTFFWjs7QUFtRkE7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsMENBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0VBQ0EsMEJBQUE7QUFoRkY7QUFtRkk7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0FBakZOO0FBb0ZJO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7QUFsRk47QUFxRkk7RUFDRSxjQUFBO0VBQ0EsU0FBQTtBQW5GTjtBQXFGTTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0FBbkZSO0FBcUZRO0VBQ0UsMEJBQUE7QUFuRlY7O0FBMkZBO0VBQ0UsbUJBbllXO0VBb1lYLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLCtDQTlYVTtBQXNTWjtBQTBGRTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0FBeEZKO0FBMkZNO0VBQ0UsY0FuWlM7RUFvWlQsaUJBQUE7RUFDQSxnQkFBQTtBQXpGUjtBQThGTTtFQUNFLGFBQUE7RUFDQSxnQkFBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0VBQ0EsV0FBQTtBQTVGUjtBQStGVTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFyYUc7RUFzYUgsY0EzYUs7RUE0YUwsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EscUJBOVpRO0FBaVVwQjtBQStGWTtFQUNFLDZEQXJiSztFQXNiTCxjQWhiRDtFQWliQywyQkFBQTtBQTdGZDtBQWdHWTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtBQTlGZDtBQWlHWTtFQUNFLGlCQUFBO0FBL0ZkO0FBa0dZO0VBSUUsaUJBQUE7QUFuR2Q7QUF1R1U7RUFDRSw2REE1Y087RUE2Y1AsY0F2Y0M7RUF3Y0QsOENBQUE7QUFyR1o7O0FBK0dFO0VBQ0UsNkRBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxxQkFBQTtFQUNBLHlDQUFBO0VBQ0EseUJBQUE7QUE1R0o7QUE4R0k7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQTVHTjtBQThHTTtFQUNFLGtCQUFBO0FBNUdSO0FBOEdRO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLDZEQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLDhDQUFBO0FBNUdWO0FBOEdVO0VBQ0UsNkRBQUE7RUFDQSwyQkFBQTtFQUNBLDRDQUFBO0FBNUdaO0FBK0dVO0VBQ0UsaUJBQUE7QUE3R1o7QUFrSE07RUFDRSxPQUFBO0FBaEhSO0FBa0hRO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7RUFDQSxpQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUFoSFY7QUFrSFU7RUFDRSxjQUFBO0VBQ0EsaUJBQUE7QUFoSFo7QUFvSFE7RUFDRSxjQUFBO0VBQ0EsU0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFsSFY7QUF1SEk7RUFDRSxhQUFBO0VBQ0EsV0FBQTtBQXJITjtBQXVITTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EscUJBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtBQXJIUjtBQXVIUTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtFQUNBLHFCQUFBO0FBckhWO0FBd0hRO0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUF0SFY7O0FBaUlFO0VBQ0UsbUJBQUE7QUE5SEo7QUFnSUk7RUFDRSxtQkE3akJPO0VBOGpCUCxtQkFBQTtFQUNBLCtDQXZqQk07RUF3akJOLHlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxxQkF0akJjO0FBd2JwQjtBQWdJTTtFQUNFLDJCQUFBO0VBQ0EsK0NBN2pCSTtFQThqQkoscUJBcmxCYTtBQXVkckI7QUFpSU07RUFDRSxnQkFBQTtFQUNBLGFBQUE7RUFDQSx1QkFBQTtFQUNBLFNBQUE7RUFDQSxnQ0FBQTtBQS9IUjtBQWtJVTtFQUNFLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsNkRBaG1CTztFQWltQlAsY0EzbEJDO0VBNGxCRCxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7QUFoSVo7QUFvSVE7RUFDRSxPQUFBO0FBbElWO0FBb0lVO0VBQ0UsY0F6bUJHO0VBMG1CSCxnQkFBQTtFQUNBLHNCQUFBO0VBQ0EsaUJBQUE7QUFsSVo7QUFxSVU7RUFDRSxhQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7QUFuSVo7QUFxSVk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0FBbklkO0FBcUljO0VBQ0Usb0NBQUE7RUFDQSxjQXJvQk07QUFrZ0J0QjtBQXNJYztFQUNFLHFDQUFBO0VBQ0EsY0Ezb0JJO0FBdWdCcEI7QUF1SWM7RUFDRSxvQ0FBQTtFQUNBLGNBL29CTTtBQTBnQnRCO0FBd0ljO0VBQ0Usa0JBQUE7QUF0SWhCO0FBNklVO0VBQ0UsY0FucEJLO0VBb3BCTCxtQkFBQTtFQUNBLG1CQWhwQkc7RUFpcEJILHlCQUFBO0VBQ0Esa0JBQUE7QUEzSVo7QUFnSk07RUFDRSxnQkFBQTtBQTlJUjtBQWdKUTtFQUNFLGFBQUE7RUFDQSwyREFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQTlJVjtBQWdKVTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFlBQUE7QUE5SVo7QUFnSlk7RUFDRSxpQkFBQTtFQUNBLGNBNXFCRztFQTZxQkgsZ0JBQUE7RUFDQSx5QkFBQTtFQUNBLHFCQUFBO0FBOUlkO0FBaUpZO0VBQ0UsZ0JBQUE7QUEvSWQ7QUFpSmM7RUFDRSxjQUFBO0VBQ0EsaUJBQUE7QUEvSWhCO0FBa0pjO0VBQ0UsY0FBQTtFQUNBLGVBQUE7QUFoSmhCO0FBdUpVO0VBQ0UsbUJBL3JCRztFQWdzQkgsOEJBQUE7RUFDQSxhQUFBO0VBQ0EsMEJBQUE7QUFySlo7QUF1Slk7RUFDRSxjQTNzQkM7RUE0c0JELGtCQUFBO0VBQ0EsZ0JBQUE7QUFySmQ7O0FBK0pBO0VBQ0UsbUJBbHRCYTtFQW10QmIsNkJBQUE7RUFDQSxvQkFBQTtBQTVKRjtBQThKRTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtBQTVKSjtBQThKSTtFQUNFLG1CQWh1Qlc7RUFpdUJYLGNBL3RCTztFQWd1QlAsWUFBQTtFQUNBLGtCQUFBO0VBQ0EscUJBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxxQkF2dEJjO0FBMmpCcEI7QUE4Sk07RUFDRSxtQkFudkJZO0VBb3ZCWiwyQkFBQTtBQTVKUjs7QUFzS0U7RUFDRSw2REFBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0FBbktKO0FBcUtJO0VBQ0UsMkJBQUE7RUFDQSw2Q0FBQTtBQW5LTjs7QUEwS0U7RUFDRSx3Q0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUF2S0o7QUF5S0k7RUFDRSxZQUFBO0VBQ0EscUJBQUE7RUFDQSxrQkFBQTtBQXZLTjtBQXlLTTtFQUNFLGdCQUFBO0VBQ0EseUJBQUE7QUF2S1I7QUEwS007RUFDRSxXQUFBO0VBQ0Esa0JBQUE7QUF4S1I7QUE2S007RUFDRSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0VBQ0EscUJBQUE7RUFDQSx5QkFBQTtBQTNLUjtBQTZLUTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtBQTNLVjtBQThLUTtFQUNFLGdCQUFBO0FBNUtWOztBQW9MQTtFQUNFLGlCQUFBO0VBQ0EsOEJBQUE7RUFDQSxxQkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7QUFqTEY7O0FBcUxBO0VBQ0UsaUJBQUE7RUFDQSw4QkFBQTtFQUNBLHFCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtBQWxMRjs7QUFzTEE7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EseUNBQUE7QUFuTEY7O0FBc0xBO0VBQ0UsZ0JBQUE7QUFuTEY7QUFxTEU7RUFDRSw2REFBQTtBQW5MSjtBQXFMSTtFQUNFLFlBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxxQkFBQTtFQUNBLGlCQUFBO0VBQ0EseUJBQUE7RUFDQSxxQkFBQTtBQW5MTjtBQXdMSTtFQUNFLHlCQUFBO0FBdExOO0FBd0xNO0VBQ0UseUJBQUE7RUFDQSwyQkFBQTtFQUNBLHdDQUFBO0FBdExSO0FBeUxNO0VBQ0UscUJBQUE7RUFDQSxxQkFBQTtFQUNBLHNCQUFBO0FBdkxSO0FBeUxRO0VBQ0UsV0FBQTtFQUNBLGdCQUFBO0FBdkxWO0FBMExRO0VBQ0UsaUJBQUE7QUF4TFY7O0FBaU1FO0VBQ0UseUJBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtBQTlMSjtBQWdNSTtFQUNFLDZEQUFBO0VBQ0EsWUFBQTtBQTlMTjtBQWdNTTtFQUNFLDJCQUFBO0VBQ0EsOENBQUE7QUE5TFI7QUFrTUk7RUFDRSw2REFBQTtFQUNBLFlBQUE7QUFoTU47QUFrTU07RUFDRSwyQkFBQTtFQUNBLDZDQUFBO0FBaE1SO0FBb01JO0VBQ0UscUJBQUE7QUFsTU47O0FBd01BO0VBQ0UsYUFBQTtBQXJNRjtBQXVNRTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7QUFyTUo7QUF3TUU7RUFDRSxpQkFBQTtFQUNBLHFCQUFBO0FBdE1KO0FBeU1FO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0FBdk1KO0FBeU1JO0VBQ0UsMEJBQUE7QUF2TU47O0FBNk1BO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHlDQUFBO0VBQ0Esa0NBQUE7QUExTUY7O0FBNk1BO0VBQ0U7SUFDRSxVQUFBO0lBQ0EsNEJBQUE7RUExTUY7RUE2TUE7SUFDRSxVQUFBO0lBQ0Esd0JBQUE7RUEzTUY7QUFDRjtBQThNQTtFQUNFLDZEQUFBO0VBQ0EsdUJBQUE7RUFDQSxnQ0FBQTtFQUNBLHlDQUFBO0FBNU1GO0FBOE1FO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUE1TUo7QUE4TUk7RUFDRSxjQUFBO0VBQ0EsZUFBQTtBQTVNTjtBQWdORTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQkFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7QUE5TUo7QUFnTkk7RUFDRSw2REFBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0FBOU1OO0FBZ05NO0VBQ0UsNkRBQUE7RUFDQSwyQkFBQTtFQUNBLDhDQUFBO0FBOU1SOztBQW9OQTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0FBak5GO0FBbU5FO0VBQ0UsbUJBQUE7RUFDQSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSw4QkFBQTtBQWpOSjtBQW1OSTtFQUNFLGNBQUE7QUFqTk47QUFzTkk7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxzQkFBQTtFQUNBLGdDQUFBO0FBcE5OO0FBc05NO0VBQ0UsY0FBQTtBQXBOUjs7QUEyTkU7RUFDRSx5QkFBQTtBQXhOSjtBQTBOSTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLCtCQUFBO0VBQ0EsZ0NBQUE7QUF4Tk47QUEyTkk7RUFDRSxtQkFBQTtBQXpOTjtBQTZOTTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0Esd0JBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtBQTNOUjtBQW1PTTtFQUNFLGlCQUFBO0VBQ0Esc0JBQUE7QUFqT1I7QUFtT1E7RUFDRSxjQUFBO0FBak9WO0FBcU9NO0VBQ0UsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0FBbk9SOztBQTBPQTtFQUNFLHFCQUFBO0FBdk9GO0FBeU9FO0VBQ0UsbUJBQUE7RUFDQSx5QkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLDBDQUFBO0VBQ0EseUJBQUE7QUF2T0o7QUF5T0k7RUFDRSwwQ0FBQTtBQXZPTjtBQTJPRTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLFNBQUE7RUFDQSxxQkFBQTtBQXpPSjtBQTJPSTtFQU5GO0lBT0ksMEJBQUE7RUF4T0o7QUFDRjtBQTJPRTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtBQXpPSjtBQTJPSTtFQUNFLGlCQUFBO0FBek9OO0FBNk9FO0VBQ0UsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxxQkFBQTtBQTNPSjtBQTZPSTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtBQTNPTjtBQStPRTtFQUNFLHFCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxtQkFBQTtBQTdPSjtBQStPSTtFQUNFLGFBQUE7RUFDQSxxQkFBQTtFQUNBLDRDQUFBO0FBN09OO0FBZ1BJO0VBQ0UsY0FBQTtBQTlPTjtBQWtQRTtFQUNFLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFoUEo7QUFtUEU7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7RUFDQSxhQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0FBalBKO0FBbVBJO0VBQ0UsY0FBQTtBQWpQTjtBQXFQRTtFQUNFLGdDQUFBO0FBblBKO0FBcVBJO0VBQ0UsbUJBQUE7QUFuUE47QUFzUEk7RUFDRSxtQkFBQTtBQXBQTjtBQXdQRTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLFlBQUE7RUFDQSxzQkFBQTtFQUNBLGVBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtBQXRQSjtBQXlQRTtFQUNFLFNBQUE7RUFDQSxvQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EscUJBQUE7QUF2UEo7QUEwUEU7RUFDRSxPQUFBO0VBQ0EsWUFBQTtBQXhQSjtBQTJQRTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQkFBQTtBQXpQSjtBQTRQRTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBMVBKO0FBNlBFO0VBQ0UsYUFBQTtFQUNBLHlCQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSw2QkFBQTtBQTNQSjtBQThQRTs7RUFFRSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7RUFDQSxlQUFBO0FBNVBKO0FBK1BFO0VBQ0UsbUJBQUE7RUFDQSxjQUFBO0FBN1BKO0FBK1BJO0VBQ0UsbUJBQUE7RUFDQSwyQkFBQTtBQTdQTjtBQWlRRTtFQUNFLDZEQUFBO0VBQ0EsWUFBQTtBQS9QSjtBQWlRSTtFQUNFLDJCQUFBO0VBQ0EsOENBQUE7QUEvUE47QUFrUUk7RUFDRSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUFoUU47O0FBeVFNO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBQXRRUjtBQXdRUTtFQUNFLHlCQUFBO0VBQ0EscUJBQUE7RUFDQSxjQUFBO0VBQ0EsMkJBQUE7QUF0UVY7QUF5UVE7RUFDRSxnREFBQTtBQXZRVjtBQTBRUTtFQUNFLGtCQUFBO0FBeFFWO0FBNFFNO0VBQ0UseUJBQUE7RUFDQSxxQkFBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtFQUNBLDRDQUFBO0FBMVFSO0FBNlFNO0VBQ0UsY0FBQTtFQUNBLHNCQUFBO0VBQ0EscUJBQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7QUEzUVI7QUE2UVE7RUFDRSxlQUFBO0FBM1FWOztBQXNSTTtFQUNFLHVCQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0FBblJSO0FBcVJRO0VBQ0UseUJBQUE7QUFuUlY7QUF1Uk07RUFDRSx5QkFBQTtFQUNBLHFCQUFBO0VBQ0EsNENBQUE7QUFyUlI7O0FBNFJBO0VBQ0UsaUNBQUE7QUF6UkY7QUEyUkU7RUFDRSx5QkFBQTtBQXpSSjtBQThSTTtFQUNFLGNBQUE7RUFDQSxrQkFBQTtBQTVSUjtBQWdTUTtFQUNFLGtCQUFBO0VBQ0Esc0JBQUE7QUE5UlY7QUFrU007RUFDRSxpQkFBQTtFQUNBLHVCQUFBO0VBQ0EseUJBQUE7RUFDQSxzQkFBQTtFQUNBLDhCQUFBO0FBaFNSOztBQXlTSTtFQUNFLGtDQUFBO0FBdFNOO0FBd1NNO0VBQ0UscUJBQUE7RUFDQSxnREFBQTtBQXRTUjtBQTJTTTtFQUNFLGdCQUFBO0FBelNSO0FBMlNRO0VBQ0Usa0NBQUE7QUF6U1Y7QUE2U1U7RUFDRSx5QkFBQTtFQUNBLHFCQUFBO0VBQ0EsWUFBQTtBQTNTWjs7QUFvVEE7RUFFSTtJQUNFLHNCQUFBO0lBQ0EsU0FBQTtJQUNBLGFBQUE7RUFsVEo7RUFvVEk7SUFDRSxlQUFBO0VBbFROO0VBcVRRO0lBQ0UsZUFBQTtFQW5UVjtFQXlUVTtJQUNFLGVBQUE7SUFDQSxpQkFBQTtFQXZUWjtFQTBUVTtJQUNFLGlCQUFBO0lBQ0Esc0JBQUE7RUF4VFo7RUE4VEk7SUFDRSxtQkFBQTtJQUNBLHVCQUFBO0lBQ0EsZUFBQTtJQUNBLFlBQUE7RUE1VE47RUE4VE07SUFDRSxPQUFBO0lBQ0EsZUFBQTtJQUNBLHVCQUFBO0lBQ0Esa0JBQUE7RUE1VFI7RUFrVUE7SUFDRSxnQ0FBQTtFQWhVRjtFQWtVRTtJQUNFLGlCQUFBO0VBaFVKO0FBQ0Y7QUFDQSw0aTFEQUE0aTFEIiwic291cmNlc0NvbnRlbnQiOlsiLy8gw6jCicKyw6XCvcKpw6jCrsKKw6bClcK4IC0gw6nCh8KRw6jCicKyw6TCuMK7w6nCocKMXHJcbiRwcmltYXJ5LWdvbGQtbGlnaHQ6ICNCOEE2NzY7XHJcbiRwcmltYXJ5LWdvbGQtZGFyazogI0FFOUI2NjtcclxuJHByaW1hcnktZ29sZC1kYXJrZXI6ICM5QjhBNUE7XHJcbiRwcmltYXJ5LWdvbGQtaG92ZXI6ICNDNEIzODI7XHJcbiRwcmltYXJ5LWdvbGQtYWN0aXZlOiAjQTg5NjYwO1xyXG5cclxuJGdyYWRpZW50LXByaW1hcnk6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICRwcmltYXJ5LWdvbGQtbGlnaHQgMCUsICRwcmltYXJ5LWdvbGQtZGFyayAxMDAlKTtcclxuJGdyYWRpZW50LXByaW1hcnktaG92ZXI6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICRwcmltYXJ5LWdvbGQtaG92ZXIgMCUsICRwcmltYXJ5LWdvbGQtYWN0aXZlIDEwMCUpO1xyXG5cclxuJHRleHQtcHJpbWFyeTogIzJDM0U1MDtcclxuJHRleHQtc2Vjb25kYXJ5OiAjNkM3NTdEO1xyXG4kdGV4dC10ZXJ0aWFyeTogI0FEQjVCRDtcclxuJHRleHQtbGlnaHQ6ICNGRkZGRkY7XHJcblxyXG4kYmctcHJpbWFyeTogI0ZGRkZGRjtcclxuJGJnLXNlY29uZGFyeTogI0Y4RjlGQTtcclxuJGJnLWNyZWFtOiAjRkVGQ0Y4O1xyXG5cclxuJGJvcmRlci1saWdodDogI0U5RUNFRjtcclxuJGJvcmRlci1tZWRpdW06ICNDRENEQ0Q7XHJcblxyXG4kc2hhZG93LXNtOiAwIDFweCAzcHggcmdiYSgxNzQsIDE1NSwgMTAyLCAwLjEpO1xyXG4kc2hhZG93LW1kOiAwIDJweCA4cHggcmdiYSgxNzQsIDE1NSwgMTAyLCAwLjE1KTtcclxuJHNoYWRvdy1sZzogMCA0cHggMTZweCByZ2JhKDE3NCwgMTU1LCAxMDIsIDAuMik7XHJcbiRzaGFkb3cteGw6IDAgOHB4IDMycHggcmdiYSgxNzQsIDE1NSwgMTAyLCAwLjI1KTtcclxuXHJcbiR0cmFuc2l0aW9uLW5vcm1hbDogMC4zcyBlYXNlO1xyXG5cclxuLy8gw6XChMKqw6XCjMKWw6fCmsKEw6bCqMKhw6bCncK/w6bCn8Klw6fCnMKLw6XCmcKow6bCqMKjw6XCvMKPXHJcbi50ZW1wbGF0ZS12aWV3ZXItY2FyZCB7XHJcbiAgd2lkdGg6IDkwdnc7XHJcbiAgbWF4LXdpZHRoOiAxMjAwcHg7XHJcbiAgaGVpZ2h0OiA4MHZoO1xyXG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgYm94LXNoYWRvdzogJHNoYWRvdy14bDtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICBiYWNrZ3JvdW5kOiAkYmctcHJpbWFyeTtcclxufVxyXG5cclxuLnRlbXBsYXRlLXZpZXdlci1oZWFkZXIge1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmZmZmYgMCUsICNmOGY5ZmEgMTAwJSk7XHJcbiAgY29sb3I6ICMyYzNlNTA7XHJcbiAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICNlOWVjZWY7XHJcbiAgcGFkZGluZzogMS41cmVtO1xyXG4gIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xyXG5cclxuICAuaGVhZGVyLXRpdGxlIHtcclxuICAgIGg1IHtcclxuICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMC4yNXJlbTtcclxuICAgICAgY29sb3I6ICMyYzNlNTA7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgZ2FwOiAwLjVyZW07XHJcblxyXG4gICAgICBpIHtcclxuICAgICAgICBjb2xvcjogIzAwN2JmZjtcclxuICAgICAgICBmb250LXNpemU6IDEuMXJlbTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHNtYWxsIHtcclxuICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuaGVhZGVyLWFjdGlvbnMge1xyXG4gICAgLmJhZGdlIHtcclxuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzAwN2JmZiAwJSwgIzAwNTZiMyAxMDAlKTtcclxuICAgICAgY29sb3I6IHdoaXRlO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcclxuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAxMjMsIDI1NSwgMC4yKTtcclxuICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLnRlbXBsYXRlLXZpZXdlci1ib2R5IHtcclxuICBvdmVyZmxvdzogYXV0bztcclxuICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgYmFja2dyb3VuZDogJGJnLXByaW1hcnk7XHJcbn1cclxuXHJcbi8vIMOlwqLCnsOlwrzCt8OnwprChMOmwpDCnMOlwrDCi8OlworCn8OowoPCvcOmwqjCo8OlwrzCj1xyXG4uZW5oYW5jZWQtc2VhcmNoLWNvbnRhaW5lciB7XHJcbiAgLnNlYXJjaC13cmFwcGVyIHtcclxuICAgIGJhY2tncm91bmQ6ICRiZy1wcmltYXJ5O1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctbWQ7XHJcblxyXG4gICAgLnNlYXJjaC1pbnB1dC1ncm91cCB7XHJcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgYmFja2dyb3VuZDogJGJnLXNlY29uZGFyeTtcclxuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICBwYWRkaW5nOiAwLjc1cmVtIDFyZW07XHJcbiAgICAgIGJvcmRlcjogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xyXG4gICAgICB0cmFuc2l0aW9uOiAkdHJhbnNpdGlvbi1ub3JtYWw7XHJcblxyXG4gICAgICAmOmZvY3VzLXdpdGhpbiB7XHJcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeS1nb2xkLWxpZ2h0O1xyXG4gICAgICAgIGJhY2tncm91bmQ6ICRiZy1wcmltYXJ5O1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5zZWFyY2gtaWNvbiB7XHJcbiAgICAgICAgY29sb3I6ICR0ZXh0LXNlY29uZGFyeTtcclxuICAgICAgICBtYXJnaW4tcmlnaHQ6IDAuNzVyZW07XHJcbiAgICAgICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuc2VhcmNoLWlucHV0IHtcclxuICAgICAgICBmbGV4OiAxO1xyXG4gICAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICAgICAgICBvdXRsaW5lOiBub25lO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgICAgICBjb2xvcjogJHRleHQtcHJpbWFyeTtcclxuXHJcbiAgICAgICAgJjo6cGxhY2Vob2xkZXIge1xyXG4gICAgICAgICAgY29sb3I6ICR0ZXh0LXRlcnRpYXJ5O1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnNlYXJjaC1hY3Rpb25zIHtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGdhcDogMC41cmVtO1xyXG5cclxuICAgICAgICAuY2xlYXItc2VhcmNoLWJ0biB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZGMzNTQ1O1xyXG4gICAgICAgICAgY29sb3I6ICR0ZXh0LWxpZ2h0O1xyXG4gICAgICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICAgICAgd2lkdGg6IDI0cHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDI0cHg7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgdHJhbnNpdGlvbjogJHRyYW5zaXRpb24tbm9ybWFsO1xyXG5cclxuICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAjYzgyMzMzO1xyXG4gICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnNlYXJjaC1zdWdnZXN0aW9ucyxcclxuICAgIC5zZWFyY2gtbm8tcmVzdWx0cyB7XHJcbiAgICAgIG1hcmdpbi10b3A6IDAuNzVyZW07XHJcbiAgICAgIHBhZGRpbmc6IDAuNXJlbSAwO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gw6bCqMKhw6bCncK/w6XCiMKXw6jCocKow6XCrsK5w6XCmcKow6bCqMKjw6XCvMKPXHJcbi50ZW1wbGF0ZS1saXN0LWNvbnRhaW5lciB7XHJcbiAgLmxpc3QtY29udHJvbHMge1xyXG4gICAgYmFja2dyb3VuZDogd2hpdGU7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBwYWRkaW5nOiAxcmVtIDEuNXJlbTtcclxuICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xyXG5cclxuICAgIC5saXN0LWluZm8ge1xyXG4gICAgICAuaW5mby10ZXh0IHtcclxuICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuXHJcbi8vIMOnwrDCocOmwr3ClMOmwqjCocOmwp3Cv8OlwojCl8OowqHCqMOmwqjCo8OlwrzCj1xyXG4udGVtcGxhdGUtbGlzdC1jb250YWluZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBnYXA6IDAuNzVyZW07XHJcblxyXG4gIC50ZW1wbGF0ZS1pdGVtIHtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmZmZmYgMCUsICNmOGY5ZmEgMTAwJSk7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xyXG4gICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjtcclxuICAgIHBhZGRpbmc6IDEuMjVyZW07XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XHJcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG5cclxuICAgICY6OmJlZm9yZSB7XHJcbiAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgIHRvcDogMDtcclxuICAgICAgbGVmdDogMDtcclxuICAgICAgd2lkdGg6IDRweDtcclxuICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMDA3YmZmIDAlLCAjMDA1NmIzIDEwMCUpO1xyXG4gICAgICBvcGFjaXR5OiAwO1xyXG4gICAgICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3MgZWFzZTtcclxuICAgIH1cclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMTIpO1xyXG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgICAgIGJvcmRlci1jb2xvcjogIzAwN2JmZjtcclxuXHJcbiAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgb3BhY2l0eTogMTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC50ZW1wbGF0ZS1tYWluLWluZm8ge1xyXG4gICAgICBmbGV4OiAxO1xyXG4gICAgICBtYXJnaW4tcmlnaHQ6IDEuNXJlbTtcclxuXHJcbiAgICAgIC50ZW1wbGF0ZS1oZWFkZXIge1xyXG4gICAgICAgIG1hcmdpbi1ib3R0b206IDAuNzVyZW07XHJcblxyXG4gICAgICAgIC50ZW1wbGF0ZS1sYWJlbCB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMDA3YmZmIDAlLCAjMDA1NmIzIDEwMCUpO1xyXG4gICAgICAgICAgY29sb3I6IHdoaXRlO1xyXG4gICAgICAgICAgcGFkZGluZzogMC4yNXJlbSAwLjc1cmVtO1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC43cmVtO1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcclxuICAgICAgICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XHJcbiAgICAgICAgICBsZXR0ZXItc3BhY2luZzogMC41cHg7XHJcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAxMjMsIDI1NSwgMC4yKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC50ZW1wbGF0ZS1uYW1lIHtcclxuICAgICAgICAgIGNvbG9yOiAjMmMzZTUwO1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICBnYXA6IDAuNXJlbTtcclxuICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjQ7XHJcblxyXG4gICAgICAgICAgaSB7XHJcbiAgICAgICAgICAgIGNvbG9yOiAjMDA3YmZmO1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAudGVtcGxhdGUtbWV0YSB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgIGdhcDogMC41cmVtO1xyXG5cclxuICAgICAgICAubWV0YS1yb3cge1xyXG4gICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICBnYXA6IDAuNXJlbTtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICBwYWRkaW5nOiAwLjM3NXJlbSAwO1xyXG5cclxuICAgICAgICAgIC5tZXRhLWxhYmVsIHtcclxuICAgICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIGdhcDogMC4yNXJlbTtcclxuICAgICAgICAgICAgbWluLXdpZHRoOiA5MHB4O1xyXG5cclxuICAgICAgICAgICAgaSB7XHJcbiAgICAgICAgICAgICAgY29sb3I6ICMwMDdiZmY7XHJcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAubWV0YS12YWx1ZSB7XHJcbiAgICAgICAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAwLjI1cmVtIDAuNzVyZW07XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDZweDtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAwLjg1cmVtO1xyXG4gICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC50ZW1wbGF0ZS1hY3Rpb25zIHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgZ2FwOiAwLjVyZW07XHJcbiAgICAgIG1pbi13aWR0aDogMTEwcHg7XHJcblxyXG4gICAgICAuYWN0aW9uLWJ0biB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICAgIGdhcDogMC41cmVtO1xyXG4gICAgICAgIHBhZGRpbmc6IDAuNjI1cmVtIDAuODc1cmVtO1xyXG4gICAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwO1xyXG4gICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuXHJcbiAgICAgICAgJi52aWV3LWJ0biB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjhhNzQ1IDAlLCAjMjBjOTk3IDEwMCUpO1xyXG4gICAgICAgICAgY29sb3I6IHdoaXRlO1xyXG4gICAgICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoNDAsIDE2NywgNjksIDAuMik7XHJcblxyXG4gICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyMTg4MzggMCUsICMxZTdlMzQgMTAwJSk7XHJcbiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoNDAsIDE2NywgNjksIDAuMyk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgaSB7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgICYuZGVsZXRlLWJ0biB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZGMzNTQ1IDAlLCAjYzgyMzMzIDEwMCUpO1xyXG4gICAgICAgICAgY29sb3I6IHdoaXRlO1xyXG4gICAgICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMjIwLCA1MywgNjksIDAuMik7XHJcblxyXG4gICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNjODIzMzMgMCUsICNhNzFlMmEgMTAwJSk7XHJcbiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoMjIwLCA1MywgNjksIDAuMyk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgaSB7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIMOnwqnCusOnwovCgMOmwoXCi8Olwo3CocOnwonCh1xyXG4uZW1wdHktc3RhdGUtY2FyZCB7XHJcbiAgYmFja2dyb3VuZDogd2hpdGU7XHJcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XHJcbiAgcGFkZGluZzogM3JlbSAycmVtO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBib3JkZXI6IDJweCBkYXNoZWQgI2RlZTJlNjtcclxuXHJcbiAgLmVtcHR5LWNvbnRlbnQge1xyXG4gICAgLmVtcHR5LWljb24ge1xyXG4gICAgICBmb250LXNpemU6IDNyZW07XHJcbiAgICAgIGNvbG9yOiAjYWRiNWJkO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG4gICAgfVxyXG5cclxuICAgIC5lbXB0eS10aXRsZSB7XHJcbiAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgICB9XHJcblxyXG4gICAgLmVtcHR5LWRlc2NyaXB0aW9uIHtcclxuICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgIG1hcmdpbjogMDtcclxuXHJcbiAgICAgIC5jbGVhci1saW5rIHtcclxuICAgICAgICBjb2xvcjogIzY2N2VlYTtcclxuICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuXHJcbiAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIMOlwqLCnsOlwrzCt8OnwprChMOlwojChsOpwqDCgcOmwo7Cp8OlwojCtsOlwpnCqMOmwqjCo8OlwrzCj1xyXG4uZW5oYW5jZWQtcGFnaW5hdGlvbi1jb250YWluZXIge1xyXG4gIGJhY2tncm91bmQ6ICRiZy1wcmltYXJ5O1xyXG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgcGFkZGluZzogMS41cmVtO1xyXG4gIGJveC1zaGFkb3c6ICRzaGFkb3ctbWQ7XHJcblxyXG4gIC5wYWdpbmF0aW9uLXdyYXBwZXIge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcblxyXG4gICAgLnBhZ2luYXRpb24taW5mbyB7XHJcbiAgICAgIC5wYWdlLWluZm8ge1xyXG4gICAgICAgIGNvbG9yOiAkdGV4dC1zZWNvbmRhcnk7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5wYWdpbmF0aW9uLW5hdiB7XHJcbiAgICAgIC5lbmhhbmNlZC1wYWdpbmF0aW9uIHtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGxpc3Qtc3R5bGU6IG5vbmU7XHJcbiAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgIHBhZGRpbmc6IDA7XHJcbiAgICAgICAgZ2FwOiAwLjVyZW07XHJcblxyXG4gICAgICAgIC5wYWdlLWl0ZW0ge1xyXG4gICAgICAgICAgLnBhZ2UtYnRuIHtcclxuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgICAgICAgIG1pbi13aWR0aDogNDBweDtcclxuICAgICAgICAgICAgaGVpZ2h0OiA0MHB4O1xyXG4gICAgICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogJGJnLXNlY29uZGFyeTtcclxuICAgICAgICAgICAgY29sb3I6ICR0ZXh0LXNlY29uZGFyeTtcclxuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiAkdHJhbnNpdGlvbi1ub3JtYWw7XHJcblxyXG4gICAgICAgICAgICAmOmhvdmVyOm5vdCg6ZGlzYWJsZWQpIHtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAkZ3JhZGllbnQtcHJpbWFyeTtcclxuICAgICAgICAgICAgICBjb2xvcjogJHRleHQtbGlnaHQ7XHJcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAmOmRpc2FibGVkIHtcclxuICAgICAgICAgICAgICBvcGFjaXR5OiAwLjU7XHJcbiAgICAgICAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgJi5wYWdlLW51bWJlciB7XHJcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICYuZmlyc3QtcGFnZSxcclxuICAgICAgICAgICAgJi5sYXN0LXBhZ2UsXHJcbiAgICAgICAgICAgICYucHJldi1wYWdlLFxyXG4gICAgICAgICAgICAmLm5leHQtcGFnZSB7XHJcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAmLmFjdGl2ZSAucGFnZS1idG4ge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAkZ3JhZGllbnQtcHJpbWFyeTtcclxuICAgICAgICAgICAgY29sb3I6ICR0ZXh0LWxpZ2h0O1xyXG4gICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgxODQsIDE2NiwgMTE4LCAwLjMpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gw6bCqMKhw6bCncK/w6jCqcKzw6bCg8KFw6jCpsKWw6XCnMKWw6bCqMKjw6XCvMKPXHJcbi50ZW1wbGF0ZS1kZXRhaWwtdmlldyB7XHJcbiAgLmRldGFpbC1oZWFkZXIge1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmZmZmZiAwJSwgI2Y4ZjlmYSAxMDAlKTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XHJcbiAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjA4KTtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNlOWVjZWY7XHJcblxyXG4gICAgLmRldGFpbC10aXRsZS1zZWN0aW9uIHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XHJcblxyXG4gICAgICAuYmFjay1idXR0b24ge1xyXG4gICAgICAgIG1hcmdpbi1yaWdodDogMXJlbTtcclxuXHJcbiAgICAgICAgLmJhY2stYnRuIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgICAgICB3aWR0aDogNDJweDtcclxuICAgICAgICAgIGhlaWdodDogNDJweDtcclxuICAgICAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2Yzc1N2QgMCUsICM1YTYyNjggMTAwJSk7XHJcbiAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG4gICAgICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMTA4LCAxMTcsIDEyNSwgMC4yKTtcclxuXHJcbiAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzAwN2JmZiAwJSwgIzAwNTZiMyAxMDAlKTtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xyXG4gICAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLCAxMjMsIDI1NSwgMC4zKTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBpIHtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAuZGV0YWlsLXRpdGxlLWluZm8ge1xyXG4gICAgICAgIGZsZXg6IDE7XHJcblxyXG4gICAgICAgIC5kZXRhaWwtdGl0bGUge1xyXG4gICAgICAgICAgY29sb3I6ICMyYzNlNTA7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAxLjJyZW07XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgIGdhcDogMC41cmVtO1xyXG5cclxuICAgICAgICAgIGkge1xyXG4gICAgICAgICAgICBjb2xvcjogIzAwN2JmZjtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuZGV0YWlsLXN1YnRpdGxlIHtcclxuICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5kZXRhaWwtc3RhdHMge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBnYXA6IDEuNXJlbTtcclxuXHJcbiAgICAgIC5zdGF0LWl0ZW0ge1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xyXG4gICAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMXJlbTtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjtcclxuICAgICAgICBtaW4td2lkdGg6IDgwcHg7XHJcblxyXG4gICAgICAgIC5zdGF0LWxhYmVsIHtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC4yNXJlbTtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xyXG4gICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnN0YXQtdmFsdWUge1xyXG4gICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgICAgICAgY29sb3I6ICMwMDdiZmY7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuXHJcbn1cclxuXHJcbi8vIMOlwqLCnsOlwrzCt8OnwprChMOowqnCs8OmwoPChcOpwqDChcOnwpvCrsOlwojCl8OowqHCqMOmwqjCo8OlwrzCj1xyXG4uZW5oYW5jZWQtZGV0YWlsLWxpc3Qge1xyXG4gIC5lbmhhbmNlZC1kZXRhaWwtaXRlbSB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG5cclxuICAgIC5kZXRhaWwtaXRlbS1jYXJkIHtcclxuICAgICAgYmFja2dyb3VuZDogJGJnLXByaW1hcnk7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctbWQ7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICRib3JkZXItbGlnaHQ7XHJcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICAgIHRyYW5zaXRpb246ICR0cmFuc2l0aW9uLW5vcm1hbDtcclxuXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LWxnO1xyXG4gICAgICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnktZ29sZC1saWdodDtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmRldGFpbC1pdGVtLWhlYWRlciB7XHJcbiAgICAgICAgcGFkZGluZzogMS4yNXJlbTtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xyXG4gICAgICAgIGdhcDogMXJlbTtcclxuICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgJGJnLXNlY29uZGFyeTtcclxuXHJcbiAgICAgICAgLml0ZW0taW5kZXgge1xyXG4gICAgICAgICAgLmluZGV4LWJhZGdlIHtcclxuICAgICAgICAgICAgZGlzcGxheTogaW5saW5lLWZsZXg7XHJcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICAgICAgICB3aWR0aDogMzJweDtcclxuICAgICAgICAgICAgaGVpZ2h0OiAzMnB4O1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAkZ3JhZGllbnQtcHJpbWFyeTtcclxuICAgICAgICAgICAgY29sb3I6ICR0ZXh0LWxpZ2h0O1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuaXRlbS1tYWluLWluZm8ge1xyXG4gICAgICAgICAgZmxleDogMTtcclxuXHJcbiAgICAgICAgICAuaXRlbS1uYW1lIHtcclxuICAgICAgICAgICAgY29sb3I6ICR0ZXh0LXByaW1hcnk7XHJcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuNzVyZW07XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC5pdGVtLW1ldGEge1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICBmbGV4LXdyYXA6IHdyYXA7XHJcbiAgICAgICAgICAgIGdhcDogMC43NXJlbTtcclxuXHJcbiAgICAgICAgICAgIC5tZXRhLWl0ZW0ge1xyXG4gICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICBnYXA6IDAuNXJlbTtcclxuICAgICAgICAgICAgICBwYWRkaW5nOiAwLjM3NXJlbSAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDZweDtcclxuICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG5cclxuICAgICAgICAgICAgICAmLmlkLW1ldGEge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgxODQsIDE2NiwgMTE4LCAwLjEpO1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICRwcmltYXJ5LWdvbGQtZGFya2VyO1xyXG4gICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgJi5ncm91cC1tZXRhIHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMTg0LCAxNjYsIDExOCwgMC4xNSk7XHJcbiAgICAgICAgICAgICAgICBjb2xvcjogJHByaW1hcnktZ29sZC1kYXJrO1xyXG4gICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgJi5jYXRlZ29yeS1tZXRhIHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMTg0LCAxNjYsIDExOCwgMC4yKTtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAkcHJpbWFyeS1nb2xkLWRhcmtlcjtcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIGkge1xyXG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLml0ZW0tYWN0aW9ucyB7XHJcbiAgICAgICAgICAuY3JlYXRlLWRhdGUge1xyXG4gICAgICAgICAgICBjb2xvcjogJHRleHQtc2Vjb25kYXJ5O1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAkYmctc2Vjb25kYXJ5O1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAwLjM3NXJlbSAwLjc1cmVtO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAuZGV0YWlsLWl0ZW0tYm9keSB7XHJcbiAgICAgICAgcGFkZGluZzogMS4yNXJlbTtcclxuXHJcbiAgICAgICAgLml0ZW0tZGV0YWlscy1ncmlkIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGdyaWQ7XHJcbiAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDIwMHB4LCAxZnIpKTtcclxuICAgICAgICAgIGdhcDogMXJlbTtcclxuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XHJcblxyXG4gICAgICAgICAgLmRldGFpbC1ncm91cCB7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgICAgIGdhcDogMC4yNXJlbTtcclxuXHJcbiAgICAgICAgICAgIC5kZXRhaWwtbGFiZWwge1xyXG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xyXG4gICAgICAgICAgICAgIGNvbG9yOiAkdGV4dC1zZWNvbmRhcnk7XHJcbiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xyXG4gICAgICAgICAgICAgIGxldHRlci1zcGFjaW5nOiAwLjVweDtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLmRldGFpbC12YWx1ZSB7XHJcbiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuXHJcbiAgICAgICAgICAgICAgJi5wcmljZS12YWx1ZSB7XHJcbiAgICAgICAgICAgICAgICBjb2xvcjogIzI4YTc0NTtcclxuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgJi5xdWFudGl0eS12YWx1ZSB7XHJcbiAgICAgICAgICAgICAgICBjb2xvcjogIzAwN2JmZjtcclxuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5pdGVtLXJlbWFyayB7XHJcbiAgICAgICAgICAucmVtYXJrLWNvbnRlbnQge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAkYmctc2Vjb25kYXJ5O1xyXG4gICAgICAgICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICRwcmltYXJ5LWdvbGQtbGlnaHQ7XHJcbiAgICAgICAgICAgIHBhZGRpbmc6IDFyZW07XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAgOHB4IDhweCAwO1xyXG5cclxuICAgICAgICAgICAgLnJlbWFyay10ZXh0IHtcclxuICAgICAgICAgICAgICBjb2xvcjogJHRleHQtcHJpbWFyeTtcclxuICAgICAgICAgICAgICBmb250LXN0eWxlOiBpdGFsaWM7XHJcbiAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gw6bCqMKhw6bCncK/w6bCn8Klw6fCnMKLw6XCmcKow6XCusKVw6nCg8Kow6bCqMKjw6XCvMKPXHJcbi50ZW1wbGF0ZS12aWV3ZXItZm9vdGVyIHtcclxuICBiYWNrZ3JvdW5kOiAkYmctc2Vjb25kYXJ5O1xyXG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCAkYm9yZGVyLWxpZ2h0O1xyXG4gIHBhZGRpbmc6IDFyZW0gMS41cmVtO1xyXG5cclxuICAuZm9vdGVyLWFjdGlvbnMge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG5cclxuICAgIC5jbG9zZS1idG4ge1xyXG4gICAgICBiYWNrZ3JvdW5kOiAkdGV4dC1zZWNvbmRhcnk7XHJcbiAgICAgIGNvbG9yOiAkdGV4dC1saWdodDtcclxuICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMnJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICB0cmFuc2l0aW9uOiAkdHJhbnNpdGlvbi1ub3JtYWw7XHJcblxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAkcHJpbWFyeS1nb2xkLWRhcms7XHJcbiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDDpMK9wr/Dp8KUwqggbmItY2FyZCDDp8K1wpDDpsKnwovDr8K8wozDp8KnwrvDqcKZwqTDpMK4wo3DpcK/woXDqMKmwoHDp8KawoTDpcKMwoXDqMKjwp3DpsKowqPDpcK8wo9cclxuXHJcbi8vIG5iLWNhcmQtaGVhZGVyIMOmwozCicOpwojClcOmwqjCo8OlwrzCj1xyXG5uYi1jYXJkLWhlYWRlciB7XHJcbiAgLmJ0bi1zdWNjZXNzIHtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyOGE3NDUgMCUsICMyMGM5OTcgMTAwJSk7XHJcbiAgICBib3JkZXI6IG5vbmU7XHJcbiAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xyXG4gICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoNDAsIDE2NywgNjksIDAuMyk7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDDpsKQwpzDpcKwwovDpcKuwrnDpcKZwqjDpsKowqPDpcK8wo9cclxuLnNlYXJjaC1jb250YWluZXIge1xyXG4gIC5pbnB1dC1ncm91cCB7XHJcbiAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuXHJcbiAgICAuZm9ybS1jb250cm9sIHtcclxuICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICBwYWRkaW5nOiAwLjc1cmVtIDFyZW07XHJcbiAgICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcclxuXHJcbiAgICAgICY6Zm9jdXMge1xyXG4gICAgICAgIGJveC1zaGFkb3c6IG5vbmU7XHJcbiAgICAgICAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcclxuICAgICAgfVxyXG5cclxuICAgICAgJjo6cGxhY2Vob2xkZXIge1xyXG4gICAgICAgIGNvbG9yOiAjOTk5O1xyXG4gICAgICAgIGZvbnQtc3R5bGU6IGl0YWxpYztcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5pbnB1dC1ncm91cC1hcHBlbmQge1xyXG4gICAgICAuYnRuIHtcclxuICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICBwYWRkaW5nOiAwLjc1cmVtIDFyZW07XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuXHJcbiAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZTllY2VmO1xyXG4gICAgICAgICAgY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmOmZvY3VzIHtcclxuICAgICAgICAgIGJveC1zaGFkb3c6IG5vbmU7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDDpsKQwpzDpcKwwovDp8K1wpDDpsKewpzDqMKzwofDqMKowopcclxuLnNlYXJjaC1yZXN1bHRzLWluZm8ge1xyXG4gIHBhZGRpbmc6IDAuNXJlbSAwO1xyXG4gIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzAwN2JmZjtcclxuICBwYWRkaW5nLWxlZnQ6IDAuNzVyZW07XHJcbiAgYmFja2dyb3VuZDogI2Y4ZjlmZjtcclxuICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbn1cclxuXHJcbi8vIMOlwojChsOpwqDCgcOowrPCh8OowqjCilxyXG4ucGFnaW5hdGlvbi1pbmZvIHtcclxuICBwYWRkaW5nOiAwLjVyZW0gMDtcclxuICBib3JkZXItbGVmdDogM3B4IHNvbGlkICMyOGE3NDU7XHJcbiAgcGFkZGluZy1sZWZ0OiAwLjc1cmVtO1xyXG4gIGJhY2tncm91bmQ6ICNmOGZmZjg7XHJcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG59XHJcblxyXG4vLyDDqMKhwqjDpsKgwrzDpsKowqPDpcK8wo9cclxuLnRhYmxlLXJlc3BvbnNpdmUge1xyXG4gIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xyXG59XHJcblxyXG4udGFibGUge1xyXG4gIG1hcmdpbi1ib3R0b206IDA7XHJcblxyXG4gIHRoZWFkLnRoZWFkLWxpZ2h0IHtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmOGY5ZmEgMCUsICNlOWVjZWYgMTAwJSk7XHJcblxyXG4gICAgdGgge1xyXG4gICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICBwYWRkaW5nOiAxcmVtIDAuNzVyZW07XHJcbiAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xyXG4gICAgICBsZXR0ZXItc3BhY2luZzogMC41cHg7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICB0Ym9keSB7XHJcbiAgICB0ciB7XHJcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcblxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZmO1xyXG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICB0ZCB7XHJcbiAgICAgICAgcGFkZGluZzogMXJlbSAwLjc1cmVtO1xyXG4gICAgICAgIGJvcmRlci1jb2xvcjogI2YwZjBmMDtcclxuICAgICAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xyXG5cclxuICAgICAgICBzdHJvbmcge1xyXG4gICAgICAgICAgY29sb3I6ICMzMzM7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnRleHQtbXV0ZWQge1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDDpsKMwonDqcKIwpXDp8K1woTDpsKowqPDpcK8wo9cclxuLmJ0bi1ncm91cC1zbSB7XHJcbiAgLmJ0biB7XHJcbiAgICBwYWRkaW5nOiAwLjM3NXJlbSAwLjc1cmVtO1xyXG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG5cclxuICAgICYuYnRuLWluZm8ge1xyXG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMTdhMmI4IDAlLCAjMTM4NDk2IDEwMCUpO1xyXG4gICAgICBib3JkZXI6IG5vbmU7XHJcblxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDIzLCAxNjIsIDE4NCwgMC4zKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgICYuYnRuLWRhbmdlciB7XHJcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNkYzM1NDUgMCUsICNjODIzMzMgMTAwJSk7XHJcbiAgICAgIGJvcmRlcjogbm9uZTtcclxuXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMjIwLCA1MywgNjksIDAuMyk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBpIHtcclxuICAgICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gw6fCqcK6w6fCi8KAw6bChcKLw6bCqMKjw6XCvMKPXHJcbi5lbXB0eS1zdGF0ZSB7XHJcbiAgcGFkZGluZzogMnJlbTtcclxuXHJcbiAgaSB7XHJcbiAgICBkaXNwbGF5OiBibG9jaztcclxuICAgIG1hcmdpbjogMCBhdXRvIDFyZW07XHJcbiAgICBvcGFjaXR5OiAwLjU7XHJcbiAgfVxyXG5cclxuICBwIHtcclxuICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xyXG4gIH1cclxuXHJcbiAgYSB7XHJcbiAgICBjb2xvcjogIzAwN2JmZjtcclxuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDDpsKowqHDpsKdwr/DqMKpwrPDpsKDwoXDpsKowqHDpsKFwovDpsKhwoZcclxuLnRlbXBsYXRlLWRldGFpbC1tb2RhbCB7XHJcbiAgbWFyZ2luLXRvcDogMS41cmVtO1xyXG4gIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgYm9yZGVyOiAycHggc29saWQgI2U5ZWNlZjtcclxuICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgYW5pbWF0aW9uOiBzbGlkZURvd24gMC4zcyBlYXNlLW91dDtcclxufVxyXG5cclxuQGtleWZyYW1lcyBzbGlkZURvd24ge1xyXG4gIGZyb20ge1xyXG4gICAgb3BhY2l0eTogMDtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMjBweCk7XHJcbiAgfVxyXG5cclxuICB0byB7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xyXG4gIH1cclxufVxyXG5cclxuLnRlbXBsYXRlLWRldGFpbC1oZWFkZXIge1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmZmZmYgMCUsICNmOGY5ZmEgMTAwJSk7XHJcbiAgcGFkZGluZzogMS4yNXJlbSAxLjVyZW07XHJcbiAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICNlOWVjZWY7XHJcbiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7XHJcblxyXG4gIGg2IHtcclxuICAgIGNvbG9yOiAjMmMzZTUwO1xyXG4gICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMDtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgZ2FwOiAwLjVyZW07XHJcblxyXG4gICAgaSB7XHJcbiAgICAgIGNvbG9yOiAjMDA3YmZmO1xyXG4gICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuYnRuIHtcclxuICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgcGFkZGluZzogMC41cmVtIDFyZW07XHJcbiAgICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG5cclxuICAgICYuYnRuLXNlY29uZGFyeSB7XHJcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2Yzc1N2QgMCUsICM1YTYyNjggMTAwJSk7XHJcbiAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgY29sb3I6IHdoaXRlO1xyXG5cclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzVhNjI2OCAwJSwgIzQ5NTA1NyAxMDAlKTtcclxuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMTA4LCAxMTcsIDEyNSwgMC4zKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLnRlbXBsYXRlLWRldGFpbC1jb250ZW50IHtcclxuICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgbWF4LWhlaWdodDogNDAwcHg7XHJcbiAgb3ZlcmZsb3cteTogYXV0bztcclxuXHJcbiAgLnRlbXBsYXRlLWRlc2NyaXB0aW9uIHtcclxuICAgIGJhY2tncm91bmQ6ICNmOGY5ZmY7XHJcbiAgICBwYWRkaW5nOiAxcmVtO1xyXG4gICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMDA3YmZmO1xyXG5cclxuICAgIHN0cm9uZyB7XHJcbiAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLnRlbXBsYXRlLWl0ZW1zIHtcclxuICAgIGg2IHtcclxuICAgICAgY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIHBhZGRpbmctYm90dG9tOiAwLjVyZW07XHJcbiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmO1xyXG5cclxuICAgICAgaSB7XHJcbiAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi5kZXRhaWwtbGlzdCB7XHJcbiAgLmRldGFpbC1pdGVtIHtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmY7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDZweDtcclxuICAgICAgbWFyZ2luOiAwIC0wLjVyZW07XHJcbiAgICAgIHBhZGRpbmctbGVmdDogMC41cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgIHBhZGRpbmctcmlnaHQ6IDAuNXJlbSAhaW1wb3J0YW50O1xyXG4gICAgfVxyXG5cclxuICAgICY6bGFzdC1jaGlsZCB7XHJcbiAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7XHJcbiAgICB9XHJcblxyXG4gICAgLmRldGFpbC1pbmRleCB7XHJcbiAgICAgIC5iYWRnZSB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogI2U5ZWNlZjtcclxuICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgIHBhZGRpbmc6IDAuMzc1cmVtIDAuNXJlbTtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgbWluLXdpZHRoOiAycmVtO1xyXG4gICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuXHJcblxyXG4gICAgLmRldGFpbC1jb250ZW50IHtcclxuXHJcbiAgICAgIC5kZXRhaWwtZmllbGQge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgIG1hcmdpbi1ib3R0b206IDAuMjVyZW07XHJcblxyXG4gICAgICAgIHN0cm9uZyB7XHJcbiAgICAgICAgICBjb2xvcjogIzQ5NTA1NztcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5kZXRhaWwtdmFsdWUge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDtcclxuICAgICAgICB3b3JkLWJyZWFrOiBicmVhay13b3JkO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDDpsKWwrDDpcKiwp7DpsKowqHDpsKdwr/DqMKhwqjDpcKWwq7DpsKowqPDpcK8wo8gLSDDp8KwwqHDpsK9wpTDp8KJwohcclxuLmFkZC10ZW1wbGF0ZS1mb3JtIHtcclxuICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XHJcblxyXG4gIC50ZW1wbGF0ZS1mb3JtIHtcclxuICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZThlY2VmO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICAgIHBhZGRpbmc6IDJyZW07XHJcbiAgICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4xMik7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuaW5wdXQtcm93IHtcclxuICAgIGRpc3BsYXk6IGdyaWQ7XHJcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnI7XHJcbiAgICBnYXA6IDFyZW07XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XHJcblxyXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmlucHV0LWdyb3VwIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG5cclxuICAgICYuZnVsbC13aWR0aCB7XHJcbiAgICAgIGdyaWQtY29sdW1uOiAxIC8gLTE7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuaW5wdXQtbGFiZWwge1xyXG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICBjb2xvcjogIzM3NDE1MTtcclxuICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcclxuXHJcbiAgICAucmVxdWlyZWQge1xyXG4gICAgICBjb2xvcjogI2VmNDQ0NDtcclxuICAgICAgbWFyZ2luLWxlZnQ6IDAuMTI1cmVtO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmlucHV0LWZpZWxkIHtcclxuICAgIHBhZGRpbmc6IDAuNzVyZW0gMXJlbTtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNkMWQ1ZGI7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7XHJcblxyXG4gICAgJjpmb2N1cyB7XHJcbiAgICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICAgIGJvcmRlci1jb2xvcjogIzI4YTc0NTtcclxuICAgICAgYm94LXNoYWRvdzogMCAwIDAgM3B4IHJnYmEoNDAsIDE2NywgNjksIDAuMSk7XHJcbiAgICB9XHJcblxyXG4gICAgJjo6cGxhY2Vob2xkZXIge1xyXG4gICAgICBjb2xvcjogIzljYTNhZjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5pdGVtcy1zZWxlY3RvciB7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZDFkNWRiO1xyXG4gICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgYmFja2dyb3VuZDogI2Y5ZmFmYjtcclxuICAgIG1heC1oZWlnaHQ6IDIwMHB4O1xyXG4gICAgb3ZlcmZsb3cteTogYXV0bztcclxuICB9XHJcblxyXG4gIC5lbXB0eS1pdGVtcyB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgZ2FwOiAwLjVyZW07XHJcbiAgICBwYWRkaW5nOiAycmVtO1xyXG4gICAgY29sb3I6ICM2YjcyODA7XHJcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG5cclxuICAgIGkge1xyXG4gICAgICBjb2xvcjogIzljYTNhZjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5pdGVtLW9wdGlvbiB7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTdlYjtcclxuXHJcbiAgICAmOmxhc3QtY2hpbGQge1xyXG4gICAgICBib3JkZXItYm90dG9tOiBub25lO1xyXG4gICAgfVxyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjZjNmNGY2O1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLml0ZW0tbGFiZWwge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xyXG4gICAgZ2FwOiAwLjc1cmVtO1xyXG4gICAgcGFkZGluZzogMC44NzVyZW0gMXJlbTtcclxuICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIG1hcmdpbjogMDtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gIH1cclxuXHJcbiAgLml0ZW0tY2hlY2tib3gge1xyXG4gICAgbWFyZ2luOiAwO1xyXG4gICAgbWFyZ2luLXRvcDogMC4xMjVyZW07XHJcbiAgICB3aWR0aDogMXJlbTtcclxuICAgIGhlaWdodDogMXJlbTtcclxuICAgIGFjY2VudC1jb2xvcjogIzI4YTc0NTtcclxuICB9XHJcblxyXG4gIC5pdGVtLWNvbnRlbnQge1xyXG4gICAgZmxleDogMTtcclxuICAgIG1pbi13aWR0aDogMDtcclxuICB9XHJcblxyXG4gIC5pdGVtLXRpdGxlIHtcclxuICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgY29sb3I6ICMzNzQxNTE7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xyXG4gICAgbGluZS1oZWlnaHQ6IDEuNDtcclxuICB9XHJcblxyXG4gIC5pdGVtLWRlc2Mge1xyXG4gICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICBjb2xvcjogIzZiNzI4MDtcclxuICAgIGxpbmUtaGVpZ2h0OiAxLjM7XHJcbiAgfVxyXG5cclxuICAuZm9ybS1hY3Rpb25zIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xyXG4gICAgZ2FwOiAwLjc1cmVtO1xyXG4gICAgbWFyZ2luLXRvcDogMS41cmVtO1xyXG4gICAgcGFkZGluZy10b3A6IDEuNXJlbTtcclxuICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gIH1cclxuXHJcbiAgLmJ0bi1jYW5jZWwsXHJcbiAgLmJ0bi1zYXZlIHtcclxuICAgIHBhZGRpbmc6IDAuNjI1cmVtIDEuMjVyZW07XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgIGJvcmRlcjogbm9uZTtcclxuICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcbiAgICBtaW4td2lkdGg6IDgwcHg7XHJcbiAgfVxyXG5cclxuICAuYnRuLWNhbmNlbCB7XHJcbiAgICBiYWNrZ3JvdW5kOiAjZjNmNGY2O1xyXG4gICAgY29sb3I6ICMzNzQxNTE7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNlNWU3ZWI7XHJcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5idG4tc2F2ZSB7XHJcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjhhNzQ1IDAlLCAjMjBjOTk3IDEwMCUpO1xyXG4gICAgY29sb3I6IHdoaXRlO1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XHJcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSg0MCwgMTY3LCA2OSwgMC4yNSk7XHJcbiAgICB9XHJcblxyXG4gICAgJjpkaXNhYmxlZCB7XHJcbiAgICAgIG9wYWNpdHk6IDAuNjtcclxuICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICAgICAgdHJhbnNmb3JtOiBub25lO1xyXG4gICAgICBib3gtc2hhZG93OiBub25lO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gw6XCiMKGw6nCoMKBw6bCjsKnw6XCiMK2w6XCmcKow6bCqMKjw6XCvMKPXHJcbi5wYWdpbmF0aW9uLWNvbnRhaW5lciB7XHJcbiAgLnBhZ2luYXRpb24ge1xyXG4gICAgLnBhZ2UtaXRlbSB7XHJcbiAgICAgIC5wYWdlLWxpbmsge1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZWUyZTY7XHJcbiAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgcGFkZGluZzogMC4zNzVyZW0gMC43NXJlbTtcclxuICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gICAgICAgIG1hcmdpbjogMCAwLjEyNXJlbTtcclxuXHJcbiAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTllY2VmO1xyXG4gICAgICAgICAgYm9yZGVyLWNvbG9yOiAjYWRiNWJkO1xyXG4gICAgICAgICAgY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmOmZvY3VzIHtcclxuICAgICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDAuMnJlbSByZ2JhKDAsIDEyMywgMjU1LCAwLjI1KTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGkge1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgJi5hY3RpdmUgLnBhZ2UtbGluayB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzAwN2JmZjtcclxuICAgICAgICBib3JkZXItY29sb3I6ICMwMDdiZmY7XHJcbiAgICAgICAgY29sb3I6IHdoaXRlO1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMTIzLCAyNTUsIDAuMyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuZGlzYWJsZWQgLnBhZ2UtbGluayB7XHJcbiAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcclxuICAgICAgICBib3JkZXItY29sb3I6ICNkZWUyZTY7XHJcbiAgICAgICAgb3BhY2l0eTogMC41O1xyXG4gICAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XHJcblxyXG4gICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgdHJhbnNmb3JtOiBub25lO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gw6jCqcKzw6bCg8KFw6XCiMKGw6nCoMKBw6bCqMKjw6XCvMKPXHJcbi5kZXRhaWwtcGFnaW5hdGlvbiB7XHJcbiAgLnBhZ2luYXRpb24ge1xyXG4gICAgLnBhZ2UtaXRlbSB7XHJcbiAgICAgIC5wYWdlLWxpbmsge1xyXG4gICAgICAgIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDtcclxuICAgICAgICBtYXJnaW46IDAgMC4wNjI1cmVtO1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAmLmFjdGl2ZSAucGFnZS1saW5rIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjhhNzQ1O1xyXG4gICAgICAgIGJvcmRlci1jb2xvcjogIzI4YTc0NTtcclxuICAgICAgICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSg0MCwgMTY3LCA2OSwgMC4zKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gw6bClsKww6fCmsKEw6jCqcKzw6bCg8KFw6nCoMKFw6fCm8Kuw6bCqMKjw6XCvMKPXHJcbi5kZXRhaWwtaXRlbSB7XHJcbiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjJzO1xyXG5cclxuICAmOmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XHJcbiAgfVxyXG5cclxuICAuZGV0YWlsLWhlYWRlciB7XHJcbiAgICAuZGV0YWlsLW1haW4ge1xyXG4gICAgICAuZGV0YWlsLW5hbWUge1xyXG4gICAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmRldGFpbC1pbmZvIHtcclxuICAgICAgICAuYmFkZ2Uge1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC4yNXJlbTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5kZXRhaWwtcmVtYXJrIHtcclxuICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcclxuICAgICAgICBwYWRkaW5nOiAwLjI1cmVtIDAuNXJlbTtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgICAgICAgYm9yZGVyLWxlZnQ6IDNweCBzb2xpZCAjZGVlMmU2O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDDqMKpwrPDpsKDwoXDpsKQwpzDpcKwwovDpsKhwobDpsKowqPDpcK8wo9cclxuLnRlbXBsYXRlLWRldGFpbC1zZWFyY2gge1xyXG4gIC5pbnB1dC1ncm91cCB7XHJcbiAgICAuZm9ybS1jb250cm9sIHtcclxuICAgICAgYm9yZGVyLXJhZGl1czogMC4yNXJlbSAwIDAgMC4yNXJlbTtcclxuXHJcbiAgICAgICY6Zm9jdXMge1xyXG4gICAgICAgIGJvcmRlci1jb2xvcjogIzAwN2JmZjtcclxuICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAwLjJyZW0gcmdiYSgwLCAxMjMsIDI1NSwgMC4yNSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuaW5wdXQtZ3JvdXAtYXBwZW5kIHtcclxuICAgICAgLmJ0biB7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMDtcclxuXHJcbiAgICAgICAgJjpsYXN0LWNoaWxkIHtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAgMC4yNXJlbSAwLjI1cmVtIDA7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmLmJ0bi1vdXRsaW5lLXNlY29uZGFyeSB7XHJcbiAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDDqcKfwr/DpsKHwonDpcK8wo/DqMKowq3DqMKowoggLSDDpsKowqHDpsKdwr/DpcKIwpfDqMKhwqjDpcKEwqrDpcKMwpZcclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLnRlbXBsYXRlLWxpc3QtY29udGFpbmVyIHtcclxuICAgIC50ZW1wbGF0ZS1pdGVtIHtcclxuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgZ2FwOiAxcmVtO1xyXG4gICAgICBwYWRkaW5nOiAxcmVtO1xyXG5cclxuICAgICAgLnRlbXBsYXRlLW1haW4taW5mbyB7XHJcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiAwO1xyXG5cclxuICAgICAgICAudGVtcGxhdGUtaGVhZGVyIHtcclxuICAgICAgICAgIC50ZW1wbGF0ZS1uYW1lIHtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnRlbXBsYXRlLW1ldGEge1xyXG4gICAgICAgICAgLm1ldGEtcm93IHtcclxuICAgICAgICAgICAgLm1ldGEtbGFiZWwge1xyXG4gICAgICAgICAgICAgIG1pbi13aWR0aDogODBweDtcclxuICAgICAgICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLm1ldGEtdmFsdWUge1xyXG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xyXG4gICAgICAgICAgICAgIHBhZGRpbmc6IDAuMnJlbSAwLjVyZW07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC50ZW1wbGF0ZS1hY3Rpb25zIHtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xyXG4gICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICAgIG1pbi13aWR0aDogYXV0bztcclxuICAgICAgICBnYXA6IDAuNzVyZW07XHJcblxyXG4gICAgICAgIC5hY3Rpb24tYnRuIHtcclxuICAgICAgICAgIGZsZXg6IDE7XHJcbiAgICAgICAgICBtaW4td2lkdGg6IDgwcHg7XHJcbiAgICAgICAgICBwYWRkaW5nOiAwLjVyZW0gMC43NXJlbTtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5saXN0LWNvbnRyb2xzIHtcclxuICAgIHBhZGRpbmc6IDAuNzVyZW0gMXJlbSAhaW1wb3J0YW50O1xyXG5cclxuICAgIC5saXN0LWluZm8gLmluZm8tdGV4dCB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xyXG4gICAgfVxyXG4gIH1cclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_div_13_div_6_Template_button_click_1_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "filteredTemplates", "length", "ɵɵtwoWayListener", "TemplateViewerComponent_div_13_Template_input_ngModelChange_5_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "searchKeyword", "TemplateViewerComponent_div_13_Template_input_input_5_listener", "onSearch", "TemplateViewerComponent_div_13_Template_input_keyup_enter_5_listener", "ɵɵtemplate", "TemplateViewerComponent_div_13_div_6_Template", "TemplateViewerComponent_div_13_div_7_Template", "TemplateViewerComponent_div_13_div_8_Template", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate3", "templatePagination", "currentPage", "pageSize", "Math", "min", "totalItems", "ɵɵtextInterpolate2", "totalPages", "ɵɵtextInterpolate", "ɵɵpipeBind2", "tpl_r5", "CreateTime", "UpdateTime", "TemplateViewerComponent_div_14_div_3_button_16_Template_button_click_0_listener", "_r6", "$implicit", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_div_14_div_3_div_9_Template", "TemplateViewerComponent_div_14_div_3_div_10_Template", "TemplateViewerComponent_div_14_div_3_Template_button_click_12_listener", "_r4", "onSelectTemplate", "TemplateViewerComponent_div_14_div_3_button_16_Template", "TemplateName", "TemplateViewerComponent_div_14_div_4_p_7_Template_a_click_2_listener", "_r7", "TemplateViewerComponent_div_14_div_4_i_3_Template", "TemplateViewerComponent_div_14_div_4_i_4_Template", "TemplateViewerComponent_div_14_div_4_p_7_Template", "TemplateViewerComponent_div_14_div_4_p_8_Template", "TemplateViewerComponent_div_14_div_5_li_13_Template_button_click_1_listener", "page_r10", "_r9", "goToTemplatePage", "ɵɵclassProp", "TemplateViewerComponent_div_14_div_5_Template_button_click_8_listener", "_r8", "TemplateViewerComponent_div_14_div_5_Template_button_click_11_listener", "TemplateViewerComponent_div_14_div_5_li_13_Template", "TemplateViewerComponent_div_14_div_5_Template_button_click_15_listener", "TemplateViewerComponent_div_14_div_5_Template_button_click_18_listener", "getTemplatePageNumbers", "TemplateViewerComponent_div_14_div_1_Template", "TemplateViewerComponent_div_14_div_3_Template", "TemplateViewerComponent_div_14_div_4_Template", "TemplateViewerComponent_div_14_div_5_Template", "paginatedTemplates", "trackByTemplateId", "selectedTemplate", "Description", "detailPagination", "TemplateViewerComponent_div_15_div_24_Template_button_click_1_listener", "_r12", "clearDetailSearch", "detail_r13", "tblRequirement", "CGroupName", "CRemark", "getFieldClass", "config_r14", "fieldName", "getFieldValue", "displayName", "getValueClass", "formatFieldValue", "TemplateViewerComponent_div_15_div_28_div_1_div_21_div_2_Template", "fieldDisplayConfig", "TemplateViewerComponent_div_15_div_28_div_1_span_15_Template", "TemplateViewerComponent_div_15_div_28_div_1_span_16_Template", "TemplateViewerComponent_div_15_div_28_div_1_div_21_Template", "i_r15", "CRequirement", "CReleateId", "CCreateDt", "hasVisibleFields", "TemplateViewerComponent_div_15_div_28_div_1_Template", "currentTemplateDetailsData", "i_r17", "detail_r16", "FieldName", "FieldValue", "TemplateViewerComponent_div_15_ng_template_29_div_0_div_1_Template", "paginatedDetails", "TemplateViewerComponent_div_15_ng_template_29_div_0_Template", "currentTemplateDetails", "noDetails_r18", "TemplateViewerComponent_div_15_div_31_li_6_Template_button_click_1_listener", "page_r21", "_r20", "goToDetailPage", "TemplateViewerComponent_div_15_div_31_Template_button_click_4_listener", "_r19", "TemplateViewerComponent_div_15_div_31_li_6_Template", "TemplateViewerComponent_div_15_div_31_Template_button_click_8_listener", "getDetailPageNumbers", "TemplateViewerComponent_div_15_Template_button_click_4_listener", "_r11", "closeTemplateDetail", "TemplateViewerComponent_div_15_p_10_Template", "TemplateViewerComponent_div_15_div_17_Template", "TemplateViewerComponent_div_15_Template_input_ngModelChange_23_listener", "detailSearchKeyword", "TemplateViewerComponent_div_15_Template_input_input_23_listener", "onDetailSearch", "TemplateViewerComponent_div_15_Template_input_keyup_enter_23_listener", "TemplateViewerComponent_div_15_div_24_Template", "TemplateViewerComponent_div_15_div_25_Template", "TemplateViewerComponent_div_15_div_26_Template", "TemplateViewerComponent_div_15_div_28_Template", "TemplateViewerComponent_div_15_ng_template_29_Template", "ɵɵtemplateRefExtractor", "TemplateViewerComponent_div_15_div_31_Template", "TemplateViewerComponent_div_15_ng_template_32_Template", "checkOldDetails_r22", "TemplateViewerComponent", "constructor", "templateService", "templateType", "selectTemplate", "close", "templates", "templateDetails", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "CUpdateDt", "Creator", "CCreator", "undefined", "Up<PERSON>tor", "CUpdator", "error", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "updateTemplatePagination", "ceil", "max", "updatePaginatedTemplates", "startIndex", "endIndex", "slice", "page", "pages", "startPage", "endPage", "i", "push", "emit", "loadTemplateDetails", "updateDetailPagination", "templateId", "pageIndex", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "allDetails", "CTemplateDetailId", "CSort", "Date", "toISOString", "CCategory", "CUnitPrice", "CQuantity", "CUnit", "detail", "CReleateName", "pagedDetails", "searchTemplateDetails", "onDetailSearchInput", "details", "updatePaginatedDetails", "onClose", "templateID", "confirm", "deleteTemplateById", "deleteTemplateArgs", "apiTemplateDeleteTemplatePost$Json", "some", "config", "classMap", "value", "Number", "toLocaleString", "unit", "toString", "d", "index", "ɵɵdirectiveInject", "i1", "TemplateService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_div_13_Template", "TemplateViewerComponent_div_14_Template", "TemplateViewerComponent_div_15_Template", "TemplateViewerComponent_Template_button_click_18_listener", "i2", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i4", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, GetTemplateByIdArgs, GetTemplateDetailByIdArgs, TemplateDetailItem as ApiTemplateDetailItem } from 'src/services/api/models';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\r\n  @Input() fieldDisplayConfig: FieldDisplayConfig[] = [\r\n    { displayName: '工程項目', fieldName: 'CRequirement' },\r\n    { displayName: '群組類別', fieldName: 'CGroupName' },\r\n    { displayName: '單價', fieldName: 'CUnitPrice' },\r\n    { displayName: '單位', fieldName: 'CUnit' },\r\n    { displayName: '備註', fieldName: 'CRemark' }\r\n  ]; // 欄位顯示設定，由父元件傳入\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 公開Math對象供模板使用\r\n  Math = Math;\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = []; // 保留用於向後相容\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 新的詳情資料管理\r\n  currentTemplateDetailsData: ApiTemplateDetailItem[] = [];\r\n  detailSearchKeyword = ''; // 明細專用搜尋關鍵字\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板列表\r\n  templatePagination = {\r\n    currentPage: 1,\r\n    pageSize: 10,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板詳情\r\n  detailPagination = {\r\n    currentPage: 1,\r\n    pageSize: 5,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedDetails: TemplateDetail[] = [];\r\n\r\n\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    this.loadTemplates();\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 載入模板列表 - 使用真實的 API 調用\r\n  loadTemplates() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      PageIndex: 1, // 暫時載入第一頁\r\n      PageSize: 100, // 暫時載入較多資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateList API\r\n    this.templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // API 調用成功\r\n\r\n          // 轉換 API 回應為內部格式\r\n          this.templates = response.Entries.map(item => ({\r\n            TemplateID: item.CTemplateId,\r\n            TemplateName: item.CTemplateName || '',\r\n            Description: item.CTemplateName || '',\r\n            CreateTime: item.CCreateDt,\r\n            UpdateTime: item.CUpdateDt,\r\n            Creator: item.CCreator || undefined,\r\n            Updator: item.CUpdator || undefined\r\n          }));\r\n\r\n          // 更新過濾列表\r\n          this.updateFilteredTemplates();\r\n\r\n          // 清空詳情資料，改為按需載入\r\n          this.templateDetails = [];\r\n          this.currentTemplateDetailsData = [];\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n          this.templateDetails = [];\r\n          this.updateFilteredTemplates();\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n        this.templateDetails = [];\r\n        this.updateFilteredTemplates();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n    this.updateTemplatePagination();\r\n  }\r\n\r\n  // 更新模板分頁\r\n  updateTemplatePagination() {\r\n    this.templatePagination.totalItems = this.filteredTemplates.length;\r\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\r\n\r\n    // 確保當前頁面不超過總頁數\r\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\r\n    }\r\n\r\n    this.updatePaginatedTemplates();\r\n  }\r\n\r\n  // 更新分頁後的模板列表\r\n  updatePaginatedTemplates() {\r\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\r\n    const endIndex = startIndex + this.templatePagination.pageSize;\r\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 模板分頁導航\r\n  goToTemplatePage(page: number) {\r\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = page;\r\n      this.updatePaginatedTemplates();\r\n    }\r\n  }\r\n\r\n  // 獲取模板分頁頁碼數組\r\n  getTemplatePageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.templatePagination.totalPages;\r\n    const currentPage = this.templatePagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n\r\n    // 按需載入模板詳情\r\n    if (template.TemplateID) {\r\n      this.loadTemplateDetails(template.TemplateID);\r\n    }\r\n\r\n    this.updateDetailPagination();\r\n  }\r\n\r\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\r\n  loadTemplateDetails(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    // 調用真實的 GetTemplateDetailById API\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n\r\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\r\n          let allDetails = response.Entries.map(item => ({\r\n            CTemplateDetailId: item.CTemplateDetailId || 0,\r\n            CTemplateId: item.CTemplateId || templateId,\r\n            CReleateId: item.CReleateId || 0,\r\n            CSort: undefined,\r\n            CRemark: undefined,\r\n            CCreateDt: new Date().toISOString(),\r\n            CCreator: '系統',\r\n            CCategory: undefined,\r\n            CUnitPrice: undefined,\r\n            CQuantity: undefined,\r\n            CUnit: undefined\r\n          } as TemplateDetailItem));\r\n\r\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\r\n          if (searchKeyword && searchKeyword.trim()) {\r\n            allDetails = allDetails.filter(detail =>\r\n              detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n              (detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n            );\r\n          }\r\n\r\n          // 前端分頁處理 (因為 API 不支援分頁參數)\r\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n          const endIndex = startIndex + this.detailPagination.pageSize;\r\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\r\n\r\n          // 更新詳情資料和分頁資訊\r\n          this.currentTemplateDetailsData = pagedDetails;\r\n          this.detailPagination.totalItems = allDetails.length;\r\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\r\n          this.detailPagination.currentPage = pageIndex;\r\n        } else {\r\n          this.currentTemplateDetailsData = [];\r\n          this.detailPagination.totalItems = 0;\r\n          this.detailPagination.totalPages = 0;\r\n          this.detailPagination.currentPage = 1;\r\n        }\r\n      },\r\n      error: () => {\r\n        this.currentTemplateDetailsData = [];\r\n        this.detailPagination.totalItems = 0;\r\n        this.detailPagination.totalPages = 0;\r\n        this.detailPagination.currentPage = 1;\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n\r\n  // 搜尋模板詳情 (明細專用搜尋)\r\n  searchTemplateDetails(keyword: string) {\r\n    this.detailSearchKeyword = keyword;\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\r\n    }\r\n  }\r\n\r\n  // 清除明細搜尋\r\n  clearDetailSearch() {\r\n    this.detailSearchKeyword = '';\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1);\r\n    }\r\n  }\r\n\r\n  // 明細搜尋輸入事件處理\r\n  onDetailSearchInput() {\r\n    // 可以在這裡添加即時搜尋邏輯，目前保持原有的 Enter 鍵搜尋方式\r\n  }\r\n\r\n  // 明細搜尋事件處理（即時搜尋）\r\n  onDetailSearch() {\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, this.detailSearchKeyword);\r\n    }\r\n  }\r\n\r\n  // 更新詳情分頁 (保留向後相容)\r\n  updateDetailPagination() {\r\n    // 如果使用新的詳情資料，直接返回\r\n    if (this.currentTemplateDetailsData.length > 0) {\r\n      return;\r\n    }\r\n\r\n    // 向後相容：使用舊的詳情資料\r\n    const details = this.currentTemplateDetails;\r\n    this.detailPagination.totalItems = details.length;\r\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\r\n    this.detailPagination.currentPage = 1; // 重置到第一頁\r\n    this.updatePaginatedDetails();\r\n  }\r\n\r\n  // 更新分頁後的詳情列表\r\n  updatePaginatedDetails() {\r\n    const details = this.currentTemplateDetails;\r\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\r\n    const endIndex = startIndex + this.detailPagination.pageSize;\r\n    this.paginatedDetails = details.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 詳情分頁導航\r\n  goToDetailPage(page: number) {\r\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\r\n      // 如果使用新的詳情資料，重新載入\r\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\r\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\r\n      } else {\r\n        // 向後相容：使用舊的分頁邏輯\r\n        this.detailPagination.currentPage = page;\r\n        this.updatePaginatedDetails();\r\n      }\r\n    }\r\n  }\r\n\r\n  // 獲取詳情分頁頁碼數組\r\n  getDetailPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.detailPagination.totalPages;\r\n    const currentPage = this.detailPagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // 刪除模板 - 使用真實的 API 調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 準備 API 請求參數\r\n    const deleteTemplateArgs: GetTemplateByIdArgs = {\r\n      CTemplateId: templateID\r\n    };\r\n\r\n    // 調用 DeleteTemplate API\r\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\r\n      body: deleteTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          // 重新載入模板列表\r\n          this.loadTemplates();\r\n\r\n          // 如果當前查看的模板被刪除，關閉詳情\r\n          if (this.selectedTemplate?.TemplateID === templateID) {\r\n            this.selectedTemplate = null;\r\n          }\r\n        } else {\r\n          // API 返回錯誤\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n      }\r\n    });\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/Template/GetTemplateList\r\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/Template/SaveTemplate\r\n   *    請求體: {\r\n   *      CTemplateName: string,\r\n   *      CTemplateType: number,  // 1=客變需求\r\n   *      CStatus: number,\r\n   *      Details: [{\r\n   *        CReleateId: number,        // 關聯主檔ID\r\n   *        CReleateName: string       // 關聯名稱\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    POST /api/Template/DeleteTemplate\r\n   *    請求體: { CTemplateId: number }\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\r\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\r\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\r\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 欄位顯示相關輔助方法\r\n  hasVisibleFields(detail: ApiTemplateDetailItem): boolean {\r\n    return this.fieldDisplayConfig.some(config =>\r\n      this.getFieldValue(detail, config.fieldName)\r\n    );\r\n  }\r\n\r\n  getFieldValue(detail: ApiTemplateDetailItem, fieldName: string): any {\r\n    // 根據templateType動態取得資料\r\n    if (this.templateType === 1) {\r\n      // 當type=1時，從tblRequirement物件中取得資料\r\n      return detail.tblRequirement?.[fieldName as keyof typeof detail.tblRequirement];\r\n    } else {\r\n      // 其他type可能從tblMaterials或其他物件中取得資料\r\n      // 目前先支援直接從detail物件取得\r\n      return (detail as any)[fieldName];\r\n    }\r\n  }\r\n\r\n  getFieldClass(fieldName: string): string {\r\n    const classMap: { [key: string]: string } = {\r\n      'CUnitPrice': 'price-group',\r\n      'CQuantity': 'quantity-group',\r\n      'CUnit': 'unit-group',\r\n      'CRemark': 'remark-group',\r\n      'CGroupName': 'group-group',\r\n      'CCategory': 'category-group'\r\n    };\r\n    return classMap[fieldName] || 'default-group';\r\n  }\r\n\r\n  getValueClass(fieldName: string): string {\r\n    const classMap: { [key: string]: string } = {\r\n      'CUnitPrice': 'price-value',\r\n      'CQuantity': 'quantity-value',\r\n      'CUnit': 'unit-value',\r\n      'CRemark': 'remark-value'\r\n    };\r\n    return classMap[fieldName] || 'default-value';\r\n  }\r\n\r\n  formatFieldValue(detail: TemplateDetailItem, fieldName: string): string {\r\n    const value = this.getFieldValue(detail, fieldName);\r\n    if (!value && value !== 0) return '';\r\n\r\n    switch (fieldName) {\r\n      case 'CUnitPrice':\r\n        return `NT$ ${Number(value).toLocaleString()}`;\r\n      case 'CQuantity':\r\n        const unit = detail.CUnit || '';\r\n        return unit ? `${value} ${unit}` : value.toString();\r\n      default:\r\n        return value.toString();\r\n    }\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n  CreateTime?: string;\r\n  UpdateTime?: string;\r\n  Creator?: string;\r\n  Updator?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  CTemplateType: number; // 模板類型，1=客變需求\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n\r\n// 擴展的詳情項目結構 (基於 API 的 TemplateDetailItem 擴展)\r\nexport interface TemplateDetailItem {\r\n  CTemplateDetailId: number;\r\n  CTemplateId: number;\r\n  CReleateId: number;            // 關聯主檔ID\r\n  CReleateName: string;          // 關聯名稱\r\n  CGroupName?: string;           // 群組名稱 (API 新增欄位)\r\n  CSort?: number;                // 排序順序\r\n  CRemark?: string;              // 備註\r\n  CCreateDt: string;             // 建立時間\r\n  CCreator: string;              // 建立者\r\n  CUpdateDt?: string;            // 更新時間\r\n  CUpdator?: string;             // 更新者\r\n\r\n  // 業務相關欄位 (依需求添加，部分由 API 提供)\r\n  CUnitPrice?: number;           // 單價\r\n  CQuantity?: number;            // 數量\r\n  CUnit?: string;                // 單位\r\n  CCategory?: string;            // 分類\r\n}\r\n\r\n// 注意：GetTemplateDetailById API 使用的是 GetTemplateDetailByIdArgs 和 TemplateDetailItemListResponseBase\r\n// 這些 interface 已經在 API models 中定義，不需要重複定義\r\n\r\n// 欄位顯示設定介面\r\nexport interface FieldDisplayConfig {\r\n  displayName: string;  // 中文名稱\r\n  fieldName: string;    // API欄位名稱\r\n}\r\n", "<nb-card class=\"template-viewer-card\">\r\n  <nb-card-header class=\"template-viewer-header\">\r\n    <div class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"header-title\">\r\n        <h5 class=\"mb-0\">\r\n          <i class=\"fas fa-layer-group mr-2 text-primary\"></i>模板管理\r\n        </h5>\r\n        <small class=\"text-muted\">管理和查看客變需求模板</small>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <span class=\"badge badge-info\">{{ templatePagination.totalItems }} 個模板</span>\r\n      </div>\r\n    </div>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"template-viewer-body\">\r\n    <!-- 增強的搜尋功能 (僅在模板列表時顯示) -->\r\n    <div class=\"enhanced-search-container mb-4\" *ngIf=\"!selectedTemplate\">\r\n      <div class=\"search-wrapper\">\r\n        <div class=\"search-input-group\">\r\n          <div class=\"search-icon\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </div>\r\n          <input type=\"text\" class=\"search-input\" placeholder=\"搜尋模板名稱、描述或關鍵字...\" [(ngModel)]=\"searchKeyword\"\r\n            (input)=\"onSearch()\" (keyup.enter)=\"onSearch()\">\r\n          <div class=\"search-actions\" *ngIf=\"searchKeyword\">\r\n            <button class=\"clear-search-btn\" type=\"button\" (click)=\"clearSearch()\" title=\"清除搜尋\">\r\n              <i class=\"fas fa-times\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"search-suggestions\" *ngIf=\"searchKeyword && filteredTemplates.length > 0\">\r\n          <small class=\"text-success\">\r\n            <i class=\"fas fa-check-circle mr-1\"></i>找到 {{ filteredTemplates.length }} 個相關模板\r\n          </small>\r\n        </div>\r\n        <div class=\"search-no-results\" *ngIf=\"searchKeyword && filteredTemplates.length === 0\">\r\n          <small class=\"text-warning\">\r\n            <i class=\"fas fa-exclamation-triangle mr-1\"></i>未找到符合條件的模板\r\n          </small>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 優化的模板列表（僅在未選擇模板細節時顯示） -->\r\n    <div class=\"template-list-container\" *ngIf=\"!selectedTemplate\">\r\n      <!-- 列表控制欄 -->\r\n      <div class=\"list-controls mb-3\" *ngIf=\"templatePagination.totalItems > 0\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"list-info\">\r\n            <span class=\"info-text\">\r\n              顯示第 {{ (templatePagination.currentPage - 1) * templatePagination.pageSize + 1 }} -\r\n              {{ Math.min(templatePagination.currentPage * templatePagination.pageSize, templatePagination.totalItems)\r\n              }} 項，\r\n              共 {{ templatePagination.totalItems }} 項模板\r\n            </span>\r\n          </div>\r\n          <div class=\"view-options\">\r\n            <small class=\"text-muted\">第 {{ templatePagination.currentPage }} / {{ templatePagination.totalPages }}\r\n              頁</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 簡潔模板列表 -->\r\n      <div class=\"template-list-container\">\r\n        <div class=\"template-item\" *ngFor=\"let tpl of paginatedTemplates; trackBy: trackByTemplateId\">\r\n          <div class=\"template-main-info\">\r\n            <div class=\"template-header\">\r\n              <span class=\"template-label\">模板名稱</span>\r\n              <h6 class=\"template-name\">\r\n                <i class=\"fas fa-file-alt mr-2\"></i>\r\n                {{ tpl.TemplateName }}\r\n              </h6>\r\n            </div>\r\n            <div class=\"template-meta\">\r\n              <div class=\"meta-row\" *ngIf=\"tpl.CreateTime\">\r\n                <span class=\"meta-label\">\r\n                  <i class=\"fas fa-calendar-plus mr-1\"></i>建立日期：\r\n                </span>\r\n                <span class=\"meta-value\">{{ tpl.CreateTime | date:'yyyy/MM/dd HH:mm' }}</span>\r\n              </div>\r\n              <div class=\"meta-row\" *ngIf=\"tpl.UpdateTime\">\r\n                <span class=\"meta-label\">\r\n                  <i class=\"fas fa-clock mr-1\"></i>更新時間：\r\n                </span>\r\n                <span class=\"meta-value\">{{ tpl.UpdateTime | date:'yyyy/MM/dd HH:mm' }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"template-actions\">\r\n            <button class=\"action-btn view-btn\" (click)=\"onSelectTemplate(tpl)\" title=\"查看詳情\">\r\n              <i class=\"fas fa-eye\"></i>\r\n              <span>查看</span>\r\n            </button>\r\n            <button class=\"action-btn delete-btn\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n              *ngIf=\"tpl.TemplateID\" title=\"刪除模板\">\r\n              <i class=\"fas fa-trash\"></i>\r\n              <span>刪除</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空狀態 -->\r\n        <div class=\"empty-state-card\" *ngIf=\"!paginatedTemplates || paginatedTemplates.length === 0\">\r\n          <div class=\"empty-content\">\r\n            <div class=\"empty-icon\">\r\n              <i class=\"fas fa-search\" *ngIf=\"searchKeyword\"></i>\r\n              <i class=\"fas fa-folder-open\" *ngIf=\"!searchKeyword\"></i>\r\n            </div>\r\n            <h6 class=\"empty-title\">\r\n              {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}\r\n            </h6>\r\n            <p class=\"empty-description\" *ngIf=\"searchKeyword\">\r\n              請嘗試其他關鍵字或\r\n              <a href=\"javascript:void(0)\" (click)=\"clearSearch()\" class=\"clear-link\">清除搜尋條件</a>\r\n            </p>\r\n            <p class=\"empty-description\" *ngIf=\"!searchKeyword\">\r\n              目前還沒有建立任何模板，請先建立模板\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 優化的分頁控制器 -->\r\n      <div class=\"enhanced-pagination-container mt-4\" *ngIf=\"templatePagination.totalPages > 1\">\r\n        <div class=\"pagination-wrapper\">\r\n          <div class=\"pagination-info\">\r\n            <span class=\"page-info\">\r\n              第 {{ templatePagination.currentPage }} 頁，共 {{ templatePagination.totalPages }} 頁\r\n            </span>\r\n          </div>\r\n          <nav aria-label=\"模板列表分頁\" class=\"pagination-nav\">\r\n            <ul class=\"enhanced-pagination\">\r\n              <!-- 第一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === 1\">\r\n                <button class=\"page-btn first-page\" (click)=\"goToTemplatePage(1)\"\r\n                  [disabled]=\"templatePagination.currentPage === 1\" title=\"第一頁\">\r\n                  <i class=\"fas fa-angle-double-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 上一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === 1\">\r\n                <button class=\"page-btn prev-page\" (click)=\"goToTemplatePage(templatePagination.currentPage - 1)\"\r\n                  [disabled]=\"templatePagination.currentPage === 1\" title=\"上一頁\">\r\n                  <i class=\"fas fa-chevron-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 頁碼 -->\r\n              <li class=\"page-item\" *ngFor=\"let page of getTemplatePageNumbers()\"\r\n                [class.active]=\"page === templatePagination.currentPage\">\r\n                <button class=\"page-btn page-number\" (click)=\"goToTemplatePage(page)\">{{ page }}</button>\r\n              </li>\r\n\r\n              <!-- 下一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n                <button class=\"page-btn next-page\" (click)=\"goToTemplatePage(templatePagination.currentPage + 1)\"\r\n                  [disabled]=\"templatePagination.currentPage === templatePagination.totalPages\" title=\"下一頁\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 最後一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n                <button class=\"page-btn last-page\" (click)=\"goToTemplatePage(templatePagination.totalPages)\"\r\n                  [disabled]=\"templatePagination.currentPage === templatePagination.totalPages\" title=\"最後一頁\">\r\n                  <i class=\"fas fa-angle-double-right\"></i>\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 優化的模板詳情視圖 -->\r\n    <div *ngIf=\"selectedTemplate\" class=\"template-detail-view\">\r\n      <!-- 詳情標題欄 -->\r\n      <div class=\"detail-header\">\r\n        <div class=\"detail-title-section\">\r\n          <div class=\"back-button\">\r\n            <button class=\"back-btn\" (click)=\"closeTemplateDetail()\" title=\"返回模板列表\">\r\n              <i class=\"fas fa-arrow-left\"></i>\r\n            </button>\r\n          </div>\r\n          <div class=\"detail-title-info\">\r\n            <h5 class=\"detail-title\">\r\n              <i class=\"fas fa-file-alt mr-2 text-primary\"></i>\r\n              {{ selectedTemplate!.TemplateName }}\r\n            </h5>\r\n            <p class=\"detail-subtitle\" *ngIf=\"selectedTemplate.Description\">\r\n              {{ selectedTemplate.Description }}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div class=\"detail-stats\">\r\n          <div class=\"stat-item\">\r\n            <span class=\"stat-label\">項目數量</span>\r\n            <span class=\"stat-value\">{{ currentTemplateDetailsData.length > 0 ? detailPagination.totalItems :\r\n              currentTemplateDetails.length }}</span>\r\n          </div>\r\n          <div class=\"stat-item\" *ngIf=\"detailPagination.totalPages > 1\">\r\n            <span class=\"stat-label\">頁數</span>\r\n            <span class=\"stat-value\">{{ detailPagination.currentPage }} / {{ detailPagination.totalPages }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 明細專用搜尋區域 -->\r\n      <div class=\"enhanced-search-container mb-4\">\r\n        <div class=\"search-wrapper\">\r\n          <div class=\"search-input-group\">\r\n            <div class=\"search-icon\">\r\n              <i class=\"fas fa-search\"></i>\r\n            </div>\r\n            <input type=\"text\" class=\"search-input\" placeholder=\"搜尋明細項目名稱、群組...\" [(ngModel)]=\"detailSearchKeyword\"\r\n              (input)=\"onDetailSearch()\" (keyup.enter)=\"onDetailSearch()\">\r\n            <div class=\"search-actions\" *ngIf=\"detailSearchKeyword\">\r\n              <button class=\"clear-search-btn\" type=\"button\" (click)=\"clearDetailSearch()\" title=\"清除搜尋\">\r\n                <i class=\"fas fa-times\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n          <div class=\"search-suggestions\" *ngIf=\"detailSearchKeyword && detailPagination.totalItems > 0\">\r\n            <small class=\"text-success\">\r\n              <i class=\"fas fa-check-circle mr-1\"></i>找到 {{ detailPagination.totalItems }} 個相關項目\r\n            </small>\r\n          </div>\r\n          <div class=\"search-no-results\" *ngIf=\"detailSearchKeyword && detailPagination.totalItems === 0\">\r\n            <small class=\"text-warning\">\r\n              <i class=\"fas fa-exclamation-triangle mr-1\"></i>未找到符合條件的項目\r\n            </small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 詳情內容區域 -->\r\n      <div class=\"detail-content\">\r\n\r\n\r\n        <!-- 優化的詳情項目顯示 -->\r\n        <div *ngIf=\"currentTemplateDetailsData.length > 0; else checkOldDetails\" class=\"enhanced-detail-list\">\r\n          <div *ngFor=\"let detail of currentTemplateDetailsData; let i = index\" class=\"enhanced-detail-item\">\r\n            <div class=\"detail-item-card\">\r\n              <div class=\"detail-item-header\">\r\n                <div class=\"item-index\">\r\n                  <span class=\"index-badge\">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i + 1\r\n                    }}</span>\r\n                </div>\r\n                <div class=\"item-main-info\">\r\n                  <h6 class=\"item-name\">\r\n                    <i class=\"fas fa-cog mr-2 text-secondary\"></i>\r\n                    {{ detail.tblRequirement?.CRequirement || '未命名項目' }}\r\n                  </h6>\r\n                  <div class=\"item-meta\">\r\n                    <span class=\"meta-item id-meta\">\r\n                      <i class=\"fas fa-hashtag\"></i>\r\n                      <span>{{ detail.CReleateId }}</span>\r\n                    </span>\r\n                    <span class=\"meta-item group-meta\" *ngIf=\"detail.tblRequirement?.CGroupName\">\r\n                      <i class=\"fas fa-layer-group\"></i>\r\n                      <span>{{ detail.tblRequirement?.CGroupName }}</span>\r\n                    </span>\r\n                    <span class=\"meta-item category-meta\" *ngIf=\"detail.tblRequirement?.CRemark\">\r\n                      <i class=\"fas fa-tag\"></i>\r\n                      <span>{{ detail.tblRequirement?.CRemark }}</span>\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item-actions\">\r\n                  <span class=\"create-date\">{{ detail.tblRequirement?.CCreateDt | date:'MM/dd' }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"detail-item-body\" *ngIf=\"hasVisibleFields(detail)\">\r\n                <div class=\"item-details-grid\">\r\n                  <div class=\"detail-group\" *ngFor=\"let config of fieldDisplayConfig\"\r\n                    [ngClass]=\"getFieldClass(config.fieldName)\" [hidden]=\"!getFieldValue(detail, config.fieldName)\">\r\n                    <span class=\"detail-label\">{{ config.displayName }}</span>\r\n                    <span class=\"detail-value\" [ngClass]=\"getValueClass(config.fieldName)\">\r\n                      {{ formatFieldValue(detail, config.fieldName) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 向後相容：舊的詳情資料顯示 -->\r\n        <ng-template #checkOldDetails>\r\n          <div *ngIf=\"currentTemplateDetails.length > 0; else noDetails\" class=\"detail-list\">\r\n            <div *ngFor=\"let detail of paginatedDetails; let i = index\"\r\n              class=\"detail-item d-flex align-items-center py-2 border-bottom\">\r\n              <div class=\"detail-index\">\r\n                <span class=\"badge badge-light\">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i\r\n                  + 1 }}</span>\r\n              </div>\r\n              <div class=\"detail-content flex-grow-1 ml-2\">\r\n                <div class=\"detail-field\">\r\n                  <strong>{{ detail.FieldName }}:</strong>\r\n                </div>\r\n                <div class=\"detail-value text-muted\">\r\n                  {{ detail.FieldValue }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-template>\r\n\r\n        <!-- 詳情分頁控制器 -->\r\n        <div class=\"detail-pagination mt-3\" *ngIf=\"detailPagination.totalPages > 1\">\r\n          <nav aria-label=\"模板詳情分頁\">\r\n            <ul class=\"pagination pagination-sm justify-content-center mb-0\">\r\n              <!-- 上一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === 1\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage - 1)\"\r\n                  [disabled]=\"detailPagination.currentPage === 1\">\r\n                  <i class=\"fas fa-chevron-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 頁碼 -->\r\n              <li class=\"page-item\" *ngFor=\"let page of getDetailPageNumbers()\"\r\n                [class.active]=\"page === detailPagination.currentPage\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(page)\">{{ page }}</button>\r\n              </li>\r\n\r\n              <!-- 下一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage + 1)\"\r\n                  [disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n\r\n        <ng-template #noDetails>\r\n          <div class=\"text-center py-3\">\r\n            <i class=\"fas fa-inbox fa-2x text-muted mb-2\"></i>\r\n            <p class=\"text-muted mb-0\">此模板暫無內容</p>\r\n          </div>\r\n        </ng-template>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"template-viewer-footer\">\r\n    <div class=\"footer-actions\">\r\n      <button class=\"close-btn\" (click)=\"onClose()\">\r\n        <i class=\"fas fa-times mr-2\"></i>關閉\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;;;;;;;;;ICsBjDC,EADF,CAAAC,cAAA,cAAkD,iBACoC;IAArCD,EAAA,CAAAE,UAAA,mBAAAC,sEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACpET,EAAA,CAAAU,SAAA,YAA4B;IAEhCV,EADE,CAAAW,YAAA,EAAS,EACL;;;;;IAGNX,EADF,CAAAC,cAAA,cAAsF,gBACxD;IAC1BD,EAAA,CAAAU,SAAA,YAAwC;IAAAV,EAAA,CAAAY,MAAA,GAC1C;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;IAFsCX,EAAA,CAAAa,SAAA,GAC1C;IAD0Cb,EAAA,CAAAc,kBAAA,kBAAAR,MAAA,CAAAS,iBAAA,CAAAC,MAAA,qCAC1C;;;;;IAGAhB,EADF,CAAAC,cAAA,cAAuF,gBACzD;IAC1BD,EAAA,CAAAU,SAAA,YAAgD;IAAAV,EAAA,CAAAY,MAAA,oEAClD;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;;;IApBJX,EAHN,CAAAC,cAAA,cAAsE,cACxC,cACM,cACL;IACvBD,EAAA,CAAAU,SAAA,YAA6B;IAC/BV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBACkD;IADqBD,EAAA,CAAAiB,gBAAA,2BAAAC,uEAAAC,MAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAqB,kBAAA,CAAAf,MAAA,CAAAgB,aAAA,EAAAH,MAAA,MAAAb,MAAA,CAAAgB,aAAA,GAAAH,MAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAAAW,MAAA;IAAA,EAA2B;IAC3EnB,EAArB,CAAAE,UAAA,mBAAAqB,+DAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkB,QAAA,EAAU;IAAA,EAAC,yBAAAC,qEAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAgBF,MAAA,CAAAkB,QAAA,EAAU;IAAA,EAAC;IADjDxB,EAAA,CAAAW,YAAA,EACkD;IAClDX,EAAA,CAAA0B,UAAA,IAAAC,6CAAA,kBAAkD;IAKpD3B,EAAA,CAAAW,YAAA,EAAM;IAMNX,EALA,CAAA0B,UAAA,IAAAE,6CAAA,kBAAsF,IAAAC,6CAAA,kBAKC;IAM3F7B,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAnBuEX,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAA8B,gBAAA,YAAAxB,MAAA,CAAAgB,aAAA,CAA2B;IAErEtB,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgB,aAAA,CAAmB;IAMjBtB,EAAA,CAAAa,SAAA,EAAmD;IAAnDb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgB,aAAA,IAAAhB,MAAA,CAAAS,iBAAA,CAAAC,MAAA,KAAmD;IAKpDhB,EAAA,CAAAa,SAAA,EAAqD;IAArDb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgB,aAAA,IAAAhB,MAAA,CAAAS,iBAAA,CAAAC,MAAA,OAAqD;;;;;IAgBjFhB,EAHN,CAAAC,cAAA,cAA0E,aACT,cACtC,eACG;IACtBD,EAAA,CAAAY,MAAA,GAIF;IACFZ,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAC,cAAA,cAA0B,eACE;IAAAD,EAAA,CAAAY,MAAA,GACvB;IAGTZ,EAHS,CAAAW,YAAA,EAAQ,EACP,EACF,EACF;;;;IAXEX,EAAA,CAAAa,SAAA,GAIF;IAJEb,EAAA,CAAAgC,kBAAA,0BAAA1B,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,QAAA5B,MAAA,CAAA2B,kBAAA,CAAAE,QAAA,aAAA7B,MAAA,CAAA8B,IAAA,CAAAC,GAAA,CAAA/B,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,GAAA5B,MAAA,CAAA2B,kBAAA,CAAAE,QAAA,EAAA7B,MAAA,CAAA2B,kBAAA,CAAAK,UAAA,4BAAAhC,MAAA,CAAA2B,kBAAA,CAAAK,UAAA,yBAIF;IAG0BtC,EAAA,CAAAa,SAAA,GACvB;IADuBb,EAAA,CAAAuC,kBAAA,YAAAjC,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,SAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,YACvB;;;;;IAkBCxC,EADF,CAAAC,cAAA,cAA6C,eAClB;IACvBD,EAAA,CAAAU,SAAA,YAAyC;IAAAV,EAAA,CAAAY,MAAA,sCAC3C;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAY,MAAA,GAA8C;;IACzEZ,EADyE,CAAAW,YAAA,EAAO,EAC1E;;;;IADqBX,EAAA,CAAAa,SAAA,GAA8C;IAA9Cb,EAAA,CAAAyC,iBAAA,CAAAzC,EAAA,CAAA0C,WAAA,OAAAC,MAAA,CAAAC,UAAA,sBAA8C;;;;;IAGvE5C,EADF,CAAAC,cAAA,cAA6C,eAClB;IACvBD,EAAA,CAAAU,SAAA,YAAiC;IAAAV,EAAA,CAAAY,MAAA,sCACnC;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAY,MAAA,GAA8C;;IACzEZ,EADyE,CAAAW,YAAA,EAAO,EAC1E;;;;IADqBX,EAAA,CAAAa,SAAA,GAA8C;IAA9Cb,EAAA,CAAAyC,iBAAA,CAAAzC,EAAA,CAAA0C,WAAA,OAAAC,MAAA,CAAAE,UAAA,sBAA8C;;;;;;IAS3E7C,EAAA,CAAAC,cAAA,iBACsC;IADAD,EAAA,CAAAE,UAAA,mBAAA4C,gFAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAA2C,GAAA;MAAA,MAAAJ,MAAA,GAAA3C,EAAA,CAAAO,aAAA,GAAAyC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAmC,MAAA,CAAAM,UAAA,IAA2B3C,MAAA,CAAA4C,gBAAA,CAAAP,MAAA,CAAAM,UAAA,CAAgC;IAAA,EAAC;IAEhGjD,EAAA,CAAAU,SAAA,YAA4B;IAC5BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IACVZ,EADU,CAAAW,YAAA,EAAO,EACR;;;;;;IA9BPX,EAHN,CAAAC,cAAA,cAA8F,cAC5D,cACD,eACE;IAAAD,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACxCX,EAAA,CAAAC,cAAA,aAA0B;IACxBD,EAAA,CAAAU,SAAA,YAAoC;IACpCV,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAK,EACD;IACNX,EAAA,CAAAC,cAAA,cAA2B;IAOzBD,EANA,CAAA0B,UAAA,IAAAyB,mDAAA,kBAA6C,KAAAC,oDAAA,kBAMA;IAOjDpD,EADE,CAAAW,YAAA,EAAM,EACF;IAEJX,EADF,CAAAC,cAAA,eAA8B,kBACqD;IAA7CD,EAAA,CAAAE,UAAA,mBAAAmD,uEAAA;MAAA,MAAAV,MAAA,GAAA3C,EAAA,CAAAI,aAAA,CAAAkD,GAAA,EAAAN,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiD,gBAAA,CAAAZ,MAAA,CAAqB;IAAA,EAAC;IACjE3C,EAAA,CAAAU,SAAA,aAA0B;IAC1BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,oBAAE;IACVZ,EADU,CAAAW,YAAA,EAAO,EACR;IACTX,EAAA,CAAA0B,UAAA,KAAA8B,uDAAA,qBACsC;IAK1CxD,EADE,CAAAW,YAAA,EAAM,EACF;;;;IA7BEX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA6B,MAAA,CAAAc,YAAA,MACF;IAGuBzD,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAA+B,UAAA,SAAAY,MAAA,CAAAC,UAAA,CAAoB;IAMpB5C,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAA+B,UAAA,SAAAY,MAAA,CAAAE,UAAA,CAAoB;IAc1C7C,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAA+B,UAAA,SAAAY,MAAA,CAAAM,UAAA,CAAoB;;;;;IAWrBjD,EAAA,CAAAU,SAAA,YAAmD;;;;;IACnDV,EAAA,CAAAU,SAAA,YAAyD;;;;;;IAK3DV,EAAA,CAAAC,cAAA,YAAmD;IACjDD,EAAA,CAAAY,MAAA,+DACA;IAAAZ,EAAA,CAAAC,cAAA,YAAwE;IAA3CD,EAAA,CAAAE,UAAA,mBAAAwD,qEAAA;MAAA1D,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAAoBT,EAAA,CAAAY,MAAA,2CAAM;IAChFZ,EADgF,CAAAW,YAAA,EAAI,EAChF;;;;;IACJX,EAAA,CAAAC,cAAA,YAAoD;IAClDD,EAAA,CAAAY,MAAA,qHACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IAbJX,EAFJ,CAAAC,cAAA,cAA6F,cAChE,cACD;IAEtBD,EADA,CAAA0B,UAAA,IAAAkC,iDAAA,gBAA+C,IAAAC,iDAAA,gBACM;IACvD7D,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,aAAwB;IACtBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAKLX,EAJA,CAAA0B,UAAA,IAAAoC,iDAAA,gBAAmD,IAAAC,iDAAA,gBAIC;IAIxD/D,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAd0BX,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgB,aAAA,CAAmB;IACdtB,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAA+B,UAAA,UAAAzB,MAAA,CAAAgB,aAAA,CAAoB;IAGnDtB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAAgB,aAAA,oGACF;IAC8BtB,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgB,aAAA,CAAmB;IAInBtB,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAA+B,UAAA,UAAAzB,MAAA,CAAAgB,aAAA,CAAoB;;;;;;IAoC9CtB,EAFF,CAAAC,cAAA,aAC2D,iBACa;IAAjCD,EAAA,CAAAE,UAAA,mBAAA8D,4EAAA;MAAA,MAAAC,QAAA,GAAAjE,EAAA,CAAAI,aAAA,CAAA8D,GAAA,EAAAlB,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,gBAAA,CAAAF,QAAA,CAAsB;IAAA,EAAC;IAACjE,EAAA,CAAAY,MAAA,GAAU;IAClFZ,EADkF,CAAAW,YAAA,EAAS,EACtF;;;;;IAFHX,EAAA,CAAAoE,WAAA,WAAAH,QAAA,KAAA3D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,CAAwD;IACclC,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAAyC,iBAAA,CAAAwB,QAAA,CAAU;;;;;;IAzBpFjE,EAHN,CAAAC,cAAA,cAA0F,cACxD,cACD,eACH;IACtBD,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAO,EACH;IAKAX,EAJN,CAAAC,cAAA,cAAgD,aACd,aAEgD,iBAEZ;IAD5BD,EAAA,CAAAE,UAAA,mBAAAmE,sEAAA;MAAArE,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,gBAAA,CAAiB,CAAC,CAAC;IAAA,EAAC;IAE/DnE,EAAA,CAAAU,SAAA,YAAwC;IAE5CV,EADE,CAAAW,YAAA,EAAS,EACN;IAIHX,EADF,CAAAC,cAAA,cAA8E,kBAEZ;IAD7BD,EAAA,CAAAE,UAAA,mBAAAqE,uEAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,gBAAA,CAAA7D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAE/FlC,EAAA,CAAAU,SAAA,aAAmC;IAEvCV,EADE,CAAAW,YAAA,EAAS,EACN;IAGLX,EAAA,CAAA0B,UAAA,KAAA8C,mDAAA,iBAC2D;IAMzDxE,EADF,CAAAC,cAAA,cAA0G,kBAEZ;IADzDD,EAAA,CAAAE,UAAA,mBAAAuE,uEAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,gBAAA,CAAA7D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAE/FlC,EAAA,CAAAU,SAAA,aAAoC;IAExCV,EADE,CAAAW,YAAA,EAAS,EACN;IAIHX,EADF,CAAAC,cAAA,cAA0G,kBAEX;IAD1DD,EAAA,CAAAE,UAAA,mBAAAwE,uEAAA;MAAA1E,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,gBAAA,CAAA7D,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,CAA+C;IAAA,EAAC;IAE1FxC,EAAA,CAAAU,SAAA,aAAyC;IAMrDV,EALU,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACF,EACF;;;;IA7CEX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAuC,kBAAA,aAAAjC,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,0BAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,aACF;IAKwBxC,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,OAAuD;IAEzElC,EAAA,CAAAa,SAAA,EAAiD;IAAjDb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,OAAiD;IAM/BlC,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,OAAuD;IAEzElC,EAAA,CAAAa,SAAA,EAAiD;IAAjDb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,OAAiD;IAMdlC,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAAqE,sBAAA,GAA2B;IAM5C3E,EAAA,CAAAa,SAAA,EAAmF;IAAnFb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,KAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,CAAmF;IAErGxC,EAAA,CAAAa,SAAA,EAA6E;IAA7Eb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,KAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,CAA6E;IAM3DxC,EAAA,CAAAa,SAAA,GAAmF;IAAnFb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,KAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,CAAmF;IAErGxC,EAAA,CAAAa,SAAA,EAA6E;IAA7Eb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,KAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,CAA6E;;;;;IA1H3FxC,EAAA,CAAAC,cAAA,cAA+D;IAE7DD,EAAA,CAAA0B,UAAA,IAAAkD,6CAAA,kBAA0E;IAkB1E5E,EAAA,CAAAC,cAAA,cAAqC;IAuCnCD,EAtCA,CAAA0B,UAAA,IAAAmD,6CAAA,mBAA8F,IAAAC,6CAAA,kBAsCD;IAkB/F9E,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA0B,UAAA,IAAAqD,6CAAA,oBAA0F;IAkD5F/E,EAAA,CAAAW,YAAA,EAAM;;;;IAhI6BX,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA2B,kBAAA,CAAAK,UAAA,KAAuC;IAmB3BtC,EAAA,CAAAa,SAAA,GAAuB;IAAAb,EAAvB,CAAA+B,UAAA,YAAAzB,MAAA,CAAA0E,kBAAA,CAAuB,iBAAA1E,MAAA,CAAA2E,iBAAA,CAA0B;IAsC7DjF,EAAA,CAAAa,SAAA,EAA4D;IAA5Db,EAAA,CAAA+B,UAAA,UAAAzB,MAAA,CAAA0E,kBAAA,IAAA1E,MAAA,CAAA0E,kBAAA,CAAAhE,MAAA,OAA4D;IAqB5ChB,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,KAAuC;;;;;IAmElFxC,EAAA,CAAAC,cAAA,aAAgE;IAC9DD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;IADFX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,MACF;;;;;IAUAnF,EADF,CAAAC,cAAA,eAA+D,gBACpC;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAClCX,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAY,MAAA,GAAsE;IACjGZ,EADiG,CAAAW,YAAA,EAAO,EAClG;;;;IADqBX,EAAA,CAAAa,SAAA,GAAsE;IAAtEb,EAAA,CAAAuC,kBAAA,KAAAjC,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,SAAA5B,MAAA,CAAA8E,gBAAA,CAAA5C,UAAA,KAAsE;;;;;;IAe7FxC,EADF,CAAAC,cAAA,cAAwD,iBACoC;IAA3CD,EAAA,CAAAE,UAAA,mBAAAmF,uEAAA;MAAArF,EAAA,CAAAI,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiF,iBAAA,EAAmB;IAAA,EAAC;IAC1EvF,EAAA,CAAAU,SAAA,YAA4B;IAEhCV,EADE,CAAAW,YAAA,EAAS,EACL;;;;;IAGNX,EADF,CAAAC,cAAA,cAA+F,gBACjE;IAC1BD,EAAA,CAAAU,SAAA,YAAwC;IAAAV,EAAA,CAAAY,MAAA,GAC1C;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;IAFsCX,EAAA,CAAAa,SAAA,GAC1C;IAD0Cb,EAAA,CAAAc,kBAAA,kBAAAR,MAAA,CAAA8E,gBAAA,CAAA9C,UAAA,qCAC1C;;;;;IAGAtC,EADF,CAAAC,cAAA,cAAgG,gBAClE;IAC1BD,EAAA,CAAAU,SAAA,YAAgD;IAAAV,EAAA,CAAAY,MAAA,oEAClD;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;;IA2BIX,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAU,SAAA,aAAkC;IAClCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAAuC;IAC/CZ,EAD+C,CAAAW,YAAA,EAAO,EAC/C;;;;IADCX,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAyC,iBAAA,CAAA+C,UAAA,CAAAC,cAAA,kBAAAD,UAAA,CAAAC,cAAA,CAAAC,UAAA,CAAuC;;;;;IAE/C1F,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAU,SAAA,aAA0B;IAC1BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAAoC;IAC5CZ,EAD4C,CAAAW,YAAA,EAAO,EAC5C;;;;IADCX,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAAyC,iBAAA,CAAA+C,UAAA,CAAAC,cAAA,kBAAAD,UAAA,CAAAC,cAAA,CAAAE,OAAA,CAAoC;;;;;IAa5C3F,EAFF,CAAAC,cAAA,eACkG,gBACrE;IAAAD,EAAA,CAAAY,MAAA,GAAwB;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAC1DX,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAO,EACH;;;;;;IALwCX,EAA5C,CAAA+B,UAAA,YAAAzB,MAAA,CAAAsF,aAAA,CAAAC,UAAA,CAAAC,SAAA,EAA2C,YAAAxF,MAAA,CAAAyF,aAAA,CAAAP,UAAA,EAAAK,UAAA,CAAAC,SAAA,EAAoD;IACpE9F,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAyC,iBAAA,CAAAoD,UAAA,CAAAG,WAAA,CAAwB;IACxBhG,EAAA,CAAAa,SAAA,EAA2C;IAA3Cb,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAA2F,aAAA,CAAAJ,UAAA,CAAAC,SAAA,EAA2C;IACpE9F,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAA4F,gBAAA,CAAAV,UAAA,EAAAK,UAAA,CAAAC,SAAA,OACF;;;;;IANJ9F,EADF,CAAAC,cAAA,eAA+D,eAC9B;IAC7BD,EAAA,CAAA0B,UAAA,IAAAyE,iEAAA,mBACkG;IAOtGnG,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAR2CX,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAA8F,kBAAA,CAAqB;;;;;IA9BlEpG,EAJR,CAAAC,cAAA,eAAmG,eACnE,eACI,eACN,gBACI;IAAAD,EAAA,CAAAY,MAAA,GACtB;IACNZ,EADM,CAAAW,YAAA,EAAO,EACP;IAEJX,EADF,CAAAC,cAAA,eAA4B,cACJ;IACpBD,EAAA,CAAAU,SAAA,aAA8C;IAC9CV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAEHX,EADF,CAAAC,cAAA,gBAAuB,iBACW;IAC9BD,EAAA,CAAAU,SAAA,cAA8B;IAC9BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,IAAuB;IAC/BZ,EAD+B,CAAAW,YAAA,EAAO,EAC/B;IAKPX,EAJA,CAAA0B,UAAA,KAAA2E,4DAAA,oBAA6E,KAAAC,4DAAA,oBAIA;IAKjFtG,EADE,CAAAW,YAAA,EAAM,EACF;IAEJX,EADF,CAAAC,cAAA,gBAA0B,iBACE;IAAAD,EAAA,CAAAY,MAAA,IAAqD;;IAEnFZ,EAFmF,CAAAW,YAAA,EAAO,EAClF,EACF;IAENX,EAAA,CAAA0B,UAAA,KAAA6E,2DAAA,mBAA+D;IAYnEvG,EADE,CAAAW,YAAA,EAAM,EACF;;;;;;IAxC4BX,EAAA,CAAAa,SAAA,GACtB;IADsBb,EAAA,CAAAyC,iBAAA,EAAAnC,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,QAAA5B,MAAA,CAAA8E,gBAAA,CAAAjD,QAAA,GAAAqE,KAAA,KACtB;IAKFxG,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,OAAA0E,UAAA,CAAAC,cAAA,kBAAAD,UAAA,CAAAC,cAAA,CAAAgB,YAAA,2CACF;IAIUzG,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAyC,iBAAA,CAAA+C,UAAA,CAAAkB,UAAA,CAAuB;IAEK1G,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAA+B,UAAA,SAAAyD,UAAA,CAAAC,cAAA,kBAAAD,UAAA,CAAAC,cAAA,CAAAC,UAAA,CAAuC;IAIpC1F,EAAA,CAAAa,SAAA,EAAoC;IAApCb,EAAA,CAAA+B,UAAA,SAAAyD,UAAA,CAAAC,cAAA,kBAAAD,UAAA,CAAAC,cAAA,CAAAE,OAAA,CAAoC;IAOnD3F,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAAyC,iBAAA,CAAAzC,EAAA,CAAA0C,WAAA,QAAA8C,UAAA,CAAAC,cAAA,kBAAAD,UAAA,CAAAC,cAAA,CAAAkB,SAAA,WAAqD;IAIpD3G,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAsG,gBAAA,CAAApB,UAAA,EAA8B;;;;;IAjCnExF,EAAA,CAAAC,cAAA,eAAsG;IACpGD,EAAA,CAAA0B,UAAA,IAAAmF,oDAAA,qBAAmG;IA6CrG7G,EAAA,CAAAW,YAAA,EAAM;;;;IA7CoBX,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAAwG,0BAAA,CAA+B;;;;;IAqDjD9G,EAHJ,CAAAC,cAAA,eACmE,eACvC,gBACQ;IAAAD,EAAA,CAAAY,MAAA,GACxB;IACVZ,EADU,CAAAW,YAAA,EAAO,EACX;IAGFX,EAFJ,CAAAC,cAAA,eAA6C,eACjB,aAChB;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IACjCZ,EADiC,CAAAW,YAAA,EAAS,EACpC;IACNX,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAY,MAAA,GACF;IAEJZ,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;;;IAX8BX,EAAA,CAAAa,SAAA,GACxB;IADwBb,EAAA,CAAAyC,iBAAA,EAAAnC,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,QAAA5B,MAAA,CAAA8E,gBAAA,CAAAjD,QAAA,GAAA4E,KAAA,KACxB;IAIE/G,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,kBAAA,KAAAkG,UAAA,CAAAC,SAAA,MAAuB;IAG/BjH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAkG,UAAA,CAAAE,UAAA,MACF;;;;;IAbNlH,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAA0B,UAAA,IAAAyF,kEAAA,oBACmE;IAcrEnH,EAAA,CAAAW,YAAA,EAAM;;;;IAfoBX,EAAA,CAAAa,SAAA,EAAqB;IAArBb,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAA8G,gBAAA,CAAqB;;;;;IAD/CpH,EAAA,CAAA0B,UAAA,IAAA2F,4DAAA,mBAAmF;;;;;;IAApCrH,EAAzC,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgH,sBAAA,CAAAtG,MAAA,KAAyC,aAAAuG,aAAA,CAAc;;;;;;IAkCvDvH,EAFF,CAAAC,cAAA,aACyD,kBACE;IAA/BD,EAAA,CAAAE,UAAA,mBAAAsH,4EAAA;MAAA,MAAAC,QAAA,GAAAzH,EAAA,CAAAI,aAAA,CAAAsH,IAAA,EAAA1E,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqH,cAAA,CAAAF,QAAA,CAAoB;IAAA,EAAC;IAACzH,EAAA,CAAAY,MAAA,GAAU;IACrEZ,EADqE,CAAAW,YAAA,EAAS,EACzE;;;;;IAFHX,EAAA,CAAAoE,WAAA,WAAAqD,QAAA,KAAAnH,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,CAAsD;IACGlC,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAAyC,iBAAA,CAAAgF,QAAA,CAAU;;;;;;IATnEzH,EALR,CAAAC,cAAA,eAA4E,eACjD,cAC0C,aAEa,kBAExB;IADxBD,EAAA,CAAAE,UAAA,mBAAA0H,uEAAA;MAAA5H,EAAA,CAAAI,aAAA,CAAAyH,IAAA;MAAA,MAAAvH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqH,cAAA,CAAArH,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElFlC,EAAA,CAAAU,SAAA,YAAmC;IAEvCV,EADE,CAAAW,YAAA,EAAS,EACN;IAGLX,EAAA,CAAA0B,UAAA,IAAAoG,mDAAA,iBACyD;IAMvD9H,EADF,CAAAC,cAAA,aAAsG,kBAExB;IADlDD,EAAA,CAAAE,UAAA,mBAAA6H,uEAAA;MAAA/H,EAAA,CAAAI,aAAA,CAAAyH,IAAA;MAAA,MAAAvH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqH,cAAA,CAAArH,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElFlC,EAAA,CAAAU,SAAA,YAAoC;IAK9CV,EAJQ,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAtBsBX,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,OAAqD;IAEvElC,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,OAA+C;IAMZlC,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAA0H,oBAAA,GAAyB;IAM1ChI,EAAA,CAAAa,SAAA,EAA+E;IAA/Eb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,KAAA5B,MAAA,CAAA8E,gBAAA,CAAA5C,UAAA,CAA+E;IAEjGxC,EAAA,CAAAa,SAAA,EAAyE;IAAzEb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,KAAA5B,MAAA,CAAA8E,gBAAA,CAAA5C,UAAA,CAAyE;;;;;IASjFxC,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAU,SAAA,aAAkD;IAClDV,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAY,MAAA,iDAAO;IACpCZ,EADoC,CAAAW,YAAA,EAAI,EAClC;;;;;;IAlKJX,EALR,CAAAC,cAAA,cAA2D,cAE9B,cACS,cACP,iBACiD;IAA/CD,EAAA,CAAAE,UAAA,mBAAA+H,gEAAA;MAAAjI,EAAA,CAAAI,aAAA,CAAA8H,IAAA;MAAA,MAAA5H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6H,mBAAA,EAAqB;IAAA,EAAC;IACtDnI,EAAA,CAAAU,SAAA,YAAiC;IAErCV,EADE,CAAAW,YAAA,EAAS,EACL;IAEJX,EADF,CAAAC,cAAA,cAA+B,aACJ;IACvBD,EAAA,CAAAU,SAAA,aAAiD;IACjDV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAA0B,UAAA,KAAA0G,4CAAA,iBAAgE;IAIpEpI,EADE,CAAAW,YAAA,EAAM,EACF;IAGFX,EAFJ,CAAAC,cAAA,gBAA0B,gBACD,iBACI;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACpCX,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAY,MAAA,IACS;IACpCZ,EADoC,CAAAW,YAAA,EAAO,EACrC;IACNX,EAAA,CAAA0B,UAAA,KAAA2G,8CAAA,mBAA+D;IAKnErI,EADE,CAAAW,YAAA,EAAM,EACF;IAMAX,EAHN,CAAAC,cAAA,eAA4C,eACd,eACM,eACL;IACvBD,EAAA,CAAAU,SAAA,aAA6B;IAC/BV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,kBAC8D;IADOD,EAAA,CAAAiB,gBAAA,2BAAAqH,wEAAAnH,MAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAA8H,IAAA;MAAA,MAAA5H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAqB,kBAAA,CAAAf,MAAA,CAAAiI,mBAAA,EAAApH,MAAA,MAAAb,MAAA,CAAAiI,mBAAA,GAAApH,MAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAAAW,MAAA;IAAA,EAAiC;IACzEnB,EAA3B,CAAAE,UAAA,mBAAAsI,gEAAA;MAAAxI,EAAA,CAAAI,aAAA,CAAA8H,IAAA;MAAA,MAAA5H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmI,cAAA,EAAgB;IAAA,EAAC,yBAAAC,sEAAA;MAAA1I,EAAA,CAAAI,aAAA,CAAA8H,IAAA;MAAA,MAAA5H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAgBF,MAAA,CAAAmI,cAAA,EAAgB;IAAA,EAAC;IAD7DzI,EAAA,CAAAW,YAAA,EAC8D;IAC9DX,EAAA,CAAA0B,UAAA,KAAAiH,8CAAA,kBAAwD;IAK1D3I,EAAA,CAAAW,YAAA,EAAM;IAMNX,EALA,CAAA0B,UAAA,KAAAkH,8CAAA,kBAA+F,KAAAC,8CAAA,kBAKC;IAMpG7I,EADE,CAAAW,YAAA,EAAM,EACF;IAGNX,EAAA,CAAAC,cAAA,gBAA4B;IAsG1BD,EAlGA,CAAA0B,UAAA,KAAAoH,8CAAA,mBAAsG,KAAAC,sDAAA,gCAAA/I,EAAA,CAAAgJ,sBAAA,CAiDxE,KAAAC,8CAAA,oBAqB8C,KAAAC,sDAAA,gCAAAlJ,EAAA,CAAAgJ,sBAAA,CA4BpD;IAO5BhJ,EADE,CAAAW,YAAA,EAAM,EACF;;;;;IA9JIX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAA4E,gBAAA,CAAAzB,YAAA,MACF;IAC4BzD,EAAA,CAAAa,SAAA,EAAkC;IAAlCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,CAAkC;IAQrCnF,EAAA,CAAAa,SAAA,GACS;IADTb,EAAA,CAAAyC,iBAAA,CAAAnC,MAAA,CAAAwG,0BAAA,CAAA9F,MAAA,OAAAV,MAAA,CAAA8E,gBAAA,CAAA9C,UAAA,GAAAhC,MAAA,CAAAgH,sBAAA,CAAAtG,MAAA,CACS;IAEZhB,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA8E,gBAAA,CAAA5C,UAAA,KAAqC;IAcUxC,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAA8B,gBAAA,YAAAxB,MAAA,CAAAiI,mBAAA,CAAiC;IAEzEvI,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAiI,mBAAA,CAAyB;IAMvBvI,EAAA,CAAAa,SAAA,EAA4D;IAA5Db,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAiI,mBAAA,IAAAjI,MAAA,CAAA8E,gBAAA,CAAA9C,UAAA,KAA4D;IAK7DtC,EAAA,CAAAa,SAAA,EAA8D;IAA9Db,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAiI,mBAAA,IAAAjI,MAAA,CAAA8E,gBAAA,CAAA9C,UAAA,OAA8D;IAa1FtC,EAAA,CAAAa,SAAA,GAA6C;IAAAb,EAA7C,CAAA+B,UAAA,SAAAzB,MAAA,CAAAwG,0BAAA,CAAA9F,MAAA,KAA6C,aAAAmI,mBAAA,CAAoB;IAsElCnJ,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA8E,gBAAA,CAAA5C,UAAA,KAAqC;;;AD5SlF,OAAM,MAAO4G,uBAAuB;EAgDlCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IA/C1B,KAAAC,YAAY,GAAW,CAAC,CAAC,CAAC;IAC1B,KAAAnD,kBAAkB,GAAyB,CAClD;MAAEJ,WAAW,EAAE,MAAM;MAAEF,SAAS,EAAE;IAAc,CAAE,EAClD;MAAEE,WAAW,EAAE,MAAM;MAAEF,SAAS,EAAE;IAAY,CAAE,EAChD;MAAEE,WAAW,EAAE,IAAI;MAAEF,SAAS,EAAE;IAAY,CAAE,EAC9C;MAAEE,WAAW,EAAE,IAAI;MAAEF,SAAS,EAAE;IAAO,CAAE,EACzC;MAAEE,WAAW,EAAE,IAAI;MAAEF,SAAS,EAAE;IAAS,CAAE,CAC5C,CAAC,CAAC;IACO,KAAA0D,cAAc,GAAG,IAAI7J,YAAY,EAAY;IAC7C,KAAA8J,KAAK,GAAG,IAAI9J,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAAyC,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAsH,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE,CAAC,CAAC;IACxC,KAAAzE,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAA4B,0BAA0B,GAA4B,EAAE;IACxD,KAAAyB,mBAAmB,GAAG,EAAE,CAAC,CAAC;IAE1B;IACA,KAAAjH,aAAa,GAAG,EAAE;IAClB,KAAAP,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAkB,kBAAkB,GAAG;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE,CAAC;MACbE,UAAU,EAAE;KACb;IACD,KAAAwC,kBAAkB,GAAe,EAAE;IAEnC;IACA,KAAAI,gBAAgB,GAAG;MACjBlD,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXG,UAAU,EAAE,CAAC;MACbE,UAAU,EAAE;KACb;IACD,KAAA4E,gBAAgB,GAAqB,EAAE;EAIiB;EAExDwC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACA,MAAMG,mBAAmB,GAAwB;MAC/CC,aAAa,EAAE,IAAI,CAACV,YAAY;MAAE;MAClCW,SAAS,EAAE,CAAC;MAAE;MACdC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAACd,eAAe,CAACe,mCAAmC,CAAC;MACvDC,IAAI,EAAEN;KACP,CAAC,CAACO,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UAEA;UACA,IAAI,CAACjB,SAAS,GAAGe,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C5H,UAAU,EAAE4H,IAAI,CAACC,WAAW;YAC5BrH,YAAY,EAAEoH,IAAI,CAACT,aAAa,IAAI,EAAE;YACtCjF,WAAW,EAAE0F,IAAI,CAACT,aAAa,IAAI,EAAE;YACrCxH,UAAU,EAAEiI,IAAI,CAAClE,SAAS;YAC1B9D,UAAU,EAAEgI,IAAI,CAACE,SAAS;YAC1BC,OAAO,EAAEH,IAAI,CAACI,QAAQ,IAAIC,SAAS;YACnCC,OAAO,EAAEN,IAAI,CAACO,QAAQ,IAAIF;WAC3B,CAAC,CAAC;UAEH;UACA,IAAI,CAACpB,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAACH,eAAe,GAAG,EAAE;UACzB,IAAI,CAAC7C,0BAA0B,GAAG,EAAE;QACtC,CAAC,MAAM;UACL;UACA,IAAI,CAAC4C,SAAS,GAAG,EAAE;UACnB,IAAI,CAACC,eAAe,GAAG,EAAE;UACzB,IAAI,CAACG,uBAAuB,EAAE;QAChC;MACF,CAAC;MACDuB,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAAC3B,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACG,uBAAuB,EAAE;MAChC;KACD,CAAC;EACJ;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACxI,aAAa,CAACgK,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACvK,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAAC2I,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAM6B,OAAO,GAAG,IAAI,CAACjK,aAAa,CAACkK,WAAW,EAAE;MAChD,IAAI,CAACzK,iBAAiB,GAAG,IAAI,CAAC2I,SAAS,CAAC+B,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAACjI,YAAY,CAAC+H,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAACvG,WAAW,IAAIuG,QAAQ,CAACvG,WAAW,CAACqG,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;IACA,IAAI,CAACK,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,IAAI,CAAC3J,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACvB,iBAAiB,CAACC,MAAM;IAClE,IAAI,CAACiB,kBAAkB,CAACO,UAAU,GAAGJ,IAAI,CAACyJ,IAAI,CAAC,IAAI,CAAC5J,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACL,kBAAkB,CAACE,QAAQ,CAAC;IAErH;IACA,IAAI,IAAI,CAACF,kBAAkB,CAACC,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACO,UAAU,EAAE;MAC5E,IAAI,CAACP,kBAAkB,CAACC,WAAW,GAAGE,IAAI,CAAC0J,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7J,kBAAkB,CAACO,UAAU,CAAC;IACvF;IAEA,IAAI,CAACuJ,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAAC/J,kBAAkB,CAACC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,kBAAkB,CAACE,QAAQ;IAC/F,MAAM8J,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC/J,kBAAkB,CAACE,QAAQ;IAC9D,IAAI,CAAC6C,kBAAkB,GAAG,IAAI,CAACjE,iBAAiB,CAACmL,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACA9H,gBAAgBA,CAACgI,IAAY;IAC3B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAClK,kBAAkB,CAACO,UAAU,EAAE;MAC3D,IAAI,CAACP,kBAAkB,CAACC,WAAW,GAAGiK,IAAI;MAC1C,IAAI,CAACJ,wBAAwB,EAAE;IACjC;EACF;EAEA;EACApH,sBAAsBA,CAAA;IACpB,MAAMyH,KAAK,GAAa,EAAE;IAC1B,MAAM5J,UAAU,GAAG,IAAI,CAACP,kBAAkB,CAACO,UAAU;IACrD,MAAMN,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACC,WAAW;IAEvD;IACA,MAAMmK,SAAS,GAAGjK,IAAI,CAAC0J,GAAG,CAAC,CAAC,EAAE5J,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMoK,OAAO,GAAGlK,IAAI,CAACC,GAAG,CAACG,UAAU,EAAEN,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIqK,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACA5K,QAAQA,CAAA;IACN,IAAI,CAACsI,uBAAuB,EAAE;EAChC;EAEA;EACArJ,WAAWA,CAAA;IACT,IAAI,CAACa,aAAa,GAAG,EAAE;IACvB,IAAI,CAACwI,uBAAuB,EAAE;EAChC;EAQA;EACAvG,gBAAgBA,CAACmI,QAAkB;IACjC,IAAI,CAACxG,gBAAgB,GAAGwG,QAAQ;IAChC,IAAI,CAAClC,cAAc,CAACiD,IAAI,CAACf,QAAQ,CAAC;IAElC;IACA,IAAIA,QAAQ,CAACzI,UAAU,EAAE;MACvB,IAAI,CAACyJ,mBAAmB,CAAChB,QAAQ,CAACzI,UAAU,CAAC;IAC/C;IAEA,IAAI,CAAC0J,sBAAsB,EAAE;EAC/B;EAEA;EACAD,mBAAmBA,CAACE,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAEvL,aAAsB;IACnF,MAAMwL,IAAI,GAA8B;MACtCF,UAAU,EAAEA;KACb;IAED;IACA,IAAI,CAACtD,eAAe,CAACyD,yCAAyC,CAAC;MAC7DzC,IAAI,EAAEwC;KACP,CAAC,CAACvC,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UAEjD;UACA,IAAIqC,UAAU,GAAGvC,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7CoC,iBAAiB,EAAEpC,IAAI,CAACoC,iBAAiB,IAAI,CAAC;YAC9CnC,WAAW,EAAED,IAAI,CAACC,WAAW,IAAI8B,UAAU;YAC3ClG,UAAU,EAAEmE,IAAI,CAACnE,UAAU,IAAI,CAAC;YAChCwG,KAAK,EAAEhC,SAAS;YAChBvF,OAAO,EAAEuF,SAAS;YAClBvE,SAAS,EAAE,IAAIwG,IAAI,EAAE,CAACC,WAAW,EAAE;YACnCnC,QAAQ,EAAE,IAAI;YACdoC,SAAS,EAAEnC,SAAS;YACpBoC,UAAU,EAAEpC,SAAS;YACrBqC,SAAS,EAAErC,SAAS;YACpBsC,KAAK,EAAEtC;WACe,EAAC;UAEzB;UACA,IAAI5J,aAAa,IAAIA,aAAa,CAACgK,IAAI,EAAE,EAAE;YACzC0B,UAAU,GAAGA,UAAU,CAACvB,MAAM,CAACgC,MAAM,IACnCA,MAAM,CAACC,YAAY,CAAClC,WAAW,EAAE,CAACG,QAAQ,CAACrK,aAAa,CAACkK,WAAW,EAAE,CAAC,IACtEiC,MAAM,CAAC/H,UAAU,IAAI+H,MAAM,CAAC/H,UAAU,CAAC8F,WAAW,EAAE,CAACG,QAAQ,CAACrK,aAAa,CAACkK,WAAW,EAAE,CAAE,CAC7F;UACH;UAEA;UACA,MAAMQ,UAAU,GAAG,CAACa,SAAS,GAAG,CAAC,IAAI,IAAI,CAACzH,gBAAgB,CAACjD,QAAQ;UACnE,MAAM8J,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC5G,gBAAgB,CAACjD,QAAQ;UAC5D,MAAMwL,YAAY,GAAGX,UAAU,CAACd,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;UAE3D;UACA,IAAI,CAACnF,0BAA0B,GAAG6G,YAAY;UAC9C,IAAI,CAACvI,gBAAgB,CAAC9C,UAAU,GAAG0K,UAAU,CAAChM,MAAM;UACpD,IAAI,CAACoE,gBAAgB,CAAC5C,UAAU,GAAGJ,IAAI,CAACyJ,IAAI,CAACmB,UAAU,CAAChM,MAAM,GAAG,IAAI,CAACoE,gBAAgB,CAACjD,QAAQ,CAAC;UAChG,IAAI,CAACiD,gBAAgB,CAAClD,WAAW,GAAG2K,SAAS;QAC/C,CAAC,MAAM;UACL,IAAI,CAAC/F,0BAA0B,GAAG,EAAE;UACpC,IAAI,CAAC1B,gBAAgB,CAAC9C,UAAU,GAAG,CAAC;UACpC,IAAI,CAAC8C,gBAAgB,CAAC5C,UAAU,GAAG,CAAC;UACpC,IAAI,CAAC4C,gBAAgB,CAAClD,WAAW,GAAG,CAAC;QACvC;MACF,CAAC;MACDmJ,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACvE,0BAA0B,GAAG,EAAE;QACpC,IAAI,CAAC1B,gBAAgB,CAAC9C,UAAU,GAAG,CAAC;QACpC,IAAI,CAAC8C,gBAAgB,CAAC5C,UAAU,GAAG,CAAC;QACpC,IAAI,CAAC4C,gBAAgB,CAAClD,WAAW,GAAG,CAAC;MACvC;KACD,CAAC;EACJ;EAIA;EACA0L,qBAAqBA,CAACrC,OAAe;IACnC,IAAI,CAAChD,mBAAmB,GAAGgD,OAAO;IAClC,IAAI,IAAI,CAACrG,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACjC,UAAU,EAAE;MAC7D,IAAI,CAACyJ,mBAAmB,CAAC,IAAI,CAACxH,gBAAgB,CAACjC,UAAU,EAAE,CAAC,EAAEsI,OAAO,CAAC;IACxE;EACF;EAEA;EACAhG,iBAAiBA,CAAA;IACf,IAAI,CAACgD,mBAAmB,GAAG,EAAE;IAC7B,IAAI,IAAI,CAACrD,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACjC,UAAU,EAAE;MAC7D,IAAI,CAACyJ,mBAAmB,CAAC,IAAI,CAACxH,gBAAgB,CAACjC,UAAU,EAAE,CAAC,CAAC;IAC/D;EACF;EAEA;EACA4K,mBAAmBA,CAAA;IACjB;EAAA;EAGF;EACApF,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACvD,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACjC,UAAU,EAAE;MAC7D,IAAI,CAACyJ,mBAAmB,CAAC,IAAI,CAACxH,gBAAgB,CAACjC,UAAU,EAAE,CAAC,EAAE,IAAI,CAACsF,mBAAmB,CAAC;IACzF;EACF;EAEA;EACAoE,sBAAsBA,CAAA;IACpB;IACA,IAAI,IAAI,CAAC7F,0BAA0B,CAAC9F,MAAM,GAAG,CAAC,EAAE;MAC9C;IACF;IAEA;IACA,MAAM8M,OAAO,GAAG,IAAI,CAACxG,sBAAsB;IAC3C,IAAI,CAAClC,gBAAgB,CAAC9C,UAAU,GAAGwL,OAAO,CAAC9M,MAAM;IACjD,IAAI,CAACoE,gBAAgB,CAAC5C,UAAU,GAAGJ,IAAI,CAACyJ,IAAI,CAAC,IAAI,CAACzG,gBAAgB,CAAC9C,UAAU,GAAG,IAAI,CAAC8C,gBAAgB,CAACjD,QAAQ,CAAC;IAC/G,IAAI,CAACiD,gBAAgB,CAAClD,WAAW,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC6L,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMD,OAAO,GAAG,IAAI,CAACxG,sBAAsB;IAC3C,MAAM0E,UAAU,GAAG,CAAC,IAAI,CAAC5G,gBAAgB,CAAClD,WAAW,GAAG,CAAC,IAAI,IAAI,CAACkD,gBAAgB,CAACjD,QAAQ;IAC3F,MAAM8J,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC5G,gBAAgB,CAACjD,QAAQ;IAC5D,IAAI,CAACiF,gBAAgB,GAAG0G,OAAO,CAAC5B,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC7D;EAEA;EACAtE,cAAcA,CAACwE,IAAY;IACzB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC/G,gBAAgB,CAAC5C,UAAU,EAAE;MACzD;MACA,IAAI,IAAI,CAAC0C,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACjC,UAAU,IAAI,IAAI,CAAC6D,0BAA0B,CAAC9F,MAAM,GAAG,CAAC,EAAE;QAC3G,IAAI,CAAC0L,mBAAmB,CAAC,IAAI,CAACxH,gBAAgB,CAACjC,UAAU,EAAEkJ,IAAI,EAAE,IAAI,CAAC5D,mBAAmB,CAAC;MAC5F,CAAC,MAAM;QACL;QACA,IAAI,CAACnD,gBAAgB,CAAClD,WAAW,GAAGiK,IAAI;QACxC,IAAI,CAAC4B,sBAAsB,EAAE;MAC/B;IACF;EACF;EAEA;EACA/F,oBAAoBA,CAAA;IAClB,MAAMoE,KAAK,GAAa,EAAE;IAC1B,MAAM5J,UAAU,GAAG,IAAI,CAAC4C,gBAAgB,CAAC5C,UAAU;IACnD,MAAMN,WAAW,GAAG,IAAI,CAACkD,gBAAgB,CAAClD,WAAW;IAErD;IACA,MAAMmK,SAAS,GAAGjK,IAAI,CAAC0J,GAAG,CAAC,CAAC,EAAE5J,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMoK,OAAO,GAAGlK,IAAI,CAACC,GAAG,CAACG,UAAU,EAAEN,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIqK,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACA4B,OAAOA,CAAA;IACL,IAAI,CAACvE,KAAK,CAACgD,IAAI,EAAE;EACnB;EAEA;EACAvJ,gBAAgBA,CAAC+K,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACA,MAAMG,kBAAkB,GAAwB;MAC9CtD,WAAW,EAAEmD;KACd;IAED;IACA,IAAI,CAAC3E,eAAe,CAAC+E,kCAAkC,CAAC;MACtD/D,IAAI,EAAE8D;KACP,CAAC,CAAC7D,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACA;UACA,IAAI,CAACb,aAAa,EAAE;UAEpB;UACA,IAAI,IAAI,CAAC3E,gBAAgB,EAAEjC,UAAU,KAAKgL,UAAU,EAAE;YACpD,IAAI,CAAC/I,gBAAgB,GAAG,IAAI;UAC9B;QACF,CAAC,MAAM;UACL;QAAA;MAEJ,CAAC;MACDmG,KAAK,EAAEA,CAAA,KAAK;QACV;MAAA;KAEH,CAAC;EACJ;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA;EACAlD,mBAAmBA,CAAA;IACjB,IAAI,CAACjD,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA0B,gBAAgBA,CAAC6G,MAA6B;IAC5C,OAAO,IAAI,CAACrH,kBAAkB,CAACkI,IAAI,CAACC,MAAM,IACxC,IAAI,CAACxI,aAAa,CAAC0H,MAAM,EAAEc,MAAM,CAACzI,SAAS,CAAC,CAC7C;EACH;EAEAC,aAAaA,CAAC0H,MAA6B,EAAE3H,SAAiB;IAC5D;IACA,IAAI,IAAI,CAACyD,YAAY,KAAK,CAAC,EAAE;MAC3B;MACA,OAAOkE,MAAM,CAAChI,cAAc,GAAGK,SAA+C,CAAC;IACjF,CAAC,MAAM;MACL;MACA;MACA,OAAQ2H,MAAc,CAAC3H,SAAS,CAAC;IACnC;EACF;EAEAF,aAAaA,CAACE,SAAiB;IAC7B,MAAM0I,QAAQ,GAA8B;MAC1C,YAAY,EAAE,aAAa;MAC3B,WAAW,EAAE,gBAAgB;MAC7B,OAAO,EAAE,YAAY;MACrB,SAAS,EAAE,cAAc;MACzB,YAAY,EAAE,aAAa;MAC3B,WAAW,EAAE;KACd;IACD,OAAOA,QAAQ,CAAC1I,SAAS,CAAC,IAAI,eAAe;EAC/C;EAEAG,aAAaA,CAACH,SAAiB;IAC7B,MAAM0I,QAAQ,GAA8B;MAC1C,YAAY,EAAE,aAAa;MAC3B,WAAW,EAAE,gBAAgB;MAC7B,OAAO,EAAE,YAAY;MACrB,SAAS,EAAE;KACZ;IACD,OAAOA,QAAQ,CAAC1I,SAAS,CAAC,IAAI,eAAe;EAC/C;EAEAI,gBAAgBA,CAACuH,MAA0B,EAAE3H,SAAiB;IAC5D,MAAM2I,KAAK,GAAG,IAAI,CAAC1I,aAAa,CAAC0H,MAAM,EAAE3H,SAAS,CAAC;IACnD,IAAI,CAAC2I,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,EAAE;IAEpC,QAAQ3I,SAAS;MACf,KAAK,YAAY;QACf,OAAO,OAAO4I,MAAM,CAACD,KAAK,CAAC,CAACE,cAAc,EAAE,EAAE;MAChD,KAAK,WAAW;QACd,MAAMC,IAAI,GAAGnB,MAAM,CAACD,KAAK,IAAI,EAAE;QAC/B,OAAOoB,IAAI,GAAG,GAAGH,KAAK,IAAIG,IAAI,EAAE,GAAGH,KAAK,CAACI,QAAQ,EAAE;MACrD;QACE,OAAOJ,KAAK,CAACI,QAAQ,EAAE;IAC3B;EACF;EAEA;EACA,IAAIvH,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACpC,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACyE,eAAe,CAAC8B,MAAM,CAACqD,CAAC,IAAIA,CAAC,CAAC7L,UAAU,KAAK,IAAI,CAACiC,gBAAiB,CAACjC,UAAU,CAAC;EAC7F;EAEA;EACAgC,iBAAiBA,CAAC8J,KAAa,EAAErD,QAAkB;IACjD,OAAOA,QAAQ,CAACzI,UAAU,IAAI8L,KAAK;EACrC;;;uCA5eW3F,uBAAuB,EAAApJ,EAAA,CAAAgP,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvB9F,uBAAuB;MAAA+F,SAAA;MAAAC,MAAA;QAAA7F,YAAA;QAAAnD,kBAAA;MAAA;MAAAiJ,OAAA;QAAA7F,cAAA;QAAAC,KAAA;MAAA;MAAA6F,UAAA;MAAAC,QAAA,GAAAvP,EAAA,CAAAwP,oBAAA,EAAAxP,EAAA,CAAAyP,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAlE,QAAA,WAAAmE,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5B9P,EAJR,CAAAC,cAAA,iBAAsC,wBACW,aACkB,aACnC,YACP;UACfD,EAAA,CAAAU,SAAA,WAAoD;UAAAV,EAAA,CAAAY,MAAA,gCACtD;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACLX,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAY,MAAA,yEAAW;UACvCZ,EADuC,CAAAW,YAAA,EAAQ,EACzC;UAEJX,EADF,CAAAC,cAAA,aAA4B,gBACK;UAAAD,EAAA,CAAAY,MAAA,IAAuC;UAG5EZ,EAH4E,CAAAW,YAAA,EAAO,EACzE,EACF,EACS;UACjBX,EAAA,CAAAC,cAAA,wBAA2C;UAqKzCD,EAnKA,CAAA0B,UAAA,KAAAsO,uCAAA,kBAAsE,KAAAC,uCAAA,kBA8BP,KAAAC,uCAAA,oBAqIJ;UA2K7DlQ,EAAA,CAAAW,YAAA,EAAe;UAGXX,EAFJ,CAAAC,cAAA,0BAA+C,eACjB,kBACoB;UAApBD,EAAA,CAAAE,UAAA,mBAAAiQ,0DAAA;YAAA,OAASJ,GAAA,CAAA/B,OAAA,EAAS;UAAA,EAAC;UAC3ChO,EAAA,CAAAU,SAAA,aAAiC;UAAAV,EAAA,CAAAY,MAAA,qBACnC;UAGNZ,EAHM,CAAAW,YAAA,EAAS,EACL,EACS,EACT;;;UA5V6BX,EAAA,CAAAa,SAAA,IAAuC;UAAvCb,EAAA,CAAAc,kBAAA,KAAAiP,GAAA,CAAA9N,kBAAA,CAAAK,UAAA,wBAAuC;UAM7BtC,EAAA,CAAAa,SAAA,GAAuB;UAAvBb,EAAA,CAAA+B,UAAA,UAAAgO,GAAA,CAAA7K,gBAAA,CAAuB;UA8B9BlF,EAAA,CAAAa,SAAA,EAAuB;UAAvBb,EAAA,CAAA+B,UAAA,UAAAgO,GAAA,CAAA7K,gBAAA,CAAuB;UAqIvDlF,EAAA,CAAAa,SAAA,EAAsB;UAAtBb,EAAA,CAAA+B,UAAA,SAAAgO,GAAA,CAAA7K,gBAAA,CAAsB;;;qBDvKpBtF,YAAY,EAAAwQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EAAE3Q,WAAW,EAAA4Q,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAE9Q,YAAY,EAAA+Q,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAElR,cAAc;MAAAmR,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}