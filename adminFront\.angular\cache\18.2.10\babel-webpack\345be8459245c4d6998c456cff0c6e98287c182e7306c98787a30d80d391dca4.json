{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\nvar days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\nvar months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n\n/**\n * @name formatRFC7231\n * @category Common Helpers\n * @summary Format the date according to the RFC 7231 standard (https://tools.ietf.org/html/rfc7231#section-*******).\n *\n * @description\n * Return the formatted date string in RFC 7231 format.\n * The result will always be in UTC timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {String} the formatted date string\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in RFC 7231 format:\n * const result = formatRFC7231(new Date(2019, 8, 18, 19, 0, 52))\n * //=> 'Wed, 18 Sep 2019 19:00:52 GMT'\n */\nexport default function formatRFC7231(dirtyDate) {\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 arguments required, but only \".concat(arguments.length, \" present\"));\n  }\n  var originalDate = toDate(dirtyDate);\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n  var dayName = days[originalDate.getUTCDay()];\n  var dayOfMonth = addLeadingZeros(originalDate.getUTCDate(), 2);\n  var monthName = months[originalDate.getUTCMonth()];\n  var year = originalDate.getUTCFullYear();\n  var hour = addLeadingZeros(originalDate.getUTCHours(), 2);\n  var minute = addLeadingZeros(originalDate.getUTCMinutes(), 2);\n  var second = addLeadingZeros(originalDate.getUTCSeconds(), 2);\n\n  // Result variables.\n  return \"\".concat(dayName, \", \").concat(dayOfMonth, \" \").concat(monthName, \" \").concat(year, \" \").concat(hour, \":\").concat(minute, \":\").concat(second, \" GMT\");\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}