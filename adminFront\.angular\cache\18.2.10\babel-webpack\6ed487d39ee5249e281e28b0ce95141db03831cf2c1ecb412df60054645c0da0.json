{"ast": null, "code": "import differenceInMonths from \"../differenceInMonths/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\n/**\n * @name differenceInQuarters\n * @category Quarter Helpers\n * @summary Get the number of quarters between the given dates.\n *\n * @description\n * Get the number of quarters between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Number} the number of full quarters\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInQuarters(new Date(2014, 6, 2), new Date(2013, 11, 31))\n * //=> 2\n */\nexport default function differenceInQuarters(dateLeft, dateRight, options) {\n  requiredArgs(2, arguments);\n  var diff = differenceInMonths(dateLeft, dateRight) / 3;\n  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}