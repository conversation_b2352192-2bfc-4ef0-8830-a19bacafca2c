/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetQuotationVersionsListResponseBase } from '../../models/get-quotation-versions-list-response-base';

export interface ApiQuotationGetQuotationHistoryPost$Plain$Params {
}

export function apiQuotationGetQuotationHistoryPost$Plain(http: HttpClient, rootUrl: string, params?: ApiQuotationGetQuotationHistoryPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationVersionsListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiQuotationGetQuotationHistoryPost$Plain.PATH, 'post');
  if (params) {
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetQuotationVersionsListResponseBase>;
    })
  );
}

apiQuotationGetQuotationHistoryPost$Plain.PATH = '/api/Quotation/GetQuotationHistory';
