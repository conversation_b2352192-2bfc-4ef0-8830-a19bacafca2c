<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>
  <nb-card-body>
    <h1 class="font-bold text-[#818181]"> 可設定單筆或批次匯入設定各區域及方案對應之建材。
    </h1>
    <div class="d-flex flex-wrap">
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2 w-[22%]">建案</label> <nb-select placeholder="建案"
            [(ngModel)]="selectedBuildCaseId" (ngModelChange)="search()" class="w-full">
            <nb-option *ngFor="let buildCase of listBuildCases" [value]="buildCase.cID">
              {{ buildCase.CBuildCaseName }}
            </nb-option>
          </nb-select>
        </div>
      </div>
      <!-- <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2  w-[22%]">建材類別</label>
          <nb-select placeholder="建材類別" [(ngModel)]="materialOptionsId" class="w-full">
            <nb-option *ngFor="let option of materialOptions" [value]="option.value">
              {{ option.label }}
            </nb-option>
          </nb-select>
        </div>
      </div> -->
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2 w-[22%]">建材選項名稱 </label>
          <input type="text" nbInput placeholder="建材選項名稱" [(ngModel)]="CSelectName" class="w-full" maxlength="50">
        </div>
      </div>
      <!-- 啟用建材代號欄位 -->
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2 w-[22%]">建材代號</label>
          <input type="text" nbInput placeholder="建材代號" [(ngModel)]="CImageCode" class="w-full" maxlength="20">
        </div>
      </div>
      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full">
          <nb-checkbox status="basic" class="flex" style="flex:auto" [(checked)]="filterMapping"
            (change)="changeFilter()">
            只顯示缺少建材圖片或示意圖片的建材
          </nb-checkbox>
          <button *ngIf="isExcelExport" class="btn btn-success mr-2" (click)="exportExelMaterialList()">匯出 <i
              class="fas fa-file-download"></i></button>
          <button *ngIf="isRead" class="btn btn-info mr-2 text-white ml-2" (click)="search()">
            查詢 <i class="fas fa-search"></i></button>
          <button *ngIf="isCreate" class="btn btn-info mx-1 ml-2 mr-2" (click)="addNew(dialog)">單筆新增 <i
              class="fas fa-plus"></i></button>
          <button class="btn btn-info mx-1" *ngIf="isExcelImport" (click)="inputFile.click()"> 批次匯入 </button>
          <input class="hidden" type="file" accept=".xls, .xlsx" #inputFile (change)="detectFileExcel($event)">
          <button class="btn btn-success ml-2" (click)="exportExelMaterialTemplate()">下載範例檔案 <i
              class="fas fa-file-download"></i></button>
        </div>
      </div>
    </div>
    <div class="table-responsive mt-4">
      <table class="table table-striped border " style="min-width: 1200px; background-color:#f3f3f3;">
        <thead>
          <tr style="background-color: #27ae60; color: white;">
            <th scope="col" class="col-1">項次</th>
            <th scope="col" class="col-1">建材代號</th>
            <th scope="col" class="col-2">選項題目</th>
            <th scope="col" class="col-1">選項名稱</th>
            <th scope="col" class="col-3">建材說明</th>
            <th scope="col" class="col-1">已綁定圖片</th>
            <th scope="col" class="col-1">價格</th>
            <th scope="col" class="col-1">狀態</th>
            <th scope="col" class="col-1">操作</th>
          </tr>
        </thead>
        <tbody *ngIf="materialList != null && materialList.length > 0">
          <tr *ngFor="let item of materialList ; let i = index">
            <td>{{ item.CId}}</td>
            <td>{{ item.CImageCode || '待設定' }}</td>
            <td>{{ item.CName }} - {{ item.CPart }} - {{ item.CLocation }}</td>
            <td [style]="!item.CIsMapping ? 'color: red' : ''">{{ item.CSelectName}}</td>
            <td>{{ item.CDescription}}</td>
            <td>
              <div class="d-flex align-items-center">
                <span *ngIf="item.CSelectPictureId && item.CSelectPictureId.length > 0"
                  class="badge badge-success mr-2">{{ item.CSelectPictureId.length }}</span>
                <span *ngIf="!item.CSelectPictureId || item.CSelectPictureId.length === 0"
                  class="badge badge-danger mr-2">0</span>
                <span>{{ (item.CSelectPictureId && item.CSelectPictureId.length > 0) ? '已綁定' : '未綁定' }}</span>
              </div>
            </td>
            <td>{{ item.CPrice}}</td>
            <td>
              <span class="badge" [class]="item.CStatus === 1 ? 'badge-success' : 'badge-secondary'">
                {{ getStatusLabel(item.CStatus || 0) }}
              </span>
            </td>
            <td class="w-32">
              <button class="btn btn-outline-primary btn-sm m-1" (click)="onSelectedMaterial(item, dialog)"
                *ngIf="isRead">編輯</button> <button class="btn btn-outline-info btn-sm m-1"
                (click)="bindImageForMaterial(item, imageBinder)" *ngIf="isRead" [title]="'為 ' + item.CName + ' 綁定圖片'">
                <i class="fas fa-images"></i> 綁定
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">
    <ngx-pagination [(CollectionSize)]="totalRecords" [(PageSize)]="pageSize" [(Page)]="pageIndex"
      (PageChange)="pageChanged($event)">
    </ngx-pagination>
  </nb-card-footer>
</nb-card>

<ng-template #dialog let-dialog let-ref="dialogRef">
  <nb-card class="w-[700px]">
    <nb-card-header>
      建材管理 > 新增建材
    </nb-card-header>
    <nb-card-body class="px-4">
      <h5 class="text-base">請輸入下方內容新增建材。</h5>
      <div class="w-full mt-3">
        <!-- 啟用建材代號欄位 -->
        <div class="flex items-center">
          <label class="required-field w-[150px]">建材代號</label>
          <input type="text" class="w-full !max-w-full p-2 rounded text-[13px]" nbInput maxlength="20"
            [(ngModel)]="selectedMaterial.CImageCode" />
        </div>

        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">名稱</label>
          <input type="text" class="w-full !max-w-full p-2 rounded text-[13px]" nbInput maxlength="30"
            [(ngModel)]="selectedMaterial.CName" />
        </div>

        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">項目</label>
          <input type="text" class="w-full !max-w-full p-2 rounded text-[13px]" nbInput maxlength="30"
            [(ngModel)]="selectedMaterial.CPart" />
        </div>
        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">位置</label>
          <input type="text" class="w-full !max-w-full p-2 rounded text-[13px]" nbInput maxlength="30"
            [(ngModel)]="selectedMaterial.CLocation" />
        </div>

        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">建材選項名稱</label>
          <input type="text" nbInput class="w-full !max-w-full p-2 rounded text-[13px]" maxlength="50"
            [(ngModel)]="selectedMaterial.CSelectName" />
        </div>

        <div class="flex items-center mt-3">
          <label class="w-[150px]">建材說明</label>
          <textarea nbInput [(ngModel)]="selectedMaterial.CDescription" [rows]="4"
            class="resize-none w-full !max-w-full p-2 rounded text-[13px]"></textarea>
        </div>
        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">價格</label>
          <input type="number" nbInput class="w-full !max-w-full p-2 rounded text-[13px]" min="0" step="0.01"
            [(ngModel)]="selectedMaterial.CPrice" placeholder="0" required />
        </div>

        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">狀態</label>
          <nb-select [(ngModel)]="selectedMaterial.CStatus" class="w-full">
            <nb-option *ngFor="let option of statusOptions" [value]="option.value">
              {{ option.label }}
            </nb-option>
          </nb-select>
        </div>

        <!-- 圖片綁定按鈕 -->
        <div class="flex items-center mt-4 pt-3 border-t border-gray-200">
          <label class="w-[150px]">圖片綁定</label>
          <div class="flex gap-2 w-full">
            <button type="button" class="btn btn-outline-info btn-sm" (click)="openImageBinder(imageBinder)"
              [title]="'為建材綁定圖片'">
              <i class="fas fa-images mr-2"></i>選擇圖片
            </button>
            <div class="text-sm text-gray-600 flex items-center" *ngIf="selectedMaterial.CImageCode">
              <i class="fas fa-check-circle text-green-500 mr-2"></i>
              已設定建材代號: {{ selectedMaterial.CImageCode }}
            </div>
          </div>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer class="d-flex justify-content-center">
      <button class="btn btn-danger btn-sm mr-4" (click)="onClose(ref)">關閉</button>
      <button class="btn btn-success btn-sm" (click)="onSubmit(ref)">儲存</button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #imageBinder let-dialog let-ref="dialogRef">
  <nb-card class="w-[90vw] max-w-[1200px] h-[85vh]">
    <nb-card-header>
      圖片綁定 - {{ selectedMaterial.CName ? '為 ' + selectedMaterial.CName + ' 選擇建材圖片' : '選擇建材圖片' }}
    </nb-card-header>
    <nb-card-body class="px-4 d-flex flex-column"
      style="height: calc(100% - 120px); overflow: hidden; padding-bottom: 0;">

      <!-- 自動綁定說明文案 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 flex-shrink-0">
        <div class="flex items-start gap-2">
          <i class="fas fa-info-circle text-blue-500 mt-1"></i>
          <div class="text-sm text-blue-700">
            <div class="font-medium mb-1">建材代號</div>
            <div class="mb-2">
              <span class="font-medium">當前建材代號：</span>
              <span class="bg-white px-2 py-1 rounded border">{{ selectedMaterial.CImageCode || '未設定' }}</span>
            </div>
            <div>選擇圖片後，建材代號將會自動設定為所選圖片的檔名，並建立圖片與建材的綁定關係。</div>
          </div>
        </div>
      </div>

      <!-- 類別選擇和搜尋 -->
      <div class="flex gap-3 mb-4 flex-shrink-0">
        <div class="w-48">
          <label class="text-sm font-medium text-gray-700 mb-2 block">圖片類別</label>
          <nb-select [(ngModel)]="selectedCategory" (selectedChange)="categoryChanged($event)" class="w-full">
            <nb-option *ngFor="let option of categoryOptions" [value]="option.value">
              {{ option.label }}
            </nb-option>
          </nb-select>
        </div>
        <div class="flex-1">
          <label class="text-sm font-medium text-gray-700 mb-2 block">搜尋圖片</label>
          <input type="text" class="w-full search-input" placeholder="搜尋圖片名稱..." [(ngModel)]="imageSearchTerm"
            (input)="filterAvailableImages()" />
        </div>
        <div class="flex flex-col justify-end">
          <button class="btn btn-info btn-image-action" (click)="loadImages()">
            重新載入 <i class="fas fa-refresh"></i>
          </button>
        </div>
      </div>

      <!-- Picklist 雙欄布局 -->
      <div class="flex gap-4 flex-1" style="min-height: 0;">

        <!-- 左側：可選擇的圖片 -->
        <div class="flex-1 d-flex flex-column border rounded p-3" style="min-height: 0;">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0 font-medium">可選擇圖片</h6>
            <div class="text-sm text-gray-600">
              共 {{ imageTotalRecords }} 張圖片
            </div>
          </div>

          <div class="image-preview-container flex-1" style="overflow-y: auto;">
            <div class="grid grid-cols-3 gap-2">
              <div *ngFor="let image of availableImages"
                class="image-grid-item border rounded p-2 cursor-pointer hover:bg-gray-50"
                (click)="moveToSelected(image)">

                <!-- 圖片預覽 -->
                <div class="w-full h-24 bg-gray-100 rounded mb-2 flex items-center justify-center overflow-hidden">
                  <img *ngIf="image.thumbnailUrl" [src]="image.thumbnailUrl" [alt]="image.name"
                    class="image-thumbnail max-w-full max-h-full object-contain" />
                  <div *ngIf="!image.thumbnailUrl" class="text-gray-400 text-center">
                    <i class="fas fa-image text-xl mb-1"></i>
                    <div class="text-xs">無預覽</div>
                  </div>
                </div>

                <!-- 圖片資訊 -->
                <div class="text-xs text-gray-600">
                  <div class="font-medium truncate" [title]="image.name">{{ image.name }}</div>
                </div>

                <!-- 操作按鈕 -->
                <div class="flex justify-between items-center mt-2">
                  <button class="btn btn-outline-info btn-xs" (click)="previewImage(image, imagePreview, $event)">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button class="btn btn-outline-primary btn-xs" (click)="moveToSelected(image, $event)">
                    <i class="fas fa-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- 空狀態 -->
            <div *ngIf="availableImages.length === 0" class="text-center text-gray-500 py-20">
              <i class="fas fa-images text-4xl mb-3"></i>
              <div>找不到可選擇的圖片</div>
            </div>
          </div>

          <!-- 分頁控制 -->
          <div class="mt-3 d-flex justify-content-center">
            <ngx-pagination [(CollectionSize)]="imageTotalRecords" [(PageSize)]="imagePageSize"
              [(Page)]="imageCurrentPage" (PageChange)="imagePageChanged($event)">
            </ngx-pagination>
          </div>
        </div>

        <!-- 中間：操作按鈕 -->
        <div class="d-flex flex-column justify-content-center gap-2" style="width: 80px;">
          <button class="btn btn-outline-primary btn-sm" (click)="moveAllToSelected()"
            [disabled]="availableImages.length === 0" title="全部移至已選">
            <i class="fas fa-angle-double-right"></i>
          </button>
          <button class="btn btn-outline-secondary btn-sm" (click)="moveAllToAvailable()"
            [disabled]="selectedImages.length === 0" title="全部移至可選">
            <i class="fas fa-angle-double-left"></i>
          </button>
          <hr class="my-2">
          <button class="btn btn-outline-danger btn-sm" (click)="clearAllSelection()"
            [disabled]="selectedImages.length === 0" title="清除所有選擇">
            <i class="fas fa-times"></i><br>
            <small>清除</small>
          </button>
        </div>

        <!-- 右側：已選擇的圖片 -->
        <div class="flex-1 d-flex flex-column border rounded p-3" style="min-height: 0;">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0 font-medium">已選擇圖片</h6>
            <div class="text-sm text-gray-600">
              已選取: {{ selectedImages.length }} 張圖片
            </div>
          </div>

          <div class="image-preview-container flex-1" style="overflow-y: auto;">
            <div class="grid grid-cols-3 gap-2">
              <div *ngFor="let image of selectedImages; let i = index"
                class="image-grid-item border rounded p-2 cursor-pointer" [class.border-success]="isImageBound(image)"
                [class.bg-green-50]="isImageBound(image)">

                <!-- 已綁定標示 -->
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <div *ngIf="isImageBound(image)" class="badge badge-success text-xs px-2 py-1" title="此圖片已經綁定到此建材">
                    已綁定
                  </div>
                  <div *ngIf="!isImageBound(image)" class="badge badge-info text-xs px-2 py-1">
                    新選擇
                  </div>
                  <small class="text-gray-500">#{{ i + 1 }}</small>
                </div>

                <!-- 圖片預覽 -->
                <div class="w-full h-24 bg-gray-100 rounded mb-2 flex items-center justify-center overflow-hidden">
                  <img *ngIf="image.thumbnailUrl" [src]="image.thumbnailUrl" [alt]="image.name"
                    class="image-thumbnail max-w-full max-h-full object-contain" />
                  <div *ngIf="!image.thumbnailUrl" class="text-gray-400 text-center">
                    <i class="fas fa-image text-xl mb-1"></i>
                    <div class="text-xs">無預覽</div>
                  </div>
                </div>

                <!-- 圖片資訊 -->
                <div class="text-xs text-gray-600">
                  <div class="font-medium truncate" [title]="image.name">{{ image.name }}</div>
                </div>

                <!-- 操作按鈕 -->
                <div class="flex justify-between items-center mt-2">
                  <button class="btn btn-outline-info btn-xs" (click)="previewImage(image, imagePreview, $event)">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button class="btn btn-outline-danger btn-xs" (click)="moveToAvailable(image, $event)">
                    <i class="fas fa-arrow-left"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- 空狀態 -->
            <div *ngIf="selectedImages.length === 0" class="text-center text-gray-500 py-20">
              <i class="fas fa-image text-4xl mb-3"></i>
              <div>尚未選擇任何圖片</div>
              <div class="text-sm mt-2">從左側選擇圖片</div>
            </div>
          </div>
        </div>
      </div>
    </nb-card-body>

    <nb-card-footer class="d-flex justify-content-between align-items-center">
      <div class="text-sm text-gray-600">
        <span *ngIf="getBoundImagesCount() > 0" class="text-success">
          <i class="fas fa-check-circle"></i> {{ getBoundImagesCount() }} 張已綁定
        </span>
        <span *ngIf="getNewSelectedCount() > 0" class="text-info ml-3">
          <i class="fas fa-plus-circle"></i> {{ getNewSelectedCount() }} 張新選擇
        </span>
      </div>
      <div class="d-flex gap-2">
        <button class="btn btn-danger btn-sm" (click)="onCloseImageBinder(ref)">取消</button>
        <button class="btn btn-success btn-sm" [disabled]="selectedImages.length === 0"
          (click)="onConfirmImageSelection(ref)">
          確定選擇 ({{ selectedImages.length }})
        </button>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>

<!-- 圖片預覽對話框 -->
<ng-template #imagePreview let-dialog let-ref="dialogRef">
  <nb-card class="w-[800px] h-[600px]">
    <nb-card-header class="d-flex justify-content-between align-items-center">
      <span>圖片預覽 - {{ previewingImage?.name }}</span>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-primary btn-sm" [disabled]="currentPreviewIndex <= 0" (click)="previousImage()">
          <i class="fas fa-chevron-left"></i> 上一張
        </button>
        <button class="btn btn-outline-primary btn-sm" [disabled]="currentPreviewIndex >= allImages.length - 1"
          (click)="nextImage()">
          下一張 <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </nb-card-header> <nb-card-body class="p-0 d-flex justify-content-center align-items-center" style="height: 500px;">
      <img *ngIf="previewingImage && previewingImage.fullUrl" [src]="previewingImage.fullUrl"
        [alt]="previewingImage.name" class="max-w-full max-h-full object-contain" />
      <div *ngIf="!previewingImage || !previewingImage.fullUrl" class="text-gray-400 text-center">
        <i class="fas fa-image text-4xl mb-3"></i>
        <div>圖片載入失敗</div>
      </div>
    </nb-card-body>
    <nb-card-footer class="d-flex justify-content-between align-items-center">
      <div class="text-sm text-gray-600">
        {{ currentPreviewIndex + 1 }} / {{ allImages.length }}
      </div>
      <div class="d-flex gap-2"> <button class="btn btn-outline-info btn-sm" (click)="toggleImageSelectionInPreview()">
          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}
        </button>
        <button class="btn btn-danger btn-sm" (click)="ref.close()">關閉</button>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>

<!-- 原有的圖片檢視對話框 -->
<ng-template #dialogImage let-data let-ref="dialogRef">
  <nb-card style="height: 100%; overflow: auto; width: 700px;" class="mr-md-5 ml-md-5">
    <nb-card-header>
      <span>
        檢視
      </span>
    </nb-card-header>
    <nb-card-body style="padding:1rem 2rem">
      <div class="w-full h-auto">
        <img class="fit-size" [src]="currentImageShowing">
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="flex justify-center items-center">
        <button class="btn btn-danger mr-2" (click)="ref.close()">取消</button>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>