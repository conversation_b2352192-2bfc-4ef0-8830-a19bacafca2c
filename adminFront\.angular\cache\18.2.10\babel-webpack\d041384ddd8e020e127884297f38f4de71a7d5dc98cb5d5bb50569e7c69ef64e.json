{"ast": null, "code": "/**\n * @name isExists\n * @category Common Helpers\n * @summary Is the given date exists?\n *\n * @description\n * Checks if the given arguments convert to an existing date.\n *\n * @param {Number} year of the date to check\n * @param {Number} month of the date to check\n * @param {Number} day of the date to check\n * @returns {<PERSON><PERSON>an} the date exists\n * @throws {TypeError} 3 arguments required\n *\n * @example\n * // For the valid date:\n * const result = isExists(2018, 0, 31)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isExists(2018, 1, 31)\n * //=> false\n */\nexport default function isExists(year, month, day) {\n  if (arguments.length < 3) {\n    throw new TypeError('3 argument required, but only ' + arguments.length + ' present');\n  }\n  var date = new Date(year, month, day);\n  return date.getFullYear() === year && date.getMonth() === month && date.getDate() === day;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}