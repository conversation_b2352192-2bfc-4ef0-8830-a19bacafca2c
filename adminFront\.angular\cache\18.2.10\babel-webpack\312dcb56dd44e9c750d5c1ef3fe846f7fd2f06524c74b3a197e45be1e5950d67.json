{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nexport let BaseFilePipe = /*#__PURE__*/(() => {\n  class BaseFilePipe {\n    constructor(sanitizer) {\n      this.sanitizer = sanitizer;\n      this.BASE_FILE = environment.BASE_FILE;\n    }\n    transform(value) {\n      if (!value) return value;\n      if (value.includes(\"/Files\")) {\n        if (value.includes(environment.BASE_WITHOUT_FILEROOT)) {\n          return this.sanitizer.bypassSecurityTrustResourceUrl(value);\n        }\n        return this.sanitizer.bypassSecurityTrustResourceUrl(environment.BASE_WITHOUT_FILEROOT + value);\n      }\n      return this.sanitizer.bypassSecurityTrustResourceUrl(this.BASE_FILE + value);\n    }\n    static {\n      this.ɵfac = function BaseFilePipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BaseFilePipe)(i0.ɵɵdirectiveInject(i1.<PERSON><PERSON><PERSON><PERSON><PERSON>, 16));\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"addBaseFile\",\n        type: BaseFilePipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return BaseFilePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}