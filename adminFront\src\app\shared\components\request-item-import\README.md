# 需求項目匯入元件 (Request Item Import Component)

## 概述
此元件提供需求項目匯入功能，基於 `SpaceTemplateSelectorComponent` 的架構設計，使用 Nebular 的 `nb-dialog` 系統，提供用戶友好的需求項目選擇和匯入介面。

## 主要功能
- **需求項目載入**: 呼叫 `GetRequestListForTemplate` API 載入建案的需求項目
- **項目選擇**: 支援單選/多選需求項目，提供全選功能
- **兩步驟流程**: 選擇項目 → 確認匯入
- **響應式設計**: 支援桌面和行動裝置
- **深色主題支援**: 自動適應 Nebular 主題

## 元件架構

### 核心元件
```
RequestItemImportComponent
├── 使用 nb-card 作為主容器
├── nb-card-header 包含標題和關閉按鈕
├── nb-card-body 包含主要內容和進度指示器
└── nb-card-footer 包含操作按鈕
```

### 相關服務和元件
- `RequestItemImportService` - 對話框管理服務
- `RequestItemImportButtonComponent` - 獨立按鈕元件
- `RequirementService` - API 呼叫服務

## API 整合

### 使用的 API
- `apiRequirementGetRequestListForTemplatePost$Json` - 載入需求項目列表

### API 參數
```typescript
GetListRequirementRequest {
  CBuildCaseID: number;     // 建案ID (必填)
  PageIndex: number;        // 頁面索引
  PageSize: number;         // 頁面大小
  CIsShow: boolean;         // 只載入顯示的項目
}
```

### API 回應
```typescript
GetRequirementListResponseBase {
  Entries: GetRequirement[]; // 需求項目陣列
  StatusCode: EnumStatusCode; // 狀態碼
  TotalItems: number;        // 總項目數
}
```

## 使用方式

### 方法 1: 使用按鈕元件 (推薦)
```html
<app-request-item-import-button
  [buildCaseId]="yourBuildCaseId"
  [text]="'匯入需求項目'"
  [icon]="'fas fa-download'"
  [disabled]="!yourBuildCaseId"
  (itemsImported)="onItemsImported($event)"
  (error)="onImportError($event)">
</app-request-item-import-button>
```

```typescript
import { RequestItemImportButtonComponent } from './request-item-import-button.component';
import { RequestItemImportConfig } from './request-item-import.component';

@Component({
  imports: [RequestItemImportButtonComponent]
})
export class YourComponent {
  onItemsImported(config: RequestItemImportConfig) {
    console.log('匯入的項目:', config.selectedItems);
    console.log('建案ID:', config.buildCaseId);
    console.log('總項目數:', config.totalItems);
  }
  
  onImportError(error: string) {
    console.error('匯入錯誤:', error);
  }
}
```

### 方法 2: 使用服務
```typescript
import { RequestItemImportService } from './request-item-import.service';

constructor(private importService: RequestItemImportService) {}

openImportDialog() {
  this.importService.openImportDialog({ 
    buildCaseId: this.buildCaseId 
  }).subscribe(result => {
    if (result) {
      console.log('匯入結果:', result);
      // 處理匯入的項目
    }
  });
}
```

### 方法 3: 直接使用 NbDialogService
```typescript
import { NbDialogService } from '@nebular/theme';
import { RequestItemImportComponent } from './request-item-import.component';

constructor(private dialogService: NbDialogService) {}

openImportDialog() {
  const dialogRef = this.dialogService.open(RequestItemImportComponent, {
    context: { buildCaseId: this.buildCaseId },
    closeOnBackdropClick: true,
    closeOnEsc: true
  });

  dialogRef.componentRef.instance.itemsImported.subscribe(config => {
    console.log('匯入配置:', config);
    dialogRef.close();
  });
}
```

## 資料結構

### ExtendedRequirementItem
```typescript
interface ExtendedRequirementItem extends GetRequirement {
  selected?: boolean; // 前端選擇狀態
}
```

### RequestItemImportConfig
```typescript
interface RequestItemImportConfig {
  buildCaseId: number;                    // 建案ID
  selectedItems: ExtendedRequirementItem[]; // 選擇的項目
  totalItems: number;                     // 總項目數
}
```

### GetRequirement (來自 API)
```typescript
interface GetRequirement {
  CRequirementID?: number;      // 需求ID
  CRequirement?: string;        // 需求名稱
  CLocation?: string;           // 位置
  CUnit?: string;              // 單位
  CUnitPrice?: number;         // 單價
  CRemark?: string;            // 備註
  CIsShow?: boolean;           // 是否顯示
  CIsSimple?: boolean;         // 是否簡化
  CStatus?: number;            // 狀態
  CBuildCaseID?: number;       // 建案ID
  // ... 其他屬性
}
```

## 元件配置

### 按鈕元件輸入屬性
```typescript
@Input() buildCaseId: number = 0;           // 建案ID (必填)
@Input() text: string = '需求項目匯入';      // 按鈕文字
@Input() icon: string = 'fas fa-download';  // 按鈕圖示 (FontAwesome 類名)
@Input() buttonClass: string = '';          // 按鈕樣式類別
@Input() disabled: boolean = false;         // 是否禁用
@Input() status: string = 'primary';        // 按鈕狀態
@Input() size: string = 'medium';           // 按鈕大小
```

### 按鈕元件輸出事件
```typescript
@Output() itemsImported = new EventEmitter<RequestItemImportConfig>(); // 項目匯入完成
@Output() beforeOpen = new EventEmitter<void>();                       // 對話框開啟前
@Output() error = new EventEmitter<string>();                          // 錯誤事件
```

## 樣式特色

### 進度指示器
- 兩步驟進度顯示
- 動態狀態變化
- 清楚的視覺回饋

### 項目列表
- 卡片式項目展示
- 詳細資訊顯示 (位置、單位、價格、備註)
- 狀態標籤 (顯示/隱藏、簡化)
- 懸停效果

### 響應式設計
- 行動裝置友好
- 適應不同螢幕尺寸
- 流動布局

## 依賴模組

### Nebular 模組
- `NbCardModule` - 卡片容器
- `NbButtonModule` - 按鈕
- `NbIconModule` - 圖示
- `NbCheckboxModule` - 核取方塊
- `NbDialogModule` - 對話框系統

### Angular 模組
- `CommonModule` - 基本指令
- `FormsModule` - 表單綁定

## 注意事項

1. **buildCaseId 必填**: 元件需要有效的建案ID才能載入項目
2. **API 權限**: 需要確保有 `GetRequestListForTemplate` API 的存取權限
3. **模組導入**: 確保在應用模組中導入 `NbDialogModule.forRoot()`
4. **Standalone 元件**: 所有元件都是 standalone，可直接使用
5. **服務注入**: 服務使用 `providedIn: 'root'`，自動在根級別提供

## 錯誤處理

### 常見錯誤情況
- buildCaseId 未設定或無效
- API 回應錯誤 (StatusCode !== 0)
- 網路連線問題
- 權限不足

### 錯誤處理機制
- API 錯誤會顯示空列表
- 載入狀態指示器
- 錯誤事件回調
- 用戶友好的錯誤訊息

## 與 SpaceTemplateSelectorComponent 的差異

### 相似之處
- 基於 nb-dialog 的架構
- 兩步驟選擇流程
- 相似的 UI/UX 設計模式
- Standalone 元件架構

### 主要差異
- **API 呼叫**: 使用 `RequirementService` 替代 `TemplateService`
- **資料結構**: 處理 `GetRequirement` 替代 `TemplateGetListResponse`
- **功能目的**: 需求項目匯入 vs 模板套用
- **詳細步驟**: 不需要載入模板詳情，簡化為確認步驟

## 未來擴展

### 可能的增強功能
- 批次操作 (全選、反選、條件篩選)
- 排序和搜尋功能
- 匯出選擇的項目
- 項目預覽模式
- 重複項目檢測
- 匯入歷史記錄
