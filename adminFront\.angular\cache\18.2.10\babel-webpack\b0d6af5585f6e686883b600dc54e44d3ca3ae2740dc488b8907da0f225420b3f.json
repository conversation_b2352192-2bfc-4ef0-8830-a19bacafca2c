{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDay\n * @category Weekday Helpers\n * @summary Get the day of the week of the given date.\n *\n * @description\n * Get the day of the week of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {0|1|2|3|4|5|6} the day of week, 0 represents Sunday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which day of the week is 29 February 2012?\n * const result = getDay(new Date(2012, 1, 29))\n * //=> 3\n */\nexport default function getDay(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var day = date.getDay();\n  return day;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}