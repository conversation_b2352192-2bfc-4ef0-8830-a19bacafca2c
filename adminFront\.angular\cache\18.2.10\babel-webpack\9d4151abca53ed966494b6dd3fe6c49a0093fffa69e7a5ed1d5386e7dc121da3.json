{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction TemplateViewerComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAddTemplate());\n    });\n    i0.ɵɵelement(1, \"i\", 15);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_7_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_7_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"input\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_7_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchKeyword, $event) || (ctx_r1.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function TemplateViewerComponent_div_7_Template_input_input_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    })(\"keyup.enter\", function TemplateViewerComponent_div_7_Template_input_keyup_enter_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19)(4, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelement(5, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_7_button_6_Template, 2, 0, \"button\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_8_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u66AB\\u7121\\u53EF\\u9078\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_8_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"label\", 45)(2, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_8_div_26_Template_input_ngModelChange_2_listener($event) {\n      const item_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r7.selected, $event) || (item_r7.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 47)(4, \"div\", 48);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"item_\", i_r8, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r7.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r7.CRequirement || item_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CGroupName || item_r7.description);\n  }\n}\nfunction TemplateViewerComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"div\", 27);\n    i0.ɵɵelement(4, \"i\", 28);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"\\u65B0\\u589E\\u6A21\\u677F\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"form\", 29);\n    i0.ɵɵlistener(\"ngSubmit\", function TemplateViewerComponent_div_8_Template_form_ngSubmit_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveNewTemplate());\n    });\n    i0.ɵɵelementStart(8, \"div\", 30)(9, \"div\", 17)(10, \"label\", 31);\n    i0.ɵɵtext(11, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementStart(12, \"span\", 32);\n    i0.ɵɵtext(13, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"input\", 33);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_8_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplate.name, $event) || (ctx_r1.newTemplate.name = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 17)(16, \"label\", 31);\n    i0.ɵɵtext(17, \"\\u6A21\\u677F\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_8_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplate.description, $event) || (ctx_r1.newTemplate.description = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 35)(20, \"label\", 31);\n    i0.ɵɵtext(21, \" \\u9078\\u64C7\\u8981\\u52A0\\u5165\\u6A21\\u677F\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementStart(22, \"span\", 32);\n    i0.ɵɵtext(23, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 36);\n    i0.ɵɵtemplate(25, TemplateViewerComponent_div_8_div_25_Template, 4, 0, \"div\", 37)(26, TemplateViewerComponent_div_8_div_26_Template, 8, 5, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 39)(28, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_8_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelAddTemplate());\n    });\n    i0.ɵɵtext(29, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 41);\n    i0.ɵɵtext(31, \" \\u5132\\u5B58\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplate.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplate.description);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availableData.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.availableData);\n  }\n}\nfunction TemplateViewerComponent_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"small\", 63);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u627E\\u5230 \", ctx_r1.filteredTemplates.length, \" \\u500B\\u7B26\\u5408\\u300C\", ctx_r1.searchKeyword, \"\\u300D\\u7684\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"small\", 63);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A\\u7B2C \", (ctx_r1.templatePagination.currentPage - 1) * ctx_r1.templatePagination.pageSize + 1, \" - \", ctx_r1.Math.min(ctx_r1.templatePagination.currentPage * ctx_r1.templatePagination.pageSize, ctx_r1.templatePagination.totalItems), \" \\u9805\\uFF0C \\u5171 \", ctx_r1.templatePagination.totalItems, \" \\u9805\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_div_9_tr_14_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_tr_14_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const tpl_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(tpl_r10.TemplateID && ctx_r1.onDeleteTemplate(tpl_r10.TemplateID));\n    });\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵtext(2, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_9_tr_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"span\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 11)(8, \"div\", 65)(9, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_tr_14_Template_button_click_9_listener() {\n      const tpl_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r10));\n    });\n    i0.ɵɵelement(10, \"i\", 67);\n    i0.ɵɵtext(11, \" \\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, TemplateViewerComponent_div_9_tr_14_button_12_Template, 3, 0, \"button\", 68);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tpl_r10 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r10.TemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r10.Description || \"\\u7121\\u63CF\\u8FF0\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", tpl_r10.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_div_9_tr_15_small_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"small\", 63);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5617\\u8A66\\u5176\\u4ED6\\u95DC\\u9375\\u5B57\\u6216 \");\n    i0.ɵɵelementStart(2, \"a\", 76);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_tr_15_small_6_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(3, \"\\u6E05\\u9664\\u641C\\u5C0B\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_9_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 71)(2, \"div\", 72);\n    i0.ɵɵelement(3, \"i\", 73);\n    i0.ɵɵelementStart(4, \"p\", 74);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_9_tr_15_small_6_Template, 4, 0, \"small\", 75);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u66AB\\u7121\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_9_div_16_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 80)(1, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_div_16_li_6_Template_button_click_1_listener() {\n      const page_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(page_r15));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r15 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r15 === ctx_r1.templatePagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r15);\n  }\n}\nfunction TemplateViewerComponent_div_9_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"nav\", 78)(2, \"ul\", 79)(3, \"li\", 80)(4, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_div_16_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_9_div_16_li_6_Template, 3, 3, \"li\", 83);\n    i0.ɵɵelementStart(7, \"li\", 80)(8, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_9_div_16_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 84);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getTemplatePageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_9_div_1_Template, 3, 2, \"div\", 51)(2, TemplateViewerComponent_div_9_div_2_Template, 3, 3, \"div\", 52);\n    i0.ɵɵelementStart(3, \"div\", 53)(4, \"table\", 54)(5, \"thead\", 55)(6, \"tr\")(7, \"th\", 56);\n    i0.ɵɵtext(8, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 57);\n    i0.ɵɵtext(10, \"\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 58);\n    i0.ɵɵtext(12, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"tbody\");\n    i0.ɵɵtemplate(14, TemplateViewerComponent_div_9_tr_14_Template, 13, 3, \"tr\", 59)(15, TemplateViewerComponent_div_9_tr_15_Template, 7, 2, \"tr\", 60);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(16, TemplateViewerComponent_div_9_div_16_Template, 10, 7, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalItems > 0);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedTemplates)(\"ngForTrackBy\", ctx_r1.trackByTemplateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.paginatedTemplates || ctx_r1.paginatedTemplates.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalPages > 1);\n  }\n}\nfunction TemplateViewerComponent_div_10_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"strong\");\n    i0.ɵɵtext(2, \"\\u63CF\\u8FF0\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 63);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedTemplate.Description);\n  }\n}\nfunction TemplateViewerComponent_div_10_small_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.detailPagination.currentPage, \" / \", ctx_r1.detailPagination.totalPages, \" \\u9801 \");\n  }\n}\nfunction TemplateViewerComponent_div_10_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_10_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.detailSearchKeyword = \"\";\n      return i0.ɵɵresetView(ctx_r1.searchTemplateDetails(\"\"));\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_10_div_23_div_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 117);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(detail_r18.CCategory);\n  }\n}\nfunction TemplateViewerComponent_div_10_div_23_div_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 118);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", detail_r18.CQuantity, \" \", detail_r18.CUnit, \"\");\n  }\n}\nfunction TemplateViewerComponent_div_10_div_23_div_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 119);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind1(2, 1, detail_r18.CUnitPrice), \"\");\n  }\n}\nfunction TemplateViewerComponent_div_10_div_23_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 120)(1, \"small\");\n    i0.ɵɵelement(2, \"i\", 121);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r18.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_10_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 106)(2, \"div\", 107)(3, \"div\", 108)(4, \"span\", 109);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"strong\", 110);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 111)(9, \"span\", 112);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, TemplateViewerComponent_div_10_div_23_div_1_span_11_Template, 2, 1, \"span\", 113)(12, TemplateViewerComponent_div_10_div_23_div_1_span_12_Template, 2, 2, \"span\", 114)(13, TemplateViewerComponent_div_10_div_23_div_1_span_13_Template, 3, 3, \"span\", 115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, TemplateViewerComponent_div_10_div_23_div_1_div_14_Template, 4, 1, \"div\", 116);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"small\", 63);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r18 = ctx.$implicit;\n    const i_r19 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r19 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(detail_r18.CReleateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", detail_r18.CReleateId, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r18.CCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r18.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r18.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r18.CRemark);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 8, detail_r18.CCreateDt, \"yyyy/MM/dd\"));\n  }\n}\nfunction TemplateViewerComponent_div_10_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_10_div_23_div_1_Template, 18, 11, \"div\", 104);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetailsData);\n  }\n}\nfunction TemplateViewerComponent_div_10_ng_template_24_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"div\", 124)(2, \"span\", 125);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 126)(5, \"div\", 127)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 128);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r20 = ctx.$implicit;\n    const i_r21 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r21 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", detail_r20.FieldName, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", detail_r20.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_10_ng_template_24_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_10_ng_template_24_div_0_div_1_Template, 10, 3, \"div\", 122);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedDetails);\n  }\n}\nfunction TemplateViewerComponent_div_10_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TemplateViewerComponent_div_10_ng_template_24_div_0_Template, 2, 1, \"div\", 100);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const noDetails_r22 = i0.ɵɵreference(28);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetails.length > 0)(\"ngIfElse\", noDetails_r22);\n  }\n}\nfunction TemplateViewerComponent_div_10_div_26_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 80)(1, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_10_div_26_li_6_Template_button_click_1_listener() {\n      const page_r25 = i0.ɵɵrestoreView(_r24).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(page_r25));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r25 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r25 === ctx_r1.detailPagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r25);\n  }\n}\nfunction TemplateViewerComponent_div_10_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 129)(1, \"nav\", 130)(2, \"ul\", 79)(3, \"li\", 80)(4, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_10_div_26_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_10_div_26_li_6_Template, 3, 3, \"li\", 83);\n    i0.ɵɵelementStart(7, \"li\", 80)(8, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_10_div_26_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 84);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getDetailPageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_10_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵelement(1, \"i\", 132);\n    i0.ɵɵelementStart(2, \"p\", 74);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"h6\", 4);\n    i0.ɵɵelement(3, \"i\", 88);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_10_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵelement(6, \"i\", 23);\n    i0.ɵɵtext(7, \" \\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 90);\n    i0.ɵɵtemplate(9, TemplateViewerComponent_div_10_div_9_Template, 5, 1, \"div\", 91);\n    i0.ɵɵelementStart(10, \"div\", 92)(11, \"div\", 93)(12, \"h6\", 4);\n    i0.ɵɵelement(13, \"i\", 94);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, TemplateViewerComponent_div_10_small_15_Template, 2, 2, \"small\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 95)(17, \"div\", 96)(18, \"input\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_10_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.detailSearchKeyword, $event) || (ctx_r1.detailSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateViewerComponent_div_10_Template_input_keyup_enter_18_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.searchTemplateDetails(ctx_r1.detailSearchKeyword));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 19)(20, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_10_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.searchTemplateDetails(ctx_r1.detailSearchKeyword));\n    });\n    i0.ɵɵelement(21, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, TemplateViewerComponent_div_10_button_22_Template, 2, 0, \"button\", 99);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(23, TemplateViewerComponent_div_10_div_23_Template, 2, 1, \"div\", 100)(24, TemplateViewerComponent_div_10_ng_template_24_Template, 1, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(26, TemplateViewerComponent_div_10_div_26_Template, 10, 7, \"div\", 101)(27, TemplateViewerComponent_div_10_ng_template_27_Template, 4, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const checkOldDetails_r26 = i0.ɵɵreference(25);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \\u6A21\\u677F\\u7D30\\u7BC0\\uFF1A\", ctx_r1.selectedTemplate.TemplateName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTemplate.Description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6A21\\u677F\\u5167\\u5BB9 (\", ctx_r1.currentTemplateDetailsData.length > 0 ? ctx_r1.detailPagination.totalItems : ctx_r1.currentTemplateDetails.length, \" \\u9805) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetailsData.length > 0)(\"ngIfElse\", checkOldDetails_r26);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.availableData = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)\n    this.templateType = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\n    this.showOnlyAddForm = false; // 是否只顯示新增模板表單\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 公開Math對象供模板使用\n    this.Math = Math;\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = []; // 保留用於向後相容\n    this.selectedTemplate = null;\n    // 新的詳情資料管理\n    this.currentTemplateDetailsData = [];\n    this.detailSearchKeyword = '';\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 分頁功能 - 模板列表\n    this.templatePagination = {\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedTemplates = [];\n    // 分頁功能 - 模板詳情\n    this.detailPagination = {\n      currentPage: 1,\n      pageSize: 5,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedDetails = [];\n    // 新增模板表單\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  ngOnInit() {\n    // 只有在非純新增模式下才載入模板列表\n    if (!this.showOnlyAddForm) {\n      this.loadTemplates();\n      this.updateFilteredTemplates();\n    }\n    // 如果只顯示新增表單，自動打開新增模板表單\n    if (this.showOnlyAddForm) {\n      this.onAddTemplate();\n    }\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 載入模板列表 - 使用真實的 API 調用\n  loadTemplates() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      PageIndex: 1,\n      // 暫時載入第一頁\n      PageSize: 100,\n      // 暫時載入較多資料\n      CTemplateName: null // 不篩選名稱\n    };\n    console.log('GetTemplateList API 調用:', getTemplateListArgs);\n    // 調用 GetTemplateList API\n    this.templateService.apiTemplateGetTemplateListPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // API 調用成功\n          console.log('模板列表載入成功:', response);\n          // 轉換 API 回應為內部格式\n          this.templates = response.Entries.map(item => ({\n            TemplateID: item.CTemplateId,\n            TemplateName: item.CTemplateName || '',\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\n          }));\n          // 更新過濾列表\n          this.updateFilteredTemplates();\n          // 清空詳情資料，改為按需載入\n          this.templateDetails = [];\n          this.currentTemplateDetailsData = [];\n        } else {\n          // API 返回錯誤\n          console.error('模板列表載入失敗:', response.Message);\n          this.templates = [];\n          this.templateDetails = [];\n          this.updateFilteredTemplates();\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        console.error('GetTemplateList API 調用失敗:', error);\n        this.templates = [];\n        this.templateDetails = [];\n        this.updateFilteredTemplates();\n      }\n    });\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n    this.updateTemplatePagination();\n  }\n  // 更新模板分頁\n  updateTemplatePagination() {\n    this.templatePagination.totalItems = this.filteredTemplates.length;\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\n    // 確保當前頁面不超過總頁數\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\n    }\n    this.updatePaginatedTemplates();\n  }\n  // 更新分頁後的模板列表\n  updatePaginatedTemplates() {\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\n    const endIndex = startIndex + this.templatePagination.pageSize;\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\n  }\n  // 模板分頁導航\n  goToTemplatePage(page) {\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = page;\n      this.updatePaginatedTemplates();\n    }\n  }\n  // 獲取模板分頁頁碼數組\n  getTemplatePageNumbers() {\n    const pages = [];\n    const totalPages = this.templatePagination.totalPages;\n    const currentPage = this.templatePagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 顯示新增模板表單\n  onAddTemplate() {\n    this.showAddForm = true;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n    // 如果不是只顯示新增表單模式，才重置選擇狀態\n    // 在只顯示新增表單模式下，保持父元件傳入的選中狀態\n    if (!this.showOnlyAddForm) {\n      this.availableData.forEach(item => item.selected = false);\n    }\n  }\n  // 取消新增模板\n  cancelAddTemplate() {\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  // 儲存新模板\n  saveNewTemplate() {\n    if (!this.newTemplate.name.trim()) {\n      alert('請輸入模板名稱');\n      return;\n    }\n    const selectedItems = this.availableData.filter(item => item.selected);\n    if (selectedItems.length === 0) {\n      alert('請至少選擇一個項目');\n      return;\n    }\n    // TODO: 替換為實際的API調用\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\n  }\n  // 創建模板 - 使用真實的 API 調用\n  createTemplate(name, description, selectedItems) {\n    // 準備 API 請求資料\n    const saveTemplateArgs = {\n      CTemplateId: null,\n      // 新增時為 null\n      CTemplateName: name.trim(),\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      CStatus: 1,\n      // 啟用狀態\n      Details: selectedItems.map(item => ({\n        CTemplateDetailId: null,\n        // 新增時為 null\n        CReleateId: this.getRefId(item),\n        // 關聯主檔ID\n        CReleateName: this.getFieldName(item) // 關聯名稱\n      }))\n    };\n    console.log('SaveTemplate API 調用:', saveTemplateArgs);\n    // 調用 SaveTemplate API\n    this.templateService.apiTemplateSaveTemplatePost$Json({\n      body: saveTemplateArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          console.log('模板創建成功:', response);\n          // 只有在非純新增模式下才重新載入模板列表\n          if (!this.showOnlyAddForm) {\n            this.loadTemplates();\n          }\n          // 關閉表單\n          this.showAddForm = false;\n          alert(`模板 \"${name}\" 已成功創建！包含 ${selectedItems.length} 個項目`);\n          // 如果是純新增模式，發出關閉事件通知父組件關閉 dialog\n          if (this.showOnlyAddForm) {\n            this.close.emit();\n          }\n        } else {\n          // API 返回錯誤\n          console.error('模板創建失敗:', response.Message);\n          alert(`模板創建失敗：${response.Message || '未知錯誤'}`);\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        console.error('SaveTemplate API 調用失敗:', error);\n        alert('模板創建失敗，請檢查網路連線或聯繫系統管理員');\n      }\n    });\n  }\n  // 獲取關聯主檔ID的輔助方法\n  getRefId(item) {\n    return item.CRequirementID || item.ID || item.id || 0;\n  }\n  // 獲取欄位名稱的輔助方法\n  getFieldName(_item) {\n    // 根據模板類型決定欄位名稱\n    switch (this.templateType) {\n      case 1:\n        // 客變需求\n        return 'CRequirement';\n      default:\n        return 'name';\n    }\n  }\n  // 獲取欄位值的輔助方法\n  getFieldValue(item) {\n    return item.CRequirement || item.name || item.title || '';\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n    // 按需載入模板詳情\n    if (template.TemplateID) {\n      this.loadTemplateDetails(template.TemplateID);\n    }\n    this.updateDetailPagination();\n  }\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\n  loadTemplateDetails(templateId, pageIndex = 1, searchKeyword) {\n    const args = {\n      templateId: templateId\n    };\n    console.log('GetTemplateDetailById API 調用:', args);\n    // 調用真實的 GetTemplateDetailById API\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          console.log('模板詳情載入成功:', response);\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\n          let allDetails = response.Entries.map(item => ({\n            CTemplateDetailId: item.CTemplateDetailId || 0,\n            CTemplateId: item.CTemplateId || templateId,\n            CReleateId: item.CReleateId || 0,\n            CReleateName: item.CReleateName || '',\n            CSort: undefined,\n            CRemark: undefined,\n            CCreateDt: new Date().toISOString(),\n            CCreator: '系統',\n            CCategory: undefined,\n            CUnitPrice: undefined,\n            CQuantity: undefined,\n            CUnit: undefined\n          }));\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\n          if (searchKeyword && searchKeyword.trim()) {\n            allDetails = allDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()));\n          }\n          // 前端分頁處理 (因為 API 不支援分頁參數)\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n          const endIndex = startIndex + this.detailPagination.pageSize;\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\n          // 更新詳情資料和分頁資訊\n          this.currentTemplateDetailsData = pagedDetails;\n          this.detailPagination.totalItems = allDetails.length;\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\n          this.detailPagination.currentPage = pageIndex;\n        } else {\n          console.error('模板詳情載入失敗:', response.Message);\n          this.currentTemplateDetailsData = [];\n          this.detailPagination.totalItems = 0;\n          this.detailPagination.totalPages = 0;\n          this.detailPagination.currentPage = 1;\n        }\n      },\n      error: error => {\n        console.error('GetTemplateDetailById API 調用失敗:', error);\n        this.currentTemplateDetailsData = [];\n        this.detailPagination.totalItems = 0;\n        this.detailPagination.totalPages = 0;\n        this.detailPagination.currentPage = 1;\n        // 如果 API 失敗，回退到模擬資料\n        console.log('回退到模擬資料');\n        this.loadTemplateDetailsMock(templateId, pageIndex, searchKeyword);\n      }\n    });\n  }\n  // 模擬載入模板詳情 (暫時使用，等待後端 API)\n  loadTemplateDetailsMock(templateId, pageIndex = 1, searchKeyword) {\n    // 模擬 API 延遲\n    setTimeout(() => {\n      // 生成模擬詳情資料\n      const mockDetails = [];\n      const itemsPerTemplate = 8; // 每個模板8個項目\n      for (let i = 1; i <= itemsPerTemplate; i++) {\n        const detail = {\n          CTemplateDetailId: templateId * 100 + i,\n          CTemplateId: templateId,\n          CReleateId: templateId * 1000 + i,\n          CReleateName: `工程項目${String.fromCharCode(64 + templateId % 26 + 1)}-${i}`,\n          CSort: i,\n          CRemark: i % 3 === 0 ? `備註說明 ${i}` : undefined,\n          CCreateDt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\n          CCreator: '系統管理員',\n          CCategory: i % 2 === 0 ? '基礎工程' : '進階工程',\n          CUnitPrice: Math.floor(Math.random() * 10000) + 1000,\n          CQuantity: Math.floor(Math.random() * 10) + 1,\n          CUnit: i % 3 === 0 ? '式' : i % 3 === 1 ? '個' : 'm²'\n        };\n        mockDetails.push(detail);\n      }\n      // 搜尋篩選\n      let filteredDetails = mockDetails;\n      if (searchKeyword && searchKeyword.trim()) {\n        filteredDetails = mockDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CRemark && detail.CRemark.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CCategory && detail.CCategory.toLowerCase().includes(searchKeyword.toLowerCase()));\n      }\n      // 分頁處理\n      const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n      const endIndex = startIndex + this.detailPagination.pageSize;\n      const pagedDetails = filteredDetails.slice(startIndex, endIndex);\n      // 更新資料\n      this.currentTemplateDetailsData = pagedDetails;\n      this.detailPagination.totalItems = filteredDetails.length;\n      this.detailPagination.totalPages = Math.ceil(filteredDetails.length / this.detailPagination.pageSize);\n      this.detailPagination.currentPage = pageIndex;\n      console.log('模擬詳情載入完成:', {\n        templateId,\n        totalItems: filteredDetails.length,\n        currentPage: pageIndex,\n        details: pagedDetails\n      });\n    }, 300);\n  }\n  // 搜尋模板詳情\n  searchTemplateDetails(keyword) {\n    this.detailSearchKeyword = keyword;\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\n    }\n  }\n  // 更新詳情分頁 (保留向後相容)\n  updateDetailPagination() {\n    // 如果使用新的詳情資料，直接返回\n    if (this.currentTemplateDetailsData.length > 0) {\n      return;\n    }\n    // 向後相容：使用舊的詳情資料\n    const details = this.currentTemplateDetails;\n    this.detailPagination.totalItems = details.length;\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\n    this.detailPagination.currentPage = 1; // 重置到第一頁\n    this.updatePaginatedDetails();\n  }\n  // 更新分頁後的詳情列表\n  updatePaginatedDetails() {\n    const details = this.currentTemplateDetails;\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\n    const endIndex = startIndex + this.detailPagination.pageSize;\n    this.paginatedDetails = details.slice(startIndex, endIndex);\n  }\n  // 詳情分頁導航\n  goToDetailPage(page) {\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\n      // 如果使用新的詳情資料，重新載入\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\n      } else {\n        // 向後相容：使用舊的分頁邏輯\n        this.detailPagination.currentPage = page;\n        this.updatePaginatedDetails();\n      }\n    }\n  }\n  // 獲取詳情分頁頁碼數組\n  getDetailPageNumbers() {\n    const pages = [];\n    const totalPages = this.detailPagination.totalPages;\n    const currentPage = this.detailPagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // 刪除模板 - 使用真實的 API 調用\n  deleteTemplateById(templateID) {\n    // 準備 API 請求參數\n    const deleteTemplateArgs = {\n      CTemplateId: templateID\n    };\n    console.log('DeleteTemplate API 調用:', deleteTemplateArgs);\n    // 調用 DeleteTemplate API\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\n      body: deleteTemplateArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          console.log('模板刪除成功:', response);\n          // 重新載入模板列表\n          this.loadTemplates();\n          // 如果當前查看的模板被刪除，關閉詳情\n          if (this.selectedTemplate?.TemplateID === templateID) {\n            this.selectedTemplate = null;\n          }\n          alert('模板已刪除');\n        } else {\n          // API 返回錯誤\n          console.error('模板刪除失敗:', response.Message);\n          alert(`模板刪除失敗：${response.Message || '未知錯誤'}`);\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        console.error('DeleteTemplate API 調用失敗:', error);\n        alert('模板刪除失敗，請檢查網路連線或聯繫系統管理員');\n      }\n    });\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/Template/GetTemplateList\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/Template/SaveTemplate\n   *    請求體: {\n   *      CTemplateName: string,\n   *      CTemplateType: number,  // 1=客變需求\n   *      CStatus: number,\n   *      Details: [{\n   *        CReleateId: number,        // 關聯主檔ID\n   *        CReleateName: string       // 關聯名稱\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    POST /api/Template/DeleteTemplate\n   *    請求體: { CTemplateId: number }\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        availableData: \"availableData\",\n        templateType: \"templateType\",\n        showOnlyAddForm: \"showOnlyAddForm\"\n      },\n      outputs: {\n        selectTemplate: \"selectTemplate\",\n        close: \"close\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 16,\n      vars: 6,\n      consts: [[\"checkOldDetails\", \"\"], [\"noDetails\", \"\"], [2, \"width\", \"90vw\", \"max-width\", \"1200px\", \"height\", \"80vh\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [\"class\", \"btn btn-success btn-sm\", 3, \"click\", 4, \"ngIf\"], [2, \"overflow\", \"auto\"], [\"class\", \"search-container mb-3\", 4, \"ngIf\"], [\"class\", \"add-template-form mb-4\", 4, \"ngIf\"], [\"class\", \"template-list\", 4, \"ngIf\"], [\"class\", \"template-detail-modal\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"search-container\", \"mb-3\"], [1, \"input-group\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u6216\\u63CF\\u8FF0...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"class\", \"btn btn-outline-secondary\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [1, \"fas\", \"fa-times\"], [1, \"add-template-form\", \"mb-4\"], [1, \"form-container\"], [1, \"form-header\"], [1, \"form-title\"], [1, \"fas\", \"fa-plus\"], [1, \"form-content\", 3, \"ngSubmit\"], [1, \"input-row\"], [1, \"input-label\"], [1, \"required\"], [\"type\", \"text\", \"name\", \"templateName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"required\", \"\", \"maxlength\", \"50\", 1, \"input-field\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"templateDescription\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u63CF\\u8FF0\\uFF08\\u53EF\\u9078\\uFF09\", \"maxlength\", \"100\", 1, \"input-field\", 3, \"ngModelChange\", \"ngModel\"], [1, \"input-group\", \"full-width\"], [1, \"items-selector\"], [\"class\", \"empty-items\", 4, \"ngIf\"], [\"class\", \"item-option\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-actions\"], [\"type\", \"button\", 1, \"btn-cancel\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn-save\"], [1, \"empty-items\"], [1, \"fas\", \"fa-info-circle\"], [1, \"item-option\"], [1, \"item-label\"], [\"type\", \"checkbox\", 1, \"item-checkbox\", 3, \"ngModelChange\", \"ngModel\", \"name\"], [1, \"item-content\"], [1, \"item-title\"], [1, \"item-desc\"], [1, \"template-list\"], [\"class\", \"search-results-info mb-2\", 4, \"ngIf\"], [\"class\", \"pagination-info mb-2\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"thead-light\"], [\"width\", \"30%\"], [\"width\", \"50%\"], [\"width\", \"20%\", 1, \"text-center\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [4, \"ngIf\"], [\"class\", \"pagination-container mt-3\", 4, \"ngIf\"], [1, \"search-results-info\", \"mb-2\"], [1, \"text-muted\"], [1, \"pagination-info\", \"mb-2\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"title\", \"\\u67E5\\u770B\\u8A73\\u60C5\", 1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-danger\", \"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"3\", 1, \"text-center\", \"py-4\"], [1, \"empty-state\"], [1, \"fas\", \"fa-folder-open\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"href\", \"javascript:void(0)\", 3, \"click\"], [1, \"pagination-container\", \"mt-3\"], [\"aria-label\", \"\\u6A21\\u677F\\u5217\\u8868\\u5206\\u9801\"], [1, \"pagination\", \"pagination-sm\", \"justify-content-center\", \"mb-0\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"page-link\", 3, \"click\"], [1, \"template-detail-modal\"], [1, \"template-detail-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"template-detail-content\"], [\"class\", \"template-description mb-3\", 4, \"ngIf\"], [1, \"template-items\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"fas\", \"fa-list\", \"mr-1\"], [1, \"template-detail-search\", \"mb-3\"], [1, \"input-group\", \"input-group-sm\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u8A73\\u60C5\\u9805\\u76EE...\", 1, \"form-control\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [\"class\", \"btn btn-outline-secondary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"detail-list\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"detail-pagination mt-3\", 4, \"ngIf\"], [1, \"template-description\", \"mb-3\"], [1, \"detail-list\"], [\"class\", \"detail-item py-2 border-bottom\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\", \"py-2\", \"border-bottom\"], [1, \"detail-header\", \"d-flex\", \"justify-content-between\", \"align-items-start\"], [1, \"detail-main\", \"flex-grow-1\"], [1, \"d-flex\", \"align-items-center\", \"mb-1\"], [1, \"badge\", \"badge-primary\", \"mr-2\"], [1, \"detail-name\"], [1, \"detail-info\"], [1, \"badge\", \"badge-info\", \"mr-2\"], [\"class\", \"badge badge-secondary mr-2\", 4, \"ngIf\"], [\"class\", \"badge badge-success mr-2\", 4, \"ngIf\"], [\"class\", \"badge badge-warning mr-2\", 4, \"ngIf\"], [\"class\", \"detail-remark text-muted mt-1\", 4, \"ngIf\"], [1, \"badge\", \"badge-secondary\", \"mr-2\"], [1, \"badge\", \"badge-success\", \"mr-2\"], [1, \"badge\", \"badge-warning\", \"mr-2\"], [1, \"detail-remark\", \"text-muted\", \"mt-1\"], [1, \"fas\", \"fa-comment\", \"mr-1\"], [\"class\", \"detail-item d-flex align-items-center py-2 border-bottom\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\", \"d-flex\", \"align-items-center\", \"py-2\", \"border-bottom\"], [1, \"detail-index\"], [1, \"badge\", \"badge-light\"], [1, \"detail-content\", \"flex-grow-1\", \"ml-2\"], [1, \"detail-field\"], [1, \"detail-value\", \"text-muted\"], [1, \"detail-pagination\", \"mt-3\"], [\"aria-label\", \"\\u6A21\\u677F\\u8A73\\u60C5\\u5206\\u9801\"], [1, \"text-center\", \"py-3\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\")(2, \"div\", 3)(3, \"h5\", 4);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, TemplateViewerComponent_button_5_Template, 3, 0, \"button\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 6);\n          i0.ɵɵtemplate(7, TemplateViewerComponent_div_7_Template, 7, 2, \"div\", 7)(8, TemplateViewerComponent_div_8_Template, 32, 4, \"div\", 8)(9, TemplateViewerComponent_div_9_Template, 17, 6, \"div\", 9)(10, TemplateViewerComponent_div_10_Template, 29, 9, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-card-footer\")(12, \"div\", 11)(13, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_13_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelement(14, \"i\", 13);\n          i0.ɵɵtext(15, \"\\u95DC\\u9589 \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.showOnlyAddForm ? \"\\u65B0\\u589E\\u6A21\\u677F\" : \"\\u6A21\\u677F\\u7BA1\\u7406\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showOnlyAddForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showOnlyAddForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedTemplate && !ctx.showOnlyAddForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate && !ctx.showOnlyAddForm);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, i2.DatePipe, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, NbCardModule, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardFooterComponent, i4.NbCardHeaderComponent, NbButtonModule],\n      styles: [\"nb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  border: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: none;\\n  padding: 0.75rem 1rem;\\n  font-size: 0.95rem;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-style: italic;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  padding: 0.75rem 1rem;\\n  transition: all 0.2s ease;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n.search-results-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #007bff;\\n  padding-left: 0.75rem;\\n  background: #f8f9ff;\\n  border-radius: 4px;\\n}\\n\\n.pagination-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #28a745;\\n  padding-left: 0.75rem;\\n  background: #f8fff8;\\n  border-radius: 4px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  font-size: 0.9rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9ff;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  border-color: #f0f0f0;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto 1rem;\\n  opacity: 0.5;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: 0.5rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.template-detail-modal[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  background: #fff;\\n  border: 2px solid #e9ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  background: #f8f9ff;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  border-left: 4px solid #007bff;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9ff;\\n  border-radius: 6px;\\n  margin: 0 -0.5rem;\\n  padding-left: 0.5rem !important;\\n  padding-right: 0.5rem !important;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0.375rem 0.5rem;\\n  border-radius: 50%;\\n  min-width: 2rem;\\n  text-align: center;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n  word-break: break-word;\\n}\\n\\n.add-template-form[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e8ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e8ecef;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 0.9rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  margin-left: 0.125rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  background: #ffffff;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .items-selector[_ngcontent-%COMP%] {\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  background: #f9fafb;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  padding: 2rem;\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n  padding: 0.875rem 1rem;\\n  cursor: pointer;\\n  margin: 0;\\n  width: 100%;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-top: 0.125rem;\\n  width: 1rem;\\n  height: 1rem;\\n  accent-color: #28a745;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.25rem;\\n  line-height: 1.4;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-desc[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 0.75rem;\\n  margin-top: 1.5rem;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%], \\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  padding: 0.625rem 1.25rem;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border: none;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  min-width: 80px;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%] {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover {\\n  background: #e5e7eb;\\n  transform: translateY(-1px);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.25);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n  color: #6c757d;\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  border-radius: 4px;\\n  margin: 0 0.125rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  color: #495057;\\n  transform: translateY(-1px);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  background-color: #fff;\\n  border-color: #dee2e6;\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n}\\n\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.8rem;\\n  border-radius: 3px;\\n  margin: 0 0.0625rem;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  border-color: #28a745;\\n  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  transition: background-color 0.2s;\\n}\\n.detail-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-name[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.95rem;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-remark[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n  border-left: 3px solid #dee2e6;\\n}\\n\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-radius: 0.25rem 0 0 0.25rem;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n  border-radius: 0 0.25rem 0.25rem 0;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_button_5_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onAddTemplate", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "TemplateViewerComponent_div_7_button_6_Template_button_click_0_listener", "_r4", "clearSearch", "ɵɵtwoWayListener", "TemplateViewerComponent_div_7_Template_input_ngModelChange_2_listener", "$event", "_r3", "ɵɵtwoWayBindingSet", "searchKeyword", "TemplateViewerComponent_div_7_Template_input_input_2_listener", "onSearch", "TemplateViewerComponent_div_7_Template_input_keyup_enter_2_listener", "TemplateViewerComponent_div_7_Template_button_click_4_listener", "ɵɵtemplate", "TemplateViewerComponent_div_7_button_6_Template", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵproperty", "TemplateViewerComponent_div_8_div_26_Template_input_ngModelChange_2_listener", "item_r7", "_r6", "$implicit", "selected", "ɵɵpropertyInterpolate1", "i_r8", "ɵɵtextInterpolate", "CRequirement", "name", "CGroupName", "description", "TemplateViewerComponent_div_8_Template_form_ngSubmit_7_listener", "_r5", "saveNewTemplate", "TemplateViewerComponent_div_8_Template_input_ngModelChange_14_listener", "newTemplate", "TemplateViewerComponent_div_8_Template_input_ngModelChange_18_listener", "TemplateViewerComponent_div_8_div_25_Template", "TemplateViewerComponent_div_8_div_26_Template", "TemplateViewerComponent_div_8_Template_button_click_28_listener", "cancelAddTemplate", "availableData", "length", "ɵɵtextInterpolate2", "filteredTemplates", "ɵɵtextInterpolate3", "templatePagination", "currentPage", "pageSize", "Math", "min", "totalItems", "TemplateViewerComponent_div_9_tr_14_button_12_Template_button_click_0_listener", "_r11", "tpl_r10", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_div_9_tr_14_Template_button_click_9_listener", "_r9", "onSelectTemplate", "TemplateViewerComponent_div_9_tr_14_button_12_Template", "TemplateName", "Description", "TemplateViewerComponent_div_9_tr_15_small_6_Template_a_click_2_listener", "_r12", "TemplateViewerComponent_div_9_tr_15_small_6_Template", "ɵɵtextInterpolate1", "TemplateViewerComponent_div_9_div_16_li_6_Template_button_click_1_listener", "page_r15", "_r14", "goToTemplatePage", "ɵɵclassProp", "TemplateViewerComponent_div_9_div_16_Template_button_click_4_listener", "_r13", "TemplateViewerComponent_div_9_div_16_li_6_Template", "TemplateViewerComponent_div_9_div_16_Template_button_click_8_listener", "getTemplatePageNumbers", "totalPages", "TemplateViewerComponent_div_9_div_1_Template", "TemplateViewerComponent_div_9_div_2_Template", "TemplateViewerComponent_div_9_tr_14_Template", "TemplateViewerComponent_div_9_tr_15_Template", "TemplateViewerComponent_div_9_div_16_Template", "paginatedTemplates", "trackByTemplateId", "selectedTemplate", "detailPagination", "TemplateViewerComponent_div_10_button_22_Template_button_click_0_listener", "_r17", "detailSearchKeyword", "searchTemplateDetails", "detail_r18", "CCategory", "CQuantity", "CUnit", "ɵɵpipeBind1", "CUnitPrice", "CRemark", "TemplateViewerComponent_div_10_div_23_div_1_span_11_Template", "TemplateViewerComponent_div_10_div_23_div_1_span_12_Template", "TemplateViewerComponent_div_10_div_23_div_1_span_13_Template", "TemplateViewerComponent_div_10_div_23_div_1_div_14_Template", "i_r19", "CReleateName", "CReleateId", "ɵɵpipeBind2", "CCreateDt", "TemplateViewerComponent_div_10_div_23_div_1_Template", "currentTemplateDetailsData", "i_r21", "detail_r20", "FieldName", "FieldValue", "TemplateViewerComponent_div_10_ng_template_24_div_0_div_1_Template", "paginatedDetails", "TemplateViewerComponent_div_10_ng_template_24_div_0_Template", "currentTemplateDetails", "noDetails_r22", "TemplateViewerComponent_div_10_div_26_li_6_Template_button_click_1_listener", "page_r25", "_r24", "goToDetailPage", "TemplateViewerComponent_div_10_div_26_Template_button_click_4_listener", "_r23", "TemplateViewerComponent_div_10_div_26_li_6_Template", "TemplateViewerComponent_div_10_div_26_Template_button_click_8_listener", "getDetailPageNumbers", "TemplateViewerComponent_div_10_Template_button_click_5_listener", "_r16", "closeTemplateDetail", "TemplateViewerComponent_div_10_div_9_Template", "TemplateViewerComponent_div_10_small_15_Template", "TemplateViewerComponent_div_10_Template_input_ngModelChange_18_listener", "TemplateViewerComponent_div_10_Template_input_keyup_enter_18_listener", "TemplateViewerComponent_div_10_Template_button_click_20_listener", "TemplateViewerComponent_div_10_button_22_Template", "TemplateViewerComponent_div_10_div_23_Template", "TemplateViewerComponent_div_10_ng_template_24_Template", "ɵɵtemplateRefExtractor", "TemplateViewerComponent_div_10_div_26_Template", "TemplateViewerComponent_div_10_ng_template_27_Template", "checkOldDetails_r26", "TemplateViewerComponent", "constructor", "templateService", "templateType", "showOnlyAddForm", "selectTemplate", "close", "templates", "templateDetails", "showAddForm", "selectedItems", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "console", "log", "apiTemplateGetTemplateListPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "Date", "toLocaleDateString", "error", "Message", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "updateTemplatePagination", "ceil", "max", "updatePaginatedTemplates", "startIndex", "endIndex", "slice", "page", "pages", "startPage", "endPage", "i", "push", "for<PERSON>ach", "alert", "createTemplate", "saveTemplateArgs", "CStatus", "Details", "CTemplateDetailId", "getRefId", "getFieldName", "apiTemplateSaveTemplatePost$Json", "emit", "CRequirementID", "ID", "id", "_item", "getFieldValue", "title", "loadTemplateDetails", "updateDetailPagination", "templateId", "pageIndex", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "allDetails", "CSort", "undefined", "toISOString", "CCreator", "detail", "pagedDetails", "loadTemplateDetailsMock", "setTimeout", "mockDetails", "itemsPerTemplate", "String", "fromCharCode", "now", "random", "floor", "filteredDetails", "details", "updatePaginatedDetails", "onClose", "templateID", "confirm", "deleteTemplateById", "deleteTemplateArgs", "apiTemplateDeleteTemplatePost$Json", "d", "index", "ɵɵdirectiveInject", "i1", "TemplateService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_button_5_Template", "TemplateViewerComponent_div_7_Template", "TemplateViewerComponent_div_8_Template", "TemplateViewerComponent_div_9_Template", "TemplateViewerComponent_div_10_Template", "TemplateViewerComponent_Template_button_click_13_listener", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "DatePipe", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "CheckboxControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MaxLengthValidator", "NgModel", "NgForm", "i4", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { SaveTemplateArgs, SaveTemplateDetailArgs, TemplateGetListArgs, TemplateGetListResponse, GetTemplateByIdArgs, GetTemplateDetailByIdArgs } from 'src/services/api/models';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() availableData: any[] = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)\r\n  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\r\n  @Input() showOnlyAddForm: boolean = false; // 是否只顯示新增模板表單\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 公開Math對象供模板使用\r\n  Math = Math;\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = []; // 保留用於向後相容\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 新的詳情資料管理\r\n  currentTemplateDetailsData: TemplateDetailItem[] = [];\r\n  detailSearchKeyword = '';\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板列表\r\n  templatePagination = {\r\n    currentPage: 1,\r\n    pageSize: 10,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板詳情\r\n  detailPagination = {\r\n    currentPage: 1,\r\n    pageSize: 5,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedDetails: TemplateDetail[] = [];\r\n\r\n  // 新增模板表單\r\n  showAddForm = false;\r\n  newTemplate = {\r\n    name: '',\r\n    description: '',\r\n    selectedItems: [] as any[]\r\n  };\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    // 只有在非純新增模式下才載入模板列表\r\n    if (!this.showOnlyAddForm) {\r\n      this.loadTemplates();\r\n      this.updateFilteredTemplates();\r\n    }\r\n\r\n    // 如果只顯示新增表單，自動打開新增模板表單\r\n    if (this.showOnlyAddForm) {\r\n      this.onAddTemplate();\r\n    }\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 載入模板列表 - 使用真實的 API 調用\r\n  loadTemplates() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      PageIndex: 1, // 暫時載入第一頁\r\n      PageSize: 100, // 暫時載入較多資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    console.log('GetTemplateList API 調用:', getTemplateListArgs);\r\n\r\n    // 調用 GetTemplateList API\r\n    this.templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // API 調用成功\r\n          console.log('模板列表載入成功:', response);\r\n\r\n          // 轉換 API 回應為內部格式\r\n          this.templates = response.Entries.map(item => ({\r\n            TemplateID: item.CTemplateId,\r\n            TemplateName: item.CTemplateName || '',\r\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\r\n          }));\r\n\r\n          // 更新過濾列表\r\n          this.updateFilteredTemplates();\r\n\r\n          // 清空詳情資料，改為按需載入\r\n          this.templateDetails = [];\r\n          this.currentTemplateDetailsData = [];\r\n        } else {\r\n          // API 返回錯誤\r\n          console.error('模板列表載入失敗:', response.Message);\r\n          this.templates = [];\r\n          this.templateDetails = [];\r\n          this.updateFilteredTemplates();\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        console.error('GetTemplateList API 調用失敗:', error);\r\n        this.templates = [];\r\n        this.templateDetails = [];\r\n        this.updateFilteredTemplates();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n    this.updateTemplatePagination();\r\n  }\r\n\r\n  // 更新模板分頁\r\n  updateTemplatePagination() {\r\n    this.templatePagination.totalItems = this.filteredTemplates.length;\r\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\r\n\r\n    // 確保當前頁面不超過總頁數\r\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\r\n    }\r\n\r\n    this.updatePaginatedTemplates();\r\n  }\r\n\r\n  // 更新分頁後的模板列表\r\n  updatePaginatedTemplates() {\r\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\r\n    const endIndex = startIndex + this.templatePagination.pageSize;\r\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 模板分頁導航\r\n  goToTemplatePage(page: number) {\r\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = page;\r\n      this.updatePaginatedTemplates();\r\n    }\r\n  }\r\n\r\n  // 獲取模板分頁頁碼數組\r\n  getTemplatePageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.templatePagination.totalPages;\r\n    const currentPage = this.templatePagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n  // 顯示新增模板表單\r\n  onAddTemplate() {\r\n    this.showAddForm = true;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n    // 如果不是只顯示新增表單模式，才重置選擇狀態\r\n    // 在只顯示新增表單模式下，保持父元件傳入的選中狀態\r\n    if (!this.showOnlyAddForm) {\r\n      this.availableData.forEach(item => item.selected = false);\r\n    }\r\n  }\r\n\r\n  // 取消新增模板\r\n  cancelAddTemplate() {\r\n    this.showAddForm = false;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n  }\r\n\r\n  // 儲存新模板\r\n  saveNewTemplate() {\r\n    if (!this.newTemplate.name.trim()) {\r\n      alert('請輸入模板名稱');\r\n      return;\r\n    }\r\n\r\n    const selectedItems = this.availableData.filter(item => item.selected);\r\n    if (selectedItems.length === 0) {\r\n      alert('請至少選擇一個項目');\r\n      return;\r\n    }\r\n\r\n    // TODO: 替換為實際的API調用\r\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\r\n  }\r\n\r\n  // 創建模板 - 使用真實的 API 調用\r\n  createTemplate(name: string, description: string, selectedItems: any[]) {\r\n    // 準備 API 請求資料\r\n    const saveTemplateArgs: SaveTemplateArgs = {\r\n      CTemplateId: null, // 新增時為 null\r\n      CTemplateName: name.trim(),\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      CStatus: 1, // 啟用狀態\r\n      Details: selectedItems.map(item => ({\r\n        CTemplateDetailId: null, // 新增時為 null\r\n        CReleateId: this.getRefId(item), // 關聯主檔ID\r\n        CReleateName: this.getFieldName(item) // 關聯名稱\r\n      } as SaveTemplateDetailArgs))\r\n    };\r\n\r\n    console.log('SaveTemplate API 調用:', saveTemplateArgs);\r\n\r\n    // 調用 SaveTemplate API\r\n    this.templateService.apiTemplateSaveTemplatePost$Json({\r\n      body: saveTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          console.log('模板創建成功:', response);\r\n\r\n          // 只有在非純新增模式下才重新載入模板列表\r\n          if (!this.showOnlyAddForm) {\r\n            this.loadTemplates();\r\n          }\r\n\r\n          // 關閉表單\r\n          this.showAddForm = false;\r\n          alert(`模板 \"${name}\" 已成功創建！包含 ${selectedItems.length} 個項目`);\r\n\r\n          // 如果是純新增模式，發出關閉事件通知父組件關閉 dialog\r\n          if (this.showOnlyAddForm) {\r\n            this.close.emit();\r\n          }\r\n        } else {\r\n          // API 返回錯誤\r\n          console.error('模板創建失敗:', response.Message);\r\n          alert(`模板創建失敗：${response.Message || '未知錯誤'}`);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        console.error('SaveTemplate API 調用失敗:', error);\r\n        alert('模板創建失敗，請檢查網路連線或聯繫系統管理員');\r\n      }\r\n    });\r\n  }\r\n\r\n  // 獲取關聯主檔ID的輔助方法\r\n  private getRefId(item: any): number {\r\n    return item.CRequirementID || item.ID || item.id || 0;\r\n  }\r\n\r\n  // 獲取欄位名稱的輔助方法\r\n  private getFieldName(_item?: any): string {\r\n    // 根據模板類型決定欄位名稱\r\n    switch (this.templateType) {\r\n      case 1: // 客變需求\r\n        return 'CRequirement';\r\n      default:\r\n        return 'name';\r\n    }\r\n  }\r\n\r\n  // 獲取欄位值的輔助方法\r\n  private getFieldValue(item: any): string {\r\n    return item.CRequirement || item.name || item.title || '';\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n\r\n    // 按需載入模板詳情\r\n    if (template.TemplateID) {\r\n      this.loadTemplateDetails(template.TemplateID);\r\n    }\r\n\r\n    this.updateDetailPagination();\r\n  }\r\n\r\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\r\n  loadTemplateDetails(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    console.log('GetTemplateDetailById API 調用:', args);\r\n\r\n    // 調用真實的 GetTemplateDetailById API\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          console.log('模板詳情載入成功:', response);\r\n\r\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\r\n          let allDetails = response.Entries.map(item => ({\r\n            CTemplateDetailId: item.CTemplateDetailId || 0,\r\n            CTemplateId: item.CTemplateId || templateId,\r\n            CReleateId: item.CReleateId || 0,\r\n            CReleateName: item.CReleateName || '',\r\n            CSort: undefined,\r\n            CRemark: undefined,\r\n            CCreateDt: new Date().toISOString(),\r\n            CCreator: '系統',\r\n            CCategory: undefined,\r\n            CUnitPrice: undefined,\r\n            CQuantity: undefined,\r\n            CUnit: undefined\r\n          } as TemplateDetailItem));\r\n\r\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\r\n          if (searchKeyword && searchKeyword.trim()) {\r\n            allDetails = allDetails.filter(detail =>\r\n              detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase())\r\n            );\r\n          }\r\n\r\n          // 前端分頁處理 (因為 API 不支援分頁參數)\r\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n          const endIndex = startIndex + this.detailPagination.pageSize;\r\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\r\n\r\n          // 更新詳情資料和分頁資訊\r\n          this.currentTemplateDetailsData = pagedDetails;\r\n          this.detailPagination.totalItems = allDetails.length;\r\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\r\n          this.detailPagination.currentPage = pageIndex;\r\n        } else {\r\n          console.error('模板詳情載入失敗:', response.Message);\r\n          this.currentTemplateDetailsData = [];\r\n          this.detailPagination.totalItems = 0;\r\n          this.detailPagination.totalPages = 0;\r\n          this.detailPagination.currentPage = 1;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('GetTemplateDetailById API 調用失敗:', error);\r\n        this.currentTemplateDetailsData = [];\r\n        this.detailPagination.totalItems = 0;\r\n        this.detailPagination.totalPages = 0;\r\n        this.detailPagination.currentPage = 1;\r\n\r\n        // 如果 API 失敗，回退到模擬資料\r\n        console.log('回退到模擬資料');\r\n        this.loadTemplateDetailsMock(templateId, pageIndex, searchKeyword);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 模擬載入模板詳情 (暫時使用，等待後端 API)\r\n  private loadTemplateDetailsMock(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    // 模擬 API 延遲\r\n    setTimeout(() => {\r\n      // 生成模擬詳情資料\r\n      const mockDetails: TemplateDetailItem[] = [];\r\n      const itemsPerTemplate = 8; // 每個模板8個項目\r\n\r\n      for (let i = 1; i <= itemsPerTemplate; i++) {\r\n        const detail: TemplateDetailItem = {\r\n          CTemplateDetailId: templateId * 100 + i,\r\n          CTemplateId: templateId,\r\n          CReleateId: templateId * 1000 + i,\r\n          CReleateName: `工程項目${String.fromCharCode(64 + (templateId % 26) + 1)}-${i}`,\r\n          CSort: i,\r\n          CRemark: i % 3 === 0 ? `備註說明 ${i}` : undefined,\r\n          CCreateDt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\r\n          CCreator: '系統管理員',\r\n          CCategory: i % 2 === 0 ? '基礎工程' : '進階工程',\r\n          CUnitPrice: Math.floor(Math.random() * 10000) + 1000,\r\n          CQuantity: Math.floor(Math.random() * 10) + 1,\r\n          CUnit: i % 3 === 0 ? '式' : (i % 3 === 1 ? '個' : 'm²')\r\n        };\r\n        mockDetails.push(detail);\r\n      }\r\n\r\n      // 搜尋篩選\r\n      let filteredDetails = mockDetails;\r\n      if (searchKeyword && searchKeyword.trim()) {\r\n        filteredDetails = mockDetails.filter(detail =>\r\n          detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n          (detail.CRemark && detail.CRemark.toLowerCase().includes(searchKeyword.toLowerCase())) ||\r\n          (detail.CCategory && detail.CCategory.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n        );\r\n      }\r\n\r\n      // 分頁處理\r\n      const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n      const endIndex = startIndex + this.detailPagination.pageSize;\r\n      const pagedDetails = filteredDetails.slice(startIndex, endIndex);\r\n\r\n      // 更新資料\r\n      this.currentTemplateDetailsData = pagedDetails;\r\n      this.detailPagination.totalItems = filteredDetails.length;\r\n      this.detailPagination.totalPages = Math.ceil(filteredDetails.length / this.detailPagination.pageSize);\r\n      this.detailPagination.currentPage = pageIndex;\r\n\r\n      console.log('模擬詳情載入完成:', {\r\n        templateId,\r\n        totalItems: filteredDetails.length,\r\n        currentPage: pageIndex,\r\n        details: pagedDetails\r\n      });\r\n    }, 300);\r\n  }\r\n\r\n  // 搜尋模板詳情\r\n  searchTemplateDetails(keyword: string) {\r\n    this.detailSearchKeyword = keyword;\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\r\n    }\r\n  }\r\n\r\n  // 更新詳情分頁 (保留向後相容)\r\n  updateDetailPagination() {\r\n    // 如果使用新的詳情資料，直接返回\r\n    if (this.currentTemplateDetailsData.length > 0) {\r\n      return;\r\n    }\r\n\r\n    // 向後相容：使用舊的詳情資料\r\n    const details = this.currentTemplateDetails;\r\n    this.detailPagination.totalItems = details.length;\r\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\r\n    this.detailPagination.currentPage = 1; // 重置到第一頁\r\n    this.updatePaginatedDetails();\r\n  }\r\n\r\n  // 更新分頁後的詳情列表\r\n  updatePaginatedDetails() {\r\n    const details = this.currentTemplateDetails;\r\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\r\n    const endIndex = startIndex + this.detailPagination.pageSize;\r\n    this.paginatedDetails = details.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 詳情分頁導航\r\n  goToDetailPage(page: number) {\r\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\r\n      // 如果使用新的詳情資料，重新載入\r\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\r\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\r\n      } else {\r\n        // 向後相容：使用舊的分頁邏輯\r\n        this.detailPagination.currentPage = page;\r\n        this.updatePaginatedDetails();\r\n      }\r\n    }\r\n  }\r\n\r\n  // 獲取詳情分頁頁碼數組\r\n  getDetailPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.detailPagination.totalPages;\r\n    const currentPage = this.detailPagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // 刪除模板 - 使用真實的 API 調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 準備 API 請求參數\r\n    const deleteTemplateArgs: GetTemplateByIdArgs = {\r\n      CTemplateId: templateID\r\n    };\r\n\r\n    console.log('DeleteTemplate API 調用:', deleteTemplateArgs);\r\n\r\n    // 調用 DeleteTemplate API\r\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\r\n      body: deleteTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          console.log('模板刪除成功:', response);\r\n\r\n          // 重新載入模板列表\r\n          this.loadTemplates();\r\n\r\n          // 如果當前查看的模板被刪除，關閉詳情\r\n          if (this.selectedTemplate?.TemplateID === templateID) {\r\n            this.selectedTemplate = null;\r\n          }\r\n\r\n          alert('模板已刪除');\r\n        } else {\r\n          // API 返回錯誤\r\n          console.error('模板刪除失敗:', response.Message);\r\n          alert(`模板刪除失敗：${response.Message || '未知錯誤'}`);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        console.error('DeleteTemplate API 調用失敗:', error);\r\n        alert('模板刪除失敗，請檢查網路連線或聯繫系統管理員');\r\n      }\r\n    });\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/Template/GetTemplateList\r\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/Template/SaveTemplate\r\n   *    請求體: {\r\n   *      CTemplateName: string,\r\n   *      CTemplateType: number,  // 1=客變需求\r\n   *      CStatus: number,\r\n   *      Details: [{\r\n   *        CReleateId: number,        // 關聯主檔ID\r\n   *        CReleateName: string       // 關聯名稱\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    POST /api/Template/DeleteTemplate\r\n   *    請求體: { CTemplateId: number }\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\r\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\r\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\r\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  CTemplateType: number; // 模板類型，1=客變需求\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n\r\n// 擴展的詳情項目結構 (基於 API 的 TemplateDetailItem 擴展)\r\nexport interface TemplateDetailItem {\r\n  CTemplateDetailId: number;\r\n  CTemplateId: number;\r\n  CReleateId: number;            // 關聯主檔ID\r\n  CReleateName: string;          // 關聯名稱\r\n  CSort?: number;                // 排序順序\r\n  CRemark?: string;              // 備註\r\n  CCreateDt: string;             // 建立時間\r\n  CCreator: string;              // 建立者\r\n  CUpdateDt?: string;            // 更新時間\r\n  CUpdator?: string;             // 更新者\r\n\r\n  // 業務相關欄位 (依需求添加，目前 API 不提供)\r\n  CUnitPrice?: number;           // 單價\r\n  CQuantity?: number;            // 數量\r\n  CUnit?: string;                // 單位\r\n  CCategory?: string;            // 分類\r\n}\r\n\r\n// 注意：GetTemplateDetailById API 使用的是 GetTemplateDetailByIdArgs 和 TemplateDetailItemListResponseBase\r\n// 這些 interface 已經在 API models 中定義，不需要重複定義\r\n", "<nb-card style=\"width: 90vw; max-width: 1200px; height: 80vh;\">\r\n  <nb-card-header>\r\n    <div class=\"d-flex justify-content-between align-items-center\">\r\n      <h5 class=\"mb-0\">{{ showOnlyAddForm ? '新增模板' : '模板管理' }}</h5>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onAddTemplate()\" *ngIf=\"!showOnlyAddForm\">\r\n        <i class=\"fas fa-plus mr-1\"></i>新增模板\r\n      </button>\r\n    </div>\r\n  </nb-card-header>\r\n  <nb-card-body style=\"overflow: auto;\">\r\n    <!-- 搜尋功能 -->\r\n    <div class=\"search-container mb-3\" *ngIf=\"!showOnlyAddForm\">\r\n      <div class=\"input-group\">\r\n        <input type=\"text\" class=\"form-control\" placeholder=\"搜尋模板名稱或描述...\" [(ngModel)]=\"searchKeyword\"\r\n          (input)=\"onSearch()\" (keyup.enter)=\"onSearch()\">\r\n        <div class=\"input-group-append\">\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"clearSearch()\" *ngIf=\"searchKeyword\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 新增模板表單 -->\r\n    <div *ngIf=\"showAddForm\" class=\"add-template-form mb-4\">\r\n      <div class=\"form-container\">\r\n        <div class=\"form-header\">\r\n          <div class=\"form-title\">\r\n            <i class=\"fas fa-plus\"></i>\r\n            <span>新增模板</span>\r\n          </div>\r\n        </div>\r\n\r\n        <form (ngSubmit)=\"saveNewTemplate()\" class=\"form-content\">\r\n          <div class=\"input-row\">\r\n            <div class=\"input-group\">\r\n              <label class=\"input-label\">\r\n                模板名稱 <span class=\"required\">*</span>\r\n              </label>\r\n              <input type=\"text\" class=\"input-field\" [(ngModel)]=\"newTemplate.name\" name=\"templateName\"\r\n                placeholder=\"請輸入模板名稱\" required maxlength=\"50\">\r\n            </div>\r\n            <div class=\"input-group\">\r\n              <label class=\"input-label\">模板描述</label>\r\n              <input type=\"text\" class=\"input-field\" [(ngModel)]=\"newTemplate.description\" name=\"templateDescription\"\r\n                placeholder=\"請輸入模板描述（可選）\" maxlength=\"100\">\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"input-group full-width\">\r\n            <label class=\"input-label\">\r\n              選擇要加入模板的項目 <span class=\"required\">*</span>\r\n            </label>\r\n            <div class=\"items-selector\">\r\n              <div *ngIf=\"availableData.length === 0\" class=\"empty-items\">\r\n                <i class=\"fas fa-info-circle\"></i>\r\n                <span>暫無可選項目</span>\r\n              </div>\r\n              <div *ngFor=\"let item of availableData; let i = index\" class=\"item-option\">\r\n                <label class=\"item-label\">\r\n                  <input type=\"checkbox\" class=\"item-checkbox\" [(ngModel)]=\"item.selected\" name=\"item_{{i}}\">\r\n                  <div class=\"item-content\">\r\n                    <div class=\"item-title\">{{ item.CRequirement || item.name }}</div>\r\n                    <div class=\"item-desc\">{{ item.CGroupName || item.description }}</div>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-actions\">\r\n            <button type=\"button\" class=\"btn-cancel\" (click)=\"cancelAddTemplate()\">\r\n              取消\r\n            </button>\r\n            <button type=\"submit\" class=\"btn-save\">\r\n              儲存模板\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模板列表（僅在未選擇模板細節時顯示） -->\r\n    <div class=\"template-list\" *ngIf=\"!selectedTemplate && !showOnlyAddForm\">\r\n      <!-- 搜尋結果統計 -->\r\n      <div class=\"search-results-info mb-2\" *ngIf=\"searchKeyword\">\r\n        <small class=\"text-muted\">\r\n          找到 {{ filteredTemplates.length }} 個符合「{{ searchKeyword }}」的模板\r\n        </small>\r\n      </div>\r\n\r\n      <!-- 分頁資訊 -->\r\n      <div class=\"pagination-info mb-2\" *ngIf=\"templatePagination.totalItems > 0\">\r\n        <small class=\"text-muted\">\r\n          顯示第 {{ (templatePagination.currentPage - 1) * templatePagination.pageSize + 1 }} -\r\n          {{ Math.min(templatePagination.currentPage * templatePagination.pageSize, templatePagination.totalItems) }} 項，\r\n          共 {{ templatePagination.totalItems }} 項模板\r\n        </small>\r\n      </div>\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped table-hover\">\r\n          <thead class=\"thead-light\">\r\n            <tr>\r\n              <th width=\"30%\">模板名稱</th>\r\n              <th width=\"50%\">描述</th>\r\n              <th width=\"20%\" class=\"text-center\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let tpl of paginatedTemplates; trackBy: trackByTemplateId\">\r\n              <td>\r\n                <strong>{{ tpl.TemplateName }}</strong>\r\n              </td>\r\n              <td>\r\n                <span class=\"text-muted\">{{ tpl.Description || '無描述' }}</span>\r\n              </td>\r\n              <td class=\"text-center\">\r\n                <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n                  <button class=\"btn btn-info\" (click)=\"onSelectTemplate(tpl)\" title=\"查看詳情\">\r\n                    <i class=\"fas fa-eye\"></i> 查看\r\n                  </button>\r\n                  <button class=\"btn btn-danger\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n                    *ngIf=\"tpl.TemplateID\" title=\"刪除模板\">\r\n                    <i class=\"fas fa-trash\"></i> 刪除\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n            <tr *ngIf=\"!paginatedTemplates || paginatedTemplates.length === 0\">\r\n              <td colspan=\"3\" class=\"text-center py-4\">\r\n                <div class=\"empty-state\">\r\n                  <i class=\"fas fa-folder-open fa-2x text-muted mb-2\"></i>\r\n                  <p class=\"text-muted mb-0\">\r\n                    {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}\r\n                  </p>\r\n                  <small class=\"text-muted\" *ngIf=\"searchKeyword\">\r\n                    請嘗試其他關鍵字或 <a href=\"javascript:void(0)\" (click)=\"clearSearch()\">清除搜尋</a>\r\n                  </small>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 模板列表分頁控制器 -->\r\n      <div class=\"pagination-container mt-3\" *ngIf=\"templatePagination.totalPages > 1\">\r\n        <nav aria-label=\"模板列表分頁\">\r\n          <ul class=\"pagination pagination-sm justify-content-center mb-0\">\r\n            <!-- 上一頁 -->\r\n            <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToTemplatePage(templatePagination.currentPage - 1)\"\r\n                [disabled]=\"templatePagination.currentPage === 1\">\r\n                <i class=\"fas fa-chevron-left\"></i>\r\n              </button>\r\n            </li>\r\n\r\n            <!-- 頁碼 -->\r\n            <li class=\"page-item\" *ngFor=\"let page of getTemplatePageNumbers()\"\r\n              [class.active]=\"page === templatePagination.currentPage\">\r\n              <button class=\"page-link\" (click)=\"goToTemplatePage(page)\">{{ page }}</button>\r\n            </li>\r\n\r\n            <!-- 下一頁 -->\r\n            <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToTemplatePage(templatePagination.currentPage + 1)\"\r\n                [disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n                <i class=\"fas fa-chevron-right\"></i>\r\n              </button>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 查看模板細節 -->\r\n    <div *ngIf=\"selectedTemplate && !showOnlyAddForm\" class=\"template-detail-modal\">\r\n      <div class=\"template-detail-header d-flex justify-content-between align-items-center\">\r\n        <h6 class=\"mb-0\">\r\n          <i class=\"fas fa-file-alt mr-2\"></i>\r\n          模板細節：{{ selectedTemplate!.TemplateName }}\r\n        </h6>\r\n        <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"closeTemplateDetail()\">\r\n          <i class=\"fas fa-times\"></i> 關閉\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"template-detail-content\">\r\n        <div *ngIf=\"selectedTemplate.Description\" class=\"template-description mb-3\">\r\n          <strong>描述：</strong>\r\n          <span class=\"text-muted\">{{ selectedTemplate.Description }}</span>\r\n        </div>\r\n\r\n        <div class=\"template-items\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n            <h6 class=\"mb-0\">\r\n              <i class=\"fas fa-list mr-1\"></i>\r\n              模板內容 ({{ currentTemplateDetailsData.length > 0 ? detailPagination.totalItems :\r\n              currentTemplateDetails.length }} 項)\r\n            </h6>\r\n            <small class=\"text-muted\" *ngIf=\"detailPagination.totalPages > 1\">\r\n              第 {{ detailPagination.currentPage }} / {{ detailPagination.totalPages }} 頁\r\n            </small>\r\n          </div>\r\n\r\n          <!-- 詳情搜尋框 -->\r\n          <div class=\"template-detail-search mb-3\">\r\n            <div class=\"input-group input-group-sm\">\r\n              <input type=\"text\" class=\"form-control\" placeholder=\"搜尋詳情項目...\" [(ngModel)]=\"detailSearchKeyword\"\r\n                (keyup.enter)=\"searchTemplateDetails(detailSearchKeyword)\">\r\n              <div class=\"input-group-append\">\r\n                <button class=\"btn btn-outline-secondary\" (click)=\"searchTemplateDetails(detailSearchKeyword)\">\r\n                  <i class=\"fas fa-search\"></i>\r\n                </button>\r\n                <button class=\"btn btn-outline-secondary\" (click)=\"detailSearchKeyword=''; searchTemplateDetails('')\"\r\n                  *ngIf=\"detailSearchKeyword\">\r\n                  <i class=\"fas fa-times\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 新的詳情資料顯示 -->\r\n          <div *ngIf=\"currentTemplateDetailsData.length > 0; else checkOldDetails\" class=\"detail-list\">\r\n            <div *ngFor=\"let detail of currentTemplateDetailsData; let i = index\"\r\n              class=\"detail-item py-2 border-bottom\">\r\n              <div class=\"detail-header d-flex justify-content-between align-items-start\">\r\n                <div class=\"detail-main flex-grow-1\">\r\n                  <div class=\"d-flex align-items-center mb-1\">\r\n                    <span class=\"badge badge-primary mr-2\">{{ (detailPagination.currentPage - 1) *\r\n                      detailPagination.pageSize + i + 1 }}</span>\r\n                    <strong class=\"detail-name\">{{ detail.CReleateName }}</strong>\r\n                  </div>\r\n                  <div class=\"detail-info\">\r\n                    <span class=\"badge badge-info mr-2\">ID: {{ detail.CReleateId }}</span>\r\n                    <span *ngIf=\"detail.CCategory\" class=\"badge badge-secondary mr-2\">{{ detail.CCategory }}</span>\r\n                    <span *ngIf=\"detail.CUnit\" class=\"badge badge-success mr-2\">{{ detail.CQuantity }} {{ detail.CUnit\r\n                      }}</span>\r\n                    <span *ngIf=\"detail.CUnitPrice\" class=\"badge badge-warning mr-2\">NT$ {{ detail.CUnitPrice | number\r\n                      }}</span>\r\n                  </div>\r\n                  <div *ngIf=\"detail.CRemark\" class=\"detail-remark text-muted mt-1\">\r\n                    <small><i class=\"fas fa-comment mr-1\"></i>{{ detail.CRemark }}</small>\r\n                  </div>\r\n                </div>\r\n                <small class=\"text-muted\">{{ detail.CCreateDt | date:'yyyy/MM/dd' }}</small>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 向後相容：舊的詳情資料顯示 -->\r\n          <ng-template #checkOldDetails>\r\n            <div *ngIf=\"currentTemplateDetails.length > 0; else noDetails\" class=\"detail-list\">\r\n              <div *ngFor=\"let detail of paginatedDetails; let i = index\"\r\n                class=\"detail-item d-flex align-items-center py-2 border-bottom\">\r\n                <div class=\"detail-index\">\r\n                  <span class=\"badge badge-light\">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i\r\n                    + 1 }}</span>\r\n                </div>\r\n                <div class=\"detail-content flex-grow-1 ml-2\">\r\n                  <div class=\"detail-field\">\r\n                    <strong>{{ detail.FieldName }}:</strong>\r\n                  </div>\r\n                  <div class=\"detail-value text-muted\">\r\n                    {{ detail.FieldValue }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </ng-template>\r\n\r\n          <!-- 詳情分頁控制器 -->\r\n          <div class=\"detail-pagination mt-3\" *ngIf=\"detailPagination.totalPages > 1\">\r\n            <nav aria-label=\"模板詳情分頁\">\r\n              <ul class=\"pagination pagination-sm justify-content-center mb-0\">\r\n                <!-- 上一頁 -->\r\n                <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === 1\">\r\n                  <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage - 1)\"\r\n                    [disabled]=\"detailPagination.currentPage === 1\">\r\n                    <i class=\"fas fa-chevron-left\"></i>\r\n                  </button>\r\n                </li>\r\n\r\n                <!-- 頁碼 -->\r\n                <li class=\"page-item\" *ngFor=\"let page of getDetailPageNumbers()\"\r\n                  [class.active]=\"page === detailPagination.currentPage\">\r\n                  <button class=\"page-link\" (click)=\"goToDetailPage(page)\">{{ page }}</button>\r\n                </li>\r\n\r\n                <!-- 下一頁 -->\r\n                <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                  <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage + 1)\"\r\n                    [disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                    <i class=\"fas fa-chevron-right\"></i>\r\n                  </button>\r\n                </li>\r\n              </ul>\r\n            </nav>\r\n          </div>\r\n\r\n          <ng-template #noDetails>\r\n            <div class=\"text-center py-3\">\r\n              <i class=\"fas fa-inbox fa-2x text-muted mb-2\"></i>\r\n              <p class=\"text-muted mb-0\">此模板暫無內容</p>\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer>\r\n    <div class=\"text-center\">\r\n      <button class=\"btn btn-secondary\" (click)=\"onClose()\">\r\n        <i class=\"fas fa-times mr-1\"></i>關閉\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;;;;;;;;;ICCvDC,EAAA,CAAAC,cAAA,iBAA0F;IAAnDD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IAC9DT,EAAA,CAAAU,SAAA,YAAgC;IAAAV,EAAA,CAAAW,MAAA,gCAClC;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;;;IAaLZ,EAAA,CAAAC,cAAA,iBAAsG;IAA9CD,EAAA,CAAAE,UAAA,mBAAAW,wEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAS,WAAA,EAAa;IAAA,EAAC;IAC7Ef,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAY,YAAA,EAAS;;;;;;IARXZ,EAFJ,CAAAC,cAAA,cAA4D,cACjC,gBAE2B;IADiBD,EAAA,CAAAgB,gBAAA,2BAAAC,sEAAAC,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAoB,kBAAA,CAAAd,MAAA,CAAAe,aAAA,EAAAH,MAAA,MAAAZ,MAAA,CAAAe,aAAA,GAAAH,MAAA;MAAA,OAAAlB,EAAA,CAAAQ,WAAA,CAAAU,MAAA;IAAA,EAA2B;IACvElB,EAArB,CAAAE,UAAA,mBAAAoB,8DAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiB,QAAA,EAAU;IAAA,EAAC,yBAAAC,oEAAA;MAAAxB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAgBF,MAAA,CAAAiB,QAAA,EAAU;IAAA,EAAC;IADjDvB,EAAA,CAAAY,YAAA,EACkD;IAEhDZ,EADF,CAAAC,cAAA,cAAgC,iBAC+C;IAArBD,EAAA,CAAAE,UAAA,mBAAAuB,+DAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiB,QAAA,EAAU;IAAA,EAAC;IAC1EvB,EAAA,CAAAU,SAAA,YAA6B;IAC/BV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAA0B,UAAA,IAAAC,+CAAA,qBAAsG;IAK5G3B,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;IAXiEZ,EAAA,CAAA4B,SAAA,GAA2B;IAA3B5B,EAAA,CAAA6B,gBAAA,YAAAvB,MAAA,CAAAe,aAAA,CAA2B;IAMXrB,EAAA,CAAA4B,SAAA,GAAmB;IAAnB5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAe,aAAA,CAAmB;;;;;IAsChGrB,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAU,SAAA,YAAkC;IAClCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,2CAAM;IACdX,EADc,CAAAY,YAAA,EAAO,EACf;;;;;;IAGFZ,EAFJ,CAAAC,cAAA,cAA2E,gBAC/C,gBACmE;IAA9CD,EAAA,CAAAgB,gBAAA,2BAAAe,6EAAAb,MAAA;MAAA,MAAAc,OAAA,GAAAhC,EAAA,CAAAI,aAAA,CAAA6B,GAAA,EAAAC,SAAA;MAAAlC,EAAA,CAAAoB,kBAAA,CAAAY,OAAA,CAAAG,QAAA,EAAAjB,MAAA,MAAAc,OAAA,CAAAG,QAAA,GAAAjB,MAAA;MAAA,OAAAlB,EAAA,CAAAQ,WAAA,CAAAU,MAAA;IAAA,EAA2B;IAAxElB,EAAA,CAAAY,YAAA,EAA2F;IAEzFZ,EADF,CAAAC,cAAA,cAA0B,cACA;IAAAD,EAAA,CAAAW,MAAA,GAAoC;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAClEZ,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAW,MAAA,GAAyC;IAGtEX,EAHsE,CAAAY,YAAA,EAAM,EAClE,EACA,EACJ;;;;;IANuEZ,EAAA,CAAA4B,SAAA,GAAiB;IAAjB5B,EAAA,CAAAoC,sBAAA,kBAAAC,IAAA,KAAiB;IAA7CrC,EAAA,CAAA6B,gBAAA,YAAAG,OAAA,CAAAG,QAAA,CAA2B;IAE9CnC,EAAA,CAAA4B,SAAA,GAAoC;IAApC5B,EAAA,CAAAsC,iBAAA,CAAAN,OAAA,CAAAO,YAAA,IAAAP,OAAA,CAAAQ,IAAA,CAAoC;IACrCxC,EAAA,CAAA4B,SAAA,GAAyC;IAAzC5B,EAAA,CAAAsC,iBAAA,CAAAN,OAAA,CAAAS,UAAA,IAAAT,OAAA,CAAAU,WAAA,CAAyC;;;;;;IApC1E1C,EAHN,CAAAC,cAAA,cAAwD,cAC1B,cACD,cACC;IACtBD,EAAA,CAAAU,SAAA,YAA2B;IAC3BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,+BAAI;IAEdX,EAFc,CAAAY,YAAA,EAAO,EACb,EACF;IAENZ,EAAA,CAAAC,cAAA,eAA0D;IAApDD,EAAA,CAAAE,UAAA,sBAAAyC,gEAAA;MAAA3C,EAAA,CAAAI,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAuC,eAAA,EAAiB;IAAA,EAAC;IAG9B7C,EAFJ,CAAAC,cAAA,cAAuB,cACI,iBACI;IACzBD,EAAA,CAAAW,MAAA,kCAAK;IAAAX,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAW,MAAA,SAAC;IAC/BX,EAD+B,CAAAY,YAAA,EAAO,EAC9B;IACRZ,EAAA,CAAAC,cAAA,iBACgD;IADTD,EAAA,CAAAgB,gBAAA,2BAAA8B,uEAAA5B,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAoB,kBAAA,CAAAd,MAAA,CAAAyC,WAAA,CAAAP,IAAA,EAAAtB,MAAA,MAAAZ,MAAA,CAAAyC,WAAA,CAAAP,IAAA,GAAAtB,MAAA;MAAA,OAAAlB,EAAA,CAAAQ,WAAA,CAAAU,MAAA;IAAA,EAA8B;IAEvElB,EAFE,CAAAY,YAAA,EACgD,EAC5C;IAEJZ,EADF,CAAAC,cAAA,eAAyB,iBACI;IAAAD,EAAA,CAAAW,MAAA,gCAAI;IAAAX,EAAA,CAAAY,YAAA,EAAQ;IACvCZ,EAAA,CAAAC,cAAA,iBAC4C;IADLD,EAAA,CAAAgB,gBAAA,2BAAAgC,uEAAA9B,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAoB,kBAAA,CAAAd,MAAA,CAAAyC,WAAA,CAAAL,WAAA,EAAAxB,MAAA,MAAAZ,MAAA,CAAAyC,WAAA,CAAAL,WAAA,GAAAxB,MAAA;MAAA,OAAAlB,EAAA,CAAAQ,WAAA,CAAAU,MAAA;IAAA,EAAqC;IAGhFlB,EAHI,CAAAY,YAAA,EAC4C,EACxC,EACF;IAGJZ,EADF,CAAAC,cAAA,eAAoC,iBACP;IACzBD,EAAA,CAAAW,MAAA,sEAAW;IAAAX,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAW,MAAA,SAAC;IACrCX,EADqC,CAAAY,YAAA,EAAO,EACpC;IACRZ,EAAA,CAAAC,cAAA,eAA4B;IAK1BD,EAJA,CAAA0B,UAAA,KAAAuB,6CAAA,kBAA4D,KAAAC,6CAAA,kBAIe;IAU/ElD,EADE,CAAAY,YAAA,EAAM,EACF;IAGJZ,EADF,CAAAC,cAAA,eAA0B,kBAC+C;IAA9BD,EAAA,CAAAE,UAAA,mBAAAiD,gEAAA;MAAAnD,EAAA,CAAAI,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8C,iBAAA,EAAmB;IAAA,EAAC;IACpEpD,EAAA,CAAAW,MAAA,sBACF;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAAuC;IACrCD,EAAA,CAAAW,MAAA,kCACF;IAIRX,EAJQ,CAAAY,YAAA,EAAS,EACL,EACD,EACH,EACF;;;;IAzC2CZ,EAAA,CAAA4B,SAAA,IAA8B;IAA9B5B,EAAA,CAAA6B,gBAAA,YAAAvB,MAAA,CAAAyC,WAAA,CAAAP,IAAA,CAA8B;IAK9BxC,EAAA,CAAA4B,SAAA,GAAqC;IAArC5B,EAAA,CAAA6B,gBAAA,YAAAvB,MAAA,CAAAyC,WAAA,CAAAL,WAAA,CAAqC;IAUtE1C,EAAA,CAAA4B,SAAA,GAAgC;IAAhC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA+C,aAAA,CAAAC,MAAA,OAAgC;IAIhBtD,EAAA,CAAA4B,SAAA,EAAkB;IAAlB5B,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAA+C,aAAA,CAAkB;;;;;IA4B9CrD,EADF,CAAAC,cAAA,cAA4D,gBAChC;IACxBD,EAAA,CAAAW,MAAA,GACF;IACFX,EADE,CAAAY,YAAA,EAAQ,EACJ;;;;IAFFZ,EAAA,CAAA4B,SAAA,GACF;IADE5B,EAAA,CAAAuD,kBAAA,mBAAAjD,MAAA,CAAAkD,iBAAA,CAAAF,MAAA,+BAAAhD,MAAA,CAAAe,aAAA,8BACF;;;;;IAKArB,EADF,CAAAC,cAAA,cAA4E,gBAChD;IACxBD,EAAA,CAAAW,MAAA,GAGF;IACFX,EADE,CAAAY,YAAA,EAAQ,EACJ;;;;IAJFZ,EAAA,CAAA4B,SAAA,GAGF;IAHE5B,EAAA,CAAAyD,kBAAA,0BAAAnD,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,QAAArD,MAAA,CAAAoD,kBAAA,CAAAE,QAAA,aAAAtD,MAAA,CAAAuD,IAAA,CAAAC,GAAA,CAAAxD,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,GAAArD,MAAA,CAAAoD,kBAAA,CAAAE,QAAA,EAAAtD,MAAA,CAAAoD,kBAAA,CAAAK,UAAA,4BAAAzD,MAAA,CAAAoD,kBAAA,CAAAK,UAAA,yBAGF;;;;;;IAyBU/D,EAAA,CAAAC,cAAA,iBACsC;IADPD,EAAA,CAAAE,UAAA,mBAAA8D,+EAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAAC,OAAA,GAAAlE,EAAA,CAAAO,aAAA,GAAA2B,SAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA0D,OAAA,CAAAC,UAAA,IAA2B7D,MAAA,CAAA8D,gBAAA,CAAAF,OAAA,CAAAC,UAAA,CAAgC;IAAA,EAAC;IAEzFnE,EAAA,CAAAU,SAAA,YAA4B;IAACV,EAAA,CAAAW,MAAA,qBAC/B;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;;;IAbXZ,EAFJ,CAAAC,cAAA,SAAuE,SACjE,aACM;IAAAD,EAAA,CAAAW,MAAA,GAAsB;IAChCX,EADgC,CAAAY,YAAA,EAAS,EACpC;IAEHZ,EADF,CAAAC,cAAA,SAAI,eACuB;IAAAD,EAAA,CAAAW,MAAA,GAA8B;IACzDX,EADyD,CAAAY,YAAA,EAAO,EAC3D;IAGDZ,EAFJ,CAAAC,cAAA,aAAwB,cAC2B,iBAC2B;IAA7CD,EAAA,CAAAE,UAAA,mBAAAmE,qEAAA;MAAA,MAAAH,OAAA,GAAAlE,EAAA,CAAAI,aAAA,CAAAkE,GAAA,EAAApC,SAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiE,gBAAA,CAAAL,OAAA,CAAqB;IAAA,EAAC;IAC1DlE,EAAA,CAAAU,SAAA,aAA0B;IAACV,EAAA,CAAAW,MAAA,sBAC7B;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAA0B,UAAA,KAAA8C,sDAAA,qBACsC;IAK5CxE,EAFI,CAAAY,YAAA,EAAM,EACH,EACF;;;;IAhBOZ,EAAA,CAAA4B,SAAA,GAAsB;IAAtB5B,EAAA,CAAAsC,iBAAA,CAAA4B,OAAA,CAAAO,YAAA,CAAsB;IAGLzE,EAAA,CAAA4B,SAAA,GAA8B;IAA9B5B,EAAA,CAAAsC,iBAAA,CAAA4B,OAAA,CAAAQ,WAAA,yBAA8B;IAQlD1E,EAAA,CAAA4B,SAAA,GAAoB;IAApB5B,EAAA,CAAA8B,UAAA,SAAAoC,OAAA,CAAAC,UAAA,CAAoB;;;;;;IAavBnE,EAAA,CAAAC,cAAA,gBAAgD;IAC9CD,EAAA,CAAAW,MAAA,+DAAU;IAAAX,EAAA,CAAAC,cAAA,YAAqD;IAAxBD,EAAA,CAAAE,UAAA,mBAAAyE,wEAAA;MAAA3E,EAAA,CAAAI,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAS,WAAA,EAAa;IAAA,EAAC;IAACf,EAAA,CAAAW,MAAA,+BAAI;IACrEX,EADqE,CAAAY,YAAA,EAAI,EACjE;;;;;IAPVZ,EAFJ,CAAAC,cAAA,SAAmE,aACxB,cACd;IACvBD,EAAA,CAAAU,SAAA,YAAwD;IACxDV,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACJZ,EAAA,CAAA0B,UAAA,IAAAmD,oDAAA,oBAAgD;IAKtD7E,EAFI,CAAAY,YAAA,EAAM,EACH,EACF;;;;IAPGZ,EAAA,CAAA4B,SAAA,GACF;IADE5B,EAAA,CAAA8E,kBAAA,MAAAxE,MAAA,CAAAe,aAAA,oGACF;IAC2BrB,EAAA,CAAA4B,SAAA,EAAmB;IAAnB5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAe,aAAA,CAAmB;;;;;;IAyBlDrB,EAFF,CAAAC,cAAA,aAC2D,iBACE;IAAjCD,EAAA,CAAAE,UAAA,mBAAA6E,2EAAA;MAAA,MAAAC,QAAA,GAAAhF,EAAA,CAAAI,aAAA,CAAA6E,IAAA,EAAA/C,SAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4E,gBAAA,CAAAF,QAAA,CAAsB;IAAA,EAAC;IAAChF,EAAA,CAAAW,MAAA,GAAU;IACvEX,EADuE,CAAAY,YAAA,EAAS,EAC3E;;;;;IAFHZ,EAAA,CAAAmF,WAAA,WAAAH,QAAA,KAAA1E,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,CAAwD;IACG3D,EAAA,CAAA4B,SAAA,GAAU;IAAV5B,EAAA,CAAAsC,iBAAA,CAAA0C,QAAA,CAAU;;;;;;IATrEhF,EALR,CAAAC,cAAA,cAAiF,cACtD,aAC0C,aAEe,iBAExB;IAD1BD,EAAA,CAAAE,UAAA,mBAAAkF,sEAAA;MAAApF,EAAA,CAAAI,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4E,gBAAA,CAAA5E,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAEtF3D,EAAA,CAAAU,SAAA,YAAmC;IAEvCV,EADE,CAAAY,YAAA,EAAS,EACN;IAGLZ,EAAA,CAAA0B,UAAA,IAAA4D,kDAAA,iBAC2D;IAMzDtF,EADF,CAAAC,cAAA,aAA0G,iBAExB;IADtDD,EAAA,CAAAE,UAAA,mBAAAqF,sEAAA;MAAAvF,EAAA,CAAAI,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4E,gBAAA,CAAA5E,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAEtF3D,EAAA,CAAAU,SAAA,YAAoC;IAK9CV,EAJQ,CAAAY,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAtBsBZ,EAAA,CAAA4B,SAAA,GAAuD;IAAvD5B,EAAA,CAAAmF,WAAA,aAAA7E,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,OAAuD;IAEzE3D,EAAA,CAAA4B,SAAA,EAAiD;IAAjD5B,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,OAAiD;IAMd3D,EAAA,CAAA4B,SAAA,GAA2B;IAA3B5B,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAAkF,sBAAA,GAA2B;IAM5CxF,EAAA,CAAA4B,SAAA,EAAmF;IAAnF5B,EAAA,CAAAmF,WAAA,aAAA7E,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,KAAArD,MAAA,CAAAoD,kBAAA,CAAA+B,UAAA,CAAmF;IAErGzF,EAAA,CAAA4B,SAAA,EAA6E;IAA7E5B,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAoD,kBAAA,CAAAC,WAAA,KAAArD,MAAA,CAAAoD,kBAAA,CAAA+B,UAAA,CAA6E;;;;;IApFzFzF,EAAA,CAAAC,cAAA,cAAyE;IASvED,EAPA,CAAA0B,UAAA,IAAAgE,4CAAA,kBAA4D,IAAAC,4CAAA,kBAOgB;IAYpE3F,EAJR,CAAAC,cAAA,cAA8B,gBACmB,gBAClB,SACrB,aACc;IAAAD,EAAA,CAAAW,MAAA,+BAAI;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACzBZ,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAW,MAAA,oBAAE;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACvBZ,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAW,MAAA,oBAAE;IAE1CX,EAF0C,CAAAY,YAAA,EAAK,EACxC,EACC;IACRZ,EAAA,CAAAC,cAAA,aAAO;IAoBLD,EAnBA,CAAA0B,UAAA,KAAAkE,4CAAA,kBAAuE,KAAAC,4CAAA,iBAmBJ;IAezE7F,EAFI,CAAAY,YAAA,EAAQ,EACF,EACJ;IAGNZ,EAAA,CAAA0B,UAAA,KAAAoE,6CAAA,mBAAiF;IA2BnF9F,EAAA,CAAAY,YAAA,EAAM;;;;IAzFmCZ,EAAA,CAAA4B,SAAA,EAAmB;IAAnB5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAe,aAAA,CAAmB;IAOvBrB,EAAA,CAAA4B,SAAA,EAAuC;IAAvC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAoD,kBAAA,CAAAK,UAAA,KAAuC;IAkBhD/D,EAAA,CAAA4B,SAAA,IAAuB;IAAA5B,EAAvB,CAAA8B,UAAA,YAAAxB,MAAA,CAAAyF,kBAAA,CAAuB,iBAAAzF,MAAA,CAAA0F,iBAAA,CAA0B;IAmBhEhG,EAAA,CAAA4B,SAAA,EAA4D;IAA5D5B,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAyF,kBAAA,IAAAzF,MAAA,CAAAyF,kBAAA,CAAAzC,MAAA,OAA4D;IAkB/BtD,EAAA,CAAA4B,SAAA,EAAuC;IAAvC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAoD,kBAAA,CAAA+B,UAAA,KAAuC;;;;;IA2C3EzF,EADF,CAAAC,cAAA,eAA4E,aAClE;IAAAD,EAAA,CAAAW,MAAA,yBAAG;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACpBZ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC7DX,EAD6D,CAAAY,YAAA,EAAO,EAC9D;;;;IADqBZ,EAAA,CAAA4B,SAAA,GAAkC;IAAlC5B,EAAA,CAAAsC,iBAAA,CAAAhC,MAAA,CAAA2F,gBAAA,CAAAvB,WAAA,CAAkC;;;;;IAUzD1E,EAAA,CAAAC,cAAA,gBAAkE;IAChED,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAQ;;;;IADNZ,EAAA,CAAA4B,SAAA,EACF;IADE5B,EAAA,CAAAuD,kBAAA,aAAAjD,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,SAAArD,MAAA,CAAA4F,gBAAA,CAAAT,UAAA,aACF;;;;;;IAYIzF,EAAA,CAAAC,cAAA,iBAC8B;IADYD,EAAA,CAAAE,UAAA,mBAAAiG,0EAAA;MAAAnG,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAD,MAAA,CAAA+F,mBAAA,GAA6B,EAAE;MAAA,OAAArG,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAgG,qBAAA,CAAsB,EAAE,CAAC;IAAA,EAAC;IAEnGtG,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAY,YAAA,EAAS;;;;;IAkBLZ,EAAA,CAAAC,cAAA,gBAAkE;IAAAD,EAAA,CAAAW,MAAA,GAAsB;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IAA7BZ,EAAA,CAAA4B,SAAA,EAAsB;IAAtB5B,EAAA,CAAAsC,iBAAA,CAAAiE,UAAA,CAAAC,SAAA,CAAsB;;;;;IACxFxG,EAAA,CAAAC,cAAA,gBAA4D;IAAAD,EAAA,CAAAW,MAAA,GACxD;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADiDZ,EAAA,CAAA4B,SAAA,EACxD;IADwD5B,EAAA,CAAAuD,kBAAA,KAAAgD,UAAA,CAAAE,SAAA,OAAAF,UAAA,CAAAG,KAAA,KACxD;;;;;IACJ1G,EAAA,CAAAC,cAAA,gBAAiE;IAAAD,EAAA,CAAAW,MAAA,GAC7D;;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADsDZ,EAAA,CAAA4B,SAAA,EAC7D;IAD6D5B,EAAA,CAAA8E,kBAAA,SAAA9E,EAAA,CAAA2G,WAAA,OAAAJ,UAAA,CAAAK,UAAA,MAC7D;;;;;IAGJ5G,EADF,CAAAC,cAAA,eAAkE,YACzD;IAAAD,EAAA,CAAAU,SAAA,aAAmC;IAAAV,EAAA,CAAAW,MAAA,GAAoB;IAChEX,EADgE,CAAAY,YAAA,EAAQ,EAClE;;;;IADsCZ,EAAA,CAAA4B,SAAA,GAAoB;IAApB5B,EAAA,CAAAsC,iBAAA,CAAAiE,UAAA,CAAAM,OAAA,CAAoB;;;;;IAb9D7G,EALR,CAAAC,cAAA,eACyC,eACqC,eACrC,eACS,gBACH;IAAAD,EAAA,CAAAW,MAAA,GACD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC7CZ,EAAA,CAAAC,cAAA,kBAA4B;IAAAD,EAAA,CAAAW,MAAA,GAAyB;IACvDX,EADuD,CAAAY,YAAA,EAAS,EAC1D;IAEJZ,EADF,CAAAC,cAAA,eAAyB,gBACa;IAAAD,EAAA,CAAAW,MAAA,IAA2B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAItEZ,EAHA,CAAA0B,UAAA,KAAAoF,4DAAA,oBAAkE,KAAAC,4DAAA,oBACN,KAAAC,4DAAA,oBAEK;IAEnEhH,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAA0B,UAAA,KAAAuF,2DAAA,mBAAkE;IAGpEjH,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAW,MAAA,IAA0C;;IAExEX,EAFwE,CAAAY,YAAA,EAAQ,EACxE,EACF;;;;;;IAlByCZ,EAAA,CAAA4B,SAAA,GACD;IADC5B,EAAA,CAAAsC,iBAAA,EAAAhC,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,QAAArD,MAAA,CAAA4F,gBAAA,CAAAtC,QAAA,GAAAsD,KAAA,KACD;IACVlH,EAAA,CAAA4B,SAAA,GAAyB;IAAzB5B,EAAA,CAAAsC,iBAAA,CAAAiE,UAAA,CAAAY,YAAA,CAAyB;IAGjBnH,EAAA,CAAA4B,SAAA,GAA2B;IAA3B5B,EAAA,CAAA8E,kBAAA,SAAAyB,UAAA,CAAAa,UAAA,KAA2B;IACxDpH,EAAA,CAAA4B,SAAA,EAAsB;IAAtB5B,EAAA,CAAA8B,UAAA,SAAAyE,UAAA,CAAAC,SAAA,CAAsB;IACtBxG,EAAA,CAAA4B,SAAA,EAAkB;IAAlB5B,EAAA,CAAA8B,UAAA,SAAAyE,UAAA,CAAAG,KAAA,CAAkB;IAElB1G,EAAA,CAAA4B,SAAA,EAAuB;IAAvB5B,EAAA,CAAA8B,UAAA,SAAAyE,UAAA,CAAAK,UAAA,CAAuB;IAG1B5G,EAAA,CAAA4B,SAAA,EAAoB;IAApB5B,EAAA,CAAA8B,UAAA,SAAAyE,UAAA,CAAAM,OAAA,CAAoB;IAIF7G,EAAA,CAAA4B,SAAA,GAA0C;IAA1C5B,EAAA,CAAAsC,iBAAA,CAAAtC,EAAA,CAAAqH,WAAA,QAAAd,UAAA,CAAAe,SAAA,gBAA0C;;;;;IAtB1EtH,EAAA,CAAAC,cAAA,eAA6F;IAC3FD,EAAA,CAAA0B,UAAA,IAAA6F,oDAAA,qBACyC;IAuB3CvH,EAAA,CAAAY,YAAA,EAAM;;;;IAxBoBZ,EAAA,CAAA4B,SAAA,EAA+B;IAA/B5B,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAAkH,0BAAA,CAA+B;;;;;IAgCjDxH,EAHJ,CAAAC,cAAA,eACmE,eACvC,gBACQ;IAAAD,EAAA,CAAAW,MAAA,GACxB;IACVX,EADU,CAAAY,YAAA,EAAO,EACX;IAGFZ,EAFJ,CAAAC,cAAA,eAA6C,eACjB,aAChB;IAAAD,EAAA,CAAAW,MAAA,GAAuB;IACjCX,EADiC,CAAAY,YAAA,EAAS,EACpC;IACNZ,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAW,MAAA,GACF;IAEJX,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;;;IAX8BZ,EAAA,CAAA4B,SAAA,GACxB;IADwB5B,EAAA,CAAAsC,iBAAA,EAAAhC,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,QAAArD,MAAA,CAAA4F,gBAAA,CAAAtC,QAAA,GAAA6D,KAAA,KACxB;IAIEzH,EAAA,CAAA4B,SAAA,GAAuB;IAAvB5B,EAAA,CAAA8E,kBAAA,KAAA4C,UAAA,CAAAC,SAAA,MAAuB;IAG/B3H,EAAA,CAAA4B,SAAA,GACF;IADE5B,EAAA,CAAA8E,kBAAA,MAAA4C,UAAA,CAAAE,UAAA,MACF;;;;;IAbN5H,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAA0B,UAAA,IAAAmG,kEAAA,oBACmE;IAcrE7H,EAAA,CAAAY,YAAA,EAAM;;;;IAfoBZ,EAAA,CAAA4B,SAAA,EAAqB;IAArB5B,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAAwH,gBAAA,CAAqB;;;;;IAD/C9H,EAAA,CAAA0B,UAAA,IAAAqG,4DAAA,mBAAmF;;;;;;IAApC/H,EAAzC,CAAA8B,UAAA,SAAAxB,MAAA,CAAA0H,sBAAA,CAAA1E,MAAA,KAAyC,aAAA2E,aAAA,CAAc;;;;;;IAkCvDjI,EAFF,CAAAC,cAAA,aACyD,iBACE;IAA/BD,EAAA,CAAAE,UAAA,mBAAAgI,4EAAA;MAAA,MAAAC,QAAA,GAAAnI,EAAA,CAAAI,aAAA,CAAAgI,IAAA,EAAAlG,SAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+H,cAAA,CAAAF,QAAA,CAAoB;IAAA,EAAC;IAACnI,EAAA,CAAAW,MAAA,GAAU;IACrEX,EADqE,CAAAY,YAAA,EAAS,EACzE;;;;;IAFHZ,EAAA,CAAAmF,WAAA,WAAAgD,QAAA,KAAA7H,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,CAAsD;IACG3D,EAAA,CAAA4B,SAAA,GAAU;IAAV5B,EAAA,CAAAsC,iBAAA,CAAA6F,QAAA,CAAU;;;;;;IATnEnI,EALR,CAAAC,cAAA,eAA4E,eACjD,aAC0C,aAEa,iBAExB;IADxBD,EAAA,CAAAE,UAAA,mBAAAoI,uEAAA;MAAAtI,EAAA,CAAAI,aAAA,CAAAmI,IAAA;MAAA,MAAAjI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+H,cAAA,CAAA/H,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElF3D,EAAA,CAAAU,SAAA,YAAmC;IAEvCV,EADE,CAAAY,YAAA,EAAS,EACN;IAGLZ,EAAA,CAAA0B,UAAA,IAAA8G,mDAAA,iBACyD;IAMvDxI,EADF,CAAAC,cAAA,aAAsG,iBAExB;IADlDD,EAAA,CAAAE,UAAA,mBAAAuI,uEAAA;MAAAzI,EAAA,CAAAI,aAAA,CAAAmI,IAAA;MAAA,MAAAjI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+H,cAAA,CAAA/H,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElF3D,EAAA,CAAAU,SAAA,YAAoC;IAK9CV,EAJQ,CAAAY,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAtBsBZ,EAAA,CAAA4B,SAAA,GAAqD;IAArD5B,EAAA,CAAAmF,WAAA,aAAA7E,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,OAAqD;IAEvE3D,EAAA,CAAA4B,SAAA,EAA+C;IAA/C5B,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,OAA+C;IAMZ3D,EAAA,CAAA4B,SAAA,GAAyB;IAAzB5B,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAAoI,oBAAA,GAAyB;IAM1C1I,EAAA,CAAA4B,SAAA,EAA+E;IAA/E5B,EAAA,CAAAmF,WAAA,aAAA7E,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,KAAArD,MAAA,CAAA4F,gBAAA,CAAAT,UAAA,CAA+E;IAEjGzF,EAAA,CAAA4B,SAAA,EAAyE;IAAzE5B,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAA4F,gBAAA,CAAAvC,WAAA,KAAArD,MAAA,CAAA4F,gBAAA,CAAAT,UAAA,CAAyE;;;;;IASjFzF,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAU,SAAA,aAAkD;IAClDV,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAW,MAAA,iDAAO;IACpCX,EADoC,CAAAY,YAAA,EAAI,EAClC;;;;;;IA9HVZ,EAFJ,CAAAC,cAAA,cAAgF,cACQ,YACnE;IACfD,EAAA,CAAAU,SAAA,YAAoC;IACpCV,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,iBAAiF;IAAhCD,EAAA,CAAAE,UAAA,mBAAAyI,gEAAA;MAAA3I,EAAA,CAAAI,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuI,mBAAA,EAAqB;IAAA,EAAC;IAC9E7I,EAAA,CAAAU,SAAA,YAA4B;IAACV,EAAA,CAAAW,MAAA,qBAC/B;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;IAENZ,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAA0B,UAAA,IAAAoH,6CAAA,kBAA4E;IAOxE9I,EAFJ,CAAAC,cAAA,eAA4B,eAC0C,aACjD;IACfD,EAAA,CAAAU,SAAA,aAAgC;IAChCV,EAAA,CAAAW,MAAA,IAEF;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAA0B,UAAA,KAAAqH,gDAAA,oBAAkE;IAGpE/I,EAAA,CAAAY,YAAA,EAAM;IAKFZ,EAFJ,CAAAC,cAAA,eAAyC,eACC,iBAEuB;IADGD,EAAA,CAAAgB,gBAAA,2BAAAgI,wEAAA9H,MAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAoB,kBAAA,CAAAd,MAAA,CAAA+F,mBAAA,EAAAnF,MAAA,MAAAZ,MAAA,CAAA+F,mBAAA,GAAAnF,MAAA;MAAA,OAAAlB,EAAA,CAAAQ,WAAA,CAAAU,MAAA;IAAA,EAAiC;IAC/FlB,EAAA,CAAAE,UAAA,yBAAA+I,sEAAA;MAAAjJ,EAAA,CAAAI,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAgG,qBAAA,CAAAhG,MAAA,CAAA+F,mBAAA,CAA0C;IAAA,EAAC;IAD5DrG,EAAA,CAAAY,YAAA,EAC6D;IAE3DZ,EADF,CAAAC,cAAA,eAAgC,kBACiE;IAArDD,EAAA,CAAAE,UAAA,mBAAAgJ,iEAAA;MAAAlJ,EAAA,CAAAI,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgG,qBAAA,CAAAhG,MAAA,CAAA+F,mBAAA,CAA0C;IAAA,EAAC;IAC5FrG,EAAA,CAAAU,SAAA,aAA6B;IAC/BV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAA0B,UAAA,KAAAyH,iDAAA,qBAC8B;IAKpCnJ,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;IAgFNZ,EA7EA,CAAA0B,UAAA,KAAA0H,8CAAA,mBAA6F,KAAAC,sDAAA,gCAAArJ,EAAA,CAAAsJ,sBAAA,CA4B/D,KAAAC,8CAAA,oBAqB8C,KAAAC,sDAAA,gCAAAxJ,EAAA,CAAAsJ,sBAAA,CA4BpD;IAQ9BtJ,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;;IAhIAZ,EAAA,CAAA4B,SAAA,GACF;IADE5B,EAAA,CAAA8E,kBAAA,oCAAAxE,MAAA,CAAA2F,gBAAA,CAAAxB,YAAA,MACF;IAOMzE,EAAA,CAAA4B,SAAA,GAAkC;IAAlC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA2F,gBAAA,CAAAvB,WAAA,CAAkC;IASlC1E,EAAA,CAAA4B,SAAA,GAEF;IAFE5B,EAAA,CAAA8E,kBAAA,gCAAAxE,MAAA,CAAAkH,0BAAA,CAAAlE,MAAA,OAAAhD,MAAA,CAAA4F,gBAAA,CAAAnC,UAAA,GAAAzD,MAAA,CAAA0H,sBAAA,CAAA1E,MAAA,cAEF;IAC2BtD,EAAA,CAAA4B,SAAA,EAAqC;IAArC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA4F,gBAAA,CAAAT,UAAA,KAAqC;IAQEzF,EAAA,CAAA4B,SAAA,GAAiC;IAAjC5B,EAAA,CAAA6B,gBAAA,YAAAvB,MAAA,CAAA+F,mBAAA,CAAiC;IAO5FrG,EAAA,CAAA4B,SAAA,GAAyB;IAAzB5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA+F,mBAAA,CAAyB;IAQ5BrG,EAAA,CAAA4B,SAAA,EAA6C;IAAA5B,EAA7C,CAAA8B,UAAA,SAAAxB,MAAA,CAAAkH,0BAAA,CAAAlE,MAAA,KAA6C,aAAAmG,mBAAA,CAAoB;IAiDlCzJ,EAAA,CAAA4B,SAAA,GAAqC;IAArC5B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA4F,gBAAA,CAAAT,UAAA,KAAqC;;;ADtQpF,OAAM,MAAOiE,uBAAuB;EAiDlCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAhD1B,KAAAvG,aAAa,GAAU,EAAE,CAAC,CAAC;IAC3B,KAAAwG,YAAY,GAAW,CAAC,CAAC,CAAC;IAC1B,KAAAC,eAAe,GAAY,KAAK,CAAC,CAAC;IACjC,KAAAC,cAAc,GAAG,IAAIpK,YAAY,EAAY;IAC7C,KAAAqK,KAAK,GAAG,IAAIrK,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAAkE,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAoG,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE,CAAC,CAAC;IACxC,KAAAjE,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAuB,0BAA0B,GAAyB,EAAE;IACrD,KAAAnB,mBAAmB,GAAG,EAAE;IAExB;IACA,KAAAhF,aAAa,GAAG,EAAE;IAClB,KAAAmC,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAE,kBAAkB,GAAG;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE,CAAC;MACb0B,UAAU,EAAE;KACb;IACD,KAAAM,kBAAkB,GAAe,EAAE;IAEnC;IACA,KAAAG,gBAAgB,GAAG;MACjBvC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXG,UAAU,EAAE,CAAC;MACb0B,UAAU,EAAE;KACb;IACD,KAAAqC,gBAAgB,GAAqB,EAAE;IAEvC;IACA,KAAAqC,WAAW,GAAG,KAAK;IACnB,KAAApH,WAAW,GAAG;MACZP,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACf0H,aAAa,EAAE;KAChB;EAEuD;EAExDC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACP,eAAe,EAAE;MACzB,IAAI,CAACQ,aAAa,EAAE;MACpB,IAAI,CAACC,uBAAuB,EAAE;IAChC;IAEA;IACA,IAAI,IAAI,CAACT,eAAe,EAAE;MACxB,IAAI,CAACrJ,aAAa,EAAE;IACtB;EACF;EAEA+J,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACA,MAAMG,mBAAmB,GAAwB;MAC/CC,aAAa,EAAE,IAAI,CAACb,YAAY;MAAE;MAClCc,SAAS,EAAE,CAAC;MAAE;MACdC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAEDC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEN,mBAAmB,CAAC;IAE3D;IACA,IAAI,CAACb,eAAe,CAACoB,mCAAmC,CAAC;MACvDC,IAAI,EAAER;KACP,CAAC,CAACS,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACAR,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,QAAQ,CAAC;UAElC;UACA,IAAI,CAACnB,SAAS,GAAGmB,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7CrH,UAAU,EAAEqH,IAAI,CAACC,WAAW;YAC5BhH,YAAY,EAAE+G,IAAI,CAACX,aAAa,IAAI,EAAE;YACtCnG,WAAW,EAAE,SAAS8G,IAAI,CAAClE,SAAS,GAAG,IAAIoE,IAAI,CAACF,IAAI,CAAClE,SAAS,CAAC,CAACqE,kBAAkB,EAAE,GAAG,IAAI;WAC5F,CAAC,CAAC;UAEH;UACA,IAAI,CAACpB,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAACL,eAAe,GAAG,EAAE;UACzB,IAAI,CAAC1C,0BAA0B,GAAG,EAAE;QACtC,CAAC,MAAM;UACL;UACAsD,OAAO,CAACc,KAAK,CAAC,WAAW,EAAER,QAAQ,CAACS,OAAO,CAAC;UAC5C,IAAI,CAAC5B,SAAS,GAAG,EAAE;UACnB,IAAI,CAACC,eAAe,GAAG,EAAE;UACzB,IAAI,CAACK,uBAAuB,EAAE;QAChC;MACF,CAAC;MACDqB,KAAK,EAAGA,KAAK,IAAI;QACf;QACAd,OAAO,CAACc,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAC3B,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACK,uBAAuB,EAAE;MAChC;KACD,CAAC;EACJ;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAClJ,aAAa,CAACyK,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACtI,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACyG,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAM8B,OAAO,GAAG,IAAI,CAAC1K,aAAa,CAAC2K,WAAW,EAAE;MAChD,IAAI,CAACxI,iBAAiB,GAAG,IAAI,CAACyG,SAAS,CAACgC,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAACzH,YAAY,CAACuH,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAACxH,WAAW,IAAIwH,QAAQ,CAACxH,WAAW,CAACsH,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;IACA,IAAI,CAACK,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,IAAI,CAAC1I,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACP,iBAAiB,CAACF,MAAM;IAClE,IAAI,CAACI,kBAAkB,CAAC+B,UAAU,GAAG5B,IAAI,CAACwI,IAAI,CAAC,IAAI,CAAC3I,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACL,kBAAkB,CAACE,QAAQ,CAAC;IAErH;IACA,IAAI,IAAI,CAACF,kBAAkB,CAACC,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC+B,UAAU,EAAE;MAC5E,IAAI,CAAC/B,kBAAkB,CAACC,WAAW,GAAGE,IAAI,CAACyI,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC5I,kBAAkB,CAAC+B,UAAU,CAAC;IACvF;IAEA,IAAI,CAAC8G,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAAC9I,kBAAkB,CAACC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,kBAAkB,CAACE,QAAQ;IAC/F,MAAM6I,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC9I,kBAAkB,CAACE,QAAQ;IAC9D,IAAI,CAACmC,kBAAkB,GAAG,IAAI,CAACvC,iBAAiB,CAACkJ,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACAvH,gBAAgBA,CAACyH,IAAY;IAC3B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACjJ,kBAAkB,CAAC+B,UAAU,EAAE;MAC3D,IAAI,CAAC/B,kBAAkB,CAACC,WAAW,GAAGgJ,IAAI;MAC1C,IAAI,CAACJ,wBAAwB,EAAE;IACjC;EACF;EAEA;EACA/G,sBAAsBA,CAAA;IACpB,MAAMoH,KAAK,GAAa,EAAE;IAC1B,MAAMnH,UAAU,GAAG,IAAI,CAAC/B,kBAAkB,CAAC+B,UAAU;IACrD,MAAM9B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACC,WAAW;IAEvD;IACA,MAAMkJ,SAAS,GAAGhJ,IAAI,CAACyI,GAAG,CAAC,CAAC,EAAE3I,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMmJ,OAAO,GAAGjJ,IAAI,CAACC,GAAG,CAAC2B,UAAU,EAAE9B,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIoJ,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACArL,QAAQA,CAAA;IACN,IAAI,CAACgJ,uBAAuB,EAAE;EAChC;EAEA;EACAxJ,WAAWA,CAAA;IACT,IAAI,CAACM,aAAa,GAAG,EAAE;IACvB,IAAI,CAACkJ,uBAAuB,EAAE;EAChC;EAIA;EACA9J,aAAaA,CAAA;IACX,IAAI,CAAC0J,WAAW,GAAG,IAAI;IACvB,IAAI,CAACpH,WAAW,GAAG;MACjBP,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACf0H,aAAa,EAAE;KAChB;IACD;IACA;IACA,IAAI,CAAC,IAAI,CAACN,eAAe,EAAE;MACzB,IAAI,CAACzG,aAAa,CAAC4J,OAAO,CAACzB,IAAI,IAAIA,IAAI,CAACrJ,QAAQ,GAAG,KAAK,CAAC;IAC3D;EACF;EAEA;EACAiB,iBAAiBA,CAAA;IACf,IAAI,CAAC+G,WAAW,GAAG,KAAK;IACxB,IAAI,CAACpH,WAAW,GAAG;MACjBP,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACf0H,aAAa,EAAE;KAChB;EACH;EAEA;EACAvH,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACE,WAAW,CAACP,IAAI,CAACsJ,IAAI,EAAE,EAAE;MACjCoB,KAAK,CAAC,SAAS,CAAC;MAChB;IACF;IAEA,MAAM9C,aAAa,GAAG,IAAI,CAAC/G,aAAa,CAAC4I,MAAM,CAACT,IAAI,IAAIA,IAAI,CAACrJ,QAAQ,CAAC;IACtE,IAAIiI,aAAa,CAAC9G,MAAM,KAAK,CAAC,EAAE;MAC9B4J,KAAK,CAAC,WAAW,CAAC;MAClB;IACF;IAEA;IACA,IAAI,CAACC,cAAc,CAAC,IAAI,CAACpK,WAAW,CAACP,IAAI,EAAE,IAAI,CAACO,WAAW,CAACL,WAAW,EAAE0H,aAAa,CAAC;EACzF;EAEA;EACA+C,cAAcA,CAAC3K,IAAY,EAAEE,WAAmB,EAAE0H,aAAoB;IACpE;IACA,MAAMgD,gBAAgB,GAAqB;MACzC3B,WAAW,EAAE,IAAI;MAAE;MACnBZ,aAAa,EAAErI,IAAI,CAACsJ,IAAI,EAAE;MAC1BpB,aAAa,EAAE,IAAI,CAACb,YAAY;MAAE;MAClCwD,OAAO,EAAE,CAAC;MAAE;MACZC,OAAO,EAAElD,aAAa,CAACmB,GAAG,CAACC,IAAI,KAAK;QAClC+B,iBAAiB,EAAE,IAAI;QAAE;QACzBnG,UAAU,EAAE,IAAI,CAACoG,QAAQ,CAAChC,IAAI,CAAC;QAAE;QACjCrE,YAAY,EAAE,IAAI,CAACsG,YAAY,CAACjC,IAAI,CAAC,CAAC;OACZ;KAC7B;IAEDV,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEqC,gBAAgB,CAAC;IAErD;IACA,IAAI,CAACxD,eAAe,CAAC8D,gCAAgC,CAAC;MACpDzC,IAAI,EAAEmC;KACP,CAAC,CAAClC,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACAP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEK,QAAQ,CAAC;UAEhC;UACA,IAAI,CAAC,IAAI,CAACtB,eAAe,EAAE;YACzB,IAAI,CAACQ,aAAa,EAAE;UACtB;UAEA;UACA,IAAI,CAACH,WAAW,GAAG,KAAK;UACxB+C,KAAK,CAAC,OAAO1K,IAAI,cAAc4H,aAAa,CAAC9G,MAAM,MAAM,CAAC;UAE1D;UACA,IAAI,IAAI,CAACwG,eAAe,EAAE;YACxB,IAAI,CAACE,KAAK,CAAC2D,IAAI,EAAE;UACnB;QACF,CAAC,MAAM;UACL;UACA7C,OAAO,CAACc,KAAK,CAAC,SAAS,EAAER,QAAQ,CAACS,OAAO,CAAC;UAC1CqB,KAAK,CAAC,UAAU9B,QAAQ,CAACS,OAAO,IAAI,MAAM,EAAE,CAAC;QAC/C;MACF,CAAC;MACDD,KAAK,EAAGA,KAAK,IAAI;QACf;QACAd,OAAO,CAACc,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CsB,KAAK,CAAC,wBAAwB,CAAC;MACjC;KACD,CAAC;EACJ;EAEA;EACQM,QAAQA,CAAChC,IAAS;IACxB,OAAOA,IAAI,CAACoC,cAAc,IAAIpC,IAAI,CAACqC,EAAE,IAAIrC,IAAI,CAACsC,EAAE,IAAI,CAAC;EACvD;EAEA;EACQL,YAAYA,CAACM,KAAW;IAC9B;IACA,QAAQ,IAAI,CAAClE,YAAY;MACvB,KAAK,CAAC;QAAE;QACN,OAAO,cAAc;MACvB;QACE,OAAO,MAAM;IACjB;EACF;EAEA;EACQmE,aAAaA,CAACxC,IAAS;IAC7B,OAAOA,IAAI,CAACjJ,YAAY,IAAIiJ,IAAI,CAAChJ,IAAI,IAAIgJ,IAAI,CAACyC,KAAK,IAAI,EAAE;EAC3D;EAEA;EACA1J,gBAAgBA,CAAC2H,QAAkB;IACjC,IAAI,CAACjG,gBAAgB,GAAGiG,QAAQ;IAChC,IAAI,CAACnC,cAAc,CAAC4D,IAAI,CAACzB,QAAQ,CAAC;IAElC;IACA,IAAIA,QAAQ,CAAC/H,UAAU,EAAE;MACvB,IAAI,CAAC+J,mBAAmB,CAAChC,QAAQ,CAAC/H,UAAU,CAAC;IAC/C;IAEA,IAAI,CAACgK,sBAAsB,EAAE;EAC/B;EAEA;EACAD,mBAAmBA,CAACE,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAEhN,aAAsB;IACnF,MAAMiN,IAAI,GAA8B;MACtCF,UAAU,EAAEA;KACb;IAEDtD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuD,IAAI,CAAC;IAElD;IACA,IAAI,CAAC1E,eAAe,CAAC2E,yCAAyC,CAAC;MAC7DtD,IAAI,EAAEqD;KACP,CAAC,CAACpD,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjDR,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,QAAQ,CAAC;UAElC;UACA,IAAIoD,UAAU,GAAGpD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C+B,iBAAiB,EAAE/B,IAAI,CAAC+B,iBAAiB,IAAI,CAAC;YAC9C9B,WAAW,EAAED,IAAI,CAACC,WAAW,IAAI2C,UAAU;YAC3ChH,UAAU,EAAEoE,IAAI,CAACpE,UAAU,IAAI,CAAC;YAChCD,YAAY,EAAEqE,IAAI,CAACrE,YAAY,IAAI,EAAE;YACrCsH,KAAK,EAAEC,SAAS;YAChB7H,OAAO,EAAE6H,SAAS;YAClBpH,SAAS,EAAE,IAAIoE,IAAI,EAAE,CAACiD,WAAW,EAAE;YACnCC,QAAQ,EAAE,IAAI;YACdpI,SAAS,EAAEkI,SAAS;YACpB9H,UAAU,EAAE8H,SAAS;YACrBjI,SAAS,EAAEiI,SAAS;YACpBhI,KAAK,EAAEgI;WACe,EAAC;UAEzB;UACA,IAAIrN,aAAa,IAAIA,aAAa,CAACyK,IAAI,EAAE,EAAE;YACzC0C,UAAU,GAAGA,UAAU,CAACvC,MAAM,CAAC4C,MAAM,IACnCA,MAAM,CAAC1H,YAAY,CAAC6E,WAAW,EAAE,CAACG,QAAQ,CAAC9K,aAAa,CAAC2K,WAAW,EAAE,CAAC,CACxE;UACH;UAEA;UACA,MAAMQ,UAAU,GAAG,CAAC6B,SAAS,GAAG,CAAC,IAAI,IAAI,CAACnI,gBAAgB,CAACtC,QAAQ;UACnE,MAAM6I,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACtG,gBAAgB,CAACtC,QAAQ;UAC5D,MAAMkL,YAAY,GAAGN,UAAU,CAAC9B,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;UAE3D;UACA,IAAI,CAACjF,0BAA0B,GAAGsH,YAAY;UAC9C,IAAI,CAAC5I,gBAAgB,CAACnC,UAAU,GAAGyK,UAAU,CAAClL,MAAM;UACpD,IAAI,CAAC4C,gBAAgB,CAACT,UAAU,GAAG5B,IAAI,CAACwI,IAAI,CAACmC,UAAU,CAAClL,MAAM,GAAG,IAAI,CAAC4C,gBAAgB,CAACtC,QAAQ,CAAC;UAChG,IAAI,CAACsC,gBAAgB,CAACvC,WAAW,GAAG0K,SAAS;QAC/C,CAAC,MAAM;UACLvD,OAAO,CAACc,KAAK,CAAC,WAAW,EAAER,QAAQ,CAACS,OAAO,CAAC;UAC5C,IAAI,CAACrE,0BAA0B,GAAG,EAAE;UACpC,IAAI,CAACtB,gBAAgB,CAACnC,UAAU,GAAG,CAAC;UACpC,IAAI,CAACmC,gBAAgB,CAACT,UAAU,GAAG,CAAC;UACpC,IAAI,CAACS,gBAAgB,CAACvC,WAAW,GAAG,CAAC;QACvC;MACF,CAAC;MACDiI,KAAK,EAAGA,KAAK,IAAI;QACfd,OAAO,CAACc,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAACpE,0BAA0B,GAAG,EAAE;QACpC,IAAI,CAACtB,gBAAgB,CAACnC,UAAU,GAAG,CAAC;QACpC,IAAI,CAACmC,gBAAgB,CAACT,UAAU,GAAG,CAAC;QACpC,IAAI,CAACS,gBAAgB,CAACvC,WAAW,GAAG,CAAC;QAErC;QACAmH,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QACtB,IAAI,CAACgE,uBAAuB,CAACX,UAAU,EAAEC,SAAS,EAAEhN,aAAa,CAAC;MACpE;KACD,CAAC;EACJ;EAEA;EACQ0N,uBAAuBA,CAACX,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAEhN,aAAsB;IAC/F;IACA2N,UAAU,CAAC,MAAK;MACd;MACA,MAAMC,WAAW,GAAyB,EAAE;MAC5C,MAAMC,gBAAgB,GAAG,CAAC,CAAC,CAAC;MAE5B,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAImC,gBAAgB,EAAEnC,CAAC,EAAE,EAAE;QAC1C,MAAM8B,MAAM,GAAuB;UACjCtB,iBAAiB,EAAEa,UAAU,GAAG,GAAG,GAAGrB,CAAC;UACvCtB,WAAW,EAAE2C,UAAU;UACvBhH,UAAU,EAAEgH,UAAU,GAAG,IAAI,GAAGrB,CAAC;UACjC5F,YAAY,EAAE,OAAOgI,MAAM,CAACC,YAAY,CAAC,EAAE,GAAIhB,UAAU,GAAG,EAAG,GAAG,CAAC,CAAC,IAAIrB,CAAC,EAAE;UAC3E0B,KAAK,EAAE1B,CAAC;UACRlG,OAAO,EAAEkG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQA,CAAC,EAAE,GAAG2B,SAAS;UAC9CpH,SAAS,EAAE,IAAIoE,IAAI,CAACA,IAAI,CAAC2D,GAAG,EAAE,GAAGxL,IAAI,CAACyL,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACX,WAAW,EAAE;UACxFC,QAAQ,EAAE,OAAO;UACjBpI,SAAS,EAAEuG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM;UACxCnG,UAAU,EAAE/C,IAAI,CAAC0L,KAAK,CAAC1L,IAAI,CAACyL,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI;UACpD7I,SAAS,EAAE5C,IAAI,CAAC0L,KAAK,CAAC1L,IAAI,CAACyL,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;UAC7C5I,KAAK,EAAEqG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG;SACjD;QACDkC,WAAW,CAACjC,IAAI,CAAC6B,MAAM,CAAC;MAC1B;MAEA;MACA,IAAIW,eAAe,GAAGP,WAAW;MACjC,IAAI5N,aAAa,IAAIA,aAAa,CAACyK,IAAI,EAAE,EAAE;QACzC0D,eAAe,GAAGP,WAAW,CAAChD,MAAM,CAAC4C,MAAM,IACzCA,MAAM,CAAC1H,YAAY,CAAC6E,WAAW,EAAE,CAACG,QAAQ,CAAC9K,aAAa,CAAC2K,WAAW,EAAE,CAAC,IACtE6C,MAAM,CAAChI,OAAO,IAAIgI,MAAM,CAAChI,OAAO,CAACmF,WAAW,EAAE,CAACG,QAAQ,CAAC9K,aAAa,CAAC2K,WAAW,EAAE,CAAE,IACrF6C,MAAM,CAACrI,SAAS,IAAIqI,MAAM,CAACrI,SAAS,CAACwF,WAAW,EAAE,CAACG,QAAQ,CAAC9K,aAAa,CAAC2K,WAAW,EAAE,CAAE,CAC3F;MACH;MAEA;MACA,MAAMQ,UAAU,GAAG,CAAC6B,SAAS,GAAG,CAAC,IAAI,IAAI,CAACnI,gBAAgB,CAACtC,QAAQ;MACnE,MAAM6I,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACtG,gBAAgB,CAACtC,QAAQ;MAC5D,MAAMkL,YAAY,GAAGU,eAAe,CAAC9C,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;MAEhE;MACA,IAAI,CAACjF,0BAA0B,GAAGsH,YAAY;MAC9C,IAAI,CAAC5I,gBAAgB,CAACnC,UAAU,GAAGyL,eAAe,CAAClM,MAAM;MACzD,IAAI,CAAC4C,gBAAgB,CAACT,UAAU,GAAG5B,IAAI,CAACwI,IAAI,CAACmD,eAAe,CAAClM,MAAM,GAAG,IAAI,CAAC4C,gBAAgB,CAACtC,QAAQ,CAAC;MACrG,IAAI,CAACsC,gBAAgB,CAACvC,WAAW,GAAG0K,SAAS;MAE7CvD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBqD,UAAU;QACVrK,UAAU,EAAEyL,eAAe,CAAClM,MAAM;QAClCK,WAAW,EAAE0K,SAAS;QACtBoB,OAAO,EAAEX;OACV,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAxI,qBAAqBA,CAACyF,OAAe;IACnC,IAAI,CAAC1F,mBAAmB,GAAG0F,OAAO;IAClC,IAAI,IAAI,CAAC9F,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC9B,UAAU,EAAE;MAC7D,IAAI,CAAC+J,mBAAmB,CAAC,IAAI,CAACjI,gBAAgB,CAAC9B,UAAU,EAAE,CAAC,EAAE4H,OAAO,CAAC;IACxE;EACF;EAEA;EACAoC,sBAAsBA,CAAA;IACpB;IACA,IAAI,IAAI,CAAC3G,0BAA0B,CAAClE,MAAM,GAAG,CAAC,EAAE;MAC9C;IACF;IAEA;IACA,MAAMmM,OAAO,GAAG,IAAI,CAACzH,sBAAsB;IAC3C,IAAI,CAAC9B,gBAAgB,CAACnC,UAAU,GAAG0L,OAAO,CAACnM,MAAM;IACjD,IAAI,CAAC4C,gBAAgB,CAACT,UAAU,GAAG5B,IAAI,CAACwI,IAAI,CAAC,IAAI,CAACnG,gBAAgB,CAACnC,UAAU,GAAG,IAAI,CAACmC,gBAAgB,CAACtC,QAAQ,CAAC;IAC/G,IAAI,CAACsC,gBAAgB,CAACvC,WAAW,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC+L,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMD,OAAO,GAAG,IAAI,CAACzH,sBAAsB;IAC3C,MAAMwE,UAAU,GAAG,CAAC,IAAI,CAACtG,gBAAgB,CAACvC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACuC,gBAAgB,CAACtC,QAAQ;IAC3F,MAAM6I,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACtG,gBAAgB,CAACtC,QAAQ;IAC5D,IAAI,CAACkE,gBAAgB,GAAG2H,OAAO,CAAC/C,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC7D;EAEA;EACApE,cAAcA,CAACsE,IAAY;IACzB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACzG,gBAAgB,CAACT,UAAU,EAAE;MACzD;MACA,IAAI,IAAI,CAACQ,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC9B,UAAU,IAAI,IAAI,CAACqD,0BAA0B,CAAClE,MAAM,GAAG,CAAC,EAAE;QAC3G,IAAI,CAAC4K,mBAAmB,CAAC,IAAI,CAACjI,gBAAgB,CAAC9B,UAAU,EAAEwI,IAAI,EAAE,IAAI,CAACtG,mBAAmB,CAAC;MAC5F,CAAC,MAAM;QACL;QACA,IAAI,CAACH,gBAAgB,CAACvC,WAAW,GAAGgJ,IAAI;QACxC,IAAI,CAAC+C,sBAAsB,EAAE;MAC/B;IACF;EACF;EAEA;EACAhH,oBAAoBA,CAAA;IAClB,MAAMkE,KAAK,GAAa,EAAE;IAC1B,MAAMnH,UAAU,GAAG,IAAI,CAACS,gBAAgB,CAACT,UAAU;IACnD,MAAM9B,WAAW,GAAG,IAAI,CAACuC,gBAAgB,CAACvC,WAAW;IAErD;IACA,MAAMkJ,SAAS,GAAGhJ,IAAI,CAACyI,GAAG,CAAC,CAAC,EAAE3I,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMmJ,OAAO,GAAGjJ,IAAI,CAACC,GAAG,CAAC2B,UAAU,EAAE9B,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIoJ,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACA+C,OAAOA,CAAA;IACL,IAAI,CAAC3F,KAAK,CAAC2D,IAAI,EAAE;EACnB;EAEA;EACAvJ,gBAAgBA,CAACwL,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACA,MAAMG,kBAAkB,GAAwB;MAC9CtE,WAAW,EAAEmE;KACd;IAED9E,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEgF,kBAAkB,CAAC;IAEzD;IACA,IAAI,CAACnG,eAAe,CAACoG,kCAAkC,CAAC;MACtD/E,IAAI,EAAE8E;KACP,CAAC,CAAC7E,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACAP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEK,QAAQ,CAAC;UAEhC;UACA,IAAI,CAACd,aAAa,EAAE;UAEpB;UACA,IAAI,IAAI,CAACrE,gBAAgB,EAAE9B,UAAU,KAAKyL,UAAU,EAAE;YACpD,IAAI,CAAC3J,gBAAgB,GAAG,IAAI;UAC9B;UAEAiH,KAAK,CAAC,OAAO,CAAC;QAChB,CAAC,MAAM;UACL;UACApC,OAAO,CAACc,KAAK,CAAC,SAAS,EAAER,QAAQ,CAACS,OAAO,CAAC;UAC1CqB,KAAK,CAAC,UAAU9B,QAAQ,CAACS,OAAO,IAAI,MAAM,EAAE,CAAC;QAC/C;MACF,CAAC;MACDD,KAAK,EAAGA,KAAK,IAAI;QACf;QACAd,OAAO,CAACc,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDsB,KAAK,CAAC,wBAAwB,CAAC;MACjC;KACD,CAAC;EACJ;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA;EACArE,mBAAmBA,CAAA;IACjB,IAAI,CAAC5C,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAI+B,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC/B,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACiE,eAAe,CAAC+B,MAAM,CAACgE,CAAC,IAAIA,CAAC,CAAC9L,UAAU,KAAK,IAAI,CAAC8B,gBAAiB,CAAC9B,UAAU,CAAC;EAC7F;EAEA;EACA6B,iBAAiBA,CAACkK,KAAa,EAAEhE,QAAkB;IACjD,OAAOA,QAAQ,CAAC/H,UAAU,IAAI+L,KAAK;EACrC;;;uCAnmBWxG,uBAAuB,EAAA1J,EAAA,CAAAmQ,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvB3G,uBAAuB;MAAA4G,SAAA;MAAAC,MAAA;QAAAlN,aAAA;QAAAwG,YAAA;QAAAC,eAAA;MAAA;MAAA0G,OAAA;QAAAzG,cAAA;QAAAC,KAAA;MAAA;MAAAyG,UAAA;MAAAC,QAAA,GAAA1Q,EAAA,CAAA2Q,oBAAA,EAAA3Q,EAAA,CAAA4Q,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7E,QAAA,WAAA8E,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX9BjR,EAHN,CAAAC,cAAA,iBAA+D,qBAC7C,aACiD,YAC5C;UAAAD,EAAA,CAAAW,MAAA,GAAuC;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAC7DZ,EAAA,CAAA0B,UAAA,IAAAyP,yCAAA,oBAA0F;UAI9FnR,EADE,CAAAY,YAAA,EAAM,EACS;UACjBZ,EAAA,CAAAC,cAAA,sBAAsC;UA2KpCD,EAzKA,CAAA0B,UAAA,IAAA0P,sCAAA,iBAA4D,IAAAC,sCAAA,kBAgBJ,IAAAC,sCAAA,kBA2DiB,KAAAC,uCAAA,mBA8FO;UAqIlFvR,EAAA,CAAAY,YAAA,EAAe;UAGXZ,EAFJ,CAAAC,cAAA,sBAAgB,eACW,kBAC+B;UAApBD,EAAA,CAAAE,UAAA,mBAAAsR,0DAAA;YAAA,OAASN,GAAA,CAAAvB,OAAA,EAAS;UAAA,EAAC;UACnD3P,EAAA,CAAAU,SAAA,aAAiC;UAAAV,EAAA,CAAAW,MAAA,qBACnC;UAGNX,EAHM,CAAAY,YAAA,EAAS,EACL,EACS,EACT;;;UA9TaZ,EAAA,CAAA4B,SAAA,GAAuC;UAAvC5B,EAAA,CAAAsC,iBAAA,CAAA4O,GAAA,CAAApH,eAAA,2DAAuC;UACU9J,EAAA,CAAA4B,SAAA,EAAsB;UAAtB5B,EAAA,CAAA8B,UAAA,UAAAoP,GAAA,CAAApH,eAAA,CAAsB;UAOtD9J,EAAA,CAAA4B,SAAA,GAAsB;UAAtB5B,EAAA,CAAA8B,UAAA,UAAAoP,GAAA,CAAApH,eAAA,CAAsB;UAgBpD9J,EAAA,CAAA4B,SAAA,EAAiB;UAAjB5B,EAAA,CAAA8B,UAAA,SAAAoP,GAAA,CAAA/G,WAAA,CAAiB;UA2DKnK,EAAA,CAAA4B,SAAA,EAA2C;UAA3C5B,EAAA,CAAA8B,UAAA,UAAAoP,GAAA,CAAAjL,gBAAA,KAAAiL,GAAA,CAAApH,eAAA,CAA2C;UA8FjE9J,EAAA,CAAA4B,SAAA,EAA0C;UAA1C5B,EAAA,CAAA8B,UAAA,SAAAoP,GAAA,CAAAjL,gBAAA,KAAAiL,GAAA,CAAApH,eAAA,CAA0C;;;qBDxKxClK,YAAY,EAAA6R,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAAH,EAAA,CAAAI,QAAA,EAAEhS,WAAW,EAAAiS,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,4BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAN,EAAA,CAAAO,kBAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,MAAA,EAAEzS,YAAY,EAAA0S,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAE7S,cAAc;MAAA8S,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}