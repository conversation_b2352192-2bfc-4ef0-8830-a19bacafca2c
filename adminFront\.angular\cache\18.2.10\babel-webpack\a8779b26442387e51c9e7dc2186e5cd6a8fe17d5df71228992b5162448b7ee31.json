{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction TemplateViewerComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_19_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(2, \"i\", 29);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"small\", 31);\n    i0.ɵɵelement(2, \"i\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u627E\\u5230 \", ctx_r1.filteredTemplates.length, \" \\u500B\\u76F8\\u95DC\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"small\", 34);\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵtext(3, \"\\u672A\\u627E\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 4)(2, \"div\", 43)(3, \"span\", 44);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 45)(6, \"small\", 8);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A\\u7B2C \", (ctx_r1.templatePagination.currentPage - 1) * ctx_r1.templatePagination.pageSize + 1, \" - \", ctx_r1.Math.min(ctx_r1.templatePagination.currentPage * ctx_r1.templatePagination.pageSize, ctx_r1.templatePagination.totalItems), \" \\u9805\\uFF0C \\u5171 \", ctx_r1.templatePagination.totalItems, \" \\u9805\\u6A21\\u677F \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\\u7B2C \", ctx_r1.templatePagination.currentPage, \" / \", ctx_r1.templatePagination.totalPages, \" \\u9801\");\n  }\n}\nfunction TemplateViewerComponent_div_22_div_3_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_3_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const tpl_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(tpl_r4.TemplateID && ctx_r1.onDeleteTemplate(tpl_r4.TemplateID));\n    });\n    i0.ɵɵelement(1, \"i\", 57);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_22_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 48)(3, \"h6\", 49);\n    i0.ɵɵelement(4, \"i\", 50);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 52)(9, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_3_Template_button_click_9_listener() {\n      const tpl_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r4));\n    });\n    i0.ɵɵelement(10, \"i\", 54);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, TemplateViewerComponent_div_22_div_3_button_13_Template, 4, 0, \"button\", 55);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tpl_r4 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", tpl_r4.TemplateName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tpl_r4.Description || \"\\u7121\\u63CF\\u8FF0\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", tpl_r4.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_div_22_div_4_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 16);\n  }\n}\nfunction TemplateViewerComponent_div_22_div_4_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 65);\n  }\n}\nfunction TemplateViewerComponent_div_22_div_4_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p\", 66);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5617\\u8A66\\u5176\\u4ED6\\u95DC\\u9375\\u5B57\\u6216 \");\n    i0.ɵɵelementStart(2, \"a\", 67);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_4_p_7_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(3, \"\\u6E05\\u9664\\u641C\\u5C0B\\u689D\\u4EF6\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_22_div_4_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 66);\n    i0.ɵɵtext(1, \" \\u76EE\\u524D\\u9084\\u6C92\\u6709\\u5EFA\\u7ACB\\u4EFB\\u4F55\\u6A21\\u677F\\uFF0C\\u8ACB\\u5148\\u5EFA\\u7ACB\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_22_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"div\", 60);\n    i0.ɵɵtemplate(3, TemplateViewerComponent_div_22_div_4_i_3_Template, 1, 0, \"i\", 61)(4, TemplateViewerComponent_div_22_div_4_i_4_Template, 1, 0, \"i\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, TemplateViewerComponent_div_22_div_4_p_7_Template, 4, 0, \"p\", 64)(8, TemplateViewerComponent_div_22_div_4_p_8_Template, 2, 0, \"p\", 64);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u66AB\\u7121\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_22_div_5_li_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 74)(1, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_5_li_13_Template_button_click_1_listener() {\n      const page_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(page_r9));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r9 === ctx_r1.templatePagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r9);\n  }\n}\nfunction TemplateViewerComponent_div_22_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69)(2, \"div\", 70)(3, \"span\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"nav\", 72)(6, \"ul\", 73)(7, \"li\", 74)(8, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_5_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(1));\n    });\n    i0.ɵɵelement(9, \"i\", 76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"li\", 74)(11, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_5_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage - 1));\n    });\n    i0.ɵɵelement(12, \"i\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, TemplateViewerComponent_div_22_div_5_li_13_Template, 3, 3, \"li\", 79);\n    i0.ɵɵelementStart(14, \"li\", 74)(15, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_5_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage + 1));\n    });\n    i0.ɵɵelement(16, \"i\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"li\", 74)(18, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_5_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.totalPages));\n    });\n    i0.ɵɵelement(19, \"i\", 83);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.templatePagination.currentPage, \" \\u9801\\uFF0C\\u5171 \", ctx_r1.templatePagination.totalPages, \" \\u9801 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getTemplatePageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_22_div_1_Template, 8, 5, \"div\", 37);\n    i0.ɵɵelementStart(2, \"div\", 38);\n    i0.ɵɵtemplate(3, TemplateViewerComponent_div_22_div_3_Template, 14, 3, \"div\", 39)(4, TemplateViewerComponent_div_22_div_4_Template, 9, 5, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TemplateViewerComponent_div_22_div_5_Template, 20, 15, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalItems > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedTemplates)(\"ngForTrackBy\", ctx_r1.trackByTemplateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.paginatedTemplates || ctx_r1.paginatedTemplates.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalPages > 1);\n  }\n}\nfunction TemplateViewerComponent_div_23_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 109);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedTemplate.Description, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_23_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"span\", 96);\n    i0.ɵɵtext(2, \"\\u9801\\u6578\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 97);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.detailPagination.currentPage, \" / \", ctx_r1.detailPagination.totalPages, \"\");\n  }\n}\nfunction TemplateViewerComponent_div_23_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_23_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.detailSearchKeyword = \"\";\n      return i0.ɵɵresetView(ctx_r1.searchTemplateDetails(\"\"));\n    });\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_23_div_29_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 114);\n    i0.ɵɵelement(1, \"i\", 115);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u627E\\u5230 \", ctx_r1.detailPagination.totalItems, \" \\u500B\\u76F8\\u95DC\\u9805\\u76EE \");\n  }\n}\nfunction TemplateViewerComponent_div_23_div_29_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 34);\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵtext(2, \"\\u672A\\u627E\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_23_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 111);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_23_div_29_small_1_Template, 3, 1, \"small\", 112)(2, TemplateViewerComponent_div_23_div_29_small_2_Template, 3, 0, \"small\", 113);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetailsData.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetailsData.length === 0);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_30_div_1_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 134);\n    i0.ɵɵelement(1, \"i\", 135);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r12.CGroupName);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_30_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 136);\n    i0.ɵɵelement(1, \"i\", 137);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r12.CCategory);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_30_div_1_div_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 143)(1, \"span\", 144);\n    i0.ɵɵtext(2, \"\\u55AE\\u50F9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 145);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind1(5, 1, detail_r12.CUnitPrice), \"\");\n  }\n}\nfunction TemplateViewerComponent_div_23_div_30_div_1_div_21_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146)(1, \"span\", 144);\n    i0.ɵɵtext(2, \"\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 147);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", detail_r12.CQuantity, \" \", detail_r12.CUnit, \"\");\n  }\n}\nfunction TemplateViewerComponent_div_23_div_30_div_1_div_21_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148)(1, \"div\", 149);\n    i0.ɵɵelement(2, \"i\", 150);\n    i0.ɵɵelementStart(3, \"span\", 151);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(detail_r12.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_30_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 138)(1, \"div\", 139);\n    i0.ɵɵtemplate(2, TemplateViewerComponent_div_23_div_30_div_1_div_21_div_2_Template, 6, 3, \"div\", 140)(3, TemplateViewerComponent_div_23_div_30_div_1_div_21_div_3_Template, 5, 2, \"div\", 141);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TemplateViewerComponent_div_23_div_30_div_1_div_21_div_4_Template, 5, 1, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r12.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r12.CQuantity && detail_r12.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r12.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"div\", 119)(2, \"div\", 120)(3, \"div\", 121)(4, \"span\", 122);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 123)(7, \"h6\", 124);\n    i0.ɵɵelement(8, \"i\", 125);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 126)(11, \"span\", 127);\n    i0.ɵɵelement(12, \"i\", 128);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, TemplateViewerComponent_div_23_div_30_div_1_span_15_Template, 4, 1, \"span\", 129)(16, TemplateViewerComponent_div_23_div_30_div_1_span_16_Template, 4, 1, \"span\", 130);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 131)(18, \"span\", 132);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, TemplateViewerComponent_div_23_div_30_div_1_div_21_Template, 5, 3, \"div\", 133);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r12 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r13 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", detail_r12.CReleateName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(detail_r12.CReleateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r12.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r12.CCategory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 7, detail_r12.CCreateDt, \"MM/dd\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r12.CUnitPrice || detail_r12.CQuantity || detail_r12.CUnit || detail_r12.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_23_div_30_div_1_Template, 22, 10, \"div\", 117);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetailsData);\n  }\n}\nfunction TemplateViewerComponent_div_23_ng_template_31_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 155)(1, \"div\", 156)(2, \"span\", 157);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 158)(5, \"div\", 159)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 160);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r14 = ctx.$implicit;\n    const i_r15 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r15 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", detail_r14.FieldName, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", detail_r14.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_23_ng_template_31_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 153);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_23_ng_template_31_div_0_div_1_Template, 10, 3, \"div\", 154);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedDetails);\n  }\n}\nfunction TemplateViewerComponent_div_23_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TemplateViewerComponent_div_23_ng_template_31_div_0_Template, 2, 1, \"div\", 152);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const noDetails_r16 = i0.ɵɵreference(35);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetails.length > 0)(\"ngIfElse\", noDetails_r16);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_33_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 74)(1, \"button\", 165);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_23_div_33_li_6_Template_button_click_1_listener() {\n      const page_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(page_r19));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r19 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r19 === ctx_r1.detailPagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r19);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 161)(1, \"nav\", 162)(2, \"ul\", 163)(3, \"li\", 74)(4, \"button\", 164);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_23_div_33_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_23_div_33_li_6_Template, 3, 3, \"li\", 79);\n    i0.ɵɵelementStart(7, \"li\", 74)(8, \"button\", 164);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_23_div_33_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 81);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getDetailPageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_23_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 166);\n    i0.ɵɵelement(1, \"i\", 167);\n    i0.ɵɵelementStart(2, \"p\", 168);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"div\", 87)(3, \"div\", 88)(4, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_23_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵelement(5, \"i\", 90);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 91)(7, \"h5\", 92);\n    i0.ɵɵelement(8, \"i\", 50);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, TemplateViewerComponent_div_23_p_10_Template, 2, 1, \"p\", 93);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 94)(12, \"div\", 95)(13, \"span\", 96);\n    i0.ɵɵtext(14, \"\\u9805\\u76EE\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 97);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, TemplateViewerComponent_div_23_div_17_Template, 5, 2, \"div\", 98);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 99)(19, \"div\", 100)(20, \"div\", 101)(21, \"div\", 102)(22, \"div\", 15);\n    i0.ɵɵelement(23, \"i\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"input\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_23_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.detailSearchKeyword, $event) || (ctx_r1.detailSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateViewerComponent_div_23_Template_input_keyup_enter_24_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.searchTemplateDetails(ctx_r1.detailSearchKeyword));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 27)(26, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_23_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.searchTemplateDetails(ctx_r1.detailSearchKeyword));\n    });\n    i0.ɵɵelement(27, \"i\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, TemplateViewerComponent_div_23_button_28_Template, 2, 0, \"button\", 105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, TemplateViewerComponent_div_23_div_29_Template, 3, 2, \"div\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(30, TemplateViewerComponent_div_23_div_30_Template, 2, 1, \"div\", 107)(31, TemplateViewerComponent_div_23_ng_template_31_Template, 1, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(33, TemplateViewerComponent_div_23_div_33_Template, 10, 7, \"div\", 108)(34, TemplateViewerComponent_div_23_ng_template_34_Template, 4, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const checkOldDetails_r20 = i0.ɵɵreference(32);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedTemplate.TemplateName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTemplate.Description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.currentTemplateDetailsData.length > 0 ? ctx_r1.detailPagination.totalItems : ctx_r1.currentTemplateDetails.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetailsData.length > 0)(\"ngIfElse\", checkOldDetails_r20);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.templateType = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 公開Math對象供模板使用\n    this.Math = Math;\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = []; // 保留用於向後相容\n    this.selectedTemplate = null;\n    // 新的詳情資料管理\n    this.currentTemplateDetailsData = [];\n    this.detailSearchKeyword = '';\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 分頁功能 - 模板列表\n    this.templatePagination = {\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedTemplates = [];\n    // 分頁功能 - 模板詳情\n    this.detailPagination = {\n      currentPage: 1,\n      pageSize: 5,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedDetails = [];\n  }\n  ngOnInit() {\n    this.loadTemplates();\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 載入模板列表 - 使用真實的 API 調用\n  loadTemplates() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      PageIndex: 1,\n      // 暫時載入第一頁\n      PageSize: 100,\n      // 暫時載入較多資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateList API\n    this.templateService.apiTemplateGetTemplateListPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // API 調用成功\n          // 轉換 API 回應為內部格式\n          this.templates = response.Entries.map(item => ({\n            TemplateID: item.CTemplateId,\n            TemplateName: item.CTemplateName || '',\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\n          }));\n          // 更新過濾列表\n          this.updateFilteredTemplates();\n          // 清空詳情資料，改為按需載入\n          this.templateDetails = [];\n          this.currentTemplateDetailsData = [];\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n          this.templateDetails = [];\n          this.updateFilteredTemplates();\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n        this.templates = [];\n        this.templateDetails = [];\n        this.updateFilteredTemplates();\n      }\n    });\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n    this.updateTemplatePagination();\n  }\n  // 更新模板分頁\n  updateTemplatePagination() {\n    this.templatePagination.totalItems = this.filteredTemplates.length;\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\n    // 確保當前頁面不超過總頁數\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\n    }\n    this.updatePaginatedTemplates();\n  }\n  // 更新分頁後的模板列表\n  updatePaginatedTemplates() {\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\n    const endIndex = startIndex + this.templatePagination.pageSize;\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\n  }\n  // 模板分頁導航\n  goToTemplatePage(page) {\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = page;\n      this.updatePaginatedTemplates();\n    }\n  }\n  // 獲取模板分頁頁碼數組\n  getTemplatePageNumbers() {\n    const pages = [];\n    const totalPages = this.templatePagination.totalPages;\n    const currentPage = this.templatePagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n    // 按需載入模板詳情\n    if (template.TemplateID) {\n      this.loadTemplateDetails(template.TemplateID);\n    }\n    this.updateDetailPagination();\n  }\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\n  loadTemplateDetails(templateId, pageIndex = 1, searchKeyword) {\n    const args = {\n      templateId: templateId\n    };\n    // 調用真實的 GetTemplateDetailById API\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\n          let allDetails = response.Entries.map(item => ({\n            CTemplateDetailId: item.CTemplateDetailId || 0,\n            CTemplateId: item.CTemplateId || templateId,\n            CReleateId: item.CReleateId || 0,\n            CReleateName: item.CReleateName || '',\n            CGroupName: item.CGroupName || '',\n            // 新增群組名稱欄位\n            CSort: undefined,\n            CRemark: undefined,\n            CCreateDt: new Date().toISOString(),\n            CCreator: '系統',\n            CCategory: undefined,\n            CUnitPrice: undefined,\n            CQuantity: undefined,\n            CUnit: undefined\n          }));\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\n          if (searchKeyword && searchKeyword.trim()) {\n            allDetails = allDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()));\n          }\n          // 前端分頁處理 (因為 API 不支援分頁參數)\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n          const endIndex = startIndex + this.detailPagination.pageSize;\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\n          // 更新詳情資料和分頁資訊\n          this.currentTemplateDetailsData = pagedDetails;\n          this.detailPagination.totalItems = allDetails.length;\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\n          this.detailPagination.currentPage = pageIndex;\n        } else {\n          this.currentTemplateDetailsData = [];\n          this.detailPagination.totalItems = 0;\n          this.detailPagination.totalPages = 0;\n          this.detailPagination.currentPage = 1;\n        }\n      },\n      error: () => {\n        this.currentTemplateDetailsData = [];\n        this.detailPagination.totalItems = 0;\n        this.detailPagination.totalPages = 0;\n        this.detailPagination.currentPage = 1;\n        // 如果 API 失敗，回退到模擬資料\n        this.loadTemplateDetailsMock(templateId, pageIndex, searchKeyword);\n      }\n    });\n  }\n  // 模擬載入模板詳情 (暫時使用，等待後端 API)\n  loadTemplateDetailsMock(templateId, pageIndex = 1, searchKeyword) {\n    // 模擬 API 延遲\n    setTimeout(() => {\n      // 生成模擬詳情資料\n      const mockDetails = [];\n      const itemsPerTemplate = 8; // 每個模板8個項目\n      for (let i = 1; i <= itemsPerTemplate; i++) {\n        const detail = {\n          CTemplateDetailId: templateId * 100 + i,\n          CTemplateId: templateId,\n          CReleateId: templateId * 1000 + i,\n          CReleateName: `工程項目${String.fromCharCode(64 + templateId % 26 + 1)}-${i}`,\n          CGroupName: i % 4 === 0 ? '結構工程' : i % 4 === 1 ? '機電工程' : i % 4 === 2 ? '裝修工程' : '其他工程',\n          // 新增群組名稱\n          CSort: i,\n          CRemark: i % 3 === 0 ? `備註說明 ${i}` : undefined,\n          CCreateDt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\n          CCreator: '系統管理員',\n          CCategory: i % 2 === 0 ? '基礎工程' : '進階工程',\n          CUnitPrice: Math.floor(Math.random() * 10000) + 1000,\n          CQuantity: Math.floor(Math.random() * 10) + 1,\n          CUnit: i % 3 === 0 ? '式' : i % 3 === 1 ? '個' : 'm²'\n        };\n        mockDetails.push(detail);\n      }\n      // 搜尋篩選\n      let filteredDetails = mockDetails;\n      if (searchKeyword && searchKeyword.trim()) {\n        filteredDetails = mockDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CRemark && detail.CRemark.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CCategory && detail.CCategory.toLowerCase().includes(searchKeyword.toLowerCase()));\n      }\n      // 分頁處理\n      const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n      const endIndex = startIndex + this.detailPagination.pageSize;\n      const pagedDetails = filteredDetails.slice(startIndex, endIndex);\n      // 更新資料\n      this.currentTemplateDetailsData = pagedDetails;\n      this.detailPagination.totalItems = filteredDetails.length;\n      this.detailPagination.totalPages = Math.ceil(filteredDetails.length / this.detailPagination.pageSize);\n      this.detailPagination.currentPage = pageIndex;\n    }, 300);\n  }\n  // 搜尋模板詳情\n  searchTemplateDetails(keyword) {\n    this.detailSearchKeyword = keyword;\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\n    }\n  }\n  // 更新詳情分頁 (保留向後相容)\n  updateDetailPagination() {\n    // 如果使用新的詳情資料，直接返回\n    if (this.currentTemplateDetailsData.length > 0) {\n      return;\n    }\n    // 向後相容：使用舊的詳情資料\n    const details = this.currentTemplateDetails;\n    this.detailPagination.totalItems = details.length;\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\n    this.detailPagination.currentPage = 1; // 重置到第一頁\n    this.updatePaginatedDetails();\n  }\n  // 更新分頁後的詳情列表\n  updatePaginatedDetails() {\n    const details = this.currentTemplateDetails;\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\n    const endIndex = startIndex + this.detailPagination.pageSize;\n    this.paginatedDetails = details.slice(startIndex, endIndex);\n  }\n  // 詳情分頁導航\n  goToDetailPage(page) {\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\n      // 如果使用新的詳情資料，重新載入\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\n      } else {\n        // 向後相容：使用舊的分頁邏輯\n        this.detailPagination.currentPage = page;\n        this.updatePaginatedDetails();\n      }\n    }\n  }\n  // 獲取詳情分頁頁碼數組\n  getDetailPageNumbers() {\n    const pages = [];\n    const totalPages = this.detailPagination.totalPages;\n    const currentPage = this.detailPagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // 刪除模板 - 使用真實的 API 調用\n  deleteTemplateById(templateID) {\n    // 準備 API 請求參數\n    const deleteTemplateArgs = {\n      CTemplateId: templateID\n    };\n    // 調用 DeleteTemplate API\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\n      body: deleteTemplateArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          // 重新載入模板列表\n          this.loadTemplates();\n          // 如果當前查看的模板被刪除，關閉詳情\n          if (this.selectedTemplate?.TemplateID === templateID) {\n            this.selectedTemplate = null;\n          }\n        } else {\n          // API 返回錯誤\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n      }\n    });\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/Template/GetTemplateList\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/Template/SaveTemplate\n   *    請求體: {\n   *      CTemplateName: string,\n   *      CTemplateType: number,  // 1=客變需求\n   *      CStatus: number,\n   *      Details: [{\n   *        CReleateId: number,        // 關聯主檔ID\n   *        CReleateName: string       // 關聯名稱\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    POST /api/Template/DeleteTemplate\n   *    請求體: { CTemplateId: number }\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        templateType: \"templateType\"\n      },\n      outputs: {\n        selectTemplate: \"selectTemplate\",\n        close: \"close\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 29,\n      vars: 7,\n      consts: [[\"checkOldDetails\", \"\"], [\"noDetails\", \"\"], [1, \"template-viewer-card\"], [1, \"template-viewer-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"header-title\"], [1, \"mb-0\"], [1, \"fas\", \"fa-layer-group\", \"mr-2\", \"text-primary\"], [1, \"text-muted\"], [1, \"header-actions\"], [1, \"badge\", \"badge-info\"], [1, \"template-viewer-body\"], [1, \"enhanced-search-container\", \"mb-4\"], [1, \"search-wrapper\"], [1, \"search-input-group\"], [1, \"search-icon\"], [1, \"fas\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u63CF\\u8FF0\\u6216\\u95DC\\u9375\\u5B57...\", 1, \"search-input\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [\"class\", \"search-actions\", 4, \"ngIf\"], [\"class\", \"search-suggestions\", 4, \"ngIf\"], [\"class\", \"search-no-results\", 4, \"ngIf\"], [\"class\", \"template-list-container\", 4, \"ngIf\"], [\"class\", \"template-detail-view\", 4, \"ngIf\"], [1, \"template-viewer-footer\"], [1, \"footer-actions\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [1, \"search-actions\"], [\"type\", \"button\", \"title\", \"\\u6E05\\u9664\\u641C\\u5C0B\", 1, \"clear-search-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"search-suggestions\"], [1, \"text-success\"], [1, \"fas\", \"fa-check-circle\", \"mr-1\"], [1, \"search-no-results\"], [1, \"text-warning\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"template-list-container\"], [\"class\", \"list-controls mb-3\", 4, \"ngIf\"], [1, \"template-cards-grid\"], [\"class\", \"template-card\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"empty-state-card\", 4, \"ngIf\"], [\"class\", \"enhanced-pagination-container mt-4\", 4, \"ngIf\"], [1, \"list-controls\", \"mb-3\"], [1, \"list-info\"], [1, \"info-text\"], [1, \"view-options\"], [1, \"template-card\"], [1, \"card-header\"], [1, \"template-info\"], [1, \"template-name\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\", \"text-primary\"], [1, \"template-description\"], [1, \"template-actions\"], [\"title\", \"\\u67E5\\u770B\\u8A73\\u60C5\", 1, \"action-btn\", \"view-btn\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"action-btn delete-btn\", \"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 1, \"action-btn\", \"delete-btn\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"empty-state-card\"], [1, \"empty-content\"], [1, \"empty-icon\"], [\"class\", \"fas fa-search\", 4, \"ngIf\"], [\"class\", \"fas fa-folder-open\", 4, \"ngIf\"], [1, \"empty-title\"], [\"class\", \"empty-description\", 4, \"ngIf\"], [1, \"fas\", \"fa-folder-open\"], [1, \"empty-description\"], [\"href\", \"javascript:void(0)\", 1, \"clear-link\", 3, \"click\"], [1, \"enhanced-pagination-container\", \"mt-4\"], [1, \"pagination-wrapper\"], [1, \"pagination-info\"], [1, \"page-info\"], [\"aria-label\", \"\\u6A21\\u677F\\u5217\\u8868\\u5206\\u9801\", 1, \"pagination-nav\"], [1, \"enhanced-pagination\"], [1, \"page-item\"], [\"title\", \"\\u7B2C\\u4E00\\u9801\", 1, \"page-btn\", \"first-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [\"title\", \"\\u4E0A\\u4E00\\u9801\", 1, \"page-btn\", \"prev-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"\\u4E0B\\u4E00\\u9801\", 1, \"page-btn\", \"next-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [\"title\", \"\\u6700\\u5F8C\\u4E00\\u9801\", 1, \"page-btn\", \"last-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"page-btn\", \"page-number\", 3, \"click\"], [1, \"template-detail-view\"], [1, \"detail-header\"], [1, \"detail-title-section\"], [1, \"back-button\"], [\"title\", \"\\u8FD4\\u56DE\\u6A21\\u677F\\u5217\\u8868\", 1, \"back-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"detail-title-info\"], [1, \"detail-title\"], [\"class\", \"detail-subtitle\", 4, \"ngIf\"], [1, \"detail-stats\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [1, \"detail-content\"], [1, \"detail-search-section\", \"mb-4\"], [1, \"detail-search-wrapper\"], [1, \"detail-search-input-group\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31\\u3001\\u7FA4\\u7D44\\u6216\\u5099\\u8A3B...\", 1, \"detail-search-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"title\", \"\\u641C\\u5C0B\", 1, \"search-btn\", 3, \"click\"], [\"class\", \"clear-btn\", \"title\", \"\\u6E05\\u9664\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"search-results-info\", 4, \"ngIf\"], [\"class\", \"enhanced-detail-list\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"detail-pagination mt-3\", 4, \"ngIf\"], [1, \"detail-subtitle\"], [\"title\", \"\\u6E05\\u9664\", 1, \"clear-btn\", 3, \"click\"], [1, \"search-results-info\"], [\"class\", \"text-info\", 4, \"ngIf\"], [\"class\", \"text-warning\", 4, \"ngIf\"], [1, \"text-info\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"enhanced-detail-list\"], [\"class\", \"enhanced-detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"enhanced-detail-item\"], [1, \"detail-item-card\"], [1, \"detail-item-header\"], [1, \"item-index\"], [1, \"index-badge\"], [1, \"item-main-info\"], [1, \"item-name\"], [1, \"fas\", \"fa-cog\", \"mr-2\", \"text-secondary\"], [1, \"item-meta\"], [1, \"meta-item\", \"id-meta\"], [1, \"fas\", \"fa-hashtag\"], [\"class\", \"meta-item group-meta\", 4, \"ngIf\"], [\"class\", \"meta-item category-meta\", 4, \"ngIf\"], [1, \"item-actions\"], [1, \"create-date\"], [\"class\", \"detail-item-body\", 4, \"ngIf\"], [1, \"meta-item\", \"group-meta\"], [1, \"fas\", \"fa-layer-group\"], [1, \"meta-item\", \"category-meta\"], [1, \"fas\", \"fa-tag\"], [1, \"detail-item-body\"], [1, \"item-details-grid\"], [\"class\", \"detail-group price-group\", 4, \"ngIf\"], [\"class\", \"detail-group quantity-group\", 4, \"ngIf\"], [\"class\", \"item-remark\", 4, \"ngIf\"], [1, \"detail-group\", \"price-group\"], [1, \"detail-label\"], [1, \"detail-value\", \"price-value\"], [1, \"detail-group\", \"quantity-group\"], [1, \"detail-value\", \"quantity-value\"], [1, \"item-remark\"], [1, \"remark-content\"], [1, \"fas\", \"fa-comment-alt\", \"mr-2\", \"text-muted\"], [1, \"remark-text\"], [\"class\", \"detail-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-list\"], [\"class\", \"detail-item d-flex align-items-center py-2 border-bottom\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\", \"d-flex\", \"align-items-center\", \"py-2\", \"border-bottom\"], [1, \"detail-index\"], [1, \"badge\", \"badge-light\"], [1, \"detail-content\", \"flex-grow-1\", \"ml-2\"], [1, \"detail-field\"], [1, \"detail-value\", \"text-muted\"], [1, \"detail-pagination\", \"mt-3\"], [\"aria-label\", \"\\u6A21\\u677F\\u8A73\\u60C5\\u5206\\u9801\"], [1, \"pagination\", \"pagination-sm\", \"justify-content-center\", \"mb-0\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"py-3\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"h5\", 6);\n          i0.ɵɵelement(5, \"i\", 7);\n          i0.ɵɵtext(6, \"\\u6A21\\u677F\\u7BA1\\u7406 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"small\", 8);\n          i0.ɵɵtext(8, \"\\u7BA1\\u7406\\u548C\\u67E5\\u770B\\u5BA2\\u8B8A\\u9700\\u6C42\\u6A21\\u677F\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"span\", 10);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"nb-card-body\", 11)(13, \"div\", 12)(14, \"div\", 13)(15, \"div\", 14)(16, \"div\", 15);\n          i0.ɵɵelement(17, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_Template_input_ngModelChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function TemplateViewerComponent_Template_input_input_18_listener() {\n            return ctx.onSearch();\n          })(\"keyup.enter\", function TemplateViewerComponent_Template_input_keyup_enter_18_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, TemplateViewerComponent_div_19_Template, 3, 0, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, TemplateViewerComponent_div_20_Template, 4, 1, \"div\", 19)(21, TemplateViewerComponent_div_21_Template, 4, 0, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(22, TemplateViewerComponent_div_22_Template, 6, 5, \"div\", 21)(23, TemplateViewerComponent_div_23_Template, 36, 10, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"nb-card-footer\", 23)(25, \"div\", 24)(26, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_26_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelement(27, \"i\", 26);\n          i0.ɵɵtext(28, \"\\u95DC\\u9589 \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\"\", ctx.templatePagination.totalItems, \" \\u500B\\u6A21\\u677F\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword && ctx.filteredTemplates.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword && ctx.filteredTemplates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, i2.DatePipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, NbCardModule, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardFooterComponent, i4.NbCardHeaderComponent, NbButtonModule],\n      styles: [\".template-viewer-card[_ngcontent-%COMP%] {\\n  width: 90vw;\\n  max-width: 1200px;\\n  height: 80vh;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(174, 155, 102, 0.25);\\n  border: none;\\n  overflow: hidden;\\n  background: #FFFFFF;\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  border-bottom: none;\\n  padding: 1.5rem;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-bottom: 0.25rem;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  opacity: 0.9;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: #FFFFFF;\\n  font-size: 0.875rem;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.template-viewer-body[_ngcontent-%COMP%] {\\n  overflow: auto;\\n  padding: 1.5rem;\\n  background: #FEFCF8;\\n}\\n\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  background: #F8F9FA;\\n  border-radius: 8px;\\n  padding: 0.75rem 1rem;\\n  border: 2px solid transparent;\\n  transition: 0.3s ease;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]:focus-within {\\n  border-color: #B8A676;\\n  background: #FFFFFF;\\n  box-shadow: 0 0 0 3px rgba(184, 166, 118, 0.1);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  margin-right: 0.75rem;\\n  font-size: 1rem;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  background: transparent;\\n  outline: none;\\n  font-size: 1rem;\\n  color: #2C3E50;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder {\\n  color: #ADB5BD;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-search-btn[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  color: #FFFFFF;\\n  border: none;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.75rem;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-search-btn[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n  transform: scale(1.1);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-suggestions[_ngcontent-%COMP%], \\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-no-results[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  padding: 0.5rem 0;\\n}\\n\\n.template-list-container[_ngcontent-%COMP%]   .list-controls[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 1rem 1.5rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n}\\n.template-list-container[_ngcontent-%COMP%]   .list-controls[_ngcontent-%COMP%]   .list-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n\\n.template-cards-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1rem;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n  transition: 0.3s ease;\\n  border: 1px solid #E9ECEF;\\n  overflow: hidden;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 16px rgba(174, 155, 102, 0.2);\\n  border-color: #B8A676;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  font-size: 1.1rem;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  margin: 0;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  margin-left: 1rem;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 1rem;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(184, 166, 118, 0.3);\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  color: #FFFFFF;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n  transform: translateY(-1px);\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  padding: 3rem 2rem;\\n  text-align: center;\\n  border: 2px dashed #dee2e6;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #adb5bd;\\n  margin-bottom: 1rem;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin: 0;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]   .clear-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]   .clear-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.enhanced-pagination-container[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]   .page-info[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n  gap: 0.5rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 40px;\\n  height: 40px;\\n  border: none;\\n  border-radius: 8px;\\n  background: #F8F9FA;\\n  color: #6C757D;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  transform: translateY(-1px);\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.page-number[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.first-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.last-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.prev-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.next-page[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.3);\\n}\\n\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  margin-bottom: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  margin-bottom: 1rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border: none;\\n  border-radius: 8px;\\n  background: #F8F9FA;\\n  color: #6C757D;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  transform: translateY(-1px);\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-title[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-subtitle[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6C757D;\\n  margin-bottom: 0.25rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  background: #F8F9FA;\\n  border-radius: 8px;\\n  padding: 0.75rem 1rem;\\n  border: 2px solid transparent;\\n  transition: 0.3s ease;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]:focus-within {\\n  border-color: #B8A676;\\n  background: #FFFFFF;\\n  box-shadow: 0 0 0 3px rgba(184, 166, 118, 0.1);\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  margin-right: 0.75rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .detail-search-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  background: transparent;\\n  outline: none;\\n  font-size: 1rem;\\n  color: #2C3E50;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .detail-search-input[_ngcontent-%COMP%]::placeholder {\\n  color: #ADB5BD;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%], \\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  border: none;\\n  border-radius: 6px;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]:hover, \\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  transform: scale(1.05);\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .search-results-info[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n}\\n\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  border: 1px solid #e9ecef;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);\\n  border-color: #667eea;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%] {\\n  padding: 1.25rem;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  border-bottom: 1px solid #f8f9fa;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-index[_ngcontent-%COMP%]   .index-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  color: white;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n  margin-bottom: 0.75rem;\\n  font-size: 1.1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.75rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 6px;\\n  font-size: 0.875rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.id-meta[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  color: #1976d2;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.group-meta[_ngcontent-%COMP%] {\\n  background: #f3e5f5;\\n  color: #7b1fa2;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.category-meta[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #388e3c;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-actions[_ngcontent-%COMP%]   .create-date[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.875rem;\\n  background: #f8f9fa;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 6px;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%] {\\n  padding: 1.25rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value.price-value[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1.1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value.quantity-value[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%]   .remark-content[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-left: 4px solid #667eea;\\n  padding: 1rem;\\n  border-radius: 0 8px 8px 0;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%]   .remark-content[_ngcontent-%COMP%]   .remark-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-style: italic;\\n  line-height: 1.5;\\n}\\n\\n.template-viewer-footer[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-top: 1px solid #e9ecef;\\n  padding: 1rem 1.5rem;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  padding: 0.75rem 2rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: #5a6268;\\n  transform: translateY(-1px);\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  border: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: none;\\n  padding: 0.75rem 1rem;\\n  font-size: 0.95rem;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-style: italic;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  padding: 0.75rem 1rem;\\n  transition: all 0.2s ease;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n.search-results-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #007bff;\\n  padding-left: 0.75rem;\\n  background: #f8f9ff;\\n  border-radius: 4px;\\n}\\n\\n.pagination-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #28a745;\\n  padding-left: 0.75rem;\\n  background: #f8fff8;\\n  border-radius: 4px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  font-size: 0.9rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9ff;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  border-color: #f0f0f0;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto 1rem;\\n  opacity: 0.5;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: 0.5rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.template-detail-modal[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  background: #fff;\\n  border: 2px solid #e9ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  background: #f8f9ff;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  border-left: 4px solid #007bff;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9ff;\\n  border-radius: 6px;\\n  margin: 0 -0.5rem;\\n  padding-left: 0.5rem !important;\\n  padding-right: 0.5rem !important;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0.375rem 0.5rem;\\n  border-radius: 50%;\\n  min-width: 2rem;\\n  text-align: center;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n  word-break: break-word;\\n}\\n\\n.add-template-form[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .template-form[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e8ecef;\\n  border-radius: 12px;\\n  padding: 2rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .template-form[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  margin-left: 0.125rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  background: #ffffff;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .items-selector[_ngcontent-%COMP%] {\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  background: #f9fafb;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  padding: 2rem;\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n  padding: 0.875rem 1rem;\\n  cursor: pointer;\\n  margin: 0;\\n  width: 100%;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-top: 0.125rem;\\n  width: 1rem;\\n  height: 1rem;\\n  accent-color: #28a745;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.25rem;\\n  line-height: 1.4;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-desc[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 0.75rem;\\n  margin-top: 1.5rem;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%], \\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  padding: 0.625rem 1.25rem;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border: none;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  min-width: 80px;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%] {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover {\\n  background: #e5e7eb;\\n  transform: translateY(-1px);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.25);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n  color: #6c757d;\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  border-radius: 4px;\\n  margin: 0 0.125rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  color: #495057;\\n  transform: translateY(-1px);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  background-color: #fff;\\n  border-color: #dee2e6;\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n}\\n\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.8rem;\\n  border-radius: 3px;\\n  margin: 0 0.0625rem;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  border-color: #28a745;\\n  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  transition: background-color 0.2s;\\n}\\n.detail-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-name[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.95rem;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-remark[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n  border-left: 3px solid #dee2e6;\\n}\\n\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-radius: 0.25rem 0 0 0.25rem;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n  border-radius: 0 0.25rem 0.25rem 0;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_div_19_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "filteredTemplates", "length", "ɵɵtextInterpolate3", "templatePagination", "currentPage", "pageSize", "Math", "min", "totalItems", "ɵɵtextInterpolate2", "totalPages", "TemplateViewerComponent_div_22_div_3_button_13_Template_button_click_0_listener", "_r5", "tpl_r4", "$implicit", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_div_22_div_3_Template_button_click_9_listener", "_r3", "onSelectTemplate", "ɵɵtemplate", "TemplateViewerComponent_div_22_div_3_button_13_Template", "TemplateName", "Description", "ɵɵproperty", "TemplateViewerComponent_div_22_div_4_p_7_Template_a_click_2_listener", "_r6", "TemplateViewerComponent_div_22_div_4_i_3_Template", "TemplateViewerComponent_div_22_div_4_i_4_Template", "TemplateViewerComponent_div_22_div_4_p_7_Template", "TemplateViewerComponent_div_22_div_4_p_8_Template", "searchKeyword", "TemplateViewerComponent_div_22_div_5_li_13_Template_button_click_1_listener", "page_r9", "_r8", "goToTemplatePage", "ɵɵclassProp", "ɵɵtextInterpolate", "TemplateViewerComponent_div_22_div_5_Template_button_click_8_listener", "_r7", "TemplateViewerComponent_div_22_div_5_Template_button_click_11_listener", "TemplateViewerComponent_div_22_div_5_li_13_Template", "TemplateViewerComponent_div_22_div_5_Template_button_click_15_listener", "TemplateViewerComponent_div_22_div_5_Template_button_click_18_listener", "getTemplatePageNumbers", "TemplateViewerComponent_div_22_div_1_Template", "TemplateViewerComponent_div_22_div_3_Template", "TemplateViewerComponent_div_22_div_4_Template", "TemplateViewerComponent_div_22_div_5_Template", "paginatedTemplates", "trackByTemplateId", "selectedTemplate", "detailPagination", "TemplateViewerComponent_div_23_button_28_Template_button_click_0_listener", "_r11", "detailSearchKeyword", "searchTemplateDetails", "TemplateViewerComponent_div_23_div_29_small_1_Template", "TemplateViewerComponent_div_23_div_29_small_2_Template", "currentTemplateDetailsData", "detail_r12", "CGroupName", "CCategory", "ɵɵpipeBind1", "CUnitPrice", "CQuantity", "CUnit", "CRemark", "TemplateViewerComponent_div_23_div_30_div_1_div_21_div_2_Template", "TemplateViewerComponent_div_23_div_30_div_1_div_21_div_3_Template", "TemplateViewerComponent_div_23_div_30_div_1_div_21_div_4_Template", "TemplateViewerComponent_div_23_div_30_div_1_span_15_Template", "TemplateViewerComponent_div_23_div_30_div_1_span_16_Template", "TemplateViewerComponent_div_23_div_30_div_1_div_21_Template", "i_r13", "CReleateName", "CReleateId", "ɵɵpipeBind2", "CCreateDt", "TemplateViewerComponent_div_23_div_30_div_1_Template", "i_r15", "detail_r14", "FieldName", "FieldValue", "TemplateViewerComponent_div_23_ng_template_31_div_0_div_1_Template", "paginatedDetails", "TemplateViewerComponent_div_23_ng_template_31_div_0_Template", "currentTemplateDetails", "noDetails_r16", "TemplateViewerComponent_div_23_div_33_li_6_Template_button_click_1_listener", "page_r19", "_r18", "goToDetailPage", "TemplateViewerComponent_div_23_div_33_Template_button_click_4_listener", "_r17", "TemplateViewerComponent_div_23_div_33_li_6_Template", "TemplateViewerComponent_div_23_div_33_Template_button_click_8_listener", "getDetailPageNumbers", "TemplateViewerComponent_div_23_Template_button_click_4_listener", "_r10", "closeTemplateDetail", "TemplateViewerComponent_div_23_p_10_Template", "TemplateViewerComponent_div_23_div_17_Template", "ɵɵtwoWayListener", "TemplateViewerComponent_div_23_Template_input_ngModelChange_24_listener", "$event", "ɵɵtwoWayBindingSet", "TemplateViewerComponent_div_23_Template_input_keyup_enter_24_listener", "TemplateViewerComponent_div_23_Template_button_click_26_listener", "TemplateViewerComponent_div_23_button_28_Template", "TemplateViewerComponent_div_23_div_29_Template", "TemplateViewerComponent_div_23_div_30_Template", "TemplateViewerComponent_div_23_ng_template_31_Template", "ɵɵtemplateRefExtractor", "TemplateViewerComponent_div_23_div_33_Template", "TemplateViewerComponent_div_23_ng_template_34_Template", "ɵɵtwoWayProperty", "checkOldDetails_r20", "TemplateViewerComponent", "constructor", "templateService", "templateType", "selectTemplate", "close", "templates", "templateDetails", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "Date", "toLocaleDateString", "error", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "updateTemplatePagination", "ceil", "max", "updatePaginatedTemplates", "startIndex", "endIndex", "slice", "page", "pages", "startPage", "endPage", "i", "push", "onSearch", "emit", "loadTemplateDetails", "updateDetailPagination", "templateId", "pageIndex", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "allDetails", "CTemplateDetailId", "CSort", "undefined", "toISOString", "CCreator", "detail", "pagedDetails", "loadTemplateDetailsMock", "setTimeout", "mockDetails", "itemsPerTemplate", "String", "fromCharCode", "now", "random", "floor", "filteredDetails", "details", "updatePaginatedDetails", "onClose", "templateID", "confirm", "deleteTemplateById", "deleteTemplateArgs", "apiTemplateDeleteTemplatePost$Json", "d", "index", "ɵɵdirectiveInject", "i1", "TemplateService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_Template_input_ngModelChange_18_listener", "TemplateViewerComponent_Template_input_input_18_listener", "TemplateViewerComponent_Template_input_keyup_enter_18_listener", "TemplateViewerComponent_div_19_Template", "TemplateViewerComponent_div_20_Template", "TemplateViewerComponent_div_21_Template", "TemplateViewerComponent_div_22_Template", "TemplateViewerComponent_div_23_Template", "TemplateViewerComponent_Template_button_click_26_listener", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "DatePipe", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i4", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, GetTemplateByIdArgs, GetTemplateDetailByIdArgs } from 'src/services/api/models';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 公開Math對象供模板使用\r\n  Math = Math;\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = []; // 保留用於向後相容\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 新的詳情資料管理\r\n  currentTemplateDetailsData: TemplateDetailItem[] = [];\r\n  detailSearchKeyword = '';\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板列表\r\n  templatePagination = {\r\n    currentPage: 1,\r\n    pageSize: 10,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板詳情\r\n  detailPagination = {\r\n    currentPage: 1,\r\n    pageSize: 5,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedDetails: TemplateDetail[] = [];\r\n\r\n\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    this.loadTemplates();\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 載入模板列表 - 使用真實的 API 調用\r\n  loadTemplates() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      PageIndex: 1, // 暫時載入第一頁\r\n      PageSize: 100, // 暫時載入較多資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateList API\r\n    this.templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // API 調用成功\r\n\r\n          // 轉換 API 回應為內部格式\r\n          this.templates = response.Entries.map(item => ({\r\n            TemplateID: item.CTemplateId,\r\n            TemplateName: item.CTemplateName || '',\r\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\r\n          }));\r\n\r\n          // 更新過濾列表\r\n          this.updateFilteredTemplates();\r\n\r\n          // 清空詳情資料，改為按需載入\r\n          this.templateDetails = [];\r\n          this.currentTemplateDetailsData = [];\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n          this.templateDetails = [];\r\n          this.updateFilteredTemplates();\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n        this.templateDetails = [];\r\n        this.updateFilteredTemplates();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n    this.updateTemplatePagination();\r\n  }\r\n\r\n  // 更新模板分頁\r\n  updateTemplatePagination() {\r\n    this.templatePagination.totalItems = this.filteredTemplates.length;\r\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\r\n\r\n    // 確保當前頁面不超過總頁數\r\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\r\n    }\r\n\r\n    this.updatePaginatedTemplates();\r\n  }\r\n\r\n  // 更新分頁後的模板列表\r\n  updatePaginatedTemplates() {\r\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\r\n    const endIndex = startIndex + this.templatePagination.pageSize;\r\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 模板分頁導航\r\n  goToTemplatePage(page: number) {\r\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = page;\r\n      this.updatePaginatedTemplates();\r\n    }\r\n  }\r\n\r\n  // 獲取模板分頁頁碼數組\r\n  getTemplatePageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.templatePagination.totalPages;\r\n    const currentPage = this.templatePagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n\r\n    // 按需載入模板詳情\r\n    if (template.TemplateID) {\r\n      this.loadTemplateDetails(template.TemplateID);\r\n    }\r\n\r\n    this.updateDetailPagination();\r\n  }\r\n\r\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\r\n  loadTemplateDetails(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    // 調用真實的 GetTemplateDetailById API\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n\r\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\r\n          let allDetails = response.Entries.map(item => ({\r\n            CTemplateDetailId: item.CTemplateDetailId || 0,\r\n            CTemplateId: item.CTemplateId || templateId,\r\n            CReleateId: item.CReleateId || 0,\r\n            CReleateName: item.CReleateName || '',\r\n            CGroupName: item.CGroupName || '', // 新增群組名稱欄位\r\n            CSort: undefined,\r\n            CRemark: undefined,\r\n            CCreateDt: new Date().toISOString(),\r\n            CCreator: '系統',\r\n            CCategory: undefined,\r\n            CUnitPrice: undefined,\r\n            CQuantity: undefined,\r\n            CUnit: undefined\r\n          } as TemplateDetailItem));\r\n\r\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\r\n          if (searchKeyword && searchKeyword.trim()) {\r\n            allDetails = allDetails.filter(detail =>\r\n              detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n              (detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n            );\r\n          }\r\n\r\n          // 前端分頁處理 (因為 API 不支援分頁參數)\r\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n          const endIndex = startIndex + this.detailPagination.pageSize;\r\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\r\n\r\n          // 更新詳情資料和分頁資訊\r\n          this.currentTemplateDetailsData = pagedDetails;\r\n          this.detailPagination.totalItems = allDetails.length;\r\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\r\n          this.detailPagination.currentPage = pageIndex;\r\n        } else {\r\n          this.currentTemplateDetailsData = [];\r\n          this.detailPagination.totalItems = 0;\r\n          this.detailPagination.totalPages = 0;\r\n          this.detailPagination.currentPage = 1;\r\n        }\r\n      },\r\n      error: () => {\r\n        this.currentTemplateDetailsData = [];\r\n        this.detailPagination.totalItems = 0;\r\n        this.detailPagination.totalPages = 0;\r\n        this.detailPagination.currentPage = 1;\r\n\r\n        // 如果 API 失敗，回退到模擬資料\r\n        this.loadTemplateDetailsMock(templateId, pageIndex, searchKeyword);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 模擬載入模板詳情 (暫時使用，等待後端 API)\r\n  private loadTemplateDetailsMock(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    // 模擬 API 延遲\r\n    setTimeout(() => {\r\n      // 生成模擬詳情資料\r\n      const mockDetails: TemplateDetailItem[] = [];\r\n      const itemsPerTemplate = 8; // 每個模板8個項目\r\n\r\n      for (let i = 1; i <= itemsPerTemplate; i++) {\r\n        const detail: TemplateDetailItem = {\r\n          CTemplateDetailId: templateId * 100 + i,\r\n          CTemplateId: templateId,\r\n          CReleateId: templateId * 1000 + i,\r\n          CReleateName: `工程項目${String.fromCharCode(64 + (templateId % 26) + 1)}-${i}`,\r\n          CGroupName: i % 4 === 0 ? '結構工程' : (i % 4 === 1 ? '機電工程' : (i % 4 === 2 ? '裝修工程' : '其他工程')), // 新增群組名稱\r\n          CSort: i,\r\n          CRemark: i % 3 === 0 ? `備註說明 ${i}` : undefined,\r\n          CCreateDt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\r\n          CCreator: '系統管理員',\r\n          CCategory: i % 2 === 0 ? '基礎工程' : '進階工程',\r\n          CUnitPrice: Math.floor(Math.random() * 10000) + 1000,\r\n          CQuantity: Math.floor(Math.random() * 10) + 1,\r\n          CUnit: i % 3 === 0 ? '式' : (i % 3 === 1 ? '個' : 'm²')\r\n        };\r\n        mockDetails.push(detail);\r\n      }\r\n\r\n      // 搜尋篩選\r\n      let filteredDetails = mockDetails;\r\n      if (searchKeyword && searchKeyword.trim()) {\r\n        filteredDetails = mockDetails.filter(detail =>\r\n          detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n          (detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase())) ||\r\n          (detail.CRemark && detail.CRemark.toLowerCase().includes(searchKeyword.toLowerCase())) ||\r\n          (detail.CCategory && detail.CCategory.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n        );\r\n      }\r\n\r\n      // 分頁處理\r\n      const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n      const endIndex = startIndex + this.detailPagination.pageSize;\r\n      const pagedDetails = filteredDetails.slice(startIndex, endIndex);\r\n\r\n      // 更新資料\r\n      this.currentTemplateDetailsData = pagedDetails;\r\n      this.detailPagination.totalItems = filteredDetails.length;\r\n      this.detailPagination.totalPages = Math.ceil(filteredDetails.length / this.detailPagination.pageSize);\r\n      this.detailPagination.currentPage = pageIndex;\r\n    }, 300);\r\n  }\r\n\r\n  // 搜尋模板詳情\r\n  searchTemplateDetails(keyword: string) {\r\n    this.detailSearchKeyword = keyword;\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\r\n    }\r\n  }\r\n\r\n  // 更新詳情分頁 (保留向後相容)\r\n  updateDetailPagination() {\r\n    // 如果使用新的詳情資料，直接返回\r\n    if (this.currentTemplateDetailsData.length > 0) {\r\n      return;\r\n    }\r\n\r\n    // 向後相容：使用舊的詳情資料\r\n    const details = this.currentTemplateDetails;\r\n    this.detailPagination.totalItems = details.length;\r\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\r\n    this.detailPagination.currentPage = 1; // 重置到第一頁\r\n    this.updatePaginatedDetails();\r\n  }\r\n\r\n  // 更新分頁後的詳情列表\r\n  updatePaginatedDetails() {\r\n    const details = this.currentTemplateDetails;\r\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\r\n    const endIndex = startIndex + this.detailPagination.pageSize;\r\n    this.paginatedDetails = details.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 詳情分頁導航\r\n  goToDetailPage(page: number) {\r\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\r\n      // 如果使用新的詳情資料，重新載入\r\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\r\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\r\n      } else {\r\n        // 向後相容：使用舊的分頁邏輯\r\n        this.detailPagination.currentPage = page;\r\n        this.updatePaginatedDetails();\r\n      }\r\n    }\r\n  }\r\n\r\n  // 獲取詳情分頁頁碼數組\r\n  getDetailPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.detailPagination.totalPages;\r\n    const currentPage = this.detailPagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // 刪除模板 - 使用真實的 API 調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 準備 API 請求參數\r\n    const deleteTemplateArgs: GetTemplateByIdArgs = {\r\n      CTemplateId: templateID\r\n    };\r\n\r\n    // 調用 DeleteTemplate API\r\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\r\n      body: deleteTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          // 重新載入模板列表\r\n          this.loadTemplates();\r\n\r\n          // 如果當前查看的模板被刪除，關閉詳情\r\n          if (this.selectedTemplate?.TemplateID === templateID) {\r\n            this.selectedTemplate = null;\r\n          }\r\n        } else {\r\n          // API 返回錯誤\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n      }\r\n    });\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/Template/GetTemplateList\r\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/Template/SaveTemplate\r\n   *    請求體: {\r\n   *      CTemplateName: string,\r\n   *      CTemplateType: number,  // 1=客變需求\r\n   *      CStatus: number,\r\n   *      Details: [{\r\n   *        CReleateId: number,        // 關聯主檔ID\r\n   *        CReleateName: string       // 關聯名稱\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    POST /api/Template/DeleteTemplate\r\n   *    請求體: { CTemplateId: number }\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\r\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\r\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\r\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  CTemplateType: number; // 模板類型，1=客變需求\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n\r\n// 擴展的詳情項目結構 (基於 API 的 TemplateDetailItem 擴展)\r\nexport interface TemplateDetailItem {\r\n  CTemplateDetailId: number;\r\n  CTemplateId: number;\r\n  CReleateId: number;            // 關聯主檔ID\r\n  CReleateName: string;          // 關聯名稱\r\n  CGroupName?: string;           // 群組名稱 (API 新增欄位)\r\n  CSort?: number;                // 排序順序\r\n  CRemark?: string;              // 備註\r\n  CCreateDt: string;             // 建立時間\r\n  CCreator: string;              // 建立者\r\n  CUpdateDt?: string;            // 更新時間\r\n  CUpdator?: string;             // 更新者\r\n\r\n  // 業務相關欄位 (依需求添加，部分由 API 提供)\r\n  CUnitPrice?: number;           // 單價\r\n  CQuantity?: number;            // 數量\r\n  CUnit?: string;                // 單位\r\n  CCategory?: string;            // 分類\r\n}\r\n\r\n// 注意：GetTemplateDetailById API 使用的是 GetTemplateDetailByIdArgs 和 TemplateDetailItemListResponseBase\r\n// 這些 interface 已經在 API models 中定義，不需要重複定義\r\n", "<nb-card class=\"template-viewer-card\">\r\n  <nb-card-header class=\"template-viewer-header\">\r\n    <div class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"header-title\">\r\n        <h5 class=\"mb-0\">\r\n          <i class=\"fas fa-layer-group mr-2 text-primary\"></i>模板管理\r\n        </h5>\r\n        <small class=\"text-muted\">管理和查看客變需求模板</small>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <span class=\"badge badge-info\">{{ templatePagination.totalItems }} 個模板</span>\r\n      </div>\r\n    </div>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"template-viewer-body\">\r\n    <!-- 增強的搜尋功能 -->\r\n    <div class=\"enhanced-search-container mb-4\">\r\n      <div class=\"search-wrapper\">\r\n        <div class=\"search-input-group\">\r\n          <div class=\"search-icon\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </div>\r\n          <input type=\"text\" class=\"search-input\" placeholder=\"搜尋模板名稱、描述或關鍵字...\" [(ngModel)]=\"searchKeyword\"\r\n            (input)=\"onSearch()\" (keyup.enter)=\"onSearch()\">\r\n          <div class=\"search-actions\" *ngIf=\"searchKeyword\">\r\n            <button class=\"clear-search-btn\" type=\"button\" (click)=\"clearSearch()\" title=\"清除搜尋\">\r\n              <i class=\"fas fa-times\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"search-suggestions\" *ngIf=\"searchKeyword && filteredTemplates.length > 0\">\r\n          <small class=\"text-success\">\r\n            <i class=\"fas fa-check-circle mr-1\"></i>找到 {{ filteredTemplates.length }} 個相關模板\r\n          </small>\r\n        </div>\r\n        <div class=\"search-no-results\" *ngIf=\"searchKeyword && filteredTemplates.length === 0\">\r\n          <small class=\"text-warning\">\r\n            <i class=\"fas fa-exclamation-triangle mr-1\"></i>未找到符合條件的模板\r\n          </small>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 優化的模板列表（僅在未選擇模板細節時顯示） -->\r\n    <div class=\"template-list-container\" *ngIf=\"!selectedTemplate\">\r\n      <!-- 列表控制欄 -->\r\n      <div class=\"list-controls mb-3\" *ngIf=\"templatePagination.totalItems > 0\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"list-info\">\r\n            <span class=\"info-text\">\r\n              顯示第 {{ (templatePagination.currentPage - 1) * templatePagination.pageSize + 1 }} -\r\n              {{ Math.min(templatePagination.currentPage * templatePagination.pageSize, templatePagination.totalItems)\r\n              }} 項，\r\n              共 {{ templatePagination.totalItems }} 項模板\r\n            </span>\r\n          </div>\r\n          <div class=\"view-options\">\r\n            <small class=\"text-muted\">第 {{ templatePagination.currentPage }} / {{ templatePagination.totalPages }}\r\n              頁</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 卡片式模板列表 -->\r\n      <div class=\"template-cards-grid\">\r\n        <div class=\"template-card\" *ngFor=\"let tpl of paginatedTemplates; trackBy: trackByTemplateId\">\r\n          <div class=\"card-header\">\r\n            <div class=\"template-info\">\r\n              <h6 class=\"template-name\">\r\n                <i class=\"fas fa-file-alt mr-2 text-primary\"></i>\r\n                {{ tpl.TemplateName }}\r\n              </h6>\r\n              <p class=\"template-description\">\r\n                {{ tpl.Description || '無描述' }}\r\n              </p>\r\n            </div>\r\n            <div class=\"template-actions\">\r\n              <button class=\"action-btn view-btn\" (click)=\"onSelectTemplate(tpl)\" title=\"查看詳情\">\r\n                <i class=\"fas fa-eye\"></i>\r\n                <span>查看</span>\r\n              </button>\r\n              <button class=\"action-btn delete-btn\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n                *ngIf=\"tpl.TemplateID\" title=\"刪除模板\">\r\n                <i class=\"fas fa-trash\"></i>\r\n                <span>刪除</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空狀態 -->\r\n        <div class=\"empty-state-card\" *ngIf=\"!paginatedTemplates || paginatedTemplates.length === 0\">\r\n          <div class=\"empty-content\">\r\n            <div class=\"empty-icon\">\r\n              <i class=\"fas fa-search\" *ngIf=\"searchKeyword\"></i>\r\n              <i class=\"fas fa-folder-open\" *ngIf=\"!searchKeyword\"></i>\r\n            </div>\r\n            <h6 class=\"empty-title\">\r\n              {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}\r\n            </h6>\r\n            <p class=\"empty-description\" *ngIf=\"searchKeyword\">\r\n              請嘗試其他關鍵字或\r\n              <a href=\"javascript:void(0)\" (click)=\"clearSearch()\" class=\"clear-link\">清除搜尋條件</a>\r\n            </p>\r\n            <p class=\"empty-description\" *ngIf=\"!searchKeyword\">\r\n              目前還沒有建立任何模板，請先建立模板\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 優化的分頁控制器 -->\r\n      <div class=\"enhanced-pagination-container mt-4\" *ngIf=\"templatePagination.totalPages > 1\">\r\n        <div class=\"pagination-wrapper\">\r\n          <div class=\"pagination-info\">\r\n            <span class=\"page-info\">\r\n              第 {{ templatePagination.currentPage }} 頁，共 {{ templatePagination.totalPages }} 頁\r\n            </span>\r\n          </div>\r\n          <nav aria-label=\"模板列表分頁\" class=\"pagination-nav\">\r\n            <ul class=\"enhanced-pagination\">\r\n              <!-- 第一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === 1\">\r\n                <button class=\"page-btn first-page\" (click)=\"goToTemplatePage(1)\"\r\n                  [disabled]=\"templatePagination.currentPage === 1\" title=\"第一頁\">\r\n                  <i class=\"fas fa-angle-double-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 上一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === 1\">\r\n                <button class=\"page-btn prev-page\" (click)=\"goToTemplatePage(templatePagination.currentPage - 1)\"\r\n                  [disabled]=\"templatePagination.currentPage === 1\" title=\"上一頁\">\r\n                  <i class=\"fas fa-chevron-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 頁碼 -->\r\n              <li class=\"page-item\" *ngFor=\"let page of getTemplatePageNumbers()\"\r\n                [class.active]=\"page === templatePagination.currentPage\">\r\n                <button class=\"page-btn page-number\" (click)=\"goToTemplatePage(page)\">{{ page }}</button>\r\n              </li>\r\n\r\n              <!-- 下一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n                <button class=\"page-btn next-page\" (click)=\"goToTemplatePage(templatePagination.currentPage + 1)\"\r\n                  [disabled]=\"templatePagination.currentPage === templatePagination.totalPages\" title=\"下一頁\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 最後一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n                <button class=\"page-btn last-page\" (click)=\"goToTemplatePage(templatePagination.totalPages)\"\r\n                  [disabled]=\"templatePagination.currentPage === templatePagination.totalPages\" title=\"最後一頁\">\r\n                  <i class=\"fas fa-angle-double-right\"></i>\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 優化的模板詳情視圖 -->\r\n    <div *ngIf=\"selectedTemplate\" class=\"template-detail-view\">\r\n      <!-- 詳情標題欄 -->\r\n      <div class=\"detail-header\">\r\n        <div class=\"detail-title-section\">\r\n          <div class=\"back-button\">\r\n            <button class=\"back-btn\" (click)=\"closeTemplateDetail()\" title=\"返回模板列表\">\r\n              <i class=\"fas fa-arrow-left\"></i>\r\n            </button>\r\n          </div>\r\n          <div class=\"detail-title-info\">\r\n            <h5 class=\"detail-title\">\r\n              <i class=\"fas fa-file-alt mr-2 text-primary\"></i>\r\n              {{ selectedTemplate!.TemplateName }}\r\n            </h5>\r\n            <p class=\"detail-subtitle\" *ngIf=\"selectedTemplate.Description\">\r\n              {{ selectedTemplate.Description }}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div class=\"detail-stats\">\r\n          <div class=\"stat-item\">\r\n            <span class=\"stat-label\">項目數量</span>\r\n            <span class=\"stat-value\">{{ currentTemplateDetailsData.length > 0 ? detailPagination.totalItems :\r\n              currentTemplateDetails.length }}</span>\r\n          </div>\r\n          <div class=\"stat-item\" *ngIf=\"detailPagination.totalPages > 1\">\r\n            <span class=\"stat-label\">頁數</span>\r\n            <span class=\"stat-value\">{{ detailPagination.currentPage }} / {{ detailPagination.totalPages }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 詳情內容區域 -->\r\n      <div class=\"detail-content\">\r\n        <!-- 增強的詳情搜尋 -->\r\n        <div class=\"detail-search-section mb-4\">\r\n          <div class=\"detail-search-wrapper\">\r\n            <div class=\"detail-search-input-group\">\r\n              <div class=\"search-icon\">\r\n                <i class=\"fas fa-search\"></i>\r\n              </div>\r\n              <input type=\"text\" class=\"detail-search-input\" placeholder=\"搜尋項目名稱、群組或備註...\"\r\n                [(ngModel)]=\"detailSearchKeyword\" (keyup.enter)=\"searchTemplateDetails(detailSearchKeyword)\">\r\n              <div class=\"search-actions\">\r\n                <button class=\"search-btn\" (click)=\"searchTemplateDetails(detailSearchKeyword)\" title=\"搜尋\">\r\n                  <i class=\"fas fa-search\"></i>\r\n                </button>\r\n                <button class=\"clear-btn\" (click)=\"detailSearchKeyword=''; searchTemplateDetails('')\"\r\n                  *ngIf=\"detailSearchKeyword\" title=\"清除\">\r\n                  <i class=\"fas fa-times\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n            <div class=\"search-results-info\" *ngIf=\"detailSearchKeyword\">\r\n              <small class=\"text-info\" *ngIf=\"currentTemplateDetailsData.length > 0\">\r\n                <i class=\"fas fa-info-circle mr-1\"></i>找到 {{ detailPagination.totalItems }} 個相關項目\r\n              </small>\r\n              <small class=\"text-warning\" *ngIf=\"currentTemplateDetailsData.length === 0\">\r\n                <i class=\"fas fa-exclamation-triangle mr-1\"></i>未找到符合條件的項目\r\n              </small>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 優化的詳情項目顯示 -->\r\n        <div *ngIf=\"currentTemplateDetailsData.length > 0; else checkOldDetails\" class=\"enhanced-detail-list\">\r\n          <div *ngFor=\"let detail of currentTemplateDetailsData; let i = index\" class=\"enhanced-detail-item\">\r\n            <div class=\"detail-item-card\">\r\n              <div class=\"detail-item-header\">\r\n                <div class=\"item-index\">\r\n                  <span class=\"index-badge\">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i + 1\r\n                    }}</span>\r\n                </div>\r\n                <div class=\"item-main-info\">\r\n                  <h6 class=\"item-name\">\r\n                    <i class=\"fas fa-cog mr-2 text-secondary\"></i>\r\n                    {{ detail.CReleateName }}\r\n                  </h6>\r\n                  <div class=\"item-meta\">\r\n                    <span class=\"meta-item id-meta\">\r\n                      <i class=\"fas fa-hashtag\"></i>\r\n                      <span>{{ detail.CReleateId }}</span>\r\n                    </span>\r\n                    <span class=\"meta-item group-meta\" *ngIf=\"detail.CGroupName\">\r\n                      <i class=\"fas fa-layer-group\"></i>\r\n                      <span>{{ detail.CGroupName }}</span>\r\n                    </span>\r\n                    <span class=\"meta-item category-meta\" *ngIf=\"detail.CCategory\">\r\n                      <i class=\"fas fa-tag\"></i>\r\n                      <span>{{ detail.CCategory }}</span>\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item-actions\">\r\n                  <span class=\"create-date\">{{ detail.CCreateDt | date:'MM/dd' }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"detail-item-body\"\r\n                *ngIf=\"detail.CUnitPrice || detail.CQuantity || detail.CUnit || detail.CRemark\">\r\n                <div class=\"item-details-grid\">\r\n                  <div class=\"detail-group price-group\" *ngIf=\"detail.CUnitPrice\">\r\n                    <span class=\"detail-label\">單價</span>\r\n                    <span class=\"detail-value price-value\">NT$ {{ detail.CUnitPrice | number }}</span>\r\n                  </div>\r\n                  <div class=\"detail-group quantity-group\" *ngIf=\"detail.CQuantity && detail.CUnit\">\r\n                    <span class=\"detail-label\">數量</span>\r\n                    <span class=\"detail-value quantity-value\">{{ detail.CQuantity }} {{ detail.CUnit }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item-remark\" *ngIf=\"detail.CRemark\">\r\n                  <div class=\"remark-content\">\r\n                    <i class=\"fas fa-comment-alt mr-2 text-muted\"></i>\r\n                    <span class=\"remark-text\">{{ detail.CRemark }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 向後相容：舊的詳情資料顯示 -->\r\n        <ng-template #checkOldDetails>\r\n          <div *ngIf=\"currentTemplateDetails.length > 0; else noDetails\" class=\"detail-list\">\r\n            <div *ngFor=\"let detail of paginatedDetails; let i = index\"\r\n              class=\"detail-item d-flex align-items-center py-2 border-bottom\">\r\n              <div class=\"detail-index\">\r\n                <span class=\"badge badge-light\">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i\r\n                  + 1 }}</span>\r\n              </div>\r\n              <div class=\"detail-content flex-grow-1 ml-2\">\r\n                <div class=\"detail-field\">\r\n                  <strong>{{ detail.FieldName }}:</strong>\r\n                </div>\r\n                <div class=\"detail-value text-muted\">\r\n                  {{ detail.FieldValue }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-template>\r\n\r\n        <!-- 詳情分頁控制器 -->\r\n        <div class=\"detail-pagination mt-3\" *ngIf=\"detailPagination.totalPages > 1\">\r\n          <nav aria-label=\"模板詳情分頁\">\r\n            <ul class=\"pagination pagination-sm justify-content-center mb-0\">\r\n              <!-- 上一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === 1\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage - 1)\"\r\n                  [disabled]=\"detailPagination.currentPage === 1\">\r\n                  <i class=\"fas fa-chevron-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 頁碼 -->\r\n              <li class=\"page-item\" *ngFor=\"let page of getDetailPageNumbers()\"\r\n                [class.active]=\"page === detailPagination.currentPage\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(page)\">{{ page }}</button>\r\n              </li>\r\n\r\n              <!-- 下一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage + 1)\"\r\n                  [disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n\r\n        <ng-template #noDetails>\r\n          <div class=\"text-center py-3\">\r\n            <i class=\"fas fa-inbox fa-2x text-muted mb-2\"></i>\r\n            <p class=\"text-muted mb-0\">此模板暫無內容</p>\r\n          </div>\r\n        </ng-template>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"template-viewer-footer\">\r\n    <div class=\"footer-actions\">\r\n      <button class=\"close-btn\" (click)=\"onClose()\">\r\n        <i class=\"fas fa-times mr-2\"></i>關閉\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;;;;;;;;;ICsBjDC,EADF,CAAAC,cAAA,cAAkD,iBACoC;IAArCD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACpET,EAAA,CAAAU,SAAA,YAA4B;IAEhCV,EADE,CAAAW,YAAA,EAAS,EACL;;;;;IAGNX,EADF,CAAAC,cAAA,cAAsF,gBACxD;IAC1BD,EAAA,CAAAU,SAAA,YAAwC;IAAAV,EAAA,CAAAY,MAAA,GAC1C;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;IAFsCX,EAAA,CAAAa,SAAA,GAC1C;IAD0Cb,EAAA,CAAAc,kBAAA,kBAAAR,MAAA,CAAAS,iBAAA,CAAAC,MAAA,qCAC1C;;;;;IAGAhB,EADF,CAAAC,cAAA,cAAuF,gBACzD;IAC1BD,EAAA,CAAAU,SAAA,YAAgD;IAAAV,EAAA,CAAAY,MAAA,oEAClD;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;;IAYFX,EAHN,CAAAC,cAAA,cAA0E,aACT,cACtC,eACG;IACtBD,EAAA,CAAAY,MAAA,GAIF;IACFZ,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAC,cAAA,cAA0B,eACE;IAAAD,EAAA,CAAAY,MAAA,GACvB;IAGTZ,EAHS,CAAAW,YAAA,EAAQ,EACP,EACF,EACF;;;;IAXEX,EAAA,CAAAa,SAAA,GAIF;IAJEb,EAAA,CAAAiB,kBAAA,0BAAAX,MAAA,CAAAY,kBAAA,CAAAC,WAAA,QAAAb,MAAA,CAAAY,kBAAA,CAAAE,QAAA,aAAAd,MAAA,CAAAe,IAAA,CAAAC,GAAA,CAAAhB,MAAA,CAAAY,kBAAA,CAAAC,WAAA,GAAAb,MAAA,CAAAY,kBAAA,CAAAE,QAAA,EAAAd,MAAA,CAAAY,kBAAA,CAAAK,UAAA,4BAAAjB,MAAA,CAAAY,kBAAA,CAAAK,UAAA,yBAIF;IAG0BvB,EAAA,CAAAa,SAAA,GACvB;IADuBb,EAAA,CAAAwB,kBAAA,YAAAlB,MAAA,CAAAY,kBAAA,CAAAC,WAAA,SAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,YACvB;;;;;;IAuBDzB,EAAA,CAAAC,cAAA,iBACsC;IADAD,EAAA,CAAAE,UAAA,mBAAAwB,gFAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAAC,MAAA,GAAA5B,EAAA,CAAAO,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAoB,MAAA,CAAAE,UAAA,IAA2BxB,MAAA,CAAAyB,gBAAA,CAAAH,MAAA,CAAAE,UAAA,CAAgC;IAAA,EAAC;IAEhG9B,EAAA,CAAAU,SAAA,YAA4B;IAC5BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IACVZ,EADU,CAAAW,YAAA,EAAO,EACR;;;;;;IAjBTX,EAHN,CAAAC,cAAA,cAA8F,cACnE,cACI,aACC;IACxBD,EAAA,CAAAU,SAAA,YAAiD;IACjDV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,YAAgC;IAC9BD,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAI,EACA;IAEJX,EADF,CAAAC,cAAA,cAA8B,iBACqD;IAA7CD,EAAA,CAAAE,UAAA,mBAAA8B,sEAAA;MAAA,MAAAJ,MAAA,GAAA5B,EAAA,CAAAI,aAAA,CAAA6B,GAAA,EAAAJ,SAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4B,gBAAA,CAAAN,MAAA,CAAqB;IAAA,EAAC;IACjE5B,EAAA,CAAAU,SAAA,aAA0B;IAC1BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,oBAAE;IACVZ,EADU,CAAAW,YAAA,EAAO,EACR;IACTX,EAAA,CAAAmC,UAAA,KAAAC,uDAAA,qBACsC;IAM5CpC,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;IAlBEX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAc,MAAA,CAAAS,YAAA,MACF;IAEErC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAc,MAAA,CAAAU,WAAA,8BACF;IAQGtC,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAuC,UAAA,SAAAX,MAAA,CAAAE,UAAA,CAAoB;;;;;IAYvB9B,EAAA,CAAAU,SAAA,YAAmD;;;;;IACnDV,EAAA,CAAAU,SAAA,YAAyD;;;;;;IAK3DV,EAAA,CAAAC,cAAA,YAAmD;IACjDD,EAAA,CAAAY,MAAA,+DACA;IAAAZ,EAAA,CAAAC,cAAA,YAAwE;IAA3CD,EAAA,CAAAE,UAAA,mBAAAsC,qEAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAAoBT,EAAA,CAAAY,MAAA,2CAAM;IAChFZ,EADgF,CAAAW,YAAA,EAAI,EAChF;;;;;IACJX,EAAA,CAAAC,cAAA,YAAoD;IAClDD,EAAA,CAAAY,MAAA,qHACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IAbJX,EAFJ,CAAAC,cAAA,cAA6F,cAChE,cACD;IAEtBD,EADA,CAAAmC,UAAA,IAAAO,iDAAA,gBAA+C,IAAAC,iDAAA,gBACM;IACvD3C,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,aAAwB;IACtBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAKLX,EAJA,CAAAmC,UAAA,IAAAS,iDAAA,gBAAmD,IAAAC,iDAAA,gBAIC;IAIxD7C,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAd0BX,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAwC,aAAA,CAAmB;IACd9C,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAAuC,UAAA,UAAAjC,MAAA,CAAAwC,aAAA,CAAoB;IAGnD9C,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAAwC,aAAA,oGACF;IAC8B9C,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAwC,aAAA,CAAmB;IAInB9C,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAAuC,UAAA,UAAAjC,MAAA,CAAAwC,aAAA,CAAoB;;;;;;IAoC9C9C,EAFF,CAAAC,cAAA,aAC2D,iBACa;IAAjCD,EAAA,CAAAE,UAAA,mBAAA6C,4EAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAAI,aAAA,CAAA6C,GAAA,EAAApB,SAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,gBAAA,CAAAF,OAAA,CAAsB;IAAA,EAAC;IAAChD,EAAA,CAAAY,MAAA,GAAU;IAClFZ,EADkF,CAAAW,YAAA,EAAS,EACtF;;;;;IAFHX,EAAA,CAAAmD,WAAA,WAAAH,OAAA,KAAA1C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,CAAwD;IACcnB,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAAoD,iBAAA,CAAAJ,OAAA,CAAU;;;;;;IAzBpFhD,EAHN,CAAAC,cAAA,cAA0F,cACxD,cACD,eACH;IACtBD,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAO,EACH;IAKAX,EAJN,CAAAC,cAAA,cAAgD,aACd,aAEgD,iBAEZ;IAD5BD,EAAA,CAAAE,UAAA,mBAAAmD,sEAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,gBAAA,CAAiB,CAAC,CAAC;IAAA,EAAC;IAE/DlD,EAAA,CAAAU,SAAA,YAAwC;IAE5CV,EADE,CAAAW,YAAA,EAAS,EACN;IAIHX,EADF,CAAAC,cAAA,cAA8E,kBAEZ;IAD7BD,EAAA,CAAAE,UAAA,mBAAAqD,uEAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,gBAAA,CAAA5C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAE/FnB,EAAA,CAAAU,SAAA,aAAmC;IAEvCV,EADE,CAAAW,YAAA,EAAS,EACN;IAGLX,EAAA,CAAAmC,UAAA,KAAAqB,mDAAA,iBAC2D;IAMzDxD,EADF,CAAAC,cAAA,cAA0G,kBAEZ;IADzDD,EAAA,CAAAE,UAAA,mBAAAuD,uEAAA;MAAAzD,EAAA,CAAAI,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,gBAAA,CAAA5C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAE/FnB,EAAA,CAAAU,SAAA,aAAoC;IAExCV,EADE,CAAAW,YAAA,EAAS,EACN;IAIHX,EADF,CAAAC,cAAA,cAA0G,kBAEX;IAD1DD,EAAA,CAAAE,UAAA,mBAAAwD,uEAAA;MAAA1D,EAAA,CAAAI,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,gBAAA,CAAA5C,MAAA,CAAAY,kBAAA,CAAAO,UAAA,CAA+C;IAAA,EAAC;IAE1FzB,EAAA,CAAAU,SAAA,aAAyC;IAMrDV,EALU,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACF,EACF;;;;IA7CEX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAwB,kBAAA,aAAAlB,MAAA,CAAAY,kBAAA,CAAAC,WAAA,0BAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,aACF;IAKwBzB,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,OAAuD;IAEzEnB,EAAA,CAAAa,SAAA,EAAiD;IAAjDb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAAY,kBAAA,CAAAC,WAAA,OAAiD;IAM/BnB,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,OAAuD;IAEzEnB,EAAA,CAAAa,SAAA,EAAiD;IAAjDb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAAY,kBAAA,CAAAC,WAAA,OAAiD;IAMdnB,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAuC,UAAA,YAAAjC,MAAA,CAAAqD,sBAAA,GAA2B;IAM5C3D,EAAA,CAAAa,SAAA,EAAmF;IAAnFb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,KAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,CAAmF;IAErGzB,EAAA,CAAAa,SAAA,EAA6E;IAA7Eb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAAY,kBAAA,CAAAC,WAAA,KAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,CAA6E;IAM3DzB,EAAA,CAAAa,SAAA,GAAmF;IAAnFb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,KAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,CAAmF;IAErGzB,EAAA,CAAAa,SAAA,EAA6E;IAA7Eb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAAY,kBAAA,CAAAC,WAAA,KAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,CAA6E;;;;;IA9G3FzB,EAAA,CAAAC,cAAA,cAA+D;IAE7DD,EAAA,CAAAmC,UAAA,IAAAyB,6CAAA,kBAA0E;IAkB1E5D,EAAA,CAAAC,cAAA,cAAiC;IA2B/BD,EA1BA,CAAAmC,UAAA,IAAA0B,6CAAA,mBAA8F,IAAAC,6CAAA,kBA0BD;IAkB/F9D,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAmC,UAAA,IAAA4B,6CAAA,oBAA0F;IAkD5F/D,EAAA,CAAAW,YAAA,EAAM;;;;IApH6BX,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAY,kBAAA,CAAAK,UAAA,KAAuC;IAmB3BvB,EAAA,CAAAa,SAAA,GAAuB;IAAAb,EAAvB,CAAAuC,UAAA,YAAAjC,MAAA,CAAA0D,kBAAA,CAAuB,iBAAA1D,MAAA,CAAA2D,iBAAA,CAA0B;IA0B7DjE,EAAA,CAAAa,SAAA,EAA4D;IAA5Db,EAAA,CAAAuC,UAAA,UAAAjC,MAAA,CAAA0D,kBAAA,IAAA1D,MAAA,CAAA0D,kBAAA,CAAAhD,MAAA,OAA4D;IAqB5ChB,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAY,kBAAA,CAAAO,UAAA,KAAuC;;;;;IAmElFzB,EAAA,CAAAC,cAAA,aAAgE;IAC9DD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;IADFX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAA4D,gBAAA,CAAA5B,WAAA,MACF;;;;;IAUAtC,EADF,CAAAC,cAAA,cAA+D,eACpC;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAClCX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAY,MAAA,GAAsE;IACjGZ,EADiG,CAAAW,YAAA,EAAO,EAClG;;;;IADqBX,EAAA,CAAAa,SAAA,GAAsE;IAAtEb,EAAA,CAAAwB,kBAAA,KAAAlB,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,SAAAb,MAAA,CAAA6D,gBAAA,CAAA1C,UAAA,KAAsE;;;;;;IAoB3FzB,EAAA,CAAAC,cAAA,kBACyC;IADfD,EAAA,CAAAE,UAAA,mBAAAkE,0EAAA;MAAApE,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAD,MAAA,CAAAgE,mBAAA,GAA6B,EAAE;MAAA,OAAAtE,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAiE,qBAAA,CAAsB,EAAE,CAAC;IAAA,EAAC;IAEnFvE,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAW,YAAA,EAAS;;;;;IAIXX,EAAA,CAAAC,cAAA,iBAAuE;IACrED,EAAA,CAAAU,SAAA,aAAuC;IAAAV,EAAA,CAAAY,MAAA,GACzC;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;;;;IADiCX,EAAA,CAAAa,SAAA,GACzC;IADyCb,EAAA,CAAAc,kBAAA,kBAAAR,MAAA,CAAA6D,gBAAA,CAAA5C,UAAA,qCACzC;;;;;IACAvB,EAAA,CAAAC,cAAA,gBAA4E;IAC1ED,EAAA,CAAAU,SAAA,YAAgD;IAAAV,EAAA,CAAAY,MAAA,oEAClD;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;;;;;IANVX,EAAA,CAAAC,cAAA,eAA6D;IAI3DD,EAHA,CAAAmC,UAAA,IAAAqC,sDAAA,qBAAuE,IAAAC,sDAAA,qBAGK;IAG9EzE,EAAA,CAAAW,YAAA,EAAM;;;;IANsBX,EAAA,CAAAa,SAAA,EAA2C;IAA3Cb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAoE,0BAAA,CAAA1D,MAAA,KAA2C;IAGxChB,EAAA,CAAAa,SAAA,EAA6C;IAA7Cb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAoE,0BAAA,CAAA1D,MAAA,OAA6C;;;;;IA0BpEhB,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAU,SAAA,aAAkC;IAClCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IAC/BZ,EAD+B,CAAAW,YAAA,EAAO,EAC/B;;;;IADCX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAoD,iBAAA,CAAAuB,UAAA,CAAAC,UAAA,CAAuB;;;;;IAE/B5E,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAU,SAAA,aAA0B;IAC1BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAC9BZ,EAD8B,CAAAW,YAAA,EAAO,EAC9B;;;;IADCX,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAoD,iBAAA,CAAAuB,UAAA,CAAAE,SAAA,CAAsB;;;;;IAa9B7E,EADF,CAAAC,cAAA,eAAgE,gBACnC;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACpCX,EAAA,CAAAC,cAAA,gBAAuC;IAAAD,EAAA,CAAAY,MAAA,GAAoC;;IAC7EZ,EAD6E,CAAAW,YAAA,EAAO,EAC9E;;;;IADmCX,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAAc,kBAAA,SAAAd,EAAA,CAAA8E,WAAA,OAAAH,UAAA,CAAAI,UAAA,MAAoC;;;;;IAG3E/E,EADF,CAAAC,cAAA,eAAkF,gBACrD;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACpCX,EAAA,CAAAC,cAAA,gBAA0C;IAAAD,EAAA,CAAAY,MAAA,GAAyC;IACrFZ,EADqF,CAAAW,YAAA,EAAO,EACtF;;;;IADsCX,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAwB,kBAAA,KAAAmD,UAAA,CAAAK,SAAA,OAAAL,UAAA,CAAAM,KAAA,KAAyC;;;;;IAIrFjF,EADF,CAAAC,cAAA,eAAgD,eAClB;IAC1BD,EAAA,CAAAU,SAAA,aAAkD;IAClDV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IAElDZ,EAFkD,CAAAW,YAAA,EAAO,EACjD,EACF;;;;IAFwBX,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAoD,iBAAA,CAAAuB,UAAA,CAAAO,OAAA,CAAoB;;;;;IAblDlF,EAFF,CAAAC,cAAA,eACkF,eACjD;IAK7BD,EAJA,CAAAmC,UAAA,IAAAgD,iEAAA,mBAAgE,IAAAC,iEAAA,mBAIkB;IAIpFpF,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAmC,UAAA,IAAAkD,iEAAA,mBAAgD;IAMlDrF,EAAA,CAAAW,YAAA,EAAM;;;;IAfqCX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAuC,UAAA,SAAAoC,UAAA,CAAAI,UAAA,CAAuB;IAIpB/E,EAAA,CAAAa,SAAA,EAAsC;IAAtCb,EAAA,CAAAuC,UAAA,SAAAoC,UAAA,CAAAK,SAAA,IAAAL,UAAA,CAAAM,KAAA,CAAsC;IAKxDjF,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAAuC,UAAA,SAAAoC,UAAA,CAAAO,OAAA,CAAoB;;;;;IAxC5ClF,EAJR,CAAAC,cAAA,eAAmG,eACnE,eACI,eACN,gBACI;IAAAD,EAAA,CAAAY,MAAA,GACtB;IACNZ,EADM,CAAAW,YAAA,EAAO,EACP;IAEJX,EADF,CAAAC,cAAA,eAA4B,cACJ;IACpBD,EAAA,CAAAU,SAAA,aAA8C;IAC9CV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAEHX,EADF,CAAAC,cAAA,gBAAuB,iBACW;IAC9BD,EAAA,CAAAU,SAAA,cAA8B;IAC9BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,IAAuB;IAC/BZ,EAD+B,CAAAW,YAAA,EAAO,EAC/B;IAKPX,EAJA,CAAAmC,UAAA,KAAAmD,4DAAA,oBAA6D,KAAAC,4DAAA,oBAIE;IAKnEvF,EADE,CAAAW,YAAA,EAAM,EACF;IAEJX,EADF,CAAAC,cAAA,gBAA0B,iBACE;IAAAD,EAAA,CAAAY,MAAA,IAAqC;;IAEnEZ,EAFmE,CAAAW,YAAA,EAAO,EAClE,EACF;IAENX,EAAA,CAAAmC,UAAA,KAAAqD,2DAAA,mBACkF;IAmBtFxF,EADE,CAAAW,YAAA,EAAM,EACF;;;;;;IAhD4BX,EAAA,CAAAa,SAAA,GACtB;IADsBb,EAAA,CAAAoD,iBAAA,EAAA9C,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,QAAAb,MAAA,CAAA6D,gBAAA,CAAA/C,QAAA,GAAAqE,KAAA,KACtB;IAKFzF,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA6D,UAAA,CAAAe,YAAA,MACF;IAIU1F,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAoD,iBAAA,CAAAuB,UAAA,CAAAgB,UAAA,CAAuB;IAEK3F,EAAA,CAAAa,SAAA,EAAuB;IAAvBb,EAAA,CAAAuC,UAAA,SAAAoC,UAAA,CAAAC,UAAA,CAAuB;IAIpB5E,EAAA,CAAAa,SAAA,EAAsB;IAAtBb,EAAA,CAAAuC,UAAA,SAAAoC,UAAA,CAAAE,SAAA,CAAsB;IAOrC7E,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAoD,iBAAA,CAAApD,EAAA,CAAA4F,WAAA,QAAAjB,UAAA,CAAAkB,SAAA,WAAqC;IAKhE7F,EAAA,CAAAa,SAAA,GAA6E;IAA7Eb,EAAA,CAAAuC,UAAA,SAAAoC,UAAA,CAAAI,UAAA,IAAAJ,UAAA,CAAAK,SAAA,IAAAL,UAAA,CAAAM,KAAA,IAAAN,UAAA,CAAAO,OAAA,CAA6E;;;;;IAlCtFlF,EAAA,CAAAC,cAAA,eAAsG;IACpGD,EAAA,CAAAmC,UAAA,IAAA2D,oDAAA,qBAAmG;IAqDrG9F,EAAA,CAAAW,YAAA,EAAM;;;;IArDoBX,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAAuC,UAAA,YAAAjC,MAAA,CAAAoE,0BAAA,CAA+B;;;;;IA6DjD1E,EAHJ,CAAAC,cAAA,eACmE,eACvC,gBACQ;IAAAD,EAAA,CAAAY,MAAA,GACxB;IACVZ,EADU,CAAAW,YAAA,EAAO,EACX;IAGFX,EAFJ,CAAAC,cAAA,eAA6C,eACjB,aAChB;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IACjCZ,EADiC,CAAAW,YAAA,EAAS,EACpC;IACNX,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAY,MAAA,GACF;IAEJZ,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;;;IAX8BX,EAAA,CAAAa,SAAA,GACxB;IADwBb,EAAA,CAAAoD,iBAAA,EAAA9C,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,QAAAb,MAAA,CAAA6D,gBAAA,CAAA/C,QAAA,GAAA2E,KAAA,KACxB;IAIE/F,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,kBAAA,KAAAkF,UAAA,CAAAC,SAAA,MAAuB;IAG/BjG,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAkF,UAAA,CAAAE,UAAA,MACF;;;;;IAbNlG,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAmC,UAAA,IAAAgE,kEAAA,oBACmE;IAcrEnG,EAAA,CAAAW,YAAA,EAAM;;;;IAfoBX,EAAA,CAAAa,SAAA,EAAqB;IAArBb,EAAA,CAAAuC,UAAA,YAAAjC,MAAA,CAAA8F,gBAAA,CAAqB;;;;;IAD/CpG,EAAA,CAAAmC,UAAA,IAAAkE,4DAAA,mBAAmF;;;;;;IAApCrG,EAAzC,CAAAuC,UAAA,SAAAjC,MAAA,CAAAgG,sBAAA,CAAAtF,MAAA,KAAyC,aAAAuF,aAAA,CAAc;;;;;;IAkCvDvG,EAFF,CAAAC,cAAA,aACyD,kBACE;IAA/BD,EAAA,CAAAE,UAAA,mBAAAsG,4EAAA;MAAA,MAAAC,QAAA,GAAAzG,EAAA,CAAAI,aAAA,CAAAsG,IAAA,EAAA7E,SAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqG,cAAA,CAAAF,QAAA,CAAoB;IAAA,EAAC;IAACzG,EAAA,CAAAY,MAAA,GAAU;IACrEZ,EADqE,CAAAW,YAAA,EAAS,EACzE;;;;;IAFHX,EAAA,CAAAmD,WAAA,WAAAsD,QAAA,KAAAnG,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,CAAsD;IACGnB,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAAoD,iBAAA,CAAAqD,QAAA,CAAU;;;;;;IATnEzG,EALR,CAAAC,cAAA,eAA4E,eACjD,cAC0C,aAEa,kBAExB;IADxBD,EAAA,CAAAE,UAAA,mBAAA0G,uEAAA;MAAA5G,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqG,cAAA,CAAArG,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElFnB,EAAA,CAAAU,SAAA,YAAmC;IAEvCV,EADE,CAAAW,YAAA,EAAS,EACN;IAGLX,EAAA,CAAAmC,UAAA,IAAA2E,mDAAA,iBACyD;IAMvD9G,EADF,CAAAC,cAAA,aAAsG,kBAExB;IADlDD,EAAA,CAAAE,UAAA,mBAAA6G,uEAAA;MAAA/G,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqG,cAAA,CAAArG,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElFnB,EAAA,CAAAU,SAAA,YAAoC;IAK9CV,EAJQ,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAtBsBX,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,OAAqD;IAEvEnB,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,OAA+C;IAMZnB,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAuC,UAAA,YAAAjC,MAAA,CAAA0G,oBAAA,GAAyB;IAM1ChH,EAAA,CAAAa,SAAA,EAA+E;IAA/Eb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,KAAAb,MAAA,CAAA6D,gBAAA,CAAA1C,UAAA,CAA+E;IAEjGzB,EAAA,CAAAa,SAAA,EAAyE;IAAzEb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,KAAAb,MAAA,CAAA6D,gBAAA,CAAA1C,UAAA,CAAyE;;;;;IASjFzB,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAU,SAAA,aAAkD;IAClDV,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAY,MAAA,iDAAO;IACpCZ,EADoC,CAAAW,YAAA,EAAI,EAClC;;;;;;IA1KJX,EALR,CAAAC,cAAA,cAA2D,cAE9B,cACS,cACP,iBACiD;IAA/CD,EAAA,CAAAE,UAAA,mBAAA+G,gEAAA;MAAAjH,EAAA,CAAAI,aAAA,CAAA8G,IAAA;MAAA,MAAA5G,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6G,mBAAA,EAAqB;IAAA,EAAC;IACtDnH,EAAA,CAAAU,SAAA,YAAiC;IAErCV,EADE,CAAAW,YAAA,EAAS,EACL;IAEJX,EADF,CAAAC,cAAA,cAA+B,aACJ;IACvBD,EAAA,CAAAU,SAAA,YAAiD;IACjDV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAmC,UAAA,KAAAiF,4CAAA,gBAAgE;IAIpEpH,EADE,CAAAW,YAAA,EAAM,EACF;IAGFX,EAFJ,CAAAC,cAAA,eAA0B,eACD,gBACI;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACpCX,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAY,MAAA,IACS;IACpCZ,EADoC,CAAAW,YAAA,EAAO,EACrC;IACNX,EAAA,CAAAmC,UAAA,KAAAkF,8CAAA,kBAA+D;IAKnErH,EADE,CAAAW,YAAA,EAAM,EACF;IAQEX,EALR,CAAAC,cAAA,eAA4B,gBAEc,gBACH,gBACM,eACZ;IACvBD,EAAA,CAAAU,SAAA,aAA6B;IAC/BV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,kBAC+F;IAA7FD,EAAA,CAAAsH,gBAAA,2BAAAC,wEAAAC,MAAA;MAAAxH,EAAA,CAAAI,aAAA,CAAA8G,IAAA;MAAA,MAAA5G,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAyH,kBAAA,CAAAnH,MAAA,CAAAgE,mBAAA,EAAAkD,MAAA,MAAAlH,MAAA,CAAAgE,mBAAA,GAAAkD,MAAA;MAAA,OAAAxH,EAAA,CAAAQ,WAAA,CAAAgH,MAAA;IAAA,EAAiC;IAACxH,EAAA,CAAAE,UAAA,yBAAAwH,sEAAA;MAAA1H,EAAA,CAAAI,aAAA,CAAA8G,IAAA;MAAA,MAAA5G,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAiE,qBAAA,CAAAjE,MAAA,CAAAgE,mBAAA,CAA0C;IAAA,EAAC;IAD9FtE,EAAA,CAAAW,YAAA,EAC+F;IAE7FX,EADF,CAAAC,cAAA,eAA4B,mBACiE;IAAhED,EAAA,CAAAE,UAAA,mBAAAyH,iEAAA;MAAA3H,EAAA,CAAAI,aAAA,CAAA8G,IAAA;MAAA,MAAA5G,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiE,qBAAA,CAAAjE,MAAA,CAAAgE,mBAAA,CAA0C;IAAA,EAAC;IAC7EtE,EAAA,CAAAU,SAAA,aAA6B;IAC/BV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAmC,UAAA,KAAAyF,iDAAA,sBACyC;IAI7C5H,EADE,CAAAW,YAAA,EAAM,EACF;IACNX,EAAA,CAAAmC,UAAA,KAAA0F,8CAAA,mBAA6D;IASjE7H,EADE,CAAAW,YAAA,EAAM,EACF;IA6GNX,EA1GA,CAAAmC,UAAA,KAAA2F,8CAAA,mBAAsG,KAAAC,sDAAA,gCAAA/H,EAAA,CAAAgI,sBAAA,CAyDxE,KAAAC,8CAAA,oBAqB8C,KAAAC,sDAAA,gCAAAlI,EAAA,CAAAgI,sBAAA,CA4BpD;IAO5BhI,EADE,CAAAW,YAAA,EAAM,EACF;;;;;IAtKIX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAA4D,gBAAA,CAAA7B,YAAA,MACF;IAC4BrC,EAAA,CAAAa,SAAA,EAAkC;IAAlCb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAA4D,gBAAA,CAAA5B,WAAA,CAAkC;IAQrCtC,EAAA,CAAAa,SAAA,GACS;IADTb,EAAA,CAAAoD,iBAAA,CAAA9C,MAAA,CAAAoE,0BAAA,CAAA1D,MAAA,OAAAV,MAAA,CAAA6D,gBAAA,CAAA5C,UAAA,GAAAjB,MAAA,CAAAgG,sBAAA,CAAAtF,MAAA,CACS;IAEZhB,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAA6D,gBAAA,CAAA1C,UAAA,KAAqC;IAiBvDzB,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAmI,gBAAA,YAAA7H,MAAA,CAAAgE,mBAAA,CAAiC;IAM9BtE,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAgE,mBAAA,CAAyB;IAKEtE,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAgE,mBAAA,CAAyB;IAYzDtE,EAAA,CAAAa,SAAA,EAA6C;IAAAb,EAA7C,CAAAuC,UAAA,SAAAjC,MAAA,CAAAoE,0BAAA,CAAA1D,MAAA,KAA6C,aAAAoH,mBAAA,CAAoB;IA8ElCpI,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAA6D,gBAAA,CAAA1C,UAAA,KAAqC;;;ADxSlF,OAAM,MAAO4G,uBAAuB;EAyClCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAxC1B,KAAAC,YAAY,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAC,cAAc,GAAG,IAAI9I,YAAY,EAAY;IAC7C,KAAA+I,KAAK,GAAG,IAAI/I,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAA0B,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAsH,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE,CAAC,CAAC;IACxC,KAAA1E,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAQ,0BAA0B,GAAyB,EAAE;IACrD,KAAAJ,mBAAmB,GAAG,EAAE;IAExB;IACA,KAAAxB,aAAa,GAAG,EAAE;IAClB,KAAA/B,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAG,kBAAkB,GAAG;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE,CAAC;MACbE,UAAU,EAAE;KACb;IACD,KAAAuC,kBAAkB,GAAe,EAAE;IAEnC;IACA,KAAAG,gBAAgB,GAAG;MACjBhD,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXG,UAAU,EAAE,CAAC;MACbE,UAAU,EAAE;KACb;IACD,KAAA2E,gBAAgB,GAAqB,EAAE;EAIiB;EAExDyC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACA,MAAMG,mBAAmB,GAAwB;MAC/CC,aAAa,EAAE,IAAI,CAACV,YAAY;MAAE;MAClCW,SAAS,EAAE,CAAC;MAAE;MACdC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAACd,eAAe,CAACe,mCAAmC,CAAC;MACvDC,IAAI,EAAEN;KACP,CAAC,CAACO,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UAEA;UACA,IAAI,CAACjB,SAAS,GAAGe,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7ChI,UAAU,EAAEgI,IAAI,CAACC,WAAW;YAC5B1H,YAAY,EAAEyH,IAAI,CAACT,aAAa,IAAI,EAAE;YACtC/G,WAAW,EAAE,SAASwH,IAAI,CAACjE,SAAS,GAAG,IAAImE,IAAI,CAACF,IAAI,CAACjE,SAAS,CAAC,CAACoE,kBAAkB,EAAE,GAAG,IAAI;WAC5F,CAAC,CAAC;UAEH;UACA,IAAI,CAAClB,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAACH,eAAe,GAAG,EAAE;UACzB,IAAI,CAAClE,0BAA0B,GAAG,EAAE;QACtC,CAAC,MAAM;UACL;UACA,IAAI,CAACiE,SAAS,GAAG,EAAE;UACnB,IAAI,CAACC,eAAe,GAAG,EAAE;UACzB,IAAI,CAACG,uBAAuB,EAAE;QAChC;MACF,CAAC;MACDmB,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACvB,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACG,uBAAuB,EAAE;MAChC;KACD,CAAC;EACJ;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACjG,aAAa,CAACqH,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACpJ,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAAC4H,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMyB,OAAO,GAAG,IAAI,CAACtH,aAAa,CAACuH,WAAW,EAAE;MAChD,IAAI,CAACtJ,iBAAiB,GAAG,IAAI,CAAC4H,SAAS,CAAC2B,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAAClI,YAAY,CAACgI,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAACjI,WAAW,IAAIiI,QAAQ,CAACjI,WAAW,CAAC+H,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;IACA,IAAI,CAACK,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,IAAI,CAACvJ,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACR,iBAAiB,CAACC,MAAM;IAClE,IAAI,CAACE,kBAAkB,CAACO,UAAU,GAAGJ,IAAI,CAACqJ,IAAI,CAAC,IAAI,CAACxJ,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACL,kBAAkB,CAACE,QAAQ,CAAC;IAErH;IACA,IAAI,IAAI,CAACF,kBAAkB,CAACC,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACO,UAAU,EAAE;MAC5E,IAAI,CAACP,kBAAkB,CAACC,WAAW,GAAGE,IAAI,CAACsJ,GAAG,CAAC,CAAC,EAAE,IAAI,CAACzJ,kBAAkB,CAACO,UAAU,CAAC;IACvF;IAEA,IAAI,CAACmJ,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAAC3J,kBAAkB,CAACC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,kBAAkB,CAACE,QAAQ;IAC/F,MAAM0J,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC3J,kBAAkB,CAACE,QAAQ;IAC9D,IAAI,CAAC4C,kBAAkB,GAAG,IAAI,CAACjD,iBAAiB,CAACgK,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACA5H,gBAAgBA,CAAC8H,IAAY;IAC3B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC9J,kBAAkB,CAACO,UAAU,EAAE;MAC3D,IAAI,CAACP,kBAAkB,CAACC,WAAW,GAAG6J,IAAI;MAC1C,IAAI,CAACJ,wBAAwB,EAAE;IACjC;EACF;EAEA;EACAjH,sBAAsBA,CAAA;IACpB,MAAMsH,KAAK,GAAa,EAAE;IAC1B,MAAMxJ,UAAU,GAAG,IAAI,CAACP,kBAAkB,CAACO,UAAU;IACrD,MAAMN,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACC,WAAW;IAEvD;IACA,MAAM+J,SAAS,GAAG7J,IAAI,CAACsJ,GAAG,CAAC,CAAC,EAAExJ,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMgK,OAAO,GAAG9J,IAAI,CAACC,GAAG,CAACG,UAAU,EAAEN,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIiK,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACAK,QAAQA,CAAA;IACN,IAAI,CAACvC,uBAAuB,EAAE;EAChC;EAEA;EACAtI,WAAWA,CAAA;IACT,IAAI,CAACqC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACiG,uBAAuB,EAAE;EAChC;EAQA;EACA7G,gBAAgBA,CAACqI,QAAkB;IACjC,IAAI,CAACrG,gBAAgB,GAAGqG,QAAQ;IAChC,IAAI,CAAC9B,cAAc,CAAC8C,IAAI,CAAChB,QAAQ,CAAC;IAElC;IACA,IAAIA,QAAQ,CAACzI,UAAU,EAAE;MACvB,IAAI,CAAC0J,mBAAmB,CAACjB,QAAQ,CAACzI,UAAU,CAAC;IAC/C;IAEA,IAAI,CAAC2J,sBAAsB,EAAE;EAC/B;EAEA;EACAD,mBAAmBA,CAACE,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAE7I,aAAsB;IACnF,MAAM8I,IAAI,GAA8B;MACtCF,UAAU,EAAEA;KACb;IAED;IACA,IAAI,CAACnD,eAAe,CAACsD,yCAAyC,CAAC;MAC7DtC,IAAI,EAAEqC;KACP,CAAC,CAACpC,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UAEjD;UACA,IAAIkC,UAAU,GAAGpC,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7CiC,iBAAiB,EAAEjC,IAAI,CAACiC,iBAAiB,IAAI,CAAC;YAC9ChC,WAAW,EAAED,IAAI,CAACC,WAAW,IAAI2B,UAAU;YAC3C/F,UAAU,EAAEmE,IAAI,CAACnE,UAAU,IAAI,CAAC;YAChCD,YAAY,EAAEoE,IAAI,CAACpE,YAAY,IAAI,EAAE;YACrCd,UAAU,EAAEkF,IAAI,CAAClF,UAAU,IAAI,EAAE;YAAE;YACnCoH,KAAK,EAAEC,SAAS;YAChB/G,OAAO,EAAE+G,SAAS;YAClBpG,SAAS,EAAE,IAAImE,IAAI,EAAE,CAACkC,WAAW,EAAE;YACnCC,QAAQ,EAAE,IAAI;YACdtH,SAAS,EAAEoH,SAAS;YACpBlH,UAAU,EAAEkH,SAAS;YACrBjH,SAAS,EAAEiH,SAAS;YACpBhH,KAAK,EAAEgH;WACe,EAAC;UAEzB;UACA,IAAInJ,aAAa,IAAIA,aAAa,CAACqH,IAAI,EAAE,EAAE;YACzC2B,UAAU,GAAGA,UAAU,CAACxB,MAAM,CAAC8B,MAAM,IACnCA,MAAM,CAAC1G,YAAY,CAAC2E,WAAW,EAAE,CAACG,QAAQ,CAAC1H,aAAa,CAACuH,WAAW,EAAE,CAAC,IACtE+B,MAAM,CAACxH,UAAU,IAAIwH,MAAM,CAACxH,UAAU,CAACyF,WAAW,EAAE,CAACG,QAAQ,CAAC1H,aAAa,CAACuH,WAAW,EAAE,CAAE,CAC7F;UACH;UAEA;UACA,MAAMQ,UAAU,GAAG,CAACc,SAAS,GAAG,CAAC,IAAI,IAAI,CAACxH,gBAAgB,CAAC/C,QAAQ;UACnE,MAAM0J,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC1G,gBAAgB,CAAC/C,QAAQ;UAC5D,MAAMiL,YAAY,GAAGP,UAAU,CAACf,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;UAE3D;UACA,IAAI,CAACpG,0BAA0B,GAAG2H,YAAY;UAC9C,IAAI,CAAClI,gBAAgB,CAAC5C,UAAU,GAAGuK,UAAU,CAAC9K,MAAM;UACpD,IAAI,CAACmD,gBAAgB,CAAC1C,UAAU,GAAGJ,IAAI,CAACqJ,IAAI,CAACoB,UAAU,CAAC9K,MAAM,GAAG,IAAI,CAACmD,gBAAgB,CAAC/C,QAAQ,CAAC;UAChG,IAAI,CAAC+C,gBAAgB,CAAChD,WAAW,GAAGwK,SAAS;QAC/C,CAAC,MAAM;UACL,IAAI,CAACjH,0BAA0B,GAAG,EAAE;UACpC,IAAI,CAACP,gBAAgB,CAAC5C,UAAU,GAAG,CAAC;UACpC,IAAI,CAAC4C,gBAAgB,CAAC1C,UAAU,GAAG,CAAC;UACpC,IAAI,CAAC0C,gBAAgB,CAAChD,WAAW,GAAG,CAAC;QACvC;MACF,CAAC;MACD+I,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACxF,0BAA0B,GAAG,EAAE;QACpC,IAAI,CAACP,gBAAgB,CAAC5C,UAAU,GAAG,CAAC;QACpC,IAAI,CAAC4C,gBAAgB,CAAC1C,UAAU,GAAG,CAAC;QACpC,IAAI,CAAC0C,gBAAgB,CAAChD,WAAW,GAAG,CAAC;QAErC;QACA,IAAI,CAACmL,uBAAuB,CAACZ,UAAU,EAAEC,SAAS,EAAE7I,aAAa,CAAC;MACpE;KACD,CAAC;EACJ;EAEA;EACQwJ,uBAAuBA,CAACZ,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAE7I,aAAsB;IAC/F;IACAyJ,UAAU,CAAC,MAAK;MACd;MACA,MAAMC,WAAW,GAAyB,EAAE;MAC5C,MAAMC,gBAAgB,GAAG,CAAC,CAAC,CAAC;MAE5B,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIqB,gBAAgB,EAAErB,CAAC,EAAE,EAAE;QAC1C,MAAMgB,MAAM,GAAuB;UACjCL,iBAAiB,EAAEL,UAAU,GAAG,GAAG,GAAGN,CAAC;UACvCrB,WAAW,EAAE2B,UAAU;UACvB/F,UAAU,EAAE+F,UAAU,GAAG,IAAI,GAAGN,CAAC;UACjC1F,YAAY,EAAE,OAAOgH,MAAM,CAACC,YAAY,CAAC,EAAE,GAAIjB,UAAU,GAAG,EAAG,GAAG,CAAC,CAAC,IAAIN,CAAC,EAAE;UAC3ExG,UAAU,EAAEwG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,MAAQ;UAAE;UAC7FY,KAAK,EAAEZ,CAAC;UACRlG,OAAO,EAAEkG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQA,CAAC,EAAE,GAAGa,SAAS;UAC9CpG,SAAS,EAAE,IAAImE,IAAI,CAACA,IAAI,CAAC4C,GAAG,EAAE,GAAGvL,IAAI,CAACwL,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACX,WAAW,EAAE;UACxFC,QAAQ,EAAE,OAAO;UACjBtH,SAAS,EAAEuG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM;UACxCrG,UAAU,EAAE1D,IAAI,CAACyL,KAAK,CAACzL,IAAI,CAACwL,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI;UACpD7H,SAAS,EAAE3D,IAAI,CAACyL,KAAK,CAACzL,IAAI,CAACwL,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;UAC7C5H,KAAK,EAAEmG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG;SACjD;QACDoB,WAAW,CAACnB,IAAI,CAACe,MAAM,CAAC;MAC1B;MAEA;MACA,IAAIW,eAAe,GAAGP,WAAW;MACjC,IAAI1J,aAAa,IAAIA,aAAa,CAACqH,IAAI,EAAE,EAAE;QACzC4C,eAAe,GAAGP,WAAW,CAAClC,MAAM,CAAC8B,MAAM,IACzCA,MAAM,CAAC1G,YAAY,CAAC2E,WAAW,EAAE,CAACG,QAAQ,CAAC1H,aAAa,CAACuH,WAAW,EAAE,CAAC,IACtE+B,MAAM,CAACxH,UAAU,IAAIwH,MAAM,CAACxH,UAAU,CAACyF,WAAW,EAAE,CAACG,QAAQ,CAAC1H,aAAa,CAACuH,WAAW,EAAE,CAAE,IAC3F+B,MAAM,CAAClH,OAAO,IAAIkH,MAAM,CAAClH,OAAO,CAACmF,WAAW,EAAE,CAACG,QAAQ,CAAC1H,aAAa,CAACuH,WAAW,EAAE,CAAE,IACrF+B,MAAM,CAACvH,SAAS,IAAIuH,MAAM,CAACvH,SAAS,CAACwF,WAAW,EAAE,CAACG,QAAQ,CAAC1H,aAAa,CAACuH,WAAW,EAAE,CAAE,CAC3F;MACH;MAEA;MACA,MAAMQ,UAAU,GAAG,CAACc,SAAS,GAAG,CAAC,IAAI,IAAI,CAACxH,gBAAgB,CAAC/C,QAAQ;MACnE,MAAM0J,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC1G,gBAAgB,CAAC/C,QAAQ;MAC5D,MAAMiL,YAAY,GAAGU,eAAe,CAAChC,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;MAEhE;MACA,IAAI,CAACpG,0BAA0B,GAAG2H,YAAY;MAC9C,IAAI,CAAClI,gBAAgB,CAAC5C,UAAU,GAAGwL,eAAe,CAAC/L,MAAM;MACzD,IAAI,CAACmD,gBAAgB,CAAC1C,UAAU,GAAGJ,IAAI,CAACqJ,IAAI,CAACqC,eAAe,CAAC/L,MAAM,GAAG,IAAI,CAACmD,gBAAgB,CAAC/C,QAAQ,CAAC;MACrG,IAAI,CAAC+C,gBAAgB,CAAChD,WAAW,GAAGwK,SAAS;IAC/C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACApH,qBAAqBA,CAAC6F,OAAe;IACnC,IAAI,CAAC9F,mBAAmB,GAAG8F,OAAO;IAClC,IAAI,IAAI,CAAClG,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACpC,UAAU,EAAE;MAC7D,IAAI,CAAC0J,mBAAmB,CAAC,IAAI,CAACtH,gBAAgB,CAACpC,UAAU,EAAE,CAAC,EAAEsI,OAAO,CAAC;IACxE;EACF;EAEA;EACAqB,sBAAsBA,CAAA;IACpB;IACA,IAAI,IAAI,CAAC/G,0BAA0B,CAAC1D,MAAM,GAAG,CAAC,EAAE;MAC9C;IACF;IAEA;IACA,MAAMgM,OAAO,GAAG,IAAI,CAAC1G,sBAAsB;IAC3C,IAAI,CAACnC,gBAAgB,CAAC5C,UAAU,GAAGyL,OAAO,CAAChM,MAAM;IACjD,IAAI,CAACmD,gBAAgB,CAAC1C,UAAU,GAAGJ,IAAI,CAACqJ,IAAI,CAAC,IAAI,CAACvG,gBAAgB,CAAC5C,UAAU,GAAG,IAAI,CAAC4C,gBAAgB,CAAC/C,QAAQ,CAAC;IAC/G,IAAI,CAAC+C,gBAAgB,CAAChD,WAAW,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC8L,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMD,OAAO,GAAG,IAAI,CAAC1G,sBAAsB;IAC3C,MAAMuE,UAAU,GAAG,CAAC,IAAI,CAAC1G,gBAAgB,CAAChD,WAAW,GAAG,CAAC,IAAI,IAAI,CAACgD,gBAAgB,CAAC/C,QAAQ;IAC3F,MAAM0J,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC1G,gBAAgB,CAAC/C,QAAQ;IAC5D,IAAI,CAACgF,gBAAgB,GAAG4G,OAAO,CAACjC,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC7D;EAEA;EACAnE,cAAcA,CAACqE,IAAY;IACzB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC7G,gBAAgB,CAAC1C,UAAU,EAAE;MACzD;MACA,IAAI,IAAI,CAACyC,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACpC,UAAU,IAAI,IAAI,CAAC4C,0BAA0B,CAAC1D,MAAM,GAAG,CAAC,EAAE;QAC3G,IAAI,CAACwK,mBAAmB,CAAC,IAAI,CAACtH,gBAAgB,CAACpC,UAAU,EAAEkJ,IAAI,EAAE,IAAI,CAAC1G,mBAAmB,CAAC;MAC5F,CAAC,MAAM;QACL;QACA,IAAI,CAACH,gBAAgB,CAAChD,WAAW,GAAG6J,IAAI;QACxC,IAAI,CAACiC,sBAAsB,EAAE;MAC/B;IACF;EACF;EAEA;EACAjG,oBAAoBA,CAAA;IAClB,MAAMiE,KAAK,GAAa,EAAE;IAC1B,MAAMxJ,UAAU,GAAG,IAAI,CAAC0C,gBAAgB,CAAC1C,UAAU;IACnD,MAAMN,WAAW,GAAG,IAAI,CAACgD,gBAAgB,CAAChD,WAAW;IAErD;IACA,MAAM+J,SAAS,GAAG7J,IAAI,CAACsJ,GAAG,CAAC,CAAC,EAAExJ,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMgK,OAAO,GAAG9J,IAAI,CAACC,GAAG,CAACG,UAAU,EAAEN,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIiK,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACAiC,OAAOA,CAAA;IACL,IAAI,CAACxE,KAAK,CAAC6C,IAAI,EAAE;EACnB;EAEA;EACAxJ,gBAAgBA,CAACoL,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACA,MAAMG,kBAAkB,GAAwB;MAC9CvD,WAAW,EAAEoD;KACd;IAED;IACA,IAAI,CAAC5E,eAAe,CAACgF,kCAAkC,CAAC;MACtDhE,IAAI,EAAE+D;KACP,CAAC,CAAC9D,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACA;UACA,IAAI,CAACb,aAAa,EAAE;UAEpB;UACA,IAAI,IAAI,CAAC5E,gBAAgB,EAAEpC,UAAU,KAAKqL,UAAU,EAAE;YACpD,IAAI,CAACjJ,gBAAgB,GAAG,IAAI;UAC9B;QACF,CAAC,MAAM;UACL;QAAA;MAEJ,CAAC;MACDgG,KAAK,EAAEA,CAAA,KAAK;QACV;MAAA;KAEH,CAAC;EACJ;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA;EACA/C,mBAAmBA,CAAA;IACjB,IAAI,CAACjD,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAIoC,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACpC,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAAC0E,eAAe,CAAC0B,MAAM,CAACkD,CAAC,IAAIA,CAAC,CAAC1L,UAAU,KAAK,IAAI,CAACoC,gBAAiB,CAACpC,UAAU,CAAC;EAC7F;EAEA;EACAmC,iBAAiBA,CAACwJ,KAAa,EAAElD,QAAkB;IACjD,OAAOA,QAAQ,CAACzI,UAAU,IAAI2L,KAAK;EACrC;;;uCA3cWpF,uBAAuB,EAAArI,EAAA,CAAA0N,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvBvF,uBAAuB;MAAAwF,SAAA;MAAAC,MAAA;QAAAtF,YAAA;MAAA;MAAAuF,OAAA;QAAAtF,cAAA;QAAAC,KAAA;MAAA;MAAAsF,UAAA;MAAAC,QAAA,GAAAjO,EAAA,CAAAkO,oBAAA,EAAAlO,EAAA,CAAAmO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA/D,QAAA,WAAAgE,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5BxO,EAJR,CAAAC,cAAA,iBAAsC,wBACW,aACkB,aACnC,YACP;UACfD,EAAA,CAAAU,SAAA,WAAoD;UAAAV,EAAA,CAAAY,MAAA,gCACtD;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACLX,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAY,MAAA,yEAAW;UACvCZ,EADuC,CAAAW,YAAA,EAAQ,EACzC;UAEJX,EADF,CAAAC,cAAA,aAA4B,gBACK;UAAAD,EAAA,CAAAY,MAAA,IAAuC;UAG5EZ,EAH4E,CAAAW,YAAA,EAAO,EACzE,EACF,EACS;UAMTX,EALR,CAAAC,cAAA,wBAA2C,eAEG,eACd,eACM,eACL;UACvBD,EAAA,CAAAU,SAAA,aAA6B;UAC/BV,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAC,cAAA,iBACkD;UADqBD,EAAA,CAAAsH,gBAAA,2BAAAoH,iEAAAlH,MAAA;YAAAxH,EAAA,CAAAyH,kBAAA,CAAAgH,GAAA,CAAA3L,aAAA,EAAA0E,MAAA,MAAAiH,GAAA,CAAA3L,aAAA,GAAA0E,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAC3ExH,EAArB,CAAAE,UAAA,mBAAAyO,yDAAA;YAAA,OAASF,GAAA,CAAAnD,QAAA,EAAU;UAAA,EAAC,yBAAAsD,+DAAA;YAAA,OAAgBH,GAAA,CAAAnD,QAAA,EAAU;UAAA,EAAC;UADjDtL,EAAA,CAAAW,YAAA,EACkD;UAClDX,EAAA,CAAAmC,UAAA,KAAA0M,uCAAA,kBAAkD;UAKpD7O,EAAA,CAAAW,YAAA,EAAM;UAMNX,EALA,CAAAmC,UAAA,KAAA2M,uCAAA,kBAAsF,KAAAC,uCAAA,kBAKC;UAM3F/O,EADE,CAAAW,YAAA,EAAM,EACF;UA8HNX,EAzHA,CAAAmC,UAAA,KAAA6M,uCAAA,kBAA+D,KAAAC,uCAAA,oBAyHJ;UAmL7DjP,EAAA,CAAAW,YAAA,EAAe;UAGXX,EAFJ,CAAAC,cAAA,0BAA+C,eACjB,kBACoB;UAApBD,EAAA,CAAAE,UAAA,mBAAAgP,0DAAA;YAAA,OAAST,GAAA,CAAAvB,OAAA,EAAS;UAAA,EAAC;UAC3ClN,EAAA,CAAAU,SAAA,aAAiC;UAAAV,EAAA,CAAAY,MAAA,qBACnC;UAGNZ,EAHM,CAAAW,YAAA,EAAS,EACL,EACS,EACT;;;UAxV6BX,EAAA,CAAAa,SAAA,IAAuC;UAAvCb,EAAA,CAAAc,kBAAA,KAAA2N,GAAA,CAAAvN,kBAAA,CAAAK,UAAA,wBAAuC;UAYGvB,EAAA,CAAAa,SAAA,GAA2B;UAA3Bb,EAAA,CAAAmI,gBAAA,YAAAsG,GAAA,CAAA3L,aAAA,CAA2B;UAErE9C,EAAA,CAAAa,SAAA,EAAmB;UAAnBb,EAAA,CAAAuC,UAAA,SAAAkM,GAAA,CAAA3L,aAAA,CAAmB;UAMjB9C,EAAA,CAAAa,SAAA,EAAmD;UAAnDb,EAAA,CAAAuC,UAAA,SAAAkM,GAAA,CAAA3L,aAAA,IAAA2L,GAAA,CAAA1N,iBAAA,CAAAC,MAAA,KAAmD;UAKpDhB,EAAA,CAAAa,SAAA,EAAqD;UAArDb,EAAA,CAAAuC,UAAA,SAAAkM,GAAA,CAAA3L,aAAA,IAAA2L,GAAA,CAAA1N,iBAAA,CAAAC,MAAA,OAAqD;UAWnDhB,EAAA,CAAAa,SAAA,EAAuB;UAAvBb,EAAA,CAAAuC,UAAA,UAAAkM,GAAA,CAAAvK,gBAAA,CAAuB;UAyHvDlE,EAAA,CAAAa,SAAA,EAAsB;UAAtBb,EAAA,CAAAuC,UAAA,SAAAkM,GAAA,CAAAvK,gBAAA,CAAsB;;;qBD3JpBtE,YAAY,EAAAuP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAAH,EAAA,CAAAI,QAAA,EAAE1P,WAAW,EAAA2P,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAE7P,YAAY,EAAA8P,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAEjQ,cAAc;MAAAkQ,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}