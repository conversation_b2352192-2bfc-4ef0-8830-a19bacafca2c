{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction TemplateViewerComponent_div_13_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_13_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(2, \"i\", 30);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_13_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\", 32);\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u627E\\u5230 \", ctx_r1.filteredTemplates.length, \" \\u500B\\u76F8\\u95DC\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_div_13_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"small\", 35);\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵtext(3, \"\\u672A\\u627E\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"div\", 21)(3, \"div\", 22);\n    i0.ɵɵelement(4, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_13_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchKeyword, $event) || (ctx_r1.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function TemplateViewerComponent_div_13_Template_input_input_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    })(\"keyup.enter\", function TemplateViewerComponent_div_13_Template_input_keyup_enter_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_13_div_6_Template, 3, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, TemplateViewerComponent_div_13_div_7_Template, 4, 1, \"div\", 26)(8, TemplateViewerComponent_div_13_div_8_Template, 4, 0, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword && ctx_r1.filteredTemplates.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword && ctx_r1.filteredTemplates.length === 0);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 4)(2, \"div\", 43)(3, \"span\", 44);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 45)(6, \"small\", 8);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A\\u7B2C \", (ctx_r1.templatePagination.currentPage - 1) * ctx_r1.templatePagination.pageSize + 1, \" - \", ctx_r1.Math.min(ctx_r1.templatePagination.currentPage * ctx_r1.templatePagination.pageSize, ctx_r1.templatePagination.totalItems), \" \\u9805\\uFF0C \\u5171 \", ctx_r1.templatePagination.totalItems, \" \\u9805\\u6A21\\u677F \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\\u7B2C \", ctx_r1.templatePagination.currentPage, \" / \", ctx_r1.templatePagination.totalPages, \" \\u9801\");\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"span\", 59);\n    i0.ɵɵtext(2, \"\\u66F4\\u65B0\\u6642\\u9593\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, tpl_r5.UpdateTime, \"yyyy/MM/dd HH:mm:ss\"));\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"span\", 59);\n    i0.ɵɵtext(2, \"\\u5EFA\\u7ACB\\u8005\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tpl_r5.Creator);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_3_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const tpl_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(tpl_r5.TemplateID && ctx_r1.onDeleteTemplate(tpl_r5.TemplateID));\n    });\n    i0.ɵɵelement(1, \"i\", 62);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 48)(3, \"span\", 49);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\", 50);\n    i0.ɵɵelement(6, \"i\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 52);\n    i0.ɵɵtemplate(9, TemplateViewerComponent_div_14_div_3_div_9_Template, 6, 4, \"div\", 53)(10, TemplateViewerComponent_div_14_div_3_div_10_Template, 5, 1, \"div\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 54)(12, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_3_Template_button_click_12_listener() {\n      const tpl_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r5));\n    });\n    i0.ɵɵelement(13, \"i\", 56);\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, TemplateViewerComponent_div_14_div_3_button_16_Template, 4, 0, \"button\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r5 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", tpl_r5.TemplateName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", tpl_r5.UpdateTime);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", tpl_r5.Creator);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", tpl_r5.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 23);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 70);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p\", 71);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5617\\u8A66\\u5176\\u4ED6\\u95DC\\u9375\\u5B57\\u6216 \");\n    i0.ɵɵelementStart(2, \"a\", 72);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_4_p_7_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(3, \"\\u6E05\\u9664\\u641C\\u5C0B\\u689D\\u4EF6\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 71);\n    i0.ɵɵtext(1, \" \\u76EE\\u524D\\u9084\\u6C92\\u6709\\u5EFA\\u7ACB\\u4EFB\\u4F55\\u6A21\\u677F\\uFF0C\\u8ACB\\u5148\\u5EFA\\u7ACB\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64)(2, \"div\", 65);\n    i0.ɵɵtemplate(3, TemplateViewerComponent_div_14_div_4_i_3_Template, 1, 0, \"i\", 66)(4, TemplateViewerComponent_div_14_div_4_i_4_Template, 1, 0, \"i\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\", 68);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, TemplateViewerComponent_div_14_div_4_p_7_Template, 4, 0, \"p\", 69)(8, TemplateViewerComponent_div_14_div_4_p_8_Template, 2, 0, \"p\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u66AB\\u7121\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_5_li_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 79)(1, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_li_13_Template_button_click_1_listener() {\n      const page_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(page_r10));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r10 === ctx_r1.templatePagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r10);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 74)(2, \"div\", 75)(3, \"span\", 76);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"nav\", 77)(6, \"ul\", 78)(7, \"li\", 79)(8, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(1));\n    });\n    i0.ɵɵelement(9, \"i\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"li\", 79)(11, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage - 1));\n    });\n    i0.ɵɵelement(12, \"i\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, TemplateViewerComponent_div_14_div_5_li_13_Template, 3, 3, \"li\", 84);\n    i0.ɵɵelementStart(14, \"li\", 79)(15, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage + 1));\n    });\n    i0.ɵɵelement(16, \"i\", 86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"li\", 79)(18, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.totalPages));\n    });\n    i0.ɵɵelement(19, \"i\", 88);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.templatePagination.currentPage, \" \\u9801\\uFF0C\\u5171 \", ctx_r1.templatePagination.totalPages, \" \\u9801 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getTemplatePageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_14_div_1_Template, 8, 5, \"div\", 38);\n    i0.ɵɵelementStart(2, \"div\", 37);\n    i0.ɵɵtemplate(3, TemplateViewerComponent_div_14_div_3_Template, 17, 4, \"div\", 39)(4, TemplateViewerComponent_div_14_div_4_Template, 9, 5, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TemplateViewerComponent_div_14_div_5_Template, 20, 15, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalItems > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedTemplates)(\"ngForTrackBy\", ctx_r1.trackByTemplateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.paginatedTemplates || ctx_r1.paginatedTemplates.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalPages > 1);\n  }\n}\nfunction TemplateViewerComponent_div_15_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 115);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedTemplate.Description, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"span\", 102);\n    i0.ɵɵtext(2, \"\\u9801\\u6578\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 103);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.detailPagination.currentPage, \" / \", ctx_r1.detailPagination.totalPages, \"\");\n  }\n}\nfunction TemplateViewerComponent_div_15_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearDetailSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 30);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u627E\\u5230 \", ctx_r1.detailPagination.totalItems, \" \\u500B\\u76F8\\u95DC\\u9805\\u76EE \");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵtext(2, \"\\u672A\\u627E\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_15_div_28_small_1_Template, 3, 1, \"small\", 118)(2, TemplateViewerComponent_div_15_div_28_small_2_Template, 3, 0, \"small\", 119);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalItems > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalItems === 0);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_30_div_1_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 138);\n    i0.ɵɵelement(1, \"i\", 139);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r13.CGroupName);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_30_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 140);\n    i0.ɵɵelement(1, \"i\", 141);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r13.CCategory);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_30_div_1_div_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"span\", 148);\n    i0.ɵɵtext(2, \"\\u55AE\\u50F9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 149);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind1(5, 1, detail_r13.CUnitPrice), \"\");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_30_div_1_div_21_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 150)(1, \"span\", 148);\n    i0.ɵɵtext(2, \"\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 151);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", detail_r13.CQuantity, \" \", detail_r13.CUnit, \"\");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_30_div_1_div_21_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 152)(1, \"div\", 153);\n    i0.ɵɵelement(2, \"i\", 154);\n    i0.ɵɵelementStart(3, \"span\", 155);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(detail_r13.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_30_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142)(1, \"div\", 143);\n    i0.ɵɵtemplate(2, TemplateViewerComponent_div_15_div_30_div_1_div_21_div_2_Template, 6, 3, \"div\", 144)(3, TemplateViewerComponent_div_15_div_30_div_1_div_21_div_3_Template, 5, 2, \"div\", 145);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TemplateViewerComponent_div_15_div_30_div_1_div_21_div_4_Template, 5, 1, \"div\", 146);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CQuantity && detail_r13.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122)(1, \"div\", 123)(2, \"div\", 124)(3, \"div\", 125)(4, \"span\", 126);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 127)(7, \"h6\", 128);\n    i0.ɵɵelement(8, \"i\", 129);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 130)(11, \"span\", 131);\n    i0.ɵɵelement(12, \"i\", 132);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, TemplateViewerComponent_div_15_div_30_div_1_span_15_Template, 4, 1, \"span\", 133)(16, TemplateViewerComponent_div_15_div_30_div_1_span_16_Template, 4, 1, \"span\", 134);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 135)(18, \"span\", 136);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, TemplateViewerComponent_div_15_div_30_div_1_div_21_Template, 5, 3, \"div\", 137);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = ctx.$implicit;\n    const i_r14 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r14 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", detail_r13.CReleateName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(detail_r13.CReleateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CCategory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 7, detail_r13.CCreateDt, \"MM/dd\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CUnitPrice || detail_r13.CQuantity || detail_r13.CUnit || detail_r13.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_15_div_30_div_1_Template, 22, 10, \"div\", 121);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetailsData);\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_31_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 159)(1, \"div\", 160)(2, \"span\", 161);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 162)(5, \"div\", 163)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 164);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r15 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r16 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", detail_r15.FieldName, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", detail_r15.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_31_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 157);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_15_ng_template_31_div_0_div_1_Template, 10, 3, \"div\", 158);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedDetails);\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TemplateViewerComponent_div_15_ng_template_31_div_0_Template, 2, 1, \"div\", 156);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const noDetails_r17 = i0.ɵɵreference(35);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetails.length > 0)(\"ngIfElse\", noDetails_r17);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_33_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 79)(1, \"button\", 169);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_div_33_li_6_Template_button_click_1_listener() {\n      const page_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(page_r20));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r20 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r20 === ctx_r1.detailPagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r20);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 165)(1, \"nav\", 166)(2, \"ul\", 167)(3, \"li\", 79)(4, \"button\", 168);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_div_33_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_15_div_33_li_6_Template, 3, 3, \"li\", 84);\n    i0.ɵɵelementStart(7, \"li\", 79)(8, \"button\", 168);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_div_33_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 86);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getDetailPageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 170);\n    i0.ɵɵelement(1, \"i\", 171);\n    i0.ɵɵelementStart(2, \"p\", 172);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"div\", 91)(2, \"div\", 92)(3, \"div\", 93)(4, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵelement(5, \"i\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 96)(7, \"h5\", 97);\n    i0.ɵɵelement(8, \"i\", 98);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, TemplateViewerComponent_div_15_p_10_Template, 2, 1, \"p\", 99);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 100)(12, \"div\", 101)(13, \"span\", 102);\n    i0.ɵɵtext(14, \"\\u9805\\u76EE\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 103);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, TemplateViewerComponent_div_15_div_17_Template, 5, 2, \"div\", 104);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 105)(19, \"div\", 106)(20, \"div\", 107)(21, \"div\", 22);\n    i0.ɵɵelement(22, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"input\", 108);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_15_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.detailSearchKeyword, $event) || (ctx_r1.detailSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateViewerComponent_div_15_Template_input_keyup_enter_23_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.searchTemplateDetails(ctx_r1.detailSearchKeyword));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 28)(25, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.searchTemplateDetails(ctx_r1.detailSearchKeyword));\n    });\n    i0.ɵɵelement(26, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, TemplateViewerComponent_div_15_button_27_Template, 2, 0, \"button\", 110);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(28, TemplateViewerComponent_div_15_div_28_Template, 3, 2, \"div\", 111);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 112);\n    i0.ɵɵtemplate(30, TemplateViewerComponent_div_15_div_30_Template, 2, 1, \"div\", 113)(31, TemplateViewerComponent_div_15_ng_template_31_Template, 1, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(33, TemplateViewerComponent_div_15_div_33_Template, 10, 7, \"div\", 114)(34, TemplateViewerComponent_div_15_ng_template_34_Template, 4, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const checkOldDetails_r21 = i0.ɵɵreference(32);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedTemplate.TemplateName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTemplate.Description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.currentTemplateDetailsData.length > 0 ? ctx_r1.detailPagination.totalItems : ctx_r1.currentTemplateDetails.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetailsData.length > 0)(\"ngIfElse\", checkOldDetails_r21);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.templateType = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 公開Math對象供模板使用\n    this.Math = Math;\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = []; // 保留用於向後相容\n    this.selectedTemplate = null;\n    // 新的詳情資料管理\n    this.currentTemplateDetailsData = [];\n    this.detailSearchKeyword = ''; // 明細專用搜尋關鍵字\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 分頁功能 - 模板列表\n    this.templatePagination = {\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedTemplates = [];\n    // 分頁功能 - 模板詳情\n    this.detailPagination = {\n      currentPage: 1,\n      pageSize: 5,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedDetails = [];\n  }\n  ngOnInit() {\n    this.loadTemplates();\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 載入模板列表 - 使用真實的 API 調用\n  loadTemplates() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      PageIndex: 1,\n      // 暫時載入第一頁\n      PageSize: 100,\n      // 暫時載入較多資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateList API\n    this.templateService.apiTemplateGetTemplateListPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // API 調用成功\n          // 轉換 API 回應為內部格式\n          this.templates = response.Entries.map(item => ({\n            TemplateID: item.CTemplateId,\n            TemplateName: item.CTemplateName || '',\n            Description: item.CTemplateName || '',\n            CreateTime: item.CCreateDt,\n            UpdateTime: item.CUpdateDt,\n            Creator: item.CCreator || undefined,\n            Updator: item.CUpdator || undefined\n          }));\n          // 更新過濾列表\n          this.updateFilteredTemplates();\n          // 清空詳情資料，改為按需載入\n          this.templateDetails = [];\n          this.currentTemplateDetailsData = [];\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n          this.templateDetails = [];\n          this.updateFilteredTemplates();\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n        this.templates = [];\n        this.templateDetails = [];\n        this.updateFilteredTemplates();\n      }\n    });\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n    this.updateTemplatePagination();\n  }\n  // 更新模板分頁\n  updateTemplatePagination() {\n    this.templatePagination.totalItems = this.filteredTemplates.length;\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\n    // 確保當前頁面不超過總頁數\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\n    }\n    this.updatePaginatedTemplates();\n  }\n  // 更新分頁後的模板列表\n  updatePaginatedTemplates() {\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\n    const endIndex = startIndex + this.templatePagination.pageSize;\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\n  }\n  // 模板分頁導航\n  goToTemplatePage(page) {\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = page;\n      this.updatePaginatedTemplates();\n    }\n  }\n  // 獲取模板分頁頁碼數組\n  getTemplatePageNumbers() {\n    const pages = [];\n    const totalPages = this.templatePagination.totalPages;\n    const currentPage = this.templatePagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n    // 按需載入模板詳情\n    if (template.TemplateID) {\n      this.loadTemplateDetails(template.TemplateID);\n    }\n    this.updateDetailPagination();\n  }\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\n  loadTemplateDetails(templateId, pageIndex = 1, searchKeyword) {\n    const args = {\n      templateId: templateId\n    };\n    // 調用真實的 GetTemplateDetailById API\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\n          let allDetails = response.Entries.map(item => ({\n            CTemplateDetailId: item.CTemplateDetailId || 0,\n            CTemplateId: item.CTemplateId || templateId,\n            CReleateId: item.CReleateId || 0,\n            CReleateName: item.CReleateName || '',\n            CGroupName: item.CGroupName || '',\n            // 新增群組名稱欄位\n            CSort: undefined,\n            CRemark: undefined,\n            CCreateDt: new Date().toISOString(),\n            CCreator: '系統',\n            CCategory: undefined,\n            CUnitPrice: undefined,\n            CQuantity: undefined,\n            CUnit: undefined\n          }));\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\n          if (searchKeyword && searchKeyword.trim()) {\n            allDetails = allDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()));\n          }\n          // 前端分頁處理 (因為 API 不支援分頁參數)\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n          const endIndex = startIndex + this.detailPagination.pageSize;\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\n          // 更新詳情資料和分頁資訊\n          this.currentTemplateDetailsData = pagedDetails;\n          this.detailPagination.totalItems = allDetails.length;\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\n          this.detailPagination.currentPage = pageIndex;\n        } else {\n          this.currentTemplateDetailsData = [];\n          this.detailPagination.totalItems = 0;\n          this.detailPagination.totalPages = 0;\n          this.detailPagination.currentPage = 1;\n        }\n      },\n      error: () => {\n        this.currentTemplateDetailsData = [];\n        this.detailPagination.totalItems = 0;\n        this.detailPagination.totalPages = 0;\n        this.detailPagination.currentPage = 1;\n      }\n    });\n  }\n  // 搜尋模板詳情 (明細專用搜尋)\n  searchTemplateDetails(keyword) {\n    this.detailSearchKeyword = keyword;\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\n    }\n  }\n  // 清除明細搜尋\n  clearDetailSearch() {\n    this.detailSearchKeyword = '';\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1);\n    }\n  }\n  // 更新詳情分頁 (保留向後相容)\n  updateDetailPagination() {\n    // 如果使用新的詳情資料，直接返回\n    if (this.currentTemplateDetailsData.length > 0) {\n      return;\n    }\n    // 向後相容：使用舊的詳情資料\n    const details = this.currentTemplateDetails;\n    this.detailPagination.totalItems = details.length;\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\n    this.detailPagination.currentPage = 1; // 重置到第一頁\n    this.updatePaginatedDetails();\n  }\n  // 更新分頁後的詳情列表\n  updatePaginatedDetails() {\n    const details = this.currentTemplateDetails;\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\n    const endIndex = startIndex + this.detailPagination.pageSize;\n    this.paginatedDetails = details.slice(startIndex, endIndex);\n  }\n  // 詳情分頁導航\n  goToDetailPage(page) {\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\n      // 如果使用新的詳情資料，重新載入\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\n      } else {\n        // 向後相容：使用舊的分頁邏輯\n        this.detailPagination.currentPage = page;\n        this.updatePaginatedDetails();\n      }\n    }\n  }\n  // 獲取詳情分頁頁碼數組\n  getDetailPageNumbers() {\n    const pages = [];\n    const totalPages = this.detailPagination.totalPages;\n    const currentPage = this.detailPagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // 刪除模板 - 使用真實的 API 調用\n  deleteTemplateById(templateID) {\n    // 準備 API 請求參數\n    const deleteTemplateArgs = {\n      CTemplateId: templateID\n    };\n    // 調用 DeleteTemplate API\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\n      body: deleteTemplateArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          // 重新載入模板列表\n          this.loadTemplates();\n          // 如果當前查看的模板被刪除，關閉詳情\n          if (this.selectedTemplate?.TemplateID === templateID) {\n            this.selectedTemplate = null;\n          }\n        } else {\n          // API 返回錯誤\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n      }\n    });\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/Template/GetTemplateList\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/Template/SaveTemplate\n   *    請求體: {\n   *      CTemplateName: string,\n   *      CTemplateType: number,  // 1=客變需求\n   *      CStatus: number,\n   *      Details: [{\n   *        CReleateId: number,        // 關聯主檔ID\n   *        CReleateName: string       // 關聯名稱\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    POST /api/Template/DeleteTemplate\n   *    請求體: { CTemplateId: number }\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        templateType: \"templateType\"\n      },\n      outputs: {\n        selectTemplate: \"selectTemplate\",\n        close: \"close\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 21,\n      vars: 4,\n      consts: [[\"checkOldDetails\", \"\"], [\"noDetails\", \"\"], [1, \"template-viewer-card\"], [1, \"template-viewer-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"header-title\"], [1, \"mb-0\"], [1, \"fas\", \"fa-layer-group\", \"mr-2\", \"text-primary\"], [1, \"text-muted\"], [1, \"header-actions\"], [1, \"badge\", \"badge-info\"], [1, \"template-viewer-body\"], [\"class\", \"enhanced-search-container mb-4\", 4, \"ngIf\"], [\"class\", \"template-list-container\", 4, \"ngIf\"], [\"class\", \"template-detail-view\", 4, \"ngIf\"], [1, \"template-viewer-footer\"], [1, \"footer-actions\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [1, \"enhanced-search-container\", \"mb-4\"], [1, \"search-wrapper\"], [1, \"search-input-group\"], [1, \"search-icon\"], [1, \"fas\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u63CF\\u8FF0\\u6216\\u95DC\\u9375\\u5B57...\", 1, \"search-input\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [\"class\", \"search-actions\", 4, \"ngIf\"], [\"class\", \"search-suggestions\", 4, \"ngIf\"], [\"class\", \"search-no-results\", 4, \"ngIf\"], [1, \"search-actions\"], [\"type\", \"button\", \"title\", \"\\u6E05\\u9664\\u641C\\u5C0B\", 1, \"clear-search-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"search-suggestions\"], [1, \"text-success\"], [1, \"fas\", \"fa-check-circle\", \"mr-1\"], [1, \"search-no-results\"], [1, \"text-warning\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"template-list-container\"], [\"class\", \"list-controls mb-3\", 4, \"ngIf\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"empty-state-card\", 4, \"ngIf\"], [\"class\", \"enhanced-pagination-container mt-4\", 4, \"ngIf\"], [1, \"list-controls\", \"mb-3\"], [1, \"list-info\"], [1, \"info-text\"], [1, \"view-options\"], [1, \"template-item\"], [1, \"template-main-info\"], [1, \"template-header\"], [1, \"template-label\"], [1, \"template-name\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\"], [1, \"template-meta\"], [\"class\", \"meta-row\", 4, \"ngIf\"], [1, \"template-actions\"], [\"title\", \"\\u67E5\\u770B\\u8A73\\u60C5\", 1, \"action-btn\", \"view-btn\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"action-btn delete-btn\", \"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 3, \"click\", 4, \"ngIf\"], [1, \"meta-row\"], [1, \"meta-label\"], [1, \"meta-value\"], [\"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 1, \"action-btn\", \"delete-btn\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"empty-state-card\"], [1, \"empty-content\"], [1, \"empty-icon\"], [\"class\", \"fas fa-search\", 4, \"ngIf\"], [\"class\", \"fas fa-folder-open\", 4, \"ngIf\"], [1, \"empty-title\"], [\"class\", \"empty-description\", 4, \"ngIf\"], [1, \"fas\", \"fa-folder-open\"], [1, \"empty-description\"], [\"href\", \"javascript:void(0)\", 1, \"clear-link\", 3, \"click\"], [1, \"enhanced-pagination-container\", \"mt-4\"], [1, \"pagination-wrapper\"], [1, \"pagination-info\"], [1, \"page-info\"], [\"aria-label\", \"\\u6A21\\u677F\\u5217\\u8868\\u5206\\u9801\", 1, \"pagination-nav\"], [1, \"enhanced-pagination\"], [1, \"page-item\"], [\"title\", \"\\u7B2C\\u4E00\\u9801\", 1, \"page-btn\", \"first-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [\"title\", \"\\u4E0A\\u4E00\\u9801\", 1, \"page-btn\", \"prev-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"\\u4E0B\\u4E00\\u9801\", 1, \"page-btn\", \"next-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [\"title\", \"\\u6700\\u5F8C\\u4E00\\u9801\", 1, \"page-btn\", \"last-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"page-btn\", \"page-number\", 3, \"click\"], [1, \"template-detail-view\"], [1, \"detail-header\"], [1, \"detail-title-section\"], [1, \"back-button\"], [\"title\", \"\\u8FD4\\u56DE\\u6A21\\u677F\\u5217\\u8868\", 1, \"back-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"detail-title-info\"], [1, \"detail-title\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\", \"text-primary\"], [\"class\", \"detail-subtitle\", 4, \"ngIf\"], [1, \"detail-stats\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [1, \"detail-search-section\", \"mb-4\"], [1, \"detail-search-wrapper\"], [1, \"detail-search-input-group\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u660E\\u7D30\\u9805\\u76EE\\u540D\\u7A31\\u3001\\u7FA4\\u7D44...\", 1, \"detail-search-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"type\", \"button\", \"title\", \"\\u641C\\u5C0B\", 1, \"search-btn\", 3, \"click\"], [\"class\", \"clear-btn\", \"type\", \"button\", \"title\", \"\\u6E05\\u9664\\u641C\\u5C0B\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"search-results-info\", 4, \"ngIf\"], [1, \"detail-content\"], [\"class\", \"enhanced-detail-list\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"detail-pagination mt-3\", 4, \"ngIf\"], [1, \"detail-subtitle\"], [\"type\", \"button\", \"title\", \"\\u6E05\\u9664\\u641C\\u5C0B\", 1, \"clear-btn\", 3, \"click\"], [1, \"search-results-info\"], [\"class\", \"text-success\", 4, \"ngIf\"], [\"class\", \"text-warning\", 4, \"ngIf\"], [1, \"enhanced-detail-list\"], [\"class\", \"enhanced-detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"enhanced-detail-item\"], [1, \"detail-item-card\"], [1, \"detail-item-header\"], [1, \"item-index\"], [1, \"index-badge\"], [1, \"item-main-info\"], [1, \"item-name\"], [1, \"fas\", \"fa-cog\", \"mr-2\", \"text-secondary\"], [1, \"item-meta\"], [1, \"meta-item\", \"id-meta\"], [1, \"fas\", \"fa-hashtag\"], [\"class\", \"meta-item group-meta\", 4, \"ngIf\"], [\"class\", \"meta-item category-meta\", 4, \"ngIf\"], [1, \"item-actions\"], [1, \"create-date\"], [\"class\", \"detail-item-body\", 4, \"ngIf\"], [1, \"meta-item\", \"group-meta\"], [1, \"fas\", \"fa-layer-group\"], [1, \"meta-item\", \"category-meta\"], [1, \"fas\", \"fa-tag\"], [1, \"detail-item-body\"], [1, \"item-details-grid\"], [\"class\", \"detail-group price-group\", 4, \"ngIf\"], [\"class\", \"detail-group quantity-group\", 4, \"ngIf\"], [\"class\", \"item-remark\", 4, \"ngIf\"], [1, \"detail-group\", \"price-group\"], [1, \"detail-label\"], [1, \"detail-value\", \"price-value\"], [1, \"detail-group\", \"quantity-group\"], [1, \"detail-value\", \"quantity-value\"], [1, \"item-remark\"], [1, \"remark-content\"], [1, \"fas\", \"fa-comment-alt\", \"mr-2\", \"text-muted\"], [1, \"remark-text\"], [\"class\", \"detail-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-list\"], [\"class\", \"detail-item d-flex align-items-center py-2 border-bottom\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\", \"d-flex\", \"align-items-center\", \"py-2\", \"border-bottom\"], [1, \"detail-index\"], [1, \"badge\", \"badge-light\"], [1, \"detail-content\", \"flex-grow-1\", \"ml-2\"], [1, \"detail-field\"], [1, \"detail-value\", \"text-muted\"], [1, \"detail-pagination\", \"mt-3\"], [\"aria-label\", \"\\u6A21\\u677F\\u8A73\\u60C5\\u5206\\u9801\"], [1, \"pagination\", \"pagination-sm\", \"justify-content-center\", \"mb-0\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"py-3\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"h5\", 6);\n          i0.ɵɵelement(5, \"i\", 7);\n          i0.ɵɵtext(6, \"\\u6A21\\u677F\\u7BA1\\u7406 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"small\", 8);\n          i0.ɵɵtext(8, \"\\u7BA1\\u7406\\u548C\\u67E5\\u770B\\u5BA2\\u8B8A\\u9700\\u6C42\\u6A21\\u677F\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"span\", 10);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"nb-card-body\", 11);\n          i0.ɵɵtemplate(13, TemplateViewerComponent_div_13_Template, 9, 4, \"div\", 12)(14, TemplateViewerComponent_div_14_Template, 6, 5, \"div\", 13)(15, TemplateViewerComponent_div_15_Template, 36, 10, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"nb-card-footer\", 15)(17, \"div\", 16)(18, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_18_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelement(19, \"i\", 18);\n          i0.ɵɵtext(20, \"\\u95DC\\u9589 \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\"\", ctx.templatePagination.totalItems, \" \\u500B\\u6A21\\u677F\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, i2.DatePipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, NbCardModule, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardFooterComponent, i4.NbCardHeaderComponent, NbButtonModule],\n      styles: [\".template-viewer-card[_ngcontent-%COMP%] {\\n  width: 90vw;\\n  max-width: 1200px;\\n  height: 80vh;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(174, 155, 102, 0.25);\\n  border: none;\\n  overflow: hidden;\\n  background: #FFFFFF;\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  border-bottom: none;\\n  padding: 1.5rem;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-bottom: 0.25rem;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  opacity: 0.9;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: #FFFFFF;\\n  font-size: 0.875rem;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.template-viewer-body[_ngcontent-%COMP%] {\\n  overflow: auto;\\n  padding: 1.5rem;\\n  background: #FFFFFF;\\n}\\n\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  background: #F8F9FA;\\n  border-radius: 8px;\\n  padding: 0.75rem 1rem;\\n  border: 2px solid transparent;\\n  transition: 0.3s ease;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]:focus-within {\\n  border-color: #B8A676;\\n  background: #FFFFFF;\\n  box-shadow: 0 0 0 3px rgba(184, 166, 118, 0.1);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  margin-right: 0.75rem;\\n  font-size: 1rem;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  background: transparent;\\n  outline: none;\\n  font-size: 1rem;\\n  color: #2C3E50;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder {\\n  color: #ADB5BD;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-search-btn[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  color: #FFFFFF;\\n  border: none;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.75rem;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-search-btn[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n  transform: scale(1.1);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-suggestions[_ngcontent-%COMP%], \\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-no-results[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  padding: 0.5rem 0;\\n}\\n\\n.template-list-container[_ngcontent-%COMP%]   .list-controls[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 1rem 1.5rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n}\\n.template-list-container[_ngcontent-%COMP%]   .list-controls[_ngcontent-%COMP%]   .list-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n\\n.template-list-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n  transition: 0.3s ease;\\n  border: 1px solid #E9ECEF;\\n  padding: 1.5rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 16px rgba(174, 155, 102, 0.2);\\n  transform: translateY(-2px);\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 1.5rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]   .template-label[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 20px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 600;\\n  margin: 0;\\n  font-size: 1.1rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #AE9B66;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 0.75rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  font-weight: 500;\\n  min-width: 60px;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-family: \\\"Courier New\\\", monospace;\\n  background: #F8F9FA;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 4px;\\n  font-size: 0.8rem;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  min-width: 120px;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1rem;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  white-space: nowrap;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(184, 166, 118, 0.3);\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  color: #FFFFFF;\\n}\\n.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);\\n}\\n\\n.empty-state-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  padding: 3rem 2rem;\\n  text-align: center;\\n  border: 2px dashed #dee2e6;\\n}\\n.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #adb5bd;\\n  margin-bottom: 1rem;\\n}\\n.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n}\\n.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin: 0;\\n}\\n.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]   .clear-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]   .clear-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.enhanced-pagination-container[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]   .page-info[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n  gap: 0.5rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 40px;\\n  height: 40px;\\n  border: none;\\n  border-radius: 8px;\\n  background: #F8F9FA;\\n  color: #6C757D;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  transform: translateY(-1px);\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.page-number[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.first-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.last-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.prev-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.next-page[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.3);\\n}\\n\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  margin-bottom: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  margin-bottom: 1rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border: none;\\n  border-radius: 8px;\\n  background: #F8F9FA;\\n  color: #6C757D;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  transform: translateY(-1px);\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-title[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-subtitle[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6C757D;\\n  margin-bottom: 0.25rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n  border: 1px solid #E9ECEF;\\n  overflow: hidden;\\n  transition: 0.3s ease;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 16px rgba(174, 155, 102, 0.2);\\n  border-color: #B8A676;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%] {\\n  padding: 1.25rem;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  border-bottom: 1px solid #F8F9FA;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-index[_ngcontent-%COMP%]   .index-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 600;\\n  margin-bottom: 0.75rem;\\n  font-size: 1.1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.75rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 6px;\\n  font-size: 0.875rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.id-meta[_ngcontent-%COMP%] {\\n  background: rgba(184, 166, 118, 0.1);\\n  color: #9B8A5A;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.group-meta[_ngcontent-%COMP%] {\\n  background: rgba(184, 166, 118, 0.15);\\n  color: #AE9B66;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.category-meta[_ngcontent-%COMP%] {\\n  background: rgba(184, 166, 118, 0.2);\\n  color: #9B8A5A;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-actions[_ngcontent-%COMP%]   .create-date[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  font-size: 0.875rem;\\n  background: #F8F9FA;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 6px;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%] {\\n  padding: 1.25rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6C757D;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value.price-value[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1.1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value.quantity-value[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%]   .remark-content[_ngcontent-%COMP%] {\\n  background: #F8F9FA;\\n  border-left: 4px solid #B8A676;\\n  padding: 1rem;\\n  border-radius: 0 8px 8px 0;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%]   .remark-content[_ngcontent-%COMP%]   .remark-text[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-style: italic;\\n  line-height: 1.5;\\n}\\n\\n.template-viewer-footer[_ngcontent-%COMP%] {\\n  background: #F8F9FA;\\n  border-top: 1px solid #E9ECEF;\\n  padding: 1rem 1.5rem;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  background: #6C757D;\\n  color: #FFFFFF;\\n  border: none;\\n  border-radius: 8px;\\n  padding: 0.75rem 2rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: #AE9B66;\\n  transform: translateY(-1px);\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  border: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: none;\\n  padding: 0.75rem 1rem;\\n  font-size: 0.95rem;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-style: italic;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  padding: 0.75rem 1rem;\\n  transition: all 0.2s ease;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n.search-results-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #007bff;\\n  padding-left: 0.75rem;\\n  background: #f8f9ff;\\n  border-radius: 4px;\\n}\\n\\n.pagination-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #28a745;\\n  padding-left: 0.75rem;\\n  background: #f8fff8;\\n  border-radius: 4px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  font-size: 0.9rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9ff;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  border-color: #f0f0f0;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto 1rem;\\n  opacity: 0.5;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: 0.5rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.template-detail-modal[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  background: #fff;\\n  border: 2px solid #e9ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  background: #f8f9ff;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  border-left: 4px solid #007bff;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9ff;\\n  border-radius: 6px;\\n  margin: 0 -0.5rem;\\n  padding-left: 0.5rem !important;\\n  padding-right: 0.5rem !important;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0.375rem 0.5rem;\\n  border-radius: 50%;\\n  min-width: 2rem;\\n  text-align: center;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fefcf8 0%, #f8f9fa 100%);\\n  border-radius: 16px;\\n  padding: 2rem;\\n  box-shadow: 0 4px 16px rgba(174, 155, 102, 0.2);\\n  border: 1px solid #E9ECEF;\\n  margin-bottom: 1.5rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1rem 1.25rem;\\n  border: 2px solid #E9ECEF;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 1px 3px rgba(174, 155, 102, 0.1);\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]:focus-within {\\n  border-color: #B8A676;\\n  background: #FFFFFF;\\n  box-shadow: 0 0 0 4px rgba(184, 166, 118, 0.15), 0 2px 8px rgba(174, 155, 102, 0.15);\\n  transform: translateY(-1px);\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  color: #AE9B66;\\n  margin-right: 1rem;\\n  font-size: 1.1rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .detail-search-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  background: transparent;\\n  outline: none;\\n  font-size: 1rem;\\n  color: #2C3E50;\\n  font-weight: 500;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .detail-search-input[_ngcontent-%COMP%]::placeholder {\\n  color: #ADB5BD;\\n  font-weight: 400;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.75rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%], \\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  border: none;\\n  border-radius: 8px;\\n  width: 36px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  font-size: 0.9rem;\\n  box-shadow: 0 1px 3px rgba(174, 155, 102, 0.1);\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]:hover, \\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]:active, \\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .detail-search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .search-results-info[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  padding: 0.75rem 1rem;\\n  border-radius: 8px;\\n  background: rgba(255, 255, 255, 0.8);\\n  border-left: 4px solid transparent;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .search-results-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .search-results-info[_ngcontent-%COMP%]   small.text-success[_ngcontent-%COMP%] {\\n  color: #28a745 !important;\\n  border-left-color: #28a745;\\n  background: rgba(40, 167, 69, 0.05);\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .search-results-info[_ngcontent-%COMP%]   small.text-success[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .search-results-info[_ngcontent-%COMP%]   small.text-warning[_ngcontent-%COMP%] {\\n  color: #ffc107 !important;\\n  border-left-color: #ffc107;\\n  background: rgba(255, 193, 7, 0.05);\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-search-section[_ngcontent-%COMP%]   .detail-search-wrapper[_ngcontent-%COMP%]   .search-results-info[_ngcontent-%COMP%]   small.text-warning[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n  word-break: break-word;\\n}\\n\\n.add-template-form[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .template-form[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e8ecef;\\n  border-radius: 12px;\\n  padding: 2rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .template-form[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  margin-left: 0.125rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  background: #ffffff;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .items-selector[_ngcontent-%COMP%] {\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  background: #f9fafb;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  padding: 2rem;\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n  padding: 0.875rem 1rem;\\n  cursor: pointer;\\n  margin: 0;\\n  width: 100%;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-top: 0.125rem;\\n  width: 1rem;\\n  height: 1rem;\\n  accent-color: #28a745;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.25rem;\\n  line-height: 1.4;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-desc[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 0.75rem;\\n  margin-top: 1.5rem;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%], \\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  padding: 0.625rem 1.25rem;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border: none;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  min-width: 80px;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%] {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover {\\n  background: #e5e7eb;\\n  transform: translateY(-1px);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.25);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n  color: #6c757d;\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  border-radius: 4px;\\n  margin: 0 0.125rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  color: #495057;\\n  transform: translateY(-1px);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  background-color: #fff;\\n  border-color: #dee2e6;\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n}\\n\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.8rem;\\n  border-radius: 3px;\\n  margin: 0 0.0625rem;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  border-color: #28a745;\\n  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  transition: background-color 0.2s;\\n}\\n.detail-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-name[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.95rem;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-remark[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n  border-left: 3px solid #dee2e6;\\n}\\n\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-radius: 0.25rem 0 0 0.25rem;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n  border-radius: 0 0.25rem 0.25rem 0;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_div_13_div_6_Template_button_click_1_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "filteredTemplates", "length", "ɵɵtwoWayListener", "TemplateViewerComponent_div_13_Template_input_ngModelChange_5_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "searchKeyword", "TemplateViewerComponent_div_13_Template_input_input_5_listener", "onSearch", "TemplateViewerComponent_div_13_Template_input_keyup_enter_5_listener", "ɵɵtemplate", "TemplateViewerComponent_div_13_div_6_Template", "TemplateViewerComponent_div_13_div_7_Template", "TemplateViewerComponent_div_13_div_8_Template", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate3", "templatePagination", "currentPage", "pageSize", "Math", "min", "totalItems", "ɵɵtextInterpolate2", "totalPages", "ɵɵtextInterpolate", "ɵɵpipeBind2", "tpl_r5", "UpdateTime", "Creator", "TemplateViewerComponent_div_14_div_3_button_16_Template_button_click_0_listener", "_r6", "$implicit", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_div_14_div_3_div_9_Template", "TemplateViewerComponent_div_14_div_3_div_10_Template", "TemplateViewerComponent_div_14_div_3_Template_button_click_12_listener", "_r4", "onSelectTemplate", "TemplateViewerComponent_div_14_div_3_button_16_Template", "TemplateName", "TemplateViewerComponent_div_14_div_4_p_7_Template_a_click_2_listener", "_r7", "TemplateViewerComponent_div_14_div_4_i_3_Template", "TemplateViewerComponent_div_14_div_4_i_4_Template", "TemplateViewerComponent_div_14_div_4_p_7_Template", "TemplateViewerComponent_div_14_div_4_p_8_Template", "TemplateViewerComponent_div_14_div_5_li_13_Template_button_click_1_listener", "page_r10", "_r9", "goToTemplatePage", "ɵɵclassProp", "TemplateViewerComponent_div_14_div_5_Template_button_click_8_listener", "_r8", "TemplateViewerComponent_div_14_div_5_Template_button_click_11_listener", "TemplateViewerComponent_div_14_div_5_li_13_Template", "TemplateViewerComponent_div_14_div_5_Template_button_click_15_listener", "TemplateViewerComponent_div_14_div_5_Template_button_click_18_listener", "getTemplatePageNumbers", "TemplateViewerComponent_div_14_div_1_Template", "TemplateViewerComponent_div_14_div_3_Template", "TemplateViewerComponent_div_14_div_4_Template", "TemplateViewerComponent_div_14_div_5_Template", "paginatedTemplates", "trackByTemplateId", "selectedTemplate", "Description", "detailPagination", "TemplateViewerComponent_div_15_button_27_Template_button_click_0_listener", "_r12", "clearDetailSearch", "TemplateViewerComponent_div_15_div_28_small_1_Template", "TemplateViewerComponent_div_15_div_28_small_2_Template", "detail_r13", "CGroupName", "CCategory", "ɵɵpipeBind1", "CUnitPrice", "CQuantity", "CUnit", "CRemark", "TemplateViewerComponent_div_15_div_30_div_1_div_21_div_2_Template", "TemplateViewerComponent_div_15_div_30_div_1_div_21_div_3_Template", "TemplateViewerComponent_div_15_div_30_div_1_div_21_div_4_Template", "TemplateViewerComponent_div_15_div_30_div_1_span_15_Template", "TemplateViewerComponent_div_15_div_30_div_1_span_16_Template", "TemplateViewerComponent_div_15_div_30_div_1_div_21_Template", "i_r14", "CReleateName", "CReleateId", "CCreateDt", "TemplateViewerComponent_div_15_div_30_div_1_Template", "currentTemplateDetailsData", "i_r16", "detail_r15", "FieldName", "FieldValue", "TemplateViewerComponent_div_15_ng_template_31_div_0_div_1_Template", "paginatedDetails", "TemplateViewerComponent_div_15_ng_template_31_div_0_Template", "currentTemplateDetails", "noDetails_r17", "TemplateViewerComponent_div_15_div_33_li_6_Template_button_click_1_listener", "page_r20", "_r19", "goToDetailPage", "TemplateViewerComponent_div_15_div_33_Template_button_click_4_listener", "_r18", "TemplateViewerComponent_div_15_div_33_li_6_Template", "TemplateViewerComponent_div_15_div_33_Template_button_click_8_listener", "getDetailPageNumbers", "TemplateViewerComponent_div_15_Template_button_click_4_listener", "_r11", "closeTemplateDetail", "TemplateViewerComponent_div_15_p_10_Template", "TemplateViewerComponent_div_15_div_17_Template", "TemplateViewerComponent_div_15_Template_input_ngModelChange_23_listener", "detailSearchKeyword", "TemplateViewerComponent_div_15_Template_input_keyup_enter_23_listener", "searchTemplateDetails", "TemplateViewerComponent_div_15_Template_button_click_25_listener", "TemplateViewerComponent_div_15_button_27_Template", "TemplateViewerComponent_div_15_div_28_Template", "TemplateViewerComponent_div_15_div_30_Template", "TemplateViewerComponent_div_15_ng_template_31_Template", "ɵɵtemplateRefExtractor", "TemplateViewerComponent_div_15_div_33_Template", "TemplateViewerComponent_div_15_ng_template_34_Template", "checkOldDetails_r21", "TemplateViewerComponent", "constructor", "templateService", "templateType", "selectTemplate", "close", "templates", "templateDetails", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "CreateTime", "CUpdateDt", "CCreator", "undefined", "Up<PERSON>tor", "CUpdator", "error", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "updateTemplatePagination", "ceil", "max", "updatePaginatedTemplates", "startIndex", "endIndex", "slice", "page", "pages", "startPage", "endPage", "i", "push", "emit", "loadTemplateDetails", "updateDetailPagination", "templateId", "pageIndex", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "allDetails", "CTemplateDetailId", "CSort", "Date", "toISOString", "detail", "pagedDetails", "details", "updatePaginatedDetails", "onClose", "templateID", "confirm", "deleteTemplateById", "deleteTemplateArgs", "apiTemplateDeleteTemplatePost$Json", "d", "index", "ɵɵdirectiveInject", "i1", "TemplateService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_div_13_Template", "TemplateViewerComponent_div_14_Template", "TemplateViewerComponent_div_15_Template", "TemplateViewerComponent_Template_button_click_18_listener", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "DatePipe", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i4", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, GetTemplateByIdArgs, GetTemplateDetailByIdArgs } from 'src/services/api/models';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 公開Math對象供模板使用\r\n  Math = Math;\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = []; // 保留用於向後相容\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 新的詳情資料管理\r\n  currentTemplateDetailsData: TemplateDetailItem[] = [];\r\n  detailSearchKeyword = ''; // 明細專用搜尋關鍵字\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板列表\r\n  templatePagination = {\r\n    currentPage: 1,\r\n    pageSize: 10,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板詳情\r\n  detailPagination = {\r\n    currentPage: 1,\r\n    pageSize: 5,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedDetails: TemplateDetail[] = [];\r\n\r\n\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    this.loadTemplates();\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 載入模板列表 - 使用真實的 API 調用\r\n  loadTemplates() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      PageIndex: 1, // 暫時載入第一頁\r\n      PageSize: 100, // 暫時載入較多資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateList API\r\n    this.templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // API 調用成功\r\n\r\n          // 轉換 API 回應為內部格式\r\n          this.templates = response.Entries.map(item => ({\r\n            TemplateID: item.CTemplateId,\r\n            TemplateName: item.CTemplateName || '',\r\n            Description: item.CTemplateName || '',\r\n            CreateTime: item.CCreateDt,\r\n            UpdateTime: item.CUpdateDt,\r\n            Creator: item.CCreator || undefined,\r\n            Updator: item.CUpdator || undefined\r\n          }));\r\n\r\n          // 更新過濾列表\r\n          this.updateFilteredTemplates();\r\n\r\n          // 清空詳情資料，改為按需載入\r\n          this.templateDetails = [];\r\n          this.currentTemplateDetailsData = [];\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n          this.templateDetails = [];\r\n          this.updateFilteredTemplates();\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n        this.templateDetails = [];\r\n        this.updateFilteredTemplates();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n    this.updateTemplatePagination();\r\n  }\r\n\r\n  // 更新模板分頁\r\n  updateTemplatePagination() {\r\n    this.templatePagination.totalItems = this.filteredTemplates.length;\r\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\r\n\r\n    // 確保當前頁面不超過總頁數\r\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\r\n    }\r\n\r\n    this.updatePaginatedTemplates();\r\n  }\r\n\r\n  // 更新分頁後的模板列表\r\n  updatePaginatedTemplates() {\r\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\r\n    const endIndex = startIndex + this.templatePagination.pageSize;\r\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 模板分頁導航\r\n  goToTemplatePage(page: number) {\r\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = page;\r\n      this.updatePaginatedTemplates();\r\n    }\r\n  }\r\n\r\n  // 獲取模板分頁頁碼數組\r\n  getTemplatePageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.templatePagination.totalPages;\r\n    const currentPage = this.templatePagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n\r\n    // 按需載入模板詳情\r\n    if (template.TemplateID) {\r\n      this.loadTemplateDetails(template.TemplateID);\r\n    }\r\n\r\n    this.updateDetailPagination();\r\n  }\r\n\r\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\r\n  loadTemplateDetails(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    // 調用真實的 GetTemplateDetailById API\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n\r\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\r\n          let allDetails = response.Entries.map(item => ({\r\n            CTemplateDetailId: item.CTemplateDetailId || 0,\r\n            CTemplateId: item.CTemplateId || templateId,\r\n            CReleateId: item.CReleateId || 0,\r\n            CReleateName: item.CReleateName || '',\r\n            CGroupName: item.CGroupName || '', // 新增群組名稱欄位\r\n            CSort: undefined,\r\n            CRemark: undefined,\r\n            CCreateDt: new Date().toISOString(),\r\n            CCreator: '系統',\r\n            CCategory: undefined,\r\n            CUnitPrice: undefined,\r\n            CQuantity: undefined,\r\n            CUnit: undefined\r\n          } as TemplateDetailItem));\r\n\r\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\r\n          if (searchKeyword && searchKeyword.trim()) {\r\n            allDetails = allDetails.filter(detail =>\r\n              detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n              (detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n            );\r\n          }\r\n\r\n          // 前端分頁處理 (因為 API 不支援分頁參數)\r\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n          const endIndex = startIndex + this.detailPagination.pageSize;\r\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\r\n\r\n          // 更新詳情資料和分頁資訊\r\n          this.currentTemplateDetailsData = pagedDetails;\r\n          this.detailPagination.totalItems = allDetails.length;\r\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\r\n          this.detailPagination.currentPage = pageIndex;\r\n        } else {\r\n          this.currentTemplateDetailsData = [];\r\n          this.detailPagination.totalItems = 0;\r\n          this.detailPagination.totalPages = 0;\r\n          this.detailPagination.currentPage = 1;\r\n        }\r\n      },\r\n      error: () => {\r\n        this.currentTemplateDetailsData = [];\r\n        this.detailPagination.totalItems = 0;\r\n        this.detailPagination.totalPages = 0;\r\n        this.detailPagination.currentPage = 1;\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n\r\n  // 搜尋模板詳情 (明細專用搜尋)\r\n  searchTemplateDetails(keyword: string) {\r\n    this.detailSearchKeyword = keyword;\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\r\n    }\r\n  }\r\n\r\n  // 清除明細搜尋\r\n  clearDetailSearch() {\r\n    this.detailSearchKeyword = '';\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1);\r\n    }\r\n  }\r\n\r\n  // 更新詳情分頁 (保留向後相容)\r\n  updateDetailPagination() {\r\n    // 如果使用新的詳情資料，直接返回\r\n    if (this.currentTemplateDetailsData.length > 0) {\r\n      return;\r\n    }\r\n\r\n    // 向後相容：使用舊的詳情資料\r\n    const details = this.currentTemplateDetails;\r\n    this.detailPagination.totalItems = details.length;\r\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\r\n    this.detailPagination.currentPage = 1; // 重置到第一頁\r\n    this.updatePaginatedDetails();\r\n  }\r\n\r\n  // 更新分頁後的詳情列表\r\n  updatePaginatedDetails() {\r\n    const details = this.currentTemplateDetails;\r\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\r\n    const endIndex = startIndex + this.detailPagination.pageSize;\r\n    this.paginatedDetails = details.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 詳情分頁導航\r\n  goToDetailPage(page: number) {\r\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\r\n      // 如果使用新的詳情資料，重新載入\r\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\r\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\r\n      } else {\r\n        // 向後相容：使用舊的分頁邏輯\r\n        this.detailPagination.currentPage = page;\r\n        this.updatePaginatedDetails();\r\n      }\r\n    }\r\n  }\r\n\r\n  // 獲取詳情分頁頁碼數組\r\n  getDetailPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.detailPagination.totalPages;\r\n    const currentPage = this.detailPagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // 刪除模板 - 使用真實的 API 調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 準備 API 請求參數\r\n    const deleteTemplateArgs: GetTemplateByIdArgs = {\r\n      CTemplateId: templateID\r\n    };\r\n\r\n    // 調用 DeleteTemplate API\r\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\r\n      body: deleteTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          // 重新載入模板列表\r\n          this.loadTemplates();\r\n\r\n          // 如果當前查看的模板被刪除，關閉詳情\r\n          if (this.selectedTemplate?.TemplateID === templateID) {\r\n            this.selectedTemplate = null;\r\n          }\r\n        } else {\r\n          // API 返回錯誤\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n      }\r\n    });\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/Template/GetTemplateList\r\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/Template/SaveTemplate\r\n   *    請求體: {\r\n   *      CTemplateName: string,\r\n   *      CTemplateType: number,  // 1=客變需求\r\n   *      CStatus: number,\r\n   *      Details: [{\r\n   *        CReleateId: number,        // 關聯主檔ID\r\n   *        CReleateName: string       // 關聯名稱\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    POST /api/Template/DeleteTemplate\r\n   *    請求體: { CTemplateId: number }\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\r\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\r\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\r\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n  CreateTime?: string;\r\n  UpdateTime?: string;\r\n  Creator?: string;\r\n  Updator?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  CTemplateType: number; // 模板類型，1=客變需求\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n\r\n// 擴展的詳情項目結構 (基於 API 的 TemplateDetailItem 擴展)\r\nexport interface TemplateDetailItem {\r\n  CTemplateDetailId: number;\r\n  CTemplateId: number;\r\n  CReleateId: number;            // 關聯主檔ID\r\n  CReleateName: string;          // 關聯名稱\r\n  CGroupName?: string;           // 群組名稱 (API 新增欄位)\r\n  CSort?: number;                // 排序順序\r\n  CRemark?: string;              // 備註\r\n  CCreateDt: string;             // 建立時間\r\n  CCreator: string;              // 建立者\r\n  CUpdateDt?: string;            // 更新時間\r\n  CUpdator?: string;             // 更新者\r\n\r\n  // 業務相關欄位 (依需求添加，部分由 API 提供)\r\n  CUnitPrice?: number;           // 單價\r\n  CQuantity?: number;            // 數量\r\n  CUnit?: string;                // 單位\r\n  CCategory?: string;            // 分類\r\n}\r\n\r\n// 注意：GetTemplateDetailById API 使用的是 GetTemplateDetailByIdArgs 和 TemplateDetailItemListResponseBase\r\n// 這些 interface 已經在 API models 中定義，不需要重複定義\r\n", "<nb-card class=\"template-viewer-card\">\r\n  <nb-card-header class=\"template-viewer-header\">\r\n    <div class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"header-title\">\r\n        <h5 class=\"mb-0\">\r\n          <i class=\"fas fa-layer-group mr-2 text-primary\"></i>模板管理\r\n        </h5>\r\n        <small class=\"text-muted\">管理和查看客變需求模板</small>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <span class=\"badge badge-info\">{{ templatePagination.totalItems }} 個模板</span>\r\n      </div>\r\n    </div>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"template-viewer-body\">\r\n    <!-- 增強的搜尋功能 (僅在模板列表時顯示) -->\r\n    <div class=\"enhanced-search-container mb-4\" *ngIf=\"!selectedTemplate\">\r\n      <div class=\"search-wrapper\">\r\n        <div class=\"search-input-group\">\r\n          <div class=\"search-icon\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </div>\r\n          <input type=\"text\" class=\"search-input\" placeholder=\"搜尋模板名稱、描述或關鍵字...\" [(ngModel)]=\"searchKeyword\"\r\n            (input)=\"onSearch()\" (keyup.enter)=\"onSearch()\">\r\n          <div class=\"search-actions\" *ngIf=\"searchKeyword\">\r\n            <button class=\"clear-search-btn\" type=\"button\" (click)=\"clearSearch()\" title=\"清除搜尋\">\r\n              <i class=\"fas fa-times\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"search-suggestions\" *ngIf=\"searchKeyword && filteredTemplates.length > 0\">\r\n          <small class=\"text-success\">\r\n            <i class=\"fas fa-check-circle mr-1\"></i>找到 {{ filteredTemplates.length }} 個相關模板\r\n          </small>\r\n        </div>\r\n        <div class=\"search-no-results\" *ngIf=\"searchKeyword && filteredTemplates.length === 0\">\r\n          <small class=\"text-warning\">\r\n            <i class=\"fas fa-exclamation-triangle mr-1\"></i>未找到符合條件的模板\r\n          </small>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 優化的模板列表（僅在未選擇模板細節時顯示） -->\r\n    <div class=\"template-list-container\" *ngIf=\"!selectedTemplate\">\r\n      <!-- 列表控制欄 -->\r\n      <div class=\"list-controls mb-3\" *ngIf=\"templatePagination.totalItems > 0\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"list-info\">\r\n            <span class=\"info-text\">\r\n              顯示第 {{ (templatePagination.currentPage - 1) * templatePagination.pageSize + 1 }} -\r\n              {{ Math.min(templatePagination.currentPage * templatePagination.pageSize, templatePagination.totalItems)\r\n              }} 項，\r\n              共 {{ templatePagination.totalItems }} 項模板\r\n            </span>\r\n          </div>\r\n          <div class=\"view-options\">\r\n            <small class=\"text-muted\">第 {{ templatePagination.currentPage }} / {{ templatePagination.totalPages }}\r\n              頁</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 簡潔模板列表 -->\r\n      <div class=\"template-list-container\">\r\n        <div class=\"template-item\" *ngFor=\"let tpl of paginatedTemplates; trackBy: trackByTemplateId\">\r\n          <div class=\"template-main-info\">\r\n            <div class=\"template-header\">\r\n              <span class=\"template-label\">模板名稱</span>\r\n              <h6 class=\"template-name\">\r\n                <i class=\"fas fa-file-alt mr-2\"></i>\r\n                {{ tpl.TemplateName }}\r\n              </h6>\r\n            </div>\r\n            <div class=\"template-meta\">\r\n              <div class=\"meta-row\" *ngIf=\"tpl.UpdateTime\">\r\n                <span class=\"meta-label\">更新時間：</span>\r\n                <span class=\"meta-value\">{{ tpl.UpdateTime | date:'yyyy/MM/dd HH:mm:ss' }}</span>\r\n              </div>\r\n              <div class=\"meta-row\" *ngIf=\"tpl.Creator\">\r\n                <span class=\"meta-label\">建立者：</span>\r\n                <span class=\"meta-value\">{{ tpl.Creator }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"template-actions\">\r\n            <button class=\"action-btn view-btn\" (click)=\"onSelectTemplate(tpl)\" title=\"查看詳情\">\r\n              <i class=\"fas fa-eye\"></i>\r\n              <span>查看</span>\r\n            </button>\r\n            <button class=\"action-btn delete-btn\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n              *ngIf=\"tpl.TemplateID\" title=\"刪除模板\">\r\n              <i class=\"fas fa-trash\"></i>\r\n              <span>刪除</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空狀態 -->\r\n        <div class=\"empty-state-card\" *ngIf=\"!paginatedTemplates || paginatedTemplates.length === 0\">\r\n          <div class=\"empty-content\">\r\n            <div class=\"empty-icon\">\r\n              <i class=\"fas fa-search\" *ngIf=\"searchKeyword\"></i>\r\n              <i class=\"fas fa-folder-open\" *ngIf=\"!searchKeyword\"></i>\r\n            </div>\r\n            <h6 class=\"empty-title\">\r\n              {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}\r\n            </h6>\r\n            <p class=\"empty-description\" *ngIf=\"searchKeyword\">\r\n              請嘗試其他關鍵字或\r\n              <a href=\"javascript:void(0)\" (click)=\"clearSearch()\" class=\"clear-link\">清除搜尋條件</a>\r\n            </p>\r\n            <p class=\"empty-description\" *ngIf=\"!searchKeyword\">\r\n              目前還沒有建立任何模板，請先建立模板\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 優化的分頁控制器 -->\r\n      <div class=\"enhanced-pagination-container mt-4\" *ngIf=\"templatePagination.totalPages > 1\">\r\n        <div class=\"pagination-wrapper\">\r\n          <div class=\"pagination-info\">\r\n            <span class=\"page-info\">\r\n              第 {{ templatePagination.currentPage }} 頁，共 {{ templatePagination.totalPages }} 頁\r\n            </span>\r\n          </div>\r\n          <nav aria-label=\"模板列表分頁\" class=\"pagination-nav\">\r\n            <ul class=\"enhanced-pagination\">\r\n              <!-- 第一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === 1\">\r\n                <button class=\"page-btn first-page\" (click)=\"goToTemplatePage(1)\"\r\n                  [disabled]=\"templatePagination.currentPage === 1\" title=\"第一頁\">\r\n                  <i class=\"fas fa-angle-double-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 上一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === 1\">\r\n                <button class=\"page-btn prev-page\" (click)=\"goToTemplatePage(templatePagination.currentPage - 1)\"\r\n                  [disabled]=\"templatePagination.currentPage === 1\" title=\"上一頁\">\r\n                  <i class=\"fas fa-chevron-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 頁碼 -->\r\n              <li class=\"page-item\" *ngFor=\"let page of getTemplatePageNumbers()\"\r\n                [class.active]=\"page === templatePagination.currentPage\">\r\n                <button class=\"page-btn page-number\" (click)=\"goToTemplatePage(page)\">{{ page }}</button>\r\n              </li>\r\n\r\n              <!-- 下一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n                <button class=\"page-btn next-page\" (click)=\"goToTemplatePage(templatePagination.currentPage + 1)\"\r\n                  [disabled]=\"templatePagination.currentPage === templatePagination.totalPages\" title=\"下一頁\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 最後一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n                <button class=\"page-btn last-page\" (click)=\"goToTemplatePage(templatePagination.totalPages)\"\r\n                  [disabled]=\"templatePagination.currentPage === templatePagination.totalPages\" title=\"最後一頁\">\r\n                  <i class=\"fas fa-angle-double-right\"></i>\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 優化的模板詳情視圖 -->\r\n    <div *ngIf=\"selectedTemplate\" class=\"template-detail-view\">\r\n      <!-- 詳情標題欄 -->\r\n      <div class=\"detail-header\">\r\n        <div class=\"detail-title-section\">\r\n          <div class=\"back-button\">\r\n            <button class=\"back-btn\" (click)=\"closeTemplateDetail()\" title=\"返回模板列表\">\r\n              <i class=\"fas fa-arrow-left\"></i>\r\n            </button>\r\n          </div>\r\n          <div class=\"detail-title-info\">\r\n            <h5 class=\"detail-title\">\r\n              <i class=\"fas fa-file-alt mr-2 text-primary\"></i>\r\n              {{ selectedTemplate!.TemplateName }}\r\n            </h5>\r\n            <p class=\"detail-subtitle\" *ngIf=\"selectedTemplate.Description\">\r\n              {{ selectedTemplate.Description }}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div class=\"detail-stats\">\r\n          <div class=\"stat-item\">\r\n            <span class=\"stat-label\">項目數量</span>\r\n            <span class=\"stat-value\">{{ currentTemplateDetailsData.length > 0 ? detailPagination.totalItems :\r\n              currentTemplateDetails.length }}</span>\r\n          </div>\r\n          <div class=\"stat-item\" *ngIf=\"detailPagination.totalPages > 1\">\r\n            <span class=\"stat-label\">頁數</span>\r\n            <span class=\"stat-value\">{{ detailPagination.currentPage }} / {{ detailPagination.totalPages }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 明細專用搜尋區域 -->\r\n      <div class=\"detail-search-section mb-4\">\r\n        <div class=\"detail-search-wrapper\">\r\n          <div class=\"detail-search-input-group\">\r\n            <div class=\"search-icon\">\r\n              <i class=\"fas fa-search\"></i>\r\n            </div>\r\n            <input type=\"text\" class=\"detail-search-input\" placeholder=\"搜尋明細項目名稱、群組...\"\r\n              [(ngModel)]=\"detailSearchKeyword\" (keyup.enter)=\"searchTemplateDetails(detailSearchKeyword)\">\r\n            <div class=\"search-actions\">\r\n              <button class=\"search-btn\" type=\"button\" (click)=\"searchTemplateDetails(detailSearchKeyword)\" title=\"搜尋\">\r\n                <i class=\"fas fa-search\"></i>\r\n              </button>\r\n              <button class=\"clear-btn\" type=\"button\" (click)=\"clearDetailSearch()\" title=\"清除搜尋\"\r\n                *ngIf=\"detailSearchKeyword\">\r\n                <i class=\"fas fa-times\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n          <div class=\"search-results-info\" *ngIf=\"detailSearchKeyword\">\r\n            <small class=\"text-success\" *ngIf=\"detailPagination.totalItems > 0\">\r\n              <i class=\"fas fa-check-circle mr-1\"></i>找到 {{ detailPagination.totalItems }} 個相關項目\r\n            </small>\r\n            <small class=\"text-warning\" *ngIf=\"detailPagination.totalItems === 0\">\r\n              <i class=\"fas fa-exclamation-triangle mr-1\"></i>未找到符合條件的項目\r\n            </small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 詳情內容區域 -->\r\n      <div class=\"detail-content\">\r\n\r\n\r\n        <!-- 優化的詳情項目顯示 -->\r\n        <div *ngIf=\"currentTemplateDetailsData.length > 0; else checkOldDetails\" class=\"enhanced-detail-list\">\r\n          <div *ngFor=\"let detail of currentTemplateDetailsData; let i = index\" class=\"enhanced-detail-item\">\r\n            <div class=\"detail-item-card\">\r\n              <div class=\"detail-item-header\">\r\n                <div class=\"item-index\">\r\n                  <span class=\"index-badge\">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i + 1\r\n                    }}</span>\r\n                </div>\r\n                <div class=\"item-main-info\">\r\n                  <h6 class=\"item-name\">\r\n                    <i class=\"fas fa-cog mr-2 text-secondary\"></i>\r\n                    {{ detail.CReleateName }}\r\n                  </h6>\r\n                  <div class=\"item-meta\">\r\n                    <span class=\"meta-item id-meta\">\r\n                      <i class=\"fas fa-hashtag\"></i>\r\n                      <span>{{ detail.CReleateId }}</span>\r\n                    </span>\r\n                    <span class=\"meta-item group-meta\" *ngIf=\"detail.CGroupName\">\r\n                      <i class=\"fas fa-layer-group\"></i>\r\n                      <span>{{ detail.CGroupName }}</span>\r\n                    </span>\r\n                    <span class=\"meta-item category-meta\" *ngIf=\"detail.CCategory\">\r\n                      <i class=\"fas fa-tag\"></i>\r\n                      <span>{{ detail.CCategory }}</span>\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item-actions\">\r\n                  <span class=\"create-date\">{{ detail.CCreateDt | date:'MM/dd' }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"detail-item-body\"\r\n                *ngIf=\"detail.CUnitPrice || detail.CQuantity || detail.CUnit || detail.CRemark\">\r\n                <div class=\"item-details-grid\">\r\n                  <div class=\"detail-group price-group\" *ngIf=\"detail.CUnitPrice\">\r\n                    <span class=\"detail-label\">單價</span>\r\n                    <span class=\"detail-value price-value\">NT$ {{ detail.CUnitPrice | number }}</span>\r\n                  </div>\r\n                  <div class=\"detail-group quantity-group\" *ngIf=\"detail.CQuantity && detail.CUnit\">\r\n                    <span class=\"detail-label\">數量</span>\r\n                    <span class=\"detail-value quantity-value\">{{ detail.CQuantity }} {{ detail.CUnit }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item-remark\" *ngIf=\"detail.CRemark\">\r\n                  <div class=\"remark-content\">\r\n                    <i class=\"fas fa-comment-alt mr-2 text-muted\"></i>\r\n                    <span class=\"remark-text\">{{ detail.CRemark }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 向後相容：舊的詳情資料顯示 -->\r\n        <ng-template #checkOldDetails>\r\n          <div *ngIf=\"currentTemplateDetails.length > 0; else noDetails\" class=\"detail-list\">\r\n            <div *ngFor=\"let detail of paginatedDetails; let i = index\"\r\n              class=\"detail-item d-flex align-items-center py-2 border-bottom\">\r\n              <div class=\"detail-index\">\r\n                <span class=\"badge badge-light\">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i\r\n                  + 1 }}</span>\r\n              </div>\r\n              <div class=\"detail-content flex-grow-1 ml-2\">\r\n                <div class=\"detail-field\">\r\n                  <strong>{{ detail.FieldName }}:</strong>\r\n                </div>\r\n                <div class=\"detail-value text-muted\">\r\n                  {{ detail.FieldValue }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-template>\r\n\r\n        <!-- 詳情分頁控制器 -->\r\n        <div class=\"detail-pagination mt-3\" *ngIf=\"detailPagination.totalPages > 1\">\r\n          <nav aria-label=\"模板詳情分頁\">\r\n            <ul class=\"pagination pagination-sm justify-content-center mb-0\">\r\n              <!-- 上一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === 1\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage - 1)\"\r\n                  [disabled]=\"detailPagination.currentPage === 1\">\r\n                  <i class=\"fas fa-chevron-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 頁碼 -->\r\n              <li class=\"page-item\" *ngFor=\"let page of getDetailPageNumbers()\"\r\n                [class.active]=\"page === detailPagination.currentPage\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(page)\">{{ page }}</button>\r\n              </li>\r\n\r\n              <!-- 下一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage + 1)\"\r\n                  [disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n\r\n        <ng-template #noDetails>\r\n          <div class=\"text-center py-3\">\r\n            <i class=\"fas fa-inbox fa-2x text-muted mb-2\"></i>\r\n            <p class=\"text-muted mb-0\">此模板暫無內容</p>\r\n          </div>\r\n        </ng-template>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"template-viewer-footer\">\r\n    <div class=\"footer-actions\">\r\n      <button class=\"close-btn\" (click)=\"onClose()\">\r\n        <i class=\"fas fa-times mr-2\"></i>關閉\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;;;;;;;;;ICsBjDC,EADF,CAAAC,cAAA,cAAkD,iBACoC;IAArCD,EAAA,CAAAE,UAAA,mBAAAC,sEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACpET,EAAA,CAAAU,SAAA,YAA4B;IAEhCV,EADE,CAAAW,YAAA,EAAS,EACL;;;;;IAGNX,EADF,CAAAC,cAAA,cAAsF,gBACxD;IAC1BD,EAAA,CAAAU,SAAA,YAAwC;IAAAV,EAAA,CAAAY,MAAA,GAC1C;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;IAFsCX,EAAA,CAAAa,SAAA,GAC1C;IAD0Cb,EAAA,CAAAc,kBAAA,kBAAAR,MAAA,CAAAS,iBAAA,CAAAC,MAAA,qCAC1C;;;;;IAGAhB,EADF,CAAAC,cAAA,cAAuF,gBACzD;IAC1BD,EAAA,CAAAU,SAAA,YAAgD;IAAAV,EAAA,CAAAY,MAAA,oEAClD;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;;;IApBJX,EAHN,CAAAC,cAAA,cAAsE,cACxC,cACM,cACL;IACvBD,EAAA,CAAAU,SAAA,YAA6B;IAC/BV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBACkD;IADqBD,EAAA,CAAAiB,gBAAA,2BAAAC,uEAAAC,MAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAqB,kBAAA,CAAAf,MAAA,CAAAgB,aAAA,EAAAH,MAAA,MAAAb,MAAA,CAAAgB,aAAA,GAAAH,MAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAAAW,MAAA;IAAA,EAA2B;IAC3EnB,EAArB,CAAAE,UAAA,mBAAAqB,+DAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkB,QAAA,EAAU;IAAA,EAAC,yBAAAC,qEAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAgBF,MAAA,CAAAkB,QAAA,EAAU;IAAA,EAAC;IADjDxB,EAAA,CAAAW,YAAA,EACkD;IAClDX,EAAA,CAAA0B,UAAA,IAAAC,6CAAA,kBAAkD;IAKpD3B,EAAA,CAAAW,YAAA,EAAM;IAMNX,EALA,CAAA0B,UAAA,IAAAE,6CAAA,kBAAsF,IAAAC,6CAAA,kBAKC;IAM3F7B,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAnBuEX,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAA8B,gBAAA,YAAAxB,MAAA,CAAAgB,aAAA,CAA2B;IAErEtB,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgB,aAAA,CAAmB;IAMjBtB,EAAA,CAAAa,SAAA,EAAmD;IAAnDb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgB,aAAA,IAAAhB,MAAA,CAAAS,iBAAA,CAAAC,MAAA,KAAmD;IAKpDhB,EAAA,CAAAa,SAAA,EAAqD;IAArDb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgB,aAAA,IAAAhB,MAAA,CAAAS,iBAAA,CAAAC,MAAA,OAAqD;;;;;IAgBjFhB,EAHN,CAAAC,cAAA,cAA0E,aACT,cACtC,eACG;IACtBD,EAAA,CAAAY,MAAA,GAIF;IACFZ,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAC,cAAA,cAA0B,eACE;IAAAD,EAAA,CAAAY,MAAA,GACvB;IAGTZ,EAHS,CAAAW,YAAA,EAAQ,EACP,EACF,EACF;;;;IAXEX,EAAA,CAAAa,SAAA,GAIF;IAJEb,EAAA,CAAAgC,kBAAA,0BAAA1B,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,QAAA5B,MAAA,CAAA2B,kBAAA,CAAAE,QAAA,aAAA7B,MAAA,CAAA8B,IAAA,CAAAC,GAAA,CAAA/B,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,GAAA5B,MAAA,CAAA2B,kBAAA,CAAAE,QAAA,EAAA7B,MAAA,CAAA2B,kBAAA,CAAAK,UAAA,4BAAAhC,MAAA,CAAA2B,kBAAA,CAAAK,UAAA,yBAIF;IAG0BtC,EAAA,CAAAa,SAAA,GACvB;IADuBb,EAAA,CAAAuC,kBAAA,YAAAjC,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,SAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,YACvB;;;;;IAkBCxC,EADF,CAAAC,cAAA,cAA6C,eAClB;IAAAD,EAAA,CAAAY,MAAA,qCAAK;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACrCX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAY,MAAA,GAAiD;;IAC5EZ,EAD4E,CAAAW,YAAA,EAAO,EAC7E;;;;IADqBX,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAyC,iBAAA,CAAAzC,EAAA,CAAA0C,WAAA,OAAAC,MAAA,CAAAC,UAAA,yBAAiD;;;;;IAG1E5C,EADF,CAAAC,cAAA,cAA0C,eACf;IAAAD,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACpCX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAY,MAAA,GAAiB;IAC5CZ,EAD4C,CAAAW,YAAA,EAAO,EAC7C;;;;IADqBX,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAyC,iBAAA,CAAAE,MAAA,CAAAE,OAAA,CAAiB;;;;;;IAS9C7C,EAAA,CAAAC,cAAA,iBACsC;IADAD,EAAA,CAAAE,UAAA,mBAAA4C,gFAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAA2C,GAAA;MAAA,MAAAJ,MAAA,GAAA3C,EAAA,CAAAO,aAAA,GAAAyC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAmC,MAAA,CAAAM,UAAA,IAA2B3C,MAAA,CAAA4C,gBAAA,CAAAP,MAAA,CAAAM,UAAA,CAAgC;IAAA,EAAC;IAEhGjD,EAAA,CAAAU,SAAA,YAA4B;IAC5BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IACVZ,EADU,CAAAW,YAAA,EAAO,EACR;;;;;;IA1BPX,EAHN,CAAAC,cAAA,cAA8F,cAC5D,cACD,eACE;IAAAD,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACxCX,EAAA,CAAAC,cAAA,aAA0B;IACxBD,EAAA,CAAAU,SAAA,YAAoC;IACpCV,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAK,EACD;IACNX,EAAA,CAAAC,cAAA,cAA2B;IAKzBD,EAJA,CAAA0B,UAAA,IAAAyB,mDAAA,kBAA6C,KAAAC,oDAAA,kBAIH;IAK9CpD,EADE,CAAAW,YAAA,EAAM,EACF;IAEJX,EADF,CAAAC,cAAA,eAA8B,kBACqD;IAA7CD,EAAA,CAAAE,UAAA,mBAAAmD,uEAAA;MAAA,MAAAV,MAAA,GAAA3C,EAAA,CAAAI,aAAA,CAAAkD,GAAA,EAAAN,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiD,gBAAA,CAAAZ,MAAA,CAAqB;IAAA,EAAC;IACjE3C,EAAA,CAAAU,SAAA,aAA0B;IAC1BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,oBAAE;IACVZ,EADU,CAAAW,YAAA,EAAO,EACR;IACTX,EAAA,CAAA0B,UAAA,KAAA8B,uDAAA,qBACsC;IAK1CxD,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAzBEX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA6B,MAAA,CAAAc,YAAA,MACF;IAGuBzD,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAA+B,UAAA,SAAAY,MAAA,CAAAC,UAAA,CAAoB;IAIpB5C,EAAA,CAAAa,SAAA,EAAiB;IAAjBb,EAAA,CAAA+B,UAAA,SAAAY,MAAA,CAAAE,OAAA,CAAiB;IAYvC7C,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAA+B,UAAA,SAAAY,MAAA,CAAAM,UAAA,CAAoB;;;;;IAWrBjD,EAAA,CAAAU,SAAA,YAAmD;;;;;IACnDV,EAAA,CAAAU,SAAA,YAAyD;;;;;;IAK3DV,EAAA,CAAAC,cAAA,YAAmD;IACjDD,EAAA,CAAAY,MAAA,+DACA;IAAAZ,EAAA,CAAAC,cAAA,YAAwE;IAA3CD,EAAA,CAAAE,UAAA,mBAAAwD,qEAAA;MAAA1D,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAAoBT,EAAA,CAAAY,MAAA,2CAAM;IAChFZ,EADgF,CAAAW,YAAA,EAAI,EAChF;;;;;IACJX,EAAA,CAAAC,cAAA,YAAoD;IAClDD,EAAA,CAAAY,MAAA,qHACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IAbJX,EAFJ,CAAAC,cAAA,cAA6F,cAChE,cACD;IAEtBD,EADA,CAAA0B,UAAA,IAAAkC,iDAAA,gBAA+C,IAAAC,iDAAA,gBACM;IACvD7D,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,aAAwB;IACtBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAKLX,EAJA,CAAA0B,UAAA,IAAAoC,iDAAA,gBAAmD,IAAAC,iDAAA,gBAIC;IAIxD/D,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAd0BX,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgB,aAAA,CAAmB;IACdtB,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAA+B,UAAA,UAAAzB,MAAA,CAAAgB,aAAA,CAAoB;IAGnDtB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAAgB,aAAA,oGACF;IAC8BtB,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgB,aAAA,CAAmB;IAInBtB,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAA+B,UAAA,UAAAzB,MAAA,CAAAgB,aAAA,CAAoB;;;;;;IAoC9CtB,EAFF,CAAAC,cAAA,aAC2D,iBACa;IAAjCD,EAAA,CAAAE,UAAA,mBAAA8D,4EAAA;MAAA,MAAAC,QAAA,GAAAjE,EAAA,CAAAI,aAAA,CAAA8D,GAAA,EAAAlB,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,gBAAA,CAAAF,QAAA,CAAsB;IAAA,EAAC;IAACjE,EAAA,CAAAY,MAAA,GAAU;IAClFZ,EADkF,CAAAW,YAAA,EAAS,EACtF;;;;;IAFHX,EAAA,CAAAoE,WAAA,WAAAH,QAAA,KAAA3D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,CAAwD;IACclC,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAAyC,iBAAA,CAAAwB,QAAA,CAAU;;;;;;IAzBpFjE,EAHN,CAAAC,cAAA,cAA0F,cACxD,cACD,eACH;IACtBD,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAO,EACH;IAKAX,EAJN,CAAAC,cAAA,cAAgD,aACd,aAEgD,iBAEZ;IAD5BD,EAAA,CAAAE,UAAA,mBAAAmE,sEAAA;MAAArE,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,gBAAA,CAAiB,CAAC,CAAC;IAAA,EAAC;IAE/DnE,EAAA,CAAAU,SAAA,YAAwC;IAE5CV,EADE,CAAAW,YAAA,EAAS,EACN;IAIHX,EADF,CAAAC,cAAA,cAA8E,kBAEZ;IAD7BD,EAAA,CAAAE,UAAA,mBAAAqE,uEAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,gBAAA,CAAA7D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAE/FlC,EAAA,CAAAU,SAAA,aAAmC;IAEvCV,EADE,CAAAW,YAAA,EAAS,EACN;IAGLX,EAAA,CAAA0B,UAAA,KAAA8C,mDAAA,iBAC2D;IAMzDxE,EADF,CAAAC,cAAA,cAA0G,kBAEZ;IADzDD,EAAA,CAAAE,UAAA,mBAAAuE,uEAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,gBAAA,CAAA7D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAE/FlC,EAAA,CAAAU,SAAA,aAAoC;IAExCV,EADE,CAAAW,YAAA,EAAS,EACN;IAIHX,EADF,CAAAC,cAAA,cAA0G,kBAEX;IAD1DD,EAAA,CAAAE,UAAA,mBAAAwE,uEAAA;MAAA1E,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6D,gBAAA,CAAA7D,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,CAA+C;IAAA,EAAC;IAE1FxC,EAAA,CAAAU,SAAA,aAAyC;IAMrDV,EALU,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACF,EACF;;;;IA7CEX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAuC,kBAAA,aAAAjC,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,0BAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,aACF;IAKwBxC,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,OAAuD;IAEzElC,EAAA,CAAAa,SAAA,EAAiD;IAAjDb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,OAAiD;IAM/BlC,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,OAAuD;IAEzElC,EAAA,CAAAa,SAAA,EAAiD;IAAjDb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,OAAiD;IAMdlC,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAAqE,sBAAA,GAA2B;IAM5C3E,EAAA,CAAAa,SAAA,EAAmF;IAAnFb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,KAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,CAAmF;IAErGxC,EAAA,CAAAa,SAAA,EAA6E;IAA7Eb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,KAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,CAA6E;IAM3DxC,EAAA,CAAAa,SAAA,GAAmF;IAAnFb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,KAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,CAAmF;IAErGxC,EAAA,CAAAa,SAAA,EAA6E;IAA7Eb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA2B,kBAAA,CAAAC,WAAA,KAAA5B,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,CAA6E;;;;;IAtH3FxC,EAAA,CAAAC,cAAA,cAA+D;IAE7DD,EAAA,CAAA0B,UAAA,IAAAkD,6CAAA,kBAA0E;IAkB1E5E,EAAA,CAAAC,cAAA,cAAqC;IAmCnCD,EAlCA,CAAA0B,UAAA,IAAAmD,6CAAA,mBAA8F,IAAAC,6CAAA,kBAkCD;IAkB/F9E,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA0B,UAAA,IAAAqD,6CAAA,oBAA0F;IAkD5F/E,EAAA,CAAAW,YAAA,EAAM;;;;IA5H6BX,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA2B,kBAAA,CAAAK,UAAA,KAAuC;IAmB3BtC,EAAA,CAAAa,SAAA,GAAuB;IAAAb,EAAvB,CAAA+B,UAAA,YAAAzB,MAAA,CAAA0E,kBAAA,CAAuB,iBAAA1E,MAAA,CAAA2E,iBAAA,CAA0B;IAkC7DjF,EAAA,CAAAa,SAAA,EAA4D;IAA5Db,EAAA,CAAA+B,UAAA,UAAAzB,MAAA,CAAA0E,kBAAA,IAAA1E,MAAA,CAAA0E,kBAAA,CAAAhE,MAAA,OAA4D;IAqB5ChB,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA2B,kBAAA,CAAAO,UAAA,KAAuC;;;;;IAmElFxC,EAAA,CAAAC,cAAA,aAAgE;IAC9DD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;IADFX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,MACF;;;;;IAUAnF,EADF,CAAAC,cAAA,eAA+D,gBACpC;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAClCX,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAY,MAAA,GAAsE;IACjGZ,EADiG,CAAAW,YAAA,EAAO,EAClG;;;;IADqBX,EAAA,CAAAa,SAAA,GAAsE;IAAtEb,EAAA,CAAAuC,kBAAA,KAAAjC,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,SAAA5B,MAAA,CAAA8E,gBAAA,CAAA5C,UAAA,KAAsE;;;;;;IAkB7FxC,EAAA,CAAAC,cAAA,kBAC8B;IADUD,EAAA,CAAAE,UAAA,mBAAAmF,0EAAA;MAAArF,EAAA,CAAAI,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiF,iBAAA,EAAmB;IAAA,EAAC;IAEnEvF,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAW,YAAA,EAAS;;;;;IAIXX,EAAA,CAAAC,cAAA,gBAAoE;IAClED,EAAA,CAAAU,SAAA,YAAwC;IAAAV,EAAA,CAAAY,MAAA,GAC1C;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;;;;IADkCX,EAAA,CAAAa,SAAA,GAC1C;IAD0Cb,EAAA,CAAAc,kBAAA,kBAAAR,MAAA,CAAA8E,gBAAA,CAAA9C,UAAA,qCAC1C;;;;;IACAtC,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAU,SAAA,YAAgD;IAAAV,EAAA,CAAAY,MAAA,oEAClD;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;;;;;IANVX,EAAA,CAAAC,cAAA,eAA6D;IAI3DD,EAHA,CAAA0B,UAAA,IAAA8D,sDAAA,qBAAoE,IAAAC,sDAAA,qBAGE;IAGxEzF,EAAA,CAAAW,YAAA,EAAM;;;;IANyBX,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA8E,gBAAA,CAAA9C,UAAA,KAAqC;IAGrCtC,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA8E,gBAAA,CAAA9C,UAAA,OAAuC;;;;;IA8B5DtC,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAU,SAAA,aAAkC;IAClCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IAC/BZ,EAD+B,CAAAW,YAAA,EAAO,EAC/B;;;;IADCX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAyC,iBAAA,CAAAiD,UAAA,CAAAC,UAAA,CAAuB;;;;;IAE/B3F,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAU,SAAA,aAA0B;IAC1BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAC9BZ,EAD8B,CAAAW,YAAA,EAAO,EAC9B;;;;IADCX,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAyC,iBAAA,CAAAiD,UAAA,CAAAE,SAAA,CAAsB;;;;;IAa9B5F,EADF,CAAAC,cAAA,eAAgE,gBACnC;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACpCX,EAAA,CAAAC,cAAA,gBAAuC;IAAAD,EAAA,CAAAY,MAAA,GAAoC;;IAC7EZ,EAD6E,CAAAW,YAAA,EAAO,EAC9E;;;;IADmCX,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAAc,kBAAA,SAAAd,EAAA,CAAA6F,WAAA,OAAAH,UAAA,CAAAI,UAAA,MAAoC;;;;;IAG3E9F,EADF,CAAAC,cAAA,eAAkF,gBACrD;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACpCX,EAAA,CAAAC,cAAA,gBAA0C;IAAAD,EAAA,CAAAY,MAAA,GAAyC;IACrFZ,EADqF,CAAAW,YAAA,EAAO,EACtF;;;;IADsCX,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAuC,kBAAA,KAAAmD,UAAA,CAAAK,SAAA,OAAAL,UAAA,CAAAM,KAAA,KAAyC;;;;;IAIrFhG,EADF,CAAAC,cAAA,eAAgD,eAClB;IAC1BD,EAAA,CAAAU,SAAA,aAAkD;IAClDV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IAElDZ,EAFkD,CAAAW,YAAA,EAAO,EACjD,EACF;;;;IAFwBX,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAyC,iBAAA,CAAAiD,UAAA,CAAAO,OAAA,CAAoB;;;;;IAblDjG,EAFF,CAAAC,cAAA,eACkF,eACjD;IAK7BD,EAJA,CAAA0B,UAAA,IAAAwE,iEAAA,mBAAgE,IAAAC,iEAAA,mBAIkB;IAIpFnG,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAA0B,UAAA,IAAA0E,iEAAA,mBAAgD;IAMlDpG,EAAA,CAAAW,YAAA,EAAM;;;;IAfqCX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAA+B,UAAA,SAAA2D,UAAA,CAAAI,UAAA,CAAuB;IAIpB9F,EAAA,CAAAa,SAAA,EAAsC;IAAtCb,EAAA,CAAA+B,UAAA,SAAA2D,UAAA,CAAAK,SAAA,IAAAL,UAAA,CAAAM,KAAA,CAAsC;IAKxDhG,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAA+B,UAAA,SAAA2D,UAAA,CAAAO,OAAA,CAAoB;;;;;IAxC5CjG,EAJR,CAAAC,cAAA,eAAmG,eACnE,eACI,eACN,gBACI;IAAAD,EAAA,CAAAY,MAAA,GACtB;IACNZ,EADM,CAAAW,YAAA,EAAO,EACP;IAEJX,EADF,CAAAC,cAAA,eAA4B,cACJ;IACpBD,EAAA,CAAAU,SAAA,aAA8C;IAC9CV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAEHX,EADF,CAAAC,cAAA,gBAAuB,iBACW;IAC9BD,EAAA,CAAAU,SAAA,cAA8B;IAC9BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,IAAuB;IAC/BZ,EAD+B,CAAAW,YAAA,EAAO,EAC/B;IAKPX,EAJA,CAAA0B,UAAA,KAAA2E,4DAAA,oBAA6D,KAAAC,4DAAA,oBAIE;IAKnEtG,EADE,CAAAW,YAAA,EAAM,EACF;IAEJX,EADF,CAAAC,cAAA,gBAA0B,iBACE;IAAAD,EAAA,CAAAY,MAAA,IAAqC;;IAEnEZ,EAFmE,CAAAW,YAAA,EAAO,EAClE,EACF;IAENX,EAAA,CAAA0B,UAAA,KAAA6E,2DAAA,mBACkF;IAmBtFvG,EADE,CAAAW,YAAA,EAAM,EACF;;;;;;IAhD4BX,EAAA,CAAAa,SAAA,GACtB;IADsBb,EAAA,CAAAyC,iBAAA,EAAAnC,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,QAAA5B,MAAA,CAAA8E,gBAAA,CAAAjD,QAAA,GAAAqE,KAAA,KACtB;IAKFxG,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA4E,UAAA,CAAAe,YAAA,MACF;IAIUzG,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAyC,iBAAA,CAAAiD,UAAA,CAAAgB,UAAA,CAAuB;IAEK1G,EAAA,CAAAa,SAAA,EAAuB;IAAvBb,EAAA,CAAA+B,UAAA,SAAA2D,UAAA,CAAAC,UAAA,CAAuB;IAIpB3F,EAAA,CAAAa,SAAA,EAAsB;IAAtBb,EAAA,CAAA+B,UAAA,SAAA2D,UAAA,CAAAE,SAAA,CAAsB;IAOrC5F,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAyC,iBAAA,CAAAzC,EAAA,CAAA0C,WAAA,QAAAgD,UAAA,CAAAiB,SAAA,WAAqC;IAKhE3G,EAAA,CAAAa,SAAA,GAA6E;IAA7Eb,EAAA,CAAA+B,UAAA,SAAA2D,UAAA,CAAAI,UAAA,IAAAJ,UAAA,CAAAK,SAAA,IAAAL,UAAA,CAAAM,KAAA,IAAAN,UAAA,CAAAO,OAAA,CAA6E;;;;;IAlCtFjG,EAAA,CAAAC,cAAA,eAAsG;IACpGD,EAAA,CAAA0B,UAAA,IAAAkF,oDAAA,qBAAmG;IAqDrG5G,EAAA,CAAAW,YAAA,EAAM;;;;IArDoBX,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAAuG,0BAAA,CAA+B;;;;;IA6DjD7G,EAHJ,CAAAC,cAAA,eACmE,eACvC,gBACQ;IAAAD,EAAA,CAAAY,MAAA,GACxB;IACVZ,EADU,CAAAW,YAAA,EAAO,EACX;IAGFX,EAFJ,CAAAC,cAAA,eAA6C,eACjB,aAChB;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IACjCZ,EADiC,CAAAW,YAAA,EAAS,EACpC;IACNX,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAY,MAAA,GACF;IAEJZ,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;;;IAX8BX,EAAA,CAAAa,SAAA,GACxB;IADwBb,EAAA,CAAAyC,iBAAA,EAAAnC,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,QAAA5B,MAAA,CAAA8E,gBAAA,CAAAjD,QAAA,GAAA2E,KAAA,KACxB;IAIE9G,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,kBAAA,KAAAiG,UAAA,CAAAC,SAAA,MAAuB;IAG/BhH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAiG,UAAA,CAAAE,UAAA,MACF;;;;;IAbNjH,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAA0B,UAAA,IAAAwF,kEAAA,oBACmE;IAcrElH,EAAA,CAAAW,YAAA,EAAM;;;;IAfoBX,EAAA,CAAAa,SAAA,EAAqB;IAArBb,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAA6G,gBAAA,CAAqB;;;;;IAD/CnH,EAAA,CAAA0B,UAAA,IAAA0F,4DAAA,mBAAmF;;;;;;IAApCpH,EAAzC,CAAA+B,UAAA,SAAAzB,MAAA,CAAA+G,sBAAA,CAAArG,MAAA,KAAyC,aAAAsG,aAAA,CAAc;;;;;;IAkCvDtH,EAFF,CAAAC,cAAA,aACyD,kBACE;IAA/BD,EAAA,CAAAE,UAAA,mBAAAqH,4EAAA;MAAA,MAAAC,QAAA,GAAAxH,EAAA,CAAAI,aAAA,CAAAqH,IAAA,EAAAzE,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoH,cAAA,CAAAF,QAAA,CAAoB;IAAA,EAAC;IAACxH,EAAA,CAAAY,MAAA,GAAU;IACrEZ,EADqE,CAAAW,YAAA,EAAS,EACzE;;;;;IAFHX,EAAA,CAAAoE,WAAA,WAAAoD,QAAA,KAAAlH,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,CAAsD;IACGlC,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAAyC,iBAAA,CAAA+E,QAAA,CAAU;;;;;;IATnExH,EALR,CAAAC,cAAA,eAA4E,eACjD,cAC0C,aAEa,kBAExB;IADxBD,EAAA,CAAAE,UAAA,mBAAAyH,uEAAA;MAAA3H,EAAA,CAAAI,aAAA,CAAAwH,IAAA;MAAA,MAAAtH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoH,cAAA,CAAApH,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElFlC,EAAA,CAAAU,SAAA,YAAmC;IAEvCV,EADE,CAAAW,YAAA,EAAS,EACN;IAGLX,EAAA,CAAA0B,UAAA,IAAAmG,mDAAA,iBACyD;IAMvD7H,EADF,CAAAC,cAAA,aAAsG,kBAExB;IADlDD,EAAA,CAAAE,UAAA,mBAAA4H,uEAAA;MAAA9H,EAAA,CAAAI,aAAA,CAAAwH,IAAA;MAAA,MAAAtH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoH,cAAA,CAAApH,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElFlC,EAAA,CAAAU,SAAA,YAAoC;IAK9CV,EAJQ,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAtBsBX,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,OAAqD;IAEvElC,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,OAA+C;IAMZlC,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAA+B,UAAA,YAAAzB,MAAA,CAAAyH,oBAAA,GAAyB;IAM1C/H,EAAA,CAAAa,SAAA,EAA+E;IAA/Eb,EAAA,CAAAoE,WAAA,aAAA9D,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,KAAA5B,MAAA,CAAA8E,gBAAA,CAAA5C,UAAA,CAA+E;IAEjGxC,EAAA,CAAAa,SAAA,EAAyE;IAAzEb,EAAA,CAAA+B,UAAA,aAAAzB,MAAA,CAAA8E,gBAAA,CAAAlD,WAAA,KAAA5B,MAAA,CAAA8E,gBAAA,CAAA5C,UAAA,CAAyE;;;;;IASjFxC,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAU,SAAA,aAAkD;IAClDV,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAY,MAAA,iDAAO;IACpCZ,EADoC,CAAAW,YAAA,EAAI,EAClC;;;;;;IA5KJX,EALR,CAAAC,cAAA,cAA2D,cAE9B,cACS,cACP,iBACiD;IAA/CD,EAAA,CAAAE,UAAA,mBAAA8H,gEAAA;MAAAhI,EAAA,CAAAI,aAAA,CAAA6H,IAAA;MAAA,MAAA3H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4H,mBAAA,EAAqB;IAAA,EAAC;IACtDlI,EAAA,CAAAU,SAAA,YAAiC;IAErCV,EADE,CAAAW,YAAA,EAAS,EACL;IAEJX,EADF,CAAAC,cAAA,cAA+B,aACJ;IACvBD,EAAA,CAAAU,SAAA,YAAiD;IACjDV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAA0B,UAAA,KAAAyG,4CAAA,gBAAgE;IAIpEnI,EADE,CAAAW,YAAA,EAAM,EACF;IAGFX,EAFJ,CAAAC,cAAA,gBAA0B,gBACD,iBACI;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACpCX,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAY,MAAA,IACS;IACpCZ,EADoC,CAAAW,YAAA,EAAO,EACrC;IACNX,EAAA,CAAA0B,UAAA,KAAA0G,8CAAA,mBAA+D;IAKnEpI,EADE,CAAAW,YAAA,EAAM,EACF;IAMAX,EAHN,CAAAC,cAAA,gBAAwC,gBACH,gBACM,eACZ;IACvBD,EAAA,CAAAU,SAAA,aAA6B;IAC/BV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,kBAC+F;IAA7FD,EAAA,CAAAiB,gBAAA,2BAAAoH,wEAAAlH,MAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAA6H,IAAA;MAAA,MAAA3H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAqB,kBAAA,CAAAf,MAAA,CAAAgI,mBAAA,EAAAnH,MAAA,MAAAb,MAAA,CAAAgI,mBAAA,GAAAnH,MAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAAAW,MAAA;IAAA,EAAiC;IAACnB,EAAA,CAAAE,UAAA,yBAAAqI,sEAAA;MAAAvI,EAAA,CAAAI,aAAA,CAAA6H,IAAA;MAAA,MAAA3H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAkI,qBAAA,CAAAlI,MAAA,CAAAgI,mBAAA,CAA0C;IAAA,EAAC;IAD9FtI,EAAA,CAAAW,YAAA,EAC+F;IAE7FX,EADF,CAAAC,cAAA,eAA4B,mBAC+E;IAAhED,EAAA,CAAAE,UAAA,mBAAAuI,iEAAA;MAAAzI,EAAA,CAAAI,aAAA,CAAA6H,IAAA;MAAA,MAAA3H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkI,qBAAA,CAAAlI,MAAA,CAAAgI,mBAAA,CAA0C;IAAA,EAAC;IAC3FtI,EAAA,CAAAU,SAAA,aAA6B;IAC/BV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAA0B,UAAA,KAAAgH,iDAAA,sBAC8B;IAIlC1I,EADE,CAAAW,YAAA,EAAM,EACF;IACNX,EAAA,CAAA0B,UAAA,KAAAiH,8CAAA,mBAA6D;IASjE3I,EADE,CAAAW,YAAA,EAAM,EACF;IAGNX,EAAA,CAAAC,cAAA,gBAA4B;IA8G1BD,EA1GA,CAAA0B,UAAA,KAAAkH,8CAAA,mBAAsG,KAAAC,sDAAA,gCAAA7I,EAAA,CAAA8I,sBAAA,CAyDxE,KAAAC,8CAAA,oBAqB8C,KAAAC,sDAAA,gCAAAhJ,EAAA,CAAA8I,sBAAA,CA4BpD;IAO5B9I,EADE,CAAAW,YAAA,EAAM,EACF;;;;;IAxKIX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAA4E,gBAAA,CAAAzB,YAAA,MACF;IAC4BzD,EAAA,CAAAa,SAAA,EAAkC;IAAlCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,CAAkC;IAQrCnF,EAAA,CAAAa,SAAA,GACS;IADTb,EAAA,CAAAyC,iBAAA,CAAAnC,MAAA,CAAAuG,0BAAA,CAAA7F,MAAA,OAAAV,MAAA,CAAA8E,gBAAA,CAAA9C,UAAA,GAAAhC,MAAA,CAAA+G,sBAAA,CAAArG,MAAA,CACS;IAEZhB,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA8E,gBAAA,CAAA5C,UAAA,KAAqC;IAezDxC,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAA8B,gBAAA,YAAAxB,MAAA,CAAAgI,mBAAA,CAAiC;IAM9BtI,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgI,mBAAA,CAAyB;IAKEtI,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAAgI,mBAAA,CAAyB;IAgBvDtI,EAAA,CAAAa,SAAA,GAA6C;IAAAb,EAA7C,CAAA+B,UAAA,SAAAzB,MAAA,CAAAuG,0BAAA,CAAA7F,MAAA,KAA6C,aAAAiI,mBAAA,CAAoB;IA8ElCjJ,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA8E,gBAAA,CAAA5C,UAAA,KAAqC;;;ADlTlF,OAAM,MAAO0G,uBAAuB;EAyClCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAxC1B,KAAAC,YAAY,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAC,cAAc,GAAG,IAAI3J,YAAY,EAAY;IAC7C,KAAA4J,KAAK,GAAG,IAAI5J,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAAyC,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAoH,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE,CAAC,CAAC;IACxC,KAAAvE,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAA2B,0BAA0B,GAAyB,EAAE;IACrD,KAAAyB,mBAAmB,GAAG,EAAE,CAAC,CAAC;IAE1B;IACA,KAAAhH,aAAa,GAAG,EAAE;IAClB,KAAAP,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAkB,kBAAkB,GAAG;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE,CAAC;MACbE,UAAU,EAAE;KACb;IACD,KAAAwC,kBAAkB,GAAe,EAAE;IAEnC;IACA,KAAAI,gBAAgB,GAAG;MACjBlD,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXG,UAAU,EAAE,CAAC;MACbE,UAAU,EAAE;KACb;IACD,KAAA2E,gBAAgB,GAAqB,EAAE;EAIiB;EAExDuC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACA,MAAMG,mBAAmB,GAAwB;MAC/CC,aAAa,EAAE,IAAI,CAACV,YAAY;MAAE;MAClCW,SAAS,EAAE,CAAC;MAAE;MACdC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAACd,eAAe,CAACe,mCAAmC,CAAC;MACvDC,IAAI,EAAEN;KACP,CAAC,CAACO,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UAEA;UACA,IAAI,CAACjB,SAAS,GAAGe,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C1H,UAAU,EAAE0H,IAAI,CAACC,WAAW;YAC5BnH,YAAY,EAAEkH,IAAI,CAACT,aAAa,IAAI,EAAE;YACtC/E,WAAW,EAAEwF,IAAI,CAACT,aAAa,IAAI,EAAE;YACrCW,UAAU,EAAEF,IAAI,CAAChE,SAAS;YAC1B/D,UAAU,EAAE+H,IAAI,CAACG,SAAS;YAC1BjI,OAAO,EAAE8H,IAAI,CAACI,QAAQ,IAAIC,SAAS;YACnCC,OAAO,EAAEN,IAAI,CAACO,QAAQ,IAAIF;WAC3B,CAAC,CAAC;UAEH;UACA,IAAI,CAACpB,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAACH,eAAe,GAAG,EAAE;UACzB,IAAI,CAAC5C,0BAA0B,GAAG,EAAE;QACtC,CAAC,MAAM;UACL;UACA,IAAI,CAAC2C,SAAS,GAAG,EAAE;UACnB,IAAI,CAACC,eAAe,GAAG,EAAE;UACzB,IAAI,CAACG,uBAAuB,EAAE;QAChC;MACF,CAAC;MACDuB,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAAC3B,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACG,uBAAuB,EAAE;MAChC;KACD,CAAC;EACJ;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACtI,aAAa,CAAC8J,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACrK,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACyI,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAM6B,OAAO,GAAG,IAAI,CAAC/J,aAAa,CAACgK,WAAW,EAAE;MAChD,IAAI,CAACvK,iBAAiB,GAAG,IAAI,CAACyI,SAAS,CAAC+B,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAAC/H,YAAY,CAAC6H,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAACrG,WAAW,IAAIqG,QAAQ,CAACrG,WAAW,CAACmG,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;IACA,IAAI,CAACK,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,IAAI,CAACzJ,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACvB,iBAAiB,CAACC,MAAM;IAClE,IAAI,CAACiB,kBAAkB,CAACO,UAAU,GAAGJ,IAAI,CAACuJ,IAAI,CAAC,IAAI,CAAC1J,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACL,kBAAkB,CAACE,QAAQ,CAAC;IAErH;IACA,IAAI,IAAI,CAACF,kBAAkB,CAACC,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACO,UAAU,EAAE;MAC5E,IAAI,CAACP,kBAAkB,CAACC,WAAW,GAAGE,IAAI,CAACwJ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC3J,kBAAkB,CAACO,UAAU,CAAC;IACvF;IAEA,IAAI,CAACqJ,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAAC7J,kBAAkB,CAACC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,kBAAkB,CAACE,QAAQ;IAC/F,MAAM4J,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC7J,kBAAkB,CAACE,QAAQ;IAC9D,IAAI,CAAC6C,kBAAkB,GAAG,IAAI,CAACjE,iBAAiB,CAACiL,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACA5H,gBAAgBA,CAAC8H,IAAY;IAC3B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAChK,kBAAkB,CAACO,UAAU,EAAE;MAC3D,IAAI,CAACP,kBAAkB,CAACC,WAAW,GAAG+J,IAAI;MAC1C,IAAI,CAACJ,wBAAwB,EAAE;IACjC;EACF;EAEA;EACAlH,sBAAsBA,CAAA;IACpB,MAAMuH,KAAK,GAAa,EAAE;IAC1B,MAAM1J,UAAU,GAAG,IAAI,CAACP,kBAAkB,CAACO,UAAU;IACrD,MAAMN,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACC,WAAW;IAEvD;IACA,MAAMiK,SAAS,GAAG/J,IAAI,CAACwJ,GAAG,CAAC,CAAC,EAAE1J,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMkK,OAAO,GAAGhK,IAAI,CAACC,GAAG,CAACG,UAAU,EAAEN,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAImK,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACA1K,QAAQA,CAAA;IACN,IAAI,CAACoI,uBAAuB,EAAE;EAChC;EAEA;EACAnJ,WAAWA,CAAA;IACT,IAAI,CAACa,aAAa,GAAG,EAAE;IACvB,IAAI,CAACsI,uBAAuB,EAAE;EAChC;EAQA;EACArG,gBAAgBA,CAACiI,QAAkB;IACjC,IAAI,CAACtG,gBAAgB,GAAGsG,QAAQ;IAChC,IAAI,CAAClC,cAAc,CAACiD,IAAI,CAACf,QAAQ,CAAC;IAElC;IACA,IAAIA,QAAQ,CAACvI,UAAU,EAAE;MACvB,IAAI,CAACuJ,mBAAmB,CAAChB,QAAQ,CAACvI,UAAU,CAAC;IAC/C;IAEA,IAAI,CAACwJ,sBAAsB,EAAE;EAC/B;EAEA;EACAD,mBAAmBA,CAACE,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAErL,aAAsB;IACnF,MAAMsL,IAAI,GAA8B;MACtCF,UAAU,EAAEA;KACb;IAED;IACA,IAAI,CAACtD,eAAe,CAACyD,yCAAyC,CAAC;MAC7DzC,IAAI,EAAEwC;KACP,CAAC,CAACvC,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UAEjD;UACA,IAAIqC,UAAU,GAAGvC,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7CoC,iBAAiB,EAAEpC,IAAI,CAACoC,iBAAiB,IAAI,CAAC;YAC9CnC,WAAW,EAAED,IAAI,CAACC,WAAW,IAAI8B,UAAU;YAC3ChG,UAAU,EAAEiE,IAAI,CAACjE,UAAU,IAAI,CAAC;YAChCD,YAAY,EAAEkE,IAAI,CAAClE,YAAY,IAAI,EAAE;YACrCd,UAAU,EAAEgF,IAAI,CAAChF,UAAU,IAAI,EAAE;YAAE;YACnCqH,KAAK,EAAEhC,SAAS;YAChB/E,OAAO,EAAE+E,SAAS;YAClBrE,SAAS,EAAE,IAAIsG,IAAI,EAAE,CAACC,WAAW,EAAE;YACnCnC,QAAQ,EAAE,IAAI;YACdnF,SAAS,EAAEoF,SAAS;YACpBlF,UAAU,EAAEkF,SAAS;YACrBjF,SAAS,EAAEiF,SAAS;YACpBhF,KAAK,EAAEgF;WACe,EAAC;UAEzB;UACA,IAAI1J,aAAa,IAAIA,aAAa,CAAC8J,IAAI,EAAE,EAAE;YACzC0B,UAAU,GAAGA,UAAU,CAACvB,MAAM,CAAC4B,MAAM,IACnCA,MAAM,CAAC1G,YAAY,CAAC6E,WAAW,EAAE,CAACG,QAAQ,CAACnK,aAAa,CAACgK,WAAW,EAAE,CAAC,IACtE6B,MAAM,CAACxH,UAAU,IAAIwH,MAAM,CAACxH,UAAU,CAAC2F,WAAW,EAAE,CAACG,QAAQ,CAACnK,aAAa,CAACgK,WAAW,EAAE,CAAE,CAC7F;UACH;UAEA;UACA,MAAMQ,UAAU,GAAG,CAACa,SAAS,GAAG,CAAC,IAAI,IAAI,CAACvH,gBAAgB,CAACjD,QAAQ;UACnE,MAAM4J,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC1G,gBAAgB,CAACjD,QAAQ;UAC5D,MAAMiL,YAAY,GAAGN,UAAU,CAACd,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;UAE3D;UACA,IAAI,CAAClF,0BAA0B,GAAGuG,YAAY;UAC9C,IAAI,CAAChI,gBAAgB,CAAC9C,UAAU,GAAGwK,UAAU,CAAC9L,MAAM;UACpD,IAAI,CAACoE,gBAAgB,CAAC5C,UAAU,GAAGJ,IAAI,CAACuJ,IAAI,CAACmB,UAAU,CAAC9L,MAAM,GAAG,IAAI,CAACoE,gBAAgB,CAACjD,QAAQ,CAAC;UAChG,IAAI,CAACiD,gBAAgB,CAAClD,WAAW,GAAGyK,SAAS;QAC/C,CAAC,MAAM;UACL,IAAI,CAAC9F,0BAA0B,GAAG,EAAE;UACpC,IAAI,CAACzB,gBAAgB,CAAC9C,UAAU,GAAG,CAAC;UACpC,IAAI,CAAC8C,gBAAgB,CAAC5C,UAAU,GAAG,CAAC;UACpC,IAAI,CAAC4C,gBAAgB,CAAClD,WAAW,GAAG,CAAC;QACvC;MACF,CAAC;MACDiJ,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACtE,0BAA0B,GAAG,EAAE;QACpC,IAAI,CAACzB,gBAAgB,CAAC9C,UAAU,GAAG,CAAC;QACpC,IAAI,CAAC8C,gBAAgB,CAAC5C,UAAU,GAAG,CAAC;QACpC,IAAI,CAAC4C,gBAAgB,CAAClD,WAAW,GAAG,CAAC;MACvC;KACD,CAAC;EACJ;EAIA;EACAsG,qBAAqBA,CAAC6C,OAAe;IACnC,IAAI,CAAC/C,mBAAmB,GAAG+C,OAAO;IAClC,IAAI,IAAI,CAACnG,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACjC,UAAU,EAAE;MAC7D,IAAI,CAACuJ,mBAAmB,CAAC,IAAI,CAACtH,gBAAgB,CAACjC,UAAU,EAAE,CAAC,EAAEoI,OAAO,CAAC;IACxE;EACF;EAEA;EACA9F,iBAAiBA,CAAA;IACf,IAAI,CAAC+C,mBAAmB,GAAG,EAAE;IAC7B,IAAI,IAAI,CAACpD,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACjC,UAAU,EAAE;MAC7D,IAAI,CAACuJ,mBAAmB,CAAC,IAAI,CAACtH,gBAAgB,CAACjC,UAAU,EAAE,CAAC,CAAC;IAC/D;EACF;EAEA;EACAwJ,sBAAsBA,CAAA;IACpB;IACA,IAAI,IAAI,CAAC5F,0BAA0B,CAAC7F,MAAM,GAAG,CAAC,EAAE;MAC9C;IACF;IAEA;IACA,MAAMqM,OAAO,GAAG,IAAI,CAAChG,sBAAsB;IAC3C,IAAI,CAACjC,gBAAgB,CAAC9C,UAAU,GAAG+K,OAAO,CAACrM,MAAM;IACjD,IAAI,CAACoE,gBAAgB,CAAC5C,UAAU,GAAGJ,IAAI,CAACuJ,IAAI,CAAC,IAAI,CAACvG,gBAAgB,CAAC9C,UAAU,GAAG,IAAI,CAAC8C,gBAAgB,CAACjD,QAAQ,CAAC;IAC/G,IAAI,CAACiD,gBAAgB,CAAClD,WAAW,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,CAACoL,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMD,OAAO,GAAG,IAAI,CAAChG,sBAAsB;IAC3C,MAAMyE,UAAU,GAAG,CAAC,IAAI,CAAC1G,gBAAgB,CAAClD,WAAW,GAAG,CAAC,IAAI,IAAI,CAACkD,gBAAgB,CAACjD,QAAQ;IAC3F,MAAM4J,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC1G,gBAAgB,CAACjD,QAAQ;IAC5D,IAAI,CAACgF,gBAAgB,GAAGkG,OAAO,CAACrB,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC7D;EAEA;EACArE,cAAcA,CAACuE,IAAY;IACzB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC7G,gBAAgB,CAAC5C,UAAU,EAAE;MACzD;MACA,IAAI,IAAI,CAAC0C,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACjC,UAAU,IAAI,IAAI,CAAC4D,0BAA0B,CAAC7F,MAAM,GAAG,CAAC,EAAE;QAC3G,IAAI,CAACwL,mBAAmB,CAAC,IAAI,CAACtH,gBAAgB,CAACjC,UAAU,EAAEgJ,IAAI,EAAE,IAAI,CAAC3D,mBAAmB,CAAC;MAC5F,CAAC,MAAM;QACL;QACA,IAAI,CAAClD,gBAAgB,CAAClD,WAAW,GAAG+J,IAAI;QACxC,IAAI,CAACqB,sBAAsB,EAAE;MAC/B;IACF;EACF;EAEA;EACAvF,oBAAoBA,CAAA;IAClB,MAAMmE,KAAK,GAAa,EAAE;IAC1B,MAAM1J,UAAU,GAAG,IAAI,CAAC4C,gBAAgB,CAAC5C,UAAU;IACnD,MAAMN,WAAW,GAAG,IAAI,CAACkD,gBAAgB,CAAClD,WAAW;IAErD;IACA,MAAMiK,SAAS,GAAG/J,IAAI,CAACwJ,GAAG,CAAC,CAAC,EAAE1J,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMkK,OAAO,GAAGhK,IAAI,CAACC,GAAG,CAACG,UAAU,EAAEN,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAImK,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACAqB,OAAOA,CAAA;IACL,IAAI,CAAChE,KAAK,CAACgD,IAAI,EAAE;EACnB;EAEA;EACArJ,gBAAgBA,CAACsK,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACA,MAAMG,kBAAkB,GAAwB;MAC9C/C,WAAW,EAAE4C;KACd;IAED;IACA,IAAI,CAACpE,eAAe,CAACwE,kCAAkC,CAAC;MACtDxD,IAAI,EAAEuD;KACP,CAAC,CAACtD,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACA;UACA,IAAI,CAACb,aAAa,EAAE;UAEpB;UACA,IAAI,IAAI,CAACzE,gBAAgB,EAAEjC,UAAU,KAAKuK,UAAU,EAAE;YACpD,IAAI,CAACtI,gBAAgB,GAAG,IAAI;UAC9B;QACF,CAAC,MAAM;UACL;QAAA;MAEJ,CAAC;MACDiG,KAAK,EAAEA,CAAA,KAAK;QACV;MAAA;KAEH,CAAC;EACJ;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA;EACAjD,mBAAmBA,CAAA;IACjB,IAAI,CAAChD,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAImC,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACnC,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACuE,eAAe,CAAC8B,MAAM,CAACsC,CAAC,IAAIA,CAAC,CAAC5K,UAAU,KAAK,IAAI,CAACiC,gBAAiB,CAACjC,UAAU,CAAC;EAC7F;EAEA;EACAgC,iBAAiBA,CAAC6I,KAAa,EAAEtC,QAAkB;IACjD,OAAOA,QAAQ,CAACvI,UAAU,IAAI6K,KAAK;EACrC;;;uCAnaW5E,uBAAuB,EAAAlJ,EAAA,CAAA+N,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvB/E,uBAAuB;MAAAgF,SAAA;MAAAC,MAAA;QAAA9E,YAAA;MAAA;MAAA+E,OAAA;QAAA9E,cAAA;QAAAC,KAAA;MAAA;MAAA8E,UAAA;MAAAC,QAAA,GAAAtO,EAAA,CAAAuO,oBAAA,EAAAvO,EAAA,CAAAwO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAnD,QAAA,WAAAoD,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5B7O,EAJR,CAAAC,cAAA,iBAAsC,wBACW,aACkB,aACnC,YACP;UACfD,EAAA,CAAAU,SAAA,WAAoD;UAAAV,EAAA,CAAAY,MAAA,gCACtD;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACLX,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAY,MAAA,yEAAW;UACvCZ,EADuC,CAAAW,YAAA,EAAQ,EACzC;UAEJX,EADF,CAAAC,cAAA,aAA4B,gBACK;UAAAD,EAAA,CAAAY,MAAA,IAAuC;UAG5EZ,EAH4E,CAAAW,YAAA,EAAO,EACzE,EACF,EACS;UACjBX,EAAA,CAAAC,cAAA,wBAA2C;UAiKzCD,EA/JA,CAAA0B,UAAA,KAAAqN,uCAAA,kBAAsE,KAAAC,uCAAA,kBA8BP,KAAAC,uCAAA,oBAiIJ;UAqL7DjP,EAAA,CAAAW,YAAA,EAAe;UAGXX,EAFJ,CAAAC,cAAA,0BAA+C,eACjB,kBACoB;UAApBD,EAAA,CAAAE,UAAA,mBAAAgP,0DAAA;YAAA,OAASJ,GAAA,CAAAvB,OAAA,EAAS;UAAA,EAAC;UAC3CvN,EAAA,CAAAU,SAAA,aAAiC;UAAAV,EAAA,CAAAY,MAAA,qBACnC;UAGNZ,EAHM,CAAAW,YAAA,EAAS,EACL,EACS,EACT;;;UAlW6BX,EAAA,CAAAa,SAAA,IAAuC;UAAvCb,EAAA,CAAAc,kBAAA,KAAAgO,GAAA,CAAA7M,kBAAA,CAAAK,UAAA,wBAAuC;UAM7BtC,EAAA,CAAAa,SAAA,GAAuB;UAAvBb,EAAA,CAAA+B,UAAA,UAAA+M,GAAA,CAAA5J,gBAAA,CAAuB;UA8B9BlF,EAAA,CAAAa,SAAA,EAAuB;UAAvBb,EAAA,CAAA+B,UAAA,UAAA+M,GAAA,CAAA5J,gBAAA,CAAuB;UAiIvDlF,EAAA,CAAAa,SAAA,EAAsB;UAAtBb,EAAA,CAAA+B,UAAA,SAAA+M,GAAA,CAAA5J,gBAAA,CAAsB;;;qBDnKpBtF,YAAY,EAAAuP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAAH,EAAA,CAAAI,QAAA,EAAE1P,WAAW,EAAA2P,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAE7P,YAAY,EAAA8P,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAEjQ,cAAc;MAAAkQ,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}