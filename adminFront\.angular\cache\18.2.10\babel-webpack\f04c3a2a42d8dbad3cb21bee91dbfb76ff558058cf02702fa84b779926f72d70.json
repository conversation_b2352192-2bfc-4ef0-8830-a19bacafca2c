{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbIconModule } from '@nebular/theme';\nimport { StepStatus } from '../../interfaces/step-config.interface';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@nebular/theme\";\nconst _c0 = (a0, a1) => ({\n  \"completed\": a0,\n  \"active\": a1\n});\nfunction StepNavigatorComponent_div_2_nb_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 12);\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"icon\", step_r2.icon);\n  }\n}\nfunction StepNavigatorComponent_div_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", step_r2.stepNumber, \".\");\n  }\n}\nfunction StepNavigatorComponent_div_2_nb_icon_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 14);\n  }\n}\nfunction StepNavigatorComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 15);\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c0, step_r2.status === ctx_r2.StepStatus.COMPLETED, step_r2.status === ctx_r2.StepStatus.ACTIVE));\n  }\n}\nfunction StepNavigatorComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function StepNavigatorComponent_div_2_Template_div_click_0_listener() {\n      const step_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onStepClick(step_r2.stepNumber, step_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4);\n    i0.ɵɵtemplate(2, StepNavigatorComponent_div_2_nb_icon_2_Template, 1, 1, \"nb-icon\", 5);\n    i0.ɵɵelementStart(3, \"div\", 6)(4, \"span\", 7);\n    i0.ɵɵtemplate(5, StepNavigatorComponent_div_2_span_5_Template, 2, 1, \"span\", 8);\n    i0.ɵɵelementStart(6, \"span\", 9);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, StepNavigatorComponent_div_2_nb_icon_8_Template, 1, 0, \"nb-icon\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, StepNavigatorComponent_div_2_div_9_Template, 1, 4, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r2 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"clickable\", ctx_r2.isStepClickable(step_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStepClasses(step_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showStepNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r2.status === ctx_r2.StepStatus.COMPLETED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r4 < ctx_r2.processedSteps.length - 1);\n  }\n}\n/**\n * 步驟導航條共用元件\n * 支持自定義步驟配置和狀態管理\n */\nexport class StepNavigatorComponent {\n  constructor() {\n    /** 步驟配置數組 */\n    this.steps = [];\n    /** 當前步驟索引（1-based） */\n    this.currentStep = 1;\n    /** 是否允許點擊導航到其他步驟 */\n    this.allowClickNavigation = false;\n    /** 是否顯示步驟編號 */\n    this.showStepNumber = true;\n    /** 自定義CSS類名 */\n    this.customClass = '';\n    /** 步驟點擊事件 */\n    this.stepClick = new EventEmitter();\n    /** 暴露枚舉給模板使用 */\n    this.StepStatus = StepStatus;\n    /** 處理後的步驟數據 */\n    this.processedSteps = [];\n  }\n  ngOnChanges(changes) {\n    if (changes['steps'] || changes['currentStep']) {\n      this.processSteps();\n    }\n  }\n  /**\n   * 處理步驟數據，計算每個步驟的狀態\n   */\n  processSteps() {\n    this.processedSteps = this.steps.map((step, index) => {\n      const stepNumber = index + 1;\n      let status;\n      if (stepNumber < this.currentStep) {\n        status = StepStatus.COMPLETED;\n      } else if (stepNumber === this.currentStep) {\n        status = StepStatus.ACTIVE;\n      } else {\n        status = StepStatus.PENDING;\n      }\n      return {\n        ...step,\n        status,\n        stepNumber\n      };\n    });\n  }\n  /**\n   * 處理步驟點擊事件\n   * @param stepNumber 步驟編號（1-based）\n   * @param step 步驟配置\n   */\n  onStepClick(stepNumber, step) {\n    // 檢查是否允許點擊導航\n    if (!this.allowClickNavigation) {\n      return;\n    }\n    // 檢查步驟是否可點擊\n    if (step.clickable === false) {\n      return;\n    }\n    // 發出步驟點擊事件\n    this.stepClick.emit(stepNumber);\n  }\n  /**\n   * 獲取步驟的CSS類名\n   * @param step 處理後的步驟數據\n   * @returns CSS類名字符串\n   */\n  getStepClasses(step) {\n    const classes = ['step-item', step.status];\n    // 添加可點擊樣式\n    if (this.allowClickNavigation && step.clickable !== false) {\n      classes.push('clickable');\n    }\n    return classes.join(' ');\n  }\n  /**\n   * 檢查步驟是否可點擊\n   * @param step 步驟配置\n   * @returns 是否可點擊\n   */\n  isStepClickable(step) {\n    return this.allowClickNavigation && step.clickable !== false;\n  }\n  static {\n    this.ɵfac = function StepNavigatorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StepNavigatorComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StepNavigatorComponent,\n      selectors: [[\"app-step-navigator\"]],\n      inputs: {\n        steps: \"steps\",\n        currentStep: \"currentStep\",\n        allowClickNavigation: \"allowClickNavigation\",\n        showStepNumber: \"showStepNumber\",\n        customClass: \"customClass\"\n      },\n      outputs: {\n        stepClick: \"stepClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"step-navigator\", 3, \"ngClass\"], [1, \"step-nav\"], [\"class\", \"step-wrapper\", 3, \"clickable\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"step-wrapper\", 3, \"click\"], [3, \"ngClass\"], [\"class\", \"step-icon\", 3, \"icon\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"step-text\"], [\"class\", \"step-number\", 4, \"ngIf\"], [1, \"step-label\"], [\"icon\", \"checkmark-outline\", \"class\", \"step-check-icon\", 4, \"ngIf\"], [\"class\", \"step-connector\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"step-icon\", 3, \"icon\"], [1, \"step-number\"], [\"icon\", \"checkmark-outline\", 1, \"step-check-icon\"], [1, \"step-connector\", 3, \"ngClass\"]],\n      template: function StepNavigatorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, StepNavigatorComponent_div_2_Template, 10, 8, \"div\", 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.customClass);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.processedSteps);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, NbIconModule, i2.NbIconComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n.step-navigator[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  padding-bottom: 1rem;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper.clickable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper.clickable[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(184, 166, 118, 0.2);\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem 1.25rem;\\n  border-radius: 0.5rem;\\n  font-weight: 500;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  min-width: 140px;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  border: 2px solid transparent;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  flex-shrink: 0;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n  font-size: 0.95rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-check-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-left: 0.25rem;\\n  flex-shrink: 0;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n  border-color: #B8A676;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%], \\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%]   .step-check-icon[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background: #17A2B8;\\n  color: #FFFFFF;\\n  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.25);\\n  border-color: #17A2B8;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%], \\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   .step-check-icon[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #F8F9FA;\\n  color: #ADB5BD;\\n  border-color: #E9ECEF;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%] {\\n  color: #ADB5BD;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  border-color: rgba(184, 166, 118, 0.3);\\n  color: #5A5A5A;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 3px;\\n  background-color: #E9ECEF;\\n  margin: 0 0.5rem;\\n  transition: all 0.3s ease;\\n  border-radius: 1.5px;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%] {\\n  background: #17A2B8;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n}\\n\\n@media (max-width: 768px) {\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.6rem 1rem;\\n    min-width: 120px;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n    width: 2rem;\\n    margin: 0 0.25rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 240px;\\n    padding: 0.8rem 1.5rem;\\n  }\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  border-bottom-color: #404040;\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  color: #CCCCCC;\\n  border-color: #404040;\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%]:hover, .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%]:hover {\\n  background-color: #3a3a3a;\\n  border-color: #5a5a5a;\\n  color: #FFFFFF;\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  box-shadow: 0 8px 24px rgba(184, 166, 118, 0.25);\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  background-color: #404040;\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%] {\\n  background: #17A2B8;\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.active[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n}\\n\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse-active 2s infinite;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   .step-check-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_check-bounce 0.6s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse-active {\\n  0%, 100% {\\n    box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n  }\\n  50% {\\n    box-shadow: 0 4px 12px rgba(184, 166, 118, 0.2);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_check-bounce {\\n  0% {\\n    transform: scale(0);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInN0ZXAtbmF2aWdhdG9yLmNvbXBvbmVudC5zY3NzIiwiLi5cXC4uXFwuLlxcQHRoZW1lXFxzdHlsZXNcXF9jb2xvcnMuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxnQkFBZ0I7QUNBaEI7OztFQUFBO0FERUE7RUFDRSxXQUFBO0FBSUY7QUFGRTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQ0FBQTtFQUNBLG9CQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7QUFJSjtBQUZJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7QUFJTjtBQUZNO0VBQ0UsZUFBQTtBQUlSO0FBRFU7RUFDRSwyQkFBQTtFQUNBLCtDQytEQTtBRDVEWjtBQUVNO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0Esd0JBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0VBQ0EsaURBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7RUFDQSxXQUFBO0VBQ0EsNkJBQUE7QUFBUjtBQUVRO0VBQ0UsaUJBQUE7RUFDQSxjQUFBO0FBQVY7QUFHUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLE9BQUE7QUFEVjtBQUdVO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtBQURaO0FBR1k7RUFDRSxnQkFBQTtFQUNBLGlCQUFBO0FBRGQ7QUFJWTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7QUFGZDtBQU9RO0VBQ0UsaUJBQUE7RUFDQSxvQkFBQTtFQUNBLGNBQUE7QUFMVjtBQVNRO0VBQ0UsNkRDcERTO0VEcURULGNDekNHO0VEMENILCtDQ09FO0VETkYscUJDNUVXO0FEcUVyQjtBQVNVOztFQUVFLGNDL0NDO0FEd0NiO0FBV1E7RUFDRSxtQkNaRTtFRGFGLGNDckRHO0VEc0RILCtDQUFBO0VBQ0EscUJDZkU7QURNWjtBQVdVOztFQUVFLGNDM0RDO0FEa0RiO0FBYVE7RUFDRSx5QkMzREs7RUQ0REwsY0NuRUc7RURvRUgscUJDckRLO0FEMENmO0FBYVU7RUFDRSxjQ3ZFQztBRDREYjtBQWNVO0VBQ0UsMkNDaEVEO0VEaUVDLHNDQzFESztFRDJETCxjQy9FSztBRG1FakI7QUFpQk07RUFDRSxhQUFBO0VBQ0EsV0FBQTtFQUNBLHlCQ3RFTztFRHVFUCxnQkFBQTtFQUNBLHlCQUFBO0VBQ0Esb0JBQUE7QUFmUjtBQWlCUTtFQUNFLG1CQ2pERTtBRGtDWjtBQWtCUTtFQUNFLDZEQ3pHUztBRHlGbkI7O0FBd0JBO0VBSVE7SUFDRSxtQkFBQTtJQUNBLG9CQUFBO0lBQ0EsZ0JBQUE7RUF4QlI7RUE0Qlk7SUFDRSxrQkFBQTtFQTFCZDtFQWdDTTtJQUNFLFdBQUE7SUFDQSxpQkFBQTtFQTlCUjtBQUNGO0FBb0NBO0VBRUk7SUFDRSxzQkFBQTtJQUNBLFNBQUE7RUFuQ0o7RUFxQ0k7SUFDRSxXQUFBO0lBQ0EsdUJBQUE7RUFuQ047RUFxQ007SUFDRSxhQUFBO0VBbkNSO0VBc0NNO0lBQ0UsV0FBQTtJQUNBLGdCQUFBO0lBQ0Esc0JBQUE7RUFwQ1I7QUFDRjtBQTZDSTtFQUNFLDRCQ1hRO0FEaENkO0FBK0NVO0VBQ0UseUJDbkJRO0VEb0JSLGNDbEJVO0VEbUJWLHFCQ2xCRTtBRDNCZDtBQStDWTtFQUNFLHlCQUFBO0VBQ0EscUJBQUE7RUFDQSxjQ3pCTTtBRHBCcEI7QUFpRFU7RUFDRSxnREMxSEE7QUQyRVo7QUFrRFU7RUFDRSw4Q0FBQTtBQWhEWjtBQW9EUTtFQUNFLHlCQ3JDSTtBRGJkO0FBb0RVO0VBQ0UsbUJDakpBO0FEK0ZaO0FBcURVO0VBQ0UsNkRDek1PO0FEc0puQjs7QUFnRVE7RUFDRSxtQ0FBQTtBQTdEVjtBQWlFVTtFQUNFLHFDQUFBO0FBL0RaOztBQXVFQTtFQUVFO0lBRUUsK0NDM0tRO0VEcUdWO0VBeUVBO0lBQ0UsK0NDOUtRO0VEdUdWO0FBQ0Y7QUEwRUE7RUFDRTtJQUNFLG1CQUFBO0VBeEVGO0VBMkVBO0lBQ0UscUJBQUE7RUF6RUY7RUE0RUE7SUFDRSxtQkFBQTtFQTFFRjtBQUNGIiwiZmlsZSI6InN0ZXAtbmF2aWdhdG9yLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiQGltcG9ydCAnLi4vLi4vLi4vQHRoZW1lL3N0eWxlcy9jb2xvcnMnO1xuXG4uc3RlcC1uYXZpZ2F0b3Ige1xuICB3aWR0aDogMTAwJTtcblxuICAuc3RlcC1uYXYge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAkYm9yZGVyLWxpZ2h0O1xuICAgIHBhZGRpbmctYm90dG9tOiAxcmVtO1xuICAgIGZsZXgtd3JhcDogd3JhcDtcbiAgICBnYXA6IDAuNXJlbTtcblxuICAgIC5zdGVwLXdyYXBwZXIge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG5cbiAgICAgICYuY2xpY2thYmxlIHtcbiAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuXG4gICAgICAgIC5zdGVwLWl0ZW0ge1xuICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICAgICAgICAgICAgYm94LXNoYWRvdzogJHNoYWRvdy1sZztcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLnN0ZXAtaXRlbSB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMS4yNXJlbTtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMC41cmVtO1xuICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xuICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICAgIG1pbi13aWR0aDogMTQwcHg7XG4gICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICBnYXA6IDAuNXJlbTtcbiAgICAgICAgYm9yZGVyOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XG5cbiAgICAgICAgLnN0ZXAtaWNvbiB7XG4gICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICAgIH1cblxuICAgICAgICAuc3RlcC1jb250ZW50IHtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgZmxleDogMTtcblxuICAgICAgICAgIC5zdGVwLXRleHQge1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICBnYXA6IDAuMjVyZW07XG5cbiAgICAgICAgICAgIC5zdGVwLW51bWJlciB7XG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAuc3RlcC1sYWJlbCB7XG4gICAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAuc3RlcC1jaGVjay1pY29uIHtcbiAgICAgICAgICBmb250LXNpemU6IDEuMXJlbTtcbiAgICAgICAgICBtYXJnaW4tbGVmdDogMC4yNXJlbTtcbiAgICAgICAgICBmbGV4LXNocmluazogMDtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOeLgOaFi+aoo+W8j1xuICAgICAgICAmLmFjdGl2ZSB7XG4gICAgICAgICAgYmFja2dyb3VuZDogJGdyYWRpZW50LXByaW1hcnk7XG4gICAgICAgICAgY29sb3I6ICR0ZXh0LWxpZ2h0O1xuICAgICAgICAgIGJveC1zaGFkb3c6ICRzaGFkb3ctbWQ7XG4gICAgICAgICAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeS1nb2xkLWxpZ2h0O1xuXG4gICAgICAgICAgLnN0ZXAtaWNvbixcbiAgICAgICAgICAuc3RlcC1jaGVjay1pY29uIHtcbiAgICAgICAgICAgIGNvbG9yOiAkdGV4dC1saWdodDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAmLmNvbXBsZXRlZCB7XG4gICAgICAgICAgYmFja2dyb3VuZDogJGluZm8tYmFzZTtcbiAgICAgICAgICBjb2xvcjogJHRleHQtbGlnaHQ7XG4gICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDIzLCAxNjIsIDE4NCwgMC4yNSk7XG4gICAgICAgICAgYm9yZGVyLWNvbG9yOiAkaW5mby1iYXNlO1xuXG4gICAgICAgICAgLnN0ZXAtaWNvbixcbiAgICAgICAgICAuc3RlcC1jaGVjay1pY29uIHtcbiAgICAgICAgICAgIGNvbG9yOiAkdGV4dC1saWdodDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAmLnBlbmRpbmcge1xuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICRiZy1zZWNvbmRhcnk7XG4gICAgICAgICAgY29sb3I6ICR0ZXh0LW11dGVkO1xuICAgICAgICAgIGJvcmRlci1jb2xvcjogJGJvcmRlci1saWdodDtcblxuICAgICAgICAgIC5zdGVwLWljb24ge1xuICAgICAgICAgICAgY29sb3I6ICR0ZXh0LW11dGVkO1xuICAgICAgICAgIH1cblxuICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJGJnLWhvdmVyO1xuICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAkYm9yZGVyLXByaW1hcnk7XG4gICAgICAgICAgICBjb2xvcjogJHRleHQtc2Vjb25kYXJ5O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAuc3RlcC1jb25uZWN0b3Ige1xuICAgICAgICB3aWR0aDogMi41cmVtO1xuICAgICAgICBoZWlnaHQ6IDNweDtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJGJvcmRlci1saWdodDtcbiAgICAgICAgbWFyZ2luOiAwIDAuNXJlbTtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMS41cHg7XG5cbiAgICAgICAgJi5jb21wbGV0ZWQge1xuICAgICAgICAgIGJhY2tncm91bmQ6ICRpbmZvLWJhc2U7XG4gICAgICAgIH1cblxuICAgICAgICAmLmFjdGl2ZSB7XG4gICAgICAgICAgYmFja2dyb3VuZDogJGdyYWRpZW50LXByaW1hcnk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8g6Z+/5oeJ5byP6Kit6KiIXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLnN0ZXAtbmF2aWdhdG9yIHtcbiAgICAuc3RlcC1uYXYge1xuICAgICAgLnN0ZXAtd3JhcHBlciB7XG4gICAgICAgIC5zdGVwLWl0ZW0ge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gICAgICAgICAgcGFkZGluZzogMC42cmVtIDFyZW07XG4gICAgICAgICAgbWluLXdpZHRoOiAxMjBweDtcblxuICAgICAgICAgIC5zdGVwLWNvbnRlbnQge1xuICAgICAgICAgICAgLnN0ZXAtdGV4dCB7XG4gICAgICAgICAgICAgIC5zdGVwLWxhYmVsIHtcbiAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODVyZW07XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAuc3RlcC1jb25uZWN0b3Ige1xuICAgICAgICAgIHdpZHRoOiAycmVtO1xuICAgICAgICAgIG1hcmdpbjogMCAwLjI1cmVtO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xuICAuc3RlcC1uYXZpZ2F0b3Ige1xuICAgIC5zdGVwLW5hdiB7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgZ2FwOiAxcmVtO1xuXG4gICAgICAuc3RlcC13cmFwcGVyIHtcbiAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuXG4gICAgICAgIC5zdGVwLWNvbm5lY3RvciB7XG4gICAgICAgICAgZGlzcGxheTogbm9uZTtcbiAgICAgICAgfVxuXG4gICAgICAgIC5zdGVwLWl0ZW0ge1xuICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgIG1heC13aWR0aDogMjQwcHg7XG4gICAgICAgICAgcGFkZGluZzogMC44cmVtIDEuNXJlbTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyDmt7HoibLkuLvpoYzmlK/mjIFcbjpob3N0LWNvbnRleHQoLmRhcmstdGhlbWUpIHtcbiAgLnN0ZXAtbmF2aWdhdG9yIHtcbiAgICAuc3RlcC1uYXYge1xuICAgICAgYm9yZGVyLWJvdHRvbS1jb2xvcjogJGRhcmstYm9yZGVyO1xuXG4gICAgICAuc3RlcC13cmFwcGVyIHtcbiAgICAgICAgLnN0ZXAtaXRlbSB7XG4gICAgICAgICAgJi5wZW5kaW5nIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICRkYXJrLWJnLXNlY29uZGFyeTtcbiAgICAgICAgICAgIGNvbG9yOiAkZGFyay10ZXh0LXNlY29uZGFyeTtcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogJGRhcmstYm9yZGVyO1xuXG4gICAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogbGlnaHRlbigkZGFyay1iZy1zZWNvbmRhcnksIDUlKTtcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiBsaWdodGVuKCRkYXJrLWJvcmRlciwgMTAlKTtcbiAgICAgICAgICAgICAgY29sb3I6ICRkYXJrLXRleHQtcHJpbWFyeTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAmLmFjdGl2ZSB7XG4gICAgICAgICAgICBib3gtc2hhZG93OiAkc2hhZG93LXhsO1xuICAgICAgICAgIH1cblxuICAgICAgICAgICYuY29tcGxldGVkIHtcbiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgyMywgMTYyLCAxODQsIDAuNCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLnN0ZXAtY29ubmVjdG9yIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkZGFyay1ib3JkZXI7XG5cbiAgICAgICAgICAmLmNvbXBsZXRlZCB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAkaW5mby1iYXNlO1xuICAgICAgICAgIH1cblxuICAgICAgICAgICYuYWN0aXZlIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICRncmFkaWVudC1wcmltYXJ5O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyDli5XnlavmlYjmnpzlop7lvLdcbi5zdGVwLW5hdmlnYXRvciB7XG4gIC5zdGVwLW5hdiB7XG4gICAgLnN0ZXAtd3JhcHBlciB7XG4gICAgICAuc3RlcC1pdGVtIHtcbiAgICAgICAgJi5hY3RpdmUge1xuICAgICAgICAgIGFuaW1hdGlvbjogcHVsc2UtYWN0aXZlIDJzIGluZmluaXRlO1xuICAgICAgICB9XG5cbiAgICAgICAgJi5jb21wbGV0ZWQge1xuICAgICAgICAgIC5zdGVwLWNoZWNrLWljb24ge1xuICAgICAgICAgICAgYW5pbWF0aW9uOiBjaGVjay1ib3VuY2UgMC42cyBlYXNlLW91dDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuQGtleWZyYW1lcyBwdWxzZS1hY3RpdmUge1xuXG4gIDAlLFxuICAxMDAlIHtcbiAgICBib3gtc2hhZG93OiAkc2hhZG93LW1kO1xuICB9XG5cbiAgNTAlIHtcbiAgICBib3gtc2hhZG93OiAkc2hhZG93LWxnO1xuICB9XG59XG5cbkBrZXlmcmFtZXMgY2hlY2stYm91bmNlIHtcbiAgMCUge1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMCk7XG4gIH1cblxuICA1MCUge1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMS4yKTtcbiAgfVxuXG4gIDEwMCUge1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMSk7XG4gIH1cbn1cbiIsIi8qKlxyXG4gKiDntbHkuIDoibLlvanns7vntbEgLSDph5HoibLkuLvpoYxcclxuICog5Z+65pa85Li76Imy57O7ICNCOEE2NzYg6Kit6KiI55qE5a6M5pW06Imy5b2p6auU57O7XHJcbiAqL1xyXG5cclxuLy8gPT09PT0g5Li76KaB5ZOB54mM6Imy5b2pID09PT09XHJcbiRwcmltYXJ5LWdvbGQtbGlnaHQ6ICNCOEE2NzY7IC8vIOS4u+imgemHkeiJsiAtIOa3uuiJslxyXG4kcHJpbWFyeS1nb2xkLWJhc2U6ICNBRTlCNjY7IC8vIOS4u+imgemHkeiJsiAtIOWfuuekjuiJslxyXG4kcHJpbWFyeS1nb2xkLWRhcms6ICNBNjk2NjA7IC8vIOS4u+imgemHkeiJsiAtIOa3seiJslxyXG4kcHJpbWFyeS1nb2xkLWRhcmtlcjogIzlCOEE1QTsgLy8g5Li76KaB6YeR6ImyIC0g5pu05rexXHJcbiRwcmltYXJ5LWdvbGQtaG92ZXI6ICNDNEIzODI7IC8vIOaHuOWBnOeLgOaFi1xyXG4kcHJpbWFyeS1nb2xkLWFjdGl2ZTogI0E4OTY2MDsgLy8g5rS75YuV54uA5oWLXHJcbiRwcmltYXJ5LWdvbGQtZGlzYWJsZWQ6ICNENEM4QTg7IC8vIOemgeeUqOeLgOaFi1xyXG5cclxuLy8gPT09PT0g6LyU5Yqp6YeR6Imy6Kq/6Imy5p2/ID09PT09XHJcbiRnb2xkLTUwOiAjRkVGQ0Y4OyAvLyDmpbXmt6Hph5HoibLog4zmma9cclxuJGdvbGQtMTAwOiAjRjhGNkYwOyAvLyDmt6Hph5HoibLog4zmma9cclxuJGdvbGQtMjAwOiAjRjBFREU1OyAvLyDmt7rph5HoibLog4zmma9cclxuJGdvbGQtMzAwOiAjRThFMkQ1OyAvLyDkuK3mt7rph5HoibJcclxuJGdvbGQtNDAwOiAjRDRDOEE4OyAvLyDkuK3ph5HoibJcclxuJGdvbGQtNTAwOiAjQjhBNjc2OyAvLyDkuLvph5HoibJcclxuJGdvbGQtNjAwOiAjQUU5QjY2OyAvLyDmt7Hph5HoibJcclxuJGdvbGQtNzAwOiAjOUI4QTVBOyAvLyDmm7Tmt7Hph5HoibJcclxuJGdvbGQtODAwOiAjOEE3QTRGOyAvLyDmmpfph5HoibJcclxuJGdvbGQtOTAwOiAjNkI1RjNFOyAvLyDmnIDmt7Hph5HoibJcclxuXHJcbi8vID09PT09IOa8uOiuiuiJsuW9qSA9PT09PVxyXG4kZ3JhZGllbnQtcHJpbWFyeTogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJHByaW1hcnktZ29sZC1saWdodCAwJSwgJHByaW1hcnktZ29sZC1kYXJrIDEwMCUpO1xyXG4kZ3JhZGllbnQtcHJpbWFyeS1ob3ZlcjogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJHByaW1hcnktZ29sZC1ob3ZlciAwJSwgJHByaW1hcnktZ29sZC1hY3RpdmUgMTAwJSk7XHJcbiRncmFkaWVudC1wcmltYXJ5LWxpZ2h0OiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAkZ29sZC0yMDAgMCUsICRnb2xkLTMwMCAxMDAlKTtcclxuJGdyYWRpZW50LXNlY29uZGFyeTogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJGdvbGQtMTAwIDAlLCAkZ29sZC0yMDAgMTAwJSk7XHJcbiRncmFkaWVudC1iYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gYm90dG9tLCAkZ29sZC01MCAwJSwgJGdvbGQtMTAwIDEwMCUpO1xyXG5cclxuLy8gPT09PT0g5paH5a2X6Imy5b2pID09PT09XHJcbiR0ZXh0LXByaW1hcnk6ICMyQzNFNTA7IC8vIOS4u+imgeaWh+Wtl1xyXG4kdGV4dC1zZWNvbmRhcnk6ICM1QTVBNUE7IC8vIOasoeimgeaWh+Wtl1xyXG4kdGV4dC10ZXJ0aWFyeTogIzZDNzU3RDsgLy8g6LyU5Yqp5paH5a2XXHJcbiR0ZXh0LW11dGVkOiAjQURCNUJEOyAvLyDpnZzpn7PmloflrZdcclxuJHRleHQtZGlzYWJsZWQ6ICNDRUQ0REE7IC8vIOemgeeUqOaWh+Wtl1xyXG4kdGV4dC1saWdodDogI0ZGRkZGRjsgLy8g5re66Imy5paH5a2XXHJcbiR0ZXh0LWRhcms6ICMyMTI1Mjk7IC8vIOa3seiJsuaWh+Wtl1xyXG5cclxuLy8gPT09PT0g6IOM5pmv6Imy5b2pID09PT09XHJcbiRiZy1wcmltYXJ5OiAjRkZGRkZGOyAvLyDkuLvopoHog4zmma9cclxuJGJnLXNlY29uZGFyeTogI0Y4RjlGQTsgLy8g5qyh6KaB6IOM5pmvXHJcbiRiZy10ZXJ0aWFyeTogI0Y1RjVGNTsgLy8g56ys5LiJ6IOM5pmvXHJcbiRiZy1jcmVhbTogI0ZFRkNGODsgLy8g5aW25rK56Imy6IOM5pmvXHJcbiRiZy1saWdodC1nb2xkOiByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMDMpOyAvLyDmpbXmt6Hph5HoibLog4zmma9cclxuJGJnLWhvdmVyOiByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMDUpOyAvLyDmh7jlgZzog4zmma9cclxuJGJnLXNlbGVjdGVkOiByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMTUpOyAvLyDpgbjkuK3og4zmma9cclxuXHJcbi8vID09PT09IOmCiuahhuiJsuW9qSA9PT09PVxyXG4kYm9yZGVyLWxpZ2h0OiAjRTlFQ0VGOyAvLyDmt7roibLpgormoYZcclxuJGJvcmRlci1tZWRpdW06ICNDRENEQ0Q7IC8vIOS4reetiemCiuahhlxyXG4kYm9yZGVyLWRhcms6ICNBREI1QkQ7IC8vIOa3seiJsumCiuahhlxyXG4kYm9yZGVyLXByaW1hcnk6IHJnYmEoMTg0LCAxNjYsIDExOCwgMC4zKTsgLy8g5Li76Imy6YKK5qGGXHJcbiRib3JkZXItZm9jdXM6IHJnYmEoMTg0LCAxNjYsIDExOCwgMC41KTsgLy8g54Sm6bue6YKK5qGGXHJcblxyXG4vLyA9PT09PSDni4DmhYvoibLlvakgPT09PT1cclxuLy8g5oiQ5Yqf54uA5oWLXHJcbiRzdWNjZXNzLWxpZ2h0OiAjRDRFRERBO1xyXG4kc3VjY2Vzcy1iYXNlOiAjMjhBNzQ1O1xyXG4kc3VjY2Vzcy1kYXJrOiAjMUU3RTM0O1xyXG4kc3VjY2Vzcy1ncmFkaWVudDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI0U4RjVFOCAwJSwgI0Q0RUREQSAxMDAlKTtcclxuXHJcbi8vIOitpuWRiueLgOaFi1xyXG4kd2FybmluZy1saWdodDogI0ZGRjNDRDtcclxuJHdhcm5pbmctYmFzZTogI0ZGQzEwNztcclxuJHdhcm5pbmctZGFyazogI0UwQTgwMDtcclxuJHdhcm5pbmctZ3JhZGllbnQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNGRkY4RTEgMCUsICNGRkYzQ0QgMTAwJSk7XHJcblxyXG4vLyDpjK/oqqTni4DmhYtcclxuJGVycm9yLWxpZ2h0OiAjRjhEN0RBO1xyXG4kZXJyb3ItYmFzZTogI0RDMzU0NTtcclxuJGVycm9yLWRhcms6ICNDODIzMzM7XHJcbiRlcnJvci1ncmFkaWVudDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI0ZGRUJFRSAwJSwgI0Y4RDdEQSAxMDAlKTtcclxuXHJcbi8vIOizh+ioiueLgOaFi1xyXG4kaW5mby1saWdodDogI0QxRUNGMTtcclxuJGluZm8tYmFzZTogIzE3QTJCODtcclxuJGluZm8tZGFyazogIzEzODQ5NjtcclxuJGluZm8tZ3JhZGllbnQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNFM0YyRkQgMCUsICNEMUVDRjEgMTAwJSk7XHJcblxyXG4vLyDpoY3lpJbmvLjororoibLlvanvvIjlnKjni4DmhYvoibLlvanlrprnvqnkuYvlvozvvIlcclxuJGdyYWRpZW50LXN1Y2Nlc3M6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICRzdWNjZXNzLWxpZ2h0IDAlLCAkc3VjY2Vzcy1iYXNlIDEwMCUpO1xyXG5cclxuLy8gPT09PT0g6Zmw5b2x57O757WxID09PT09XHJcbiRzaGFkb3ctc206IDAgMXB4IDNweCByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMSk7XHJcbiRzaGFkb3ctbWQ6IDAgMnB4IDhweCByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMTUpO1xyXG4kc2hhZG93LWxnOiAwIDRweCAxMnB4IHJnYmEoMTg0LCAxNjYsIDExOCwgMC4yKTtcclxuJHNoYWRvdy14bDogMCA4cHggMjRweCByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMjUpO1xyXG5cclxuLy8g54m55q6K6Zmw5b2xXHJcbiRzaGFkb3ctZm9jdXM6IDAgMCAwIDAuMnJlbSByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMjUpO1xyXG4kc2hhZG93LWluc2V0OiBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKSwgaW5zZXQgMCAtMXB4IDAgcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG5cclxuLy8gPT09PT0g5YuV55Wr5ZKM6YGO5rihID09PT09XHJcbiR0cmFuc2l0aW9uLWZhc3Q6IDAuMTVzIGVhc2U7XHJcbiR0cmFuc2l0aW9uLW5vcm1hbDogMC4zcyBlYXNlO1xyXG4kdHJhbnNpdGlvbi1zbG93OiAwLjVzIGVhc2U7XHJcbiR0cmFuc2l0aW9uLWJvdW5jZTogMC4zcyBjdWJpYy1iZXppZXIoMC42OCwgLTAuNTUsIDAuMjY1LCAxLjU1KTtcclxuXHJcbi8vID09PT09IOmCiuahhuWck+inkuezu+e1sSA9PT09PVxyXG4kYm9yZGVyLXJhZGl1cy1zbTogMC4yNXJlbTsgLy8g5bCP5ZyT6KeSXHJcbiRib3JkZXItcmFkaXVzLW1kOiAwLjM3NXJlbTsgLy8g5Lit562J5ZyT6KeSXHJcbiRib3JkZXItcmFkaXVzLWxnOiAwLjVyZW07IC8vIOWkp+Wck+inklxyXG4kYm9yZGVyLXJhZGl1cy14bDogMC43NXJlbTsgLy8g54m55aSn5ZyT6KeSXHJcbiRib3JkZXItcmFkaXVzLTJ4bDogMXJlbTsgLy8g6LaF5aSn5ZyT6KeSXHJcbiRib3JkZXItcmFkaXVzLWZ1bGw6IDUwJTsgLy8g5a6M5YWo5ZyT5b2iXHJcblxyXG4vLyA9PT09PSDmloflrZfpmbDlvbHns7vntbEgPT09PT1cclxuJHRleHQtc2hhZG93LXNtOiAwIDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4kdGV4dC1zaGFkb3ctbWQ6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xyXG4kdGV4dC1zaGFkb3ctbGc6IDAgNHB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMik7XHJcblxyXG4vLyA9PT09PSBOZWJ1bGFyIOS4u+mhjOiJsuW9qeaYoOWwhCA9PT09PVxyXG4vLyDkuLvopoHoibLlvalcclxuJG5iLXByaW1hcnk6ICRwcmltYXJ5LWdvbGQtbGlnaHQ7XHJcbiRuYi1wcmltYXJ5LWxpZ2h0OiAkcHJpbWFyeS1nb2xkLWhvdmVyO1xyXG4kbmItcHJpbWFyeS1kYXJrOiAkcHJpbWFyeS1nb2xkLWRhcms7XHJcblxyXG4vLyDmrKHopoHoibLlvalcclxuJG5iLXN1Y2Nlc3M6ICRzdWNjZXNzLWJhc2U7XHJcbiRuYi1zdWNjZXNzLWxpZ2h0OiAkc3VjY2Vzcy1saWdodDtcclxuJG5iLXN1Y2Nlc3MtZGFyazogJHN1Y2Nlc3MtZGFyaztcclxuXHJcbiRuYi13YXJuaW5nOiAkd2FybmluZy1iYXNlO1xyXG4kbmItd2FybmluZy1saWdodDogJHdhcm5pbmctbGlnaHQ7XHJcbiRuYi13YXJuaW5nLWRhcms6ICR3YXJuaW5nLWRhcms7XHJcblxyXG4kbmItZGFuZ2VyOiAkZXJyb3ItYmFzZTtcclxuJG5iLWRhbmdlci1saWdodDogJGVycm9yLWxpZ2h0O1xyXG4kbmItZGFuZ2VyLWRhcms6ICRlcnJvci1kYXJrO1xyXG5cclxuJG5iLWluZm86ICRpbmZvLWJhc2U7XHJcbiRuYi1pbmZvLWxpZ2h0OiAkaW5mby1saWdodDtcclxuJG5iLWluZm8tZGFyazogJGluZm8tZGFyaztcclxuXHJcbi8vIOiDjOaZr+WSjOaWh+Wtl1xyXG4kbmItYmctcHJpbWFyeTogJGJnLXByaW1hcnk7XHJcbiRuYi1iZy1zZWNvbmRhcnk6ICRiZy1zZWNvbmRhcnk7XHJcbiRuYi10ZXh0LXByaW1hcnk6ICR0ZXh0LXByaW1hcnk7XHJcbiRuYi10ZXh0LXNlY29uZGFyeTogJHRleHQtc2Vjb25kYXJ5O1xyXG4kbmItdGV4dC1oaW50OiAkdGV4dC1tdXRlZDtcclxuXHJcbi8vIOmCiuahhlxyXG4kbmItYm9yZGVyLWJhc2ljOiAkYm9yZGVyLWxpZ2h0O1xyXG4kbmItYm9yZGVyLWFsdGVybmF0aXZlOiAkYm9yZGVyLW1lZGl1bTtcclxuXHJcbi8vID09PT09IOeJueauiueUqOmAlOiJsuW9qSA9PT09PVxyXG4vLyBSYWRpbyBCdXR0b24g5bCI55SoXHJcbiRyYWRpby1iZy1ob3ZlcjogcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCBjZW50ZXIsIHJnYmEoMTg0LCAxNjYsIDExOCwgMC4xKSAwJSwgdHJhbnNwYXJlbnQgNzAlKTtcclxuJHJhZGlvLWJnLXNlbGVjdGVkOiAkZ3JhZGllbnQtcHJpbWFyeTtcclxuJHJhZGlvLWJnLXNlbGVjdGVkLWhvdmVyOiAkZ3JhZGllbnQtcHJpbWFyeS1ob3ZlcjtcclxuJHJhZGlvLWlubmVyLWRvdDogcmFkaWFsLWdyYWRpZW50KGNpcmNsZSwgJHRleHQtbGlnaHQgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KSAxMDAlKTtcclxuXHJcbi8vIOihqOagvOWwiOeUqFxyXG4kdGFibGUtaGVhZGVyLWJnOiAkZ3JhZGllbnQtcHJpbWFyeS1saWdodDtcclxuJHRhYmxlLXJvdy1ob3ZlcjogJGJnLWhvdmVyO1xyXG4kdGFibGUtcm93LXNlbGVjdGVkOiAkYmctc2VsZWN0ZWQ7XHJcbiR0YWJsZS1ib3JkZXI6ICRib3JkZXItcHJpbWFyeTtcclxuXHJcbi8vIOWNoeeJh+WwiOeUqFxyXG4kY2FyZC1iZzogJGJnLXByaW1hcnk7XHJcbiRjYXJkLWhlYWRlci1iZzogJGdyYWRpZW50LXByaW1hcnk7XHJcbiRjYXJkLWJvcmRlcjogJGJvcmRlci1wcmltYXJ5O1xyXG4kY2FyZC1zaGFkb3c6ICRzaGFkb3ctbWQ7XHJcblxyXG4vLyDmjInpiJXlsIjnlKhcclxuJGJ0bi1wcmltYXJ5LWJnOiAkZ3JhZGllbnQtcHJpbWFyeTtcclxuJGJ0bi1wcmltYXJ5LWhvdmVyOiAkZ3JhZGllbnQtcHJpbWFyeS1ob3ZlcjtcclxuJGJ0bi1zZWNvbmRhcnktYmc6ICRiZy1zZWNvbmRhcnk7XHJcbiRidG4tc2Vjb25kYXJ5LWhvdmVyOiAkYmctaG92ZXI7XHJcblxyXG4vLyA9PT09PSDpn7/mh4nlvI/mlrfpu57oibLlvanoqr/mlbQgPT09PT1cclxuLy8g5Zyo5bCP6J6i5bmV5LiK5L2/55So5pu05p+U5ZKM55qE6Imy5b2pXHJcbiRtb2JpbGUtcHJpbWFyeTogbGlnaHRlbigkcHJpbWFyeS1nb2xkLWxpZ2h0LCA1JSk7XHJcbiRtb2JpbGUtc2hhZG93OiByZ2JhKDE4NCwgMTY2LCAxMTgsIDAuMDgpO1xyXG5cclxuLy8gPT09PT0g5rex6Imy5Li76aGM5pSv5o+0ID09PT09XHJcbiRkYXJrLWJnLXByaW1hcnk6ICMxQTFBMUE7XHJcbiRkYXJrLWJnLXNlY29uZGFyeTogIzJEMkQyRDtcclxuJGRhcmstdGV4dC1wcmltYXJ5OiAjRkZGRkZGO1xyXG4kZGFyay10ZXh0LXNlY29uZGFyeTogI0NDQ0NDQztcclxuJGRhcmstYm9yZGVyOiAjNDA0MDQwO1xyXG5cclxuLy8gPT09PT0g6LyU5Yqp5Ye95pW46Imy5b2pID09PT09XHJcbi8vIOmAj+aYjuW6puiuiuWMllxyXG5AZnVuY3Rpb24gYWxwaGEtZ29sZCgkYWxwaGEpIHtcclxuICBAcmV0dXJuIHJnYmEoMTg0LCAxNjYsIDExOCwgJGFscGhhKTtcclxufVxyXG5cclxuLy8g5Lqu5bqm6Kq/5pW0XHJcbkBmdW5jdGlvbiBsaWdodGVuLWdvbGQoJGFtb3VudCkge1xyXG4gIEByZXR1cm4gbGlnaHRlbigkcHJpbWFyeS1nb2xkLWxpZ2h0LCAkYW1vdW50KTtcclxufVxyXG5cclxuQGZ1bmN0aW9uIGRhcmtlbi1nb2xkKCRhbW91bnQpIHtcclxuICBAcmV0dXJuIGRhcmtlbigkcHJpbWFyeS1nb2xkLWxpZ2h0LCAkYW1vdW50KTtcclxufVxyXG5cclxuLy8gPT09PT0g6Imy5b2p6amX6K2JID09PT09XHJcbi8vIOeiuuS/neiJsuW9qeWwjeavlOW6puespuWQiCBXQ0FHIOaomea6llxyXG4kY29udHJhc3QtcmF0aW8tYWE6IDQuNTtcclxuJGNvbnRyYXN0LXJhdGlvLWFhYTogNztcclxuXHJcbi8vID09PT09IOiIiueJiOebuOWuueaApyA9PT09PVxyXG4vLyDkv53mjIHlkJHlvoznm7jlrrnmgKfnmoTorormlbjmmKDlsIRcclxuJG1haW5Db2xvckI6ICRwcmltYXJ5LWdvbGQtbGlnaHQ7XHJcbiRtYWluQ29sb3JHOiAkc3VjY2Vzcy1iYXNlO1xyXG4kbWFpbkNvbG9yR3JheTogJHRleHQtc2Vjb25kYXJ5O1xyXG4kdGV4dENvbG9yOiAkdGV4dC1wcmltYXJ5O1xyXG4kbWFpbkNvbG9yWTogJHdhcm5pbmctYmFzZTtcclxuJGNvbG9yLWRyb3A6ICRzaGFkb3ctbWQ7Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "NbIconModule", "StepStatus", "i0", "ɵɵelement", "ɵɵproperty", "step_r2", "icon", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpureFunction2", "_c0", "status", "ctx_r2", "COMPLETED", "ACTIVE", "ɵɵlistener", "StepNavigatorComponent_div_2_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onStepClick", "ɵɵtemplate", "StepNavigatorComponent_div_2_nb_icon_2_Template", "StepNavigatorComponent_div_2_span_5_Template", "StepNavigatorComponent_div_2_nb_icon_8_Template", "StepNavigatorComponent_div_2_div_9_Template", "ɵɵclassProp", "isStepClickable", "getStepClasses", "showStepNumber", "ɵɵtextInterpolate", "label", "i_r4", "processedSteps", "length", "StepNavigatorComponent", "constructor", "steps", "currentStep", "allowClickNavigation", "customClass", "step<PERSON>lick", "ngOnChanges", "changes", "processSteps", "map", "step", "index", "PENDING", "clickable", "emit", "classes", "push", "join", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "StepNavigatorComponent_Template", "rf", "ctx", "StepNavigatorComponent_div_2_Template", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "NbIconComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\step-navigator\\step-navigator.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\step-navigator\\step-navigator.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbIconModule } from '@nebular/theme';\nimport { StepConfig, StepStatus } from '../../interfaces/step-config.interface';\n\n/**\n * 步驟導航條共用元件\n * 支持自定義步驟配置和狀態管理\n */\n@Component({\n  selector: 'app-step-navigator',\n  standalone: true,\n  imports: [\n    CommonModule,\n    NbIconModule\n  ],\n  templateUrl: './step-navigator.component.html',\n  styleUrls: ['./step-navigator.component.scss']\n})\nexport class StepNavigatorComponent implements OnChanges {\n  /** 步驟配置數組 */\n  @Input() steps: StepConfig[] = [];\n  \n  /** 當前步驟索引（1-based） */\n  @Input() currentStep: number = 1;\n  \n  /** 是否允許點擊導航到其他步驟 */\n  @Input() allowClickNavigation: boolean = false;\n  \n  /** 是否顯示步驟編號 */\n  @Input() showStepNumber: boolean = true;\n  \n  /** 自定義CSS類名 */\n  @Input() customClass: string = '';\n  \n  /** 步驟點擊事件 */\n  @Output() stepClick = new EventEmitter<number>();\n  \n  /** 暴露枚舉給模板使用 */\n  StepStatus = StepStatus;\n  \n  /** 處理後的步驟數據 */\n  processedSteps: Array<StepConfig & { status: StepStatus; stepNumber: number }> = [];\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes['steps'] || changes['currentStep']) {\n      this.processSteps();\n    }\n  }\n\n  /**\n   * 處理步驟數據，計算每個步驟的狀態\n   */\n  private processSteps(): void {\n    this.processedSteps = this.steps.map((step, index) => {\n      const stepNumber = index + 1;\n      let status: StepStatus;\n      \n      if (stepNumber < this.currentStep) {\n        status = StepStatus.COMPLETED;\n      } else if (stepNumber === this.currentStep) {\n        status = StepStatus.ACTIVE;\n      } else {\n        status = StepStatus.PENDING;\n      }\n      \n      return {\n        ...step,\n        status,\n        stepNumber\n      };\n    });\n  }\n\n  /**\n   * 處理步驟點擊事件\n   * @param stepNumber 步驟編號（1-based）\n   * @param step 步驟配置\n   */\n  onStepClick(stepNumber: number, step: StepConfig): void {\n    // 檢查是否允許點擊導航\n    if (!this.allowClickNavigation) {\n      return;\n    }\n    \n    // 檢查步驟是否可點擊\n    if (step.clickable === false) {\n      return;\n    }\n    \n    // 發出步驟點擊事件\n    this.stepClick.emit(stepNumber);\n  }\n\n  /**\n   * 獲取步驟的CSS類名\n   * @param step 處理後的步驟數據\n   * @returns CSS類名字符串\n   */\n  getStepClasses(step: StepConfig & { status: StepStatus; stepNumber: number }): string {\n    const classes = ['step-item', step.status];\n    \n    // 添加可點擊樣式\n    if (this.allowClickNavigation && step.clickable !== false) {\n      classes.push('clickable');\n    }\n    \n    return classes.join(' ');\n  }\n\n  /**\n   * 檢查步驟是否可點擊\n   * @param step 步驟配置\n   * @returns 是否可點擊\n   */\n  isStepClickable(step: StepConfig): boolean {\n    return this.allowClickNavigation && step.clickable !== false;\n  }\n}\n", "<!-- 步驟導航條 -->\n<div class=\"step-navigator\" [ngClass]=\"customClass\">\n  <div class=\"step-nav\">\n    <div \n      *ngFor=\"let step of processedSteps; let i = index\"\n      class=\"step-wrapper\"\n      [class.clickable]=\"isStepClickable(step)\"\n      (click)=\"onStepClick(step.stepNumber, step)\">\n      \n      <!-- 步驟項目 -->\n      <div [ngClass]=\"getStepClasses(step)\">\n        <!-- 步驟圖標（如果有） -->\n        <nb-icon \n          *ngIf=\"step.icon\" \n          [icon]=\"step.icon\" \n          class=\"step-icon\">\n        </nb-icon>\n        \n        <!-- 步驟內容 -->\n        <div class=\"step-content\">\n          <!-- 步驟編號和標籤 -->\n          <span class=\"step-text\">\n            <span *ngIf=\"showStepNumber\" class=\"step-number\">{{ step.stepNumber }}.</span>\n            <span class=\"step-label\">{{ step.label }}</span>\n          </span>\n        </div>\n        \n        <!-- 完成狀態圖標 -->\n        <nb-icon \n          *ngIf=\"step.status === StepStatus.COMPLETED\" \n          icon=\"checkmark-outline\" \n          class=\"step-check-icon\">\n        </nb-icon>\n      </div>\n      \n      <!-- 步驟連接線（除了最後一個步驟） -->\n      <div \n        *ngIf=\"i < processedSteps.length - 1\" \n        class=\"step-connector\"\n        [ngClass]=\"{\n          'completed': step.status === StepStatus.COMPLETED,\n          'active': step.status === StepStatus.ACTIVE\n        }\">\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAkC,eAAe;AAChG,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAAqBC,UAAU,QAAQ,wCAAwC;;;;;;;;;;ICSvEC,EAAA,CAAAC,SAAA,kBAIU;;;;IAFRD,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAC,IAAA,CAAkB;;;;;IAQhBJ,EAAA,CAAAK,cAAA,eAAiD;IAAAL,EAAA,CAAAM,MAAA,GAAsB;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;IAA7BP,EAAA,CAAAQ,SAAA,EAAsB;IAAtBR,EAAA,CAAAS,kBAAA,KAAAN,OAAA,CAAAO,UAAA,MAAsB;;;;;IAM3EV,EAAA,CAAAC,SAAA,kBAIU;;;;;IAIZD,EAAA,CAAAC,SAAA,cAOM;;;;;IAJJD,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAW,eAAA,IAAAC,GAAA,EAAAT,OAAA,CAAAU,MAAA,KAAAC,MAAA,CAAAf,UAAA,CAAAgB,SAAA,EAAAZ,OAAA,CAAAU,MAAA,KAAAC,MAAA,CAAAf,UAAA,CAAAiB,MAAA,EAGE;;;;;;IAvCNhB,EAAA,CAAAK,cAAA,aAI+C;IAA7CL,EAAA,CAAAiB,UAAA,mBAAAC,2DAAA;MAAA,MAAAf,OAAA,GAAAH,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAU,WAAA,CAAArB,OAAA,CAAAO,UAAA,EAAAP,OAAA,CAAkC;IAAA,EAAC;IAG5CH,EAAA,CAAAK,cAAA,aAAsC;IAEpCL,EAAA,CAAAyB,UAAA,IAAAC,+CAAA,qBAGoB;IAMlB1B,EAFF,CAAAK,cAAA,aAA0B,cAEA;IACtBL,EAAA,CAAAyB,UAAA,IAAAE,4CAAA,kBAAiD;IACjD3B,EAAA,CAAAK,cAAA,cAAyB;IAAAL,EAAA,CAAAM,MAAA,GAAgB;IAE7CN,EAF6C,CAAAO,YAAA,EAAO,EAC3C,EACH;IAGNP,EAAA,CAAAyB,UAAA,IAAAG,+CAAA,sBAG0B;IAE5B5B,EAAA,CAAAO,YAAA,EAAM;IAGNP,EAAA,CAAAyB,UAAA,IAAAI,2CAAA,kBAMK;IAEP7B,EAAA,CAAAO,YAAA,EAAM;;;;;;IAtCJP,EAAA,CAAA8B,WAAA,cAAAhB,MAAA,CAAAiB,eAAA,CAAA5B,OAAA,EAAyC;IAIpCH,EAAA,CAAAQ,SAAA,EAAgC;IAAhCR,EAAA,CAAAE,UAAA,YAAAY,MAAA,CAAAkB,cAAA,CAAA7B,OAAA,EAAgC;IAGhCH,EAAA,CAAAQ,SAAA,EAAe;IAAfR,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAC,IAAA,CAAe;IASPJ,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAE,UAAA,SAAAY,MAAA,CAAAmB,cAAA,CAAoB;IACFjC,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAkC,iBAAA,CAAA/B,OAAA,CAAAgC,KAAA,CAAgB;IAM1CnC,EAAA,CAAAQ,SAAA,EAA0C;IAA1CR,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAU,MAAA,KAAAC,MAAA,CAAAf,UAAA,CAAAgB,SAAA,CAA0C;IAQ5Cf,EAAA,CAAAQ,SAAA,EAAmC;IAAnCR,EAAA,CAAAE,UAAA,SAAAkC,IAAA,GAAAtB,MAAA,CAAAuB,cAAA,CAAAC,MAAA,KAAmC;;;ADhC5C;;;;AAcA,OAAM,MAAOC,sBAAsB;EAVnCC,YAAA;IAWE;IACS,KAAAC,KAAK,GAAiB,EAAE;IAEjC;IACS,KAAAC,WAAW,GAAW,CAAC;IAEhC;IACS,KAAAC,oBAAoB,GAAY,KAAK;IAE9C;IACS,KAAAV,cAAc,GAAY,IAAI;IAEvC;IACS,KAAAW,WAAW,GAAW,EAAE;IAEjC;IACU,KAAAC,SAAS,GAAG,IAAIjD,YAAY,EAAU;IAEhD;IACA,KAAAG,UAAU,GAAGA,UAAU;IAEvB;IACA,KAAAsC,cAAc,GAAmE,EAAE;;EAEnFS,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,OAAO,CAAC,IAAIA,OAAO,CAAC,aAAa,CAAC,EAAE;MAC9C,IAAI,CAACC,YAAY,EAAE;IACrB;EACF;EAEA;;;EAGQA,YAAYA,CAAA;IAClB,IAAI,CAACX,cAAc,GAAG,IAAI,CAACI,KAAK,CAACQ,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;MACnD,MAAMzC,UAAU,GAAGyC,KAAK,GAAG,CAAC;MAC5B,IAAItC,MAAkB;MAEtB,IAAIH,UAAU,GAAG,IAAI,CAACgC,WAAW,EAAE;QACjC7B,MAAM,GAAGd,UAAU,CAACgB,SAAS;MAC/B,CAAC,MAAM,IAAIL,UAAU,KAAK,IAAI,CAACgC,WAAW,EAAE;QAC1C7B,MAAM,GAAGd,UAAU,CAACiB,MAAM;MAC5B,CAAC,MAAM;QACLH,MAAM,GAAGd,UAAU,CAACqD,OAAO;MAC7B;MAEA,OAAO;QACL,GAAGF,IAAI;QACPrC,MAAM;QACNH;OACD;IACH,CAAC,CAAC;EACJ;EAEA;;;;;EAKAc,WAAWA,CAACd,UAAkB,EAAEwC,IAAgB;IAC9C;IACA,IAAI,CAAC,IAAI,CAACP,oBAAoB,EAAE;MAC9B;IACF;IAEA;IACA,IAAIO,IAAI,CAACG,SAAS,KAAK,KAAK,EAAE;MAC5B;IACF;IAEA;IACA,IAAI,CAACR,SAAS,CAACS,IAAI,CAAC5C,UAAU,CAAC;EACjC;EAEA;;;;;EAKAsB,cAAcA,CAACkB,IAA6D;IAC1E,MAAMK,OAAO,GAAG,CAAC,WAAW,EAAEL,IAAI,CAACrC,MAAM,CAAC;IAE1C;IACA,IAAI,IAAI,CAAC8B,oBAAoB,IAAIO,IAAI,CAACG,SAAS,KAAK,KAAK,EAAE;MACzDE,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;IAC3B;IAEA,OAAOD,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;EAC1B;EAEA;;;;;EAKA1B,eAAeA,CAACmB,IAAgB;IAC9B,OAAO,IAAI,CAACP,oBAAoB,IAAIO,IAAI,CAACG,SAAS,KAAK,KAAK;EAC9D;;;uCAlGWd,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAmB,SAAA;MAAAC,MAAA;QAAAlB,KAAA;QAAAC,WAAA;QAAAC,oBAAA;QAAAV,cAAA;QAAAW,WAAA;MAAA;MAAAgB,OAAA;QAAAf,SAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GAAA9D,EAAA,CAAA+D,oBAAA,EAAA/D,EAAA,CAAAgE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjCtE,EADF,CAAAK,cAAA,aAAoD,aAC5B;UACpBL,EAAA,CAAAyB,UAAA,IAAA+C,qCAAA,kBAI+C;UAuCnDxE,EADE,CAAAO,YAAA,EAAM,EACF;;;UA7CsBP,EAAA,CAAAE,UAAA,YAAAqE,GAAA,CAAA3B,WAAA,CAAuB;UAG5B5C,EAAA,CAAAQ,SAAA,GAAmB;UAAnBR,EAAA,CAAAE,UAAA,YAAAqE,GAAA,CAAAlC,cAAA,CAAmB;;;qBDStCxC,YAAY,EAAA4E,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZ9E,YAAY,EAAA+E,EAAA,CAAAC,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}