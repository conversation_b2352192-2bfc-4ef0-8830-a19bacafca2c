{"ast": null, "code": "export var EnumTemplateType;\n(function (EnumTemplateType) {\n  EnumTemplateType[EnumTemplateType[\"SpaceTemplate\"] = 1] = \"SpaceTemplate\";\n  EnumTemplateType[EnumTemplateType[\"ItemTemplate\"] = 2] = \"ItemTemplate\"; // 項目模板\n})(EnumTemplateType || (EnumTemplateType = {}));\nexport class EnumTemplateTypeHelper {\n  static getDisplayName(templateType) {\n    switch (templateType) {\n      case EnumTemplateType.SpaceTemplate:\n        return '空間模板';\n      case EnumTemplateType.ItemTemplate:\n        return '項目模板';\n      default:\n        return '未知';\n    }\n  }\n  static getTemplateTypeList() {\n    return [{\n      value: EnumTemplateType.SpaceTemplate,\n      label: '空間模板'\n    }, {\n      value: EnumTemplateType.ItemTemplate,\n      label: '項目模板'\n    }];\n  }\n}", "map": {"version": 3, "names": ["EnumTemplateType", "EnumTemplateTypeHelper", "getDisplayName", "templateType", "SpaceTemplate", "ItemTemplate", "getTemplateTypeList", "value", "label"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\enum\\enumTemplateType.ts"], "sourcesContent": ["export enum EnumTemplateType {\r\n  SpaceTemplate = 1, // 空間模板\r\n  ItemTemplate = 2   // 項目模板\r\n}\r\n\r\nexport class EnumTemplateTypeHelper {\r\n  static getDisplayName(templateType: EnumTemplateType): string {\r\n    switch (templateType) {\r\n      case EnumTemplateType.SpaceTemplate:\r\n        return '空間模板';\r\n      case EnumTemplateType.ItemTemplate:\r\n        return '項目模板';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  static getTemplateTypeList(): Array<{ value: EnumTemplateType; label: string }> {\r\n    return [\r\n      { value: EnumTemplateType.SpaceTemplate, label: '空間模板' },\r\n      { value: EnumTemplateType.ItemTemplate, label: '項目模板' }\r\n    ];\r\n  }\r\n}\r\n"], "mappings": "AAAA,WAAYA,gBAGX;AAHD,WAAYA,gBAAgB;EAC1BA,gBAAA,CAAAA,gBAAA,wCAAiB;EACjBA,gBAAA,CAAAA,gBAAA,sCAAgB,EAAG;AACrB,CAAC,EAHWA,gBAAgB,KAAhBA,gBAAgB;AAK5B,OAAM,MAAOC,sBAAsB;EACjC,OAAOC,cAAcA,CAACC,YAA8B;IAClD,QAAQA,YAAY;MAClB,KAAKH,gBAAgB,CAACI,aAAa;QACjC,OAAO,MAAM;MACf,KAAKJ,gBAAgB,CAACK,YAAY;QAChC,OAAO,MAAM;MACf;QACE,OAAO,IAAI;IACf;EACF;EAEA,OAAOC,mBAAmBA,CAAA;IACxB,OAAO,CACL;MAAEC,KAAK,EAAEP,gBAAgB,CAACI,aAAa;MAAEI,KAAK,EAAE;IAAM,CAAE,EACxD;MAAED,KAAK,EAAEP,gBAAgB,CAACK,YAAY;MAAEG,KAAK,EAAE;IAAM,CAAE,CACxD;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}