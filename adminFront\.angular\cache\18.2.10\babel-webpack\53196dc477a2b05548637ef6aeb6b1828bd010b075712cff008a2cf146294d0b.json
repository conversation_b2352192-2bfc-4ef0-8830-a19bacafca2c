{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbIconModule } from '@nebular/theme';\nimport { StepStatus } from '../../interfaces/step-config.interface';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@nebular/theme\";\nconst _c0 = (a0, a1) => ({\n  \"completed\": a0,\n  \"active\": a1\n});\nfunction StepNavigatorComponent_div_2_nb_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 12);\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"icon\", step_r2.icon);\n  }\n}\nfunction StepNavigatorComponent_div_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", step_r2.stepNumber, \".\");\n  }\n}\nfunction StepNavigatorComponent_div_2_nb_icon_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 14);\n  }\n}\nfunction StepNavigatorComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 15);\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c0, step_r2.status === ctx_r2.StepStatus.COMPLETED, step_r2.status === ctx_r2.StepStatus.ACTIVE));\n  }\n}\nfunction StepNavigatorComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function StepNavigatorComponent_div_2_Template_div_click_0_listener() {\n      const step_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onStepClick(step_r2.stepNumber, step_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4);\n    i0.ɵɵtemplate(2, StepNavigatorComponent_div_2_nb_icon_2_Template, 1, 1, \"nb-icon\", 5);\n    i0.ɵɵelementStart(3, \"div\", 6)(4, \"span\", 7);\n    i0.ɵɵtemplate(5, StepNavigatorComponent_div_2_span_5_Template, 2, 1, \"span\", 8);\n    i0.ɵɵelementStart(6, \"span\", 9);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, StepNavigatorComponent_div_2_nb_icon_8_Template, 1, 0, \"nb-icon\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, StepNavigatorComponent_div_2_div_9_Template, 1, 4, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r2 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"clickable\", ctx_r2.isStepClickable(step_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStepClasses(step_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showStepNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r2.status === ctx_r2.StepStatus.COMPLETED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r4 < ctx_r2.processedSteps.length - 1);\n  }\n}\n/**\n * 步驟導航條共用元件\n * 支持自定義步驟配置和狀態管理\n */\nexport class StepNavigatorComponent {\n  constructor() {\n    /** 步驟配置數組 */\n    this.steps = [];\n    /** 當前步驟索引（1-based） */\n    this.currentStep = 1;\n    /** 是否允許點擊導航到其他步驟 */\n    this.allowClickNavigation = false;\n    /** 是否顯示步驟編號 */\n    this.showStepNumber = true;\n    /** 自定義CSS類名 */\n    this.customClass = '';\n    /** 步驟點擊事件 */\n    this.stepClick = new EventEmitter();\n    /** 暴露枚舉給模板使用 */\n    this.StepStatus = StepStatus;\n    /** 處理後的步驟數據 */\n    this.processedSteps = [];\n  }\n  ngOnChanges(changes) {\n    if (changes['steps'] || changes['currentStep']) {\n      this.processSteps();\n    }\n  }\n  /**\n   * 處理步驟數據，計算每個步驟的狀態\n   */\n  processSteps() {\n    this.processedSteps = this.steps.map((step, index) => {\n      const stepNumber = index + 1;\n      let status;\n      if (stepNumber < this.currentStep) {\n        status = StepStatus.COMPLETED;\n      } else if (stepNumber === this.currentStep) {\n        status = StepStatus.ACTIVE;\n      } else {\n        status = StepStatus.PENDING;\n      }\n      return {\n        ...step,\n        status,\n        stepNumber\n      };\n    });\n  }\n  /**\n   * 處理步驟點擊事件\n   * @param stepNumber 步驟編號（1-based）\n   * @param step 步驟配置\n   */\n  onStepClick(stepNumber, step) {\n    // 檢查是否允許點擊導航\n    if (!this.allowClickNavigation) {\n      return;\n    }\n    // 檢查步驟是否可點擊\n    if (step.clickable === false) {\n      return;\n    }\n    // 發出步驟點擊事件\n    this.stepClick.emit(stepNumber);\n  }\n  /**\n   * 獲取步驟的CSS類名\n   * @param step 處理後的步驟數據\n   * @returns CSS類名字符串\n   */\n  getStepClasses(step) {\n    const classes = ['step-item', step.status];\n    // 添加可點擊樣式\n    if (this.allowClickNavigation && step.clickable !== false) {\n      classes.push('clickable');\n    }\n    return classes.join(' ');\n  }\n  /**\n   * 檢查步驟是否可點擊\n   * @param step 步驟配置\n   * @returns 是否可點擊\n   */\n  isStepClickable(step) {\n    return this.allowClickNavigation && step.clickable !== false;\n  }\n  static {\n    this.ɵfac = function StepNavigatorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StepNavigatorComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StepNavigatorComponent,\n      selectors: [[\"app-step-navigator\"]],\n      inputs: {\n        steps: \"steps\",\n        currentStep: \"currentStep\",\n        allowClickNavigation: \"allowClickNavigation\",\n        showStepNumber: \"showStepNumber\",\n        customClass: \"customClass\"\n      },\n      outputs: {\n        stepClick: \"stepClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"step-navigator\", 3, \"ngClass\"], [1, \"step-nav\"], [\"class\", \"step-wrapper\", 3, \"clickable\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"step-wrapper\", 3, \"click\"], [3, \"ngClass\"], [\"class\", \"step-icon\", 3, \"icon\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"step-text\"], [\"class\", \"step-number\", 4, \"ngIf\"], [1, \"step-label\"], [\"icon\", \"checkmark-outline\", \"class\", \"step-check-icon\", 4, \"ngIf\"], [\"class\", \"step-connector\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"step-icon\", 3, \"icon\"], [1, \"step-number\"], [\"icon\", \"checkmark-outline\", 1, \"step-check-icon\"], [1, \"step-connector\", 3, \"ngClass\"]],\n      template: function StepNavigatorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, StepNavigatorComponent_div_2_Template, 10, 8, \"div\", 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.customClass);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.processedSteps);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, NbIconModule, i2.NbIconComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n.step-navigator[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  padding-bottom: 1rem;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper.clickable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper.clickable[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem 1.25rem;\\n  border-radius: 0.5rem;\\n  font-weight: 500;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  min-width: 140px;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  border: 2px solid transparent;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  flex-shrink: 0;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n  font-size: 0.95rem;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-check-icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-left: 0.25rem;\\n  flex-shrink: 0;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 100%);\\n  color: white;\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);\\n  border-color: #3B82F6;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%], \\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%]   .step-check-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #34D399 0%, #10B981 100%);\\n  color: white;\\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.25);\\n  border-color: #10B981;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%], \\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   .step-check-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #F3F4F6;\\n  color: #9CA3AF;\\n  border-color: #E5E7EB;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%] {\\n  color: #9CA3AF;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%]:hover {\\n  background-color: #F9FAFB;\\n  border-color: #D1D5DB;\\n  color: #6B7280;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 3px;\\n  background-color: #E5E7EB;\\n  margin: 0 0.5rem;\\n  transition: all 0.3s ease;\\n  border-radius: 1.5px;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #34D399 0%, #10B981 100%);\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 100%);\\n}\\n\\n@media (max-width: 768px) {\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.6rem 1rem;\\n    min-width: 120px;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n    width: 2rem;\\n    margin: 0 0.25rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 240px;\\n    padding: 0.8rem 1.5rem;\\n  }\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  border-bottom-color: #374151;\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #1F2937;\\n  color: #9CA3AF;\\n  border-color: #374151;\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%]:hover, .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%]:hover {\\n  background-color: #374151;\\n  border-color: #4B5563;\\n  color: #D1D5DB;\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  background-color: #374151;\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #34D399 0%, #10B981 100%);\\n}\\n.dark-theme[_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.active[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-connector.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 100%);\\n}\\n\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse-active 2s infinite;\\n}\\n.step-navigator[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%]   .step-check-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_check-bounce 0.6s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse-active {\\n  0%, 100% {\\n    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);\\n  }\\n  50% {\\n    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.35);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_check-bounce {\\n  0% {\\n    transform: scale(0);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "NbIconModule", "StepStatus", "i0", "ɵɵelement", "ɵɵproperty", "step_r2", "icon", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpureFunction2", "_c0", "status", "ctx_r2", "COMPLETED", "ACTIVE", "ɵɵlistener", "StepNavigatorComponent_div_2_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onStepClick", "ɵɵtemplate", "StepNavigatorComponent_div_2_nb_icon_2_Template", "StepNavigatorComponent_div_2_span_5_Template", "StepNavigatorComponent_div_2_nb_icon_8_Template", "StepNavigatorComponent_div_2_div_9_Template", "ɵɵclassProp", "isStepClickable", "getStepClasses", "showStepNumber", "ɵɵtextInterpolate", "label", "i_r4", "processedSteps", "length", "StepNavigatorComponent", "constructor", "steps", "currentStep", "allowClickNavigation", "customClass", "step<PERSON>lick", "ngOnChanges", "changes", "processSteps", "map", "step", "index", "PENDING", "clickable", "emit", "classes", "push", "join", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "StepNavigatorComponent_Template", "rf", "ctx", "StepNavigatorComponent_div_2_Template", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "NbIconComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\step-navigator\\step-navigator.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\step-navigator\\step-navigator.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbIconModule } from '@nebular/theme';\nimport { StepConfig, StepStatus } from '../../interfaces/step-config.interface';\n\n/**\n * 步驟導航條共用元件\n * 支持自定義步驟配置和狀態管理\n */\n@Component({\n  selector: 'app-step-navigator',\n  standalone: true,\n  imports: [\n    CommonModule,\n    NbIconModule\n  ],\n  templateUrl: './step-navigator.component.html',\n  styleUrls: ['./step-navigator.component.scss']\n})\nexport class StepNavigatorComponent implements OnChanges {\n  /** 步驟配置數組 */\n  @Input() steps: StepConfig[] = [];\n  \n  /** 當前步驟索引（1-based） */\n  @Input() currentStep: number = 1;\n  \n  /** 是否允許點擊導航到其他步驟 */\n  @Input() allowClickNavigation: boolean = false;\n  \n  /** 是否顯示步驟編號 */\n  @Input() showStepNumber: boolean = true;\n  \n  /** 自定義CSS類名 */\n  @Input() customClass: string = '';\n  \n  /** 步驟點擊事件 */\n  @Output() stepClick = new EventEmitter<number>();\n  \n  /** 暴露枚舉給模板使用 */\n  StepStatus = StepStatus;\n  \n  /** 處理後的步驟數據 */\n  processedSteps: Array<StepConfig & { status: StepStatus; stepNumber: number }> = [];\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes['steps'] || changes['currentStep']) {\n      this.processSteps();\n    }\n  }\n\n  /**\n   * 處理步驟數據，計算每個步驟的狀態\n   */\n  private processSteps(): void {\n    this.processedSteps = this.steps.map((step, index) => {\n      const stepNumber = index + 1;\n      let status: StepStatus;\n      \n      if (stepNumber < this.currentStep) {\n        status = StepStatus.COMPLETED;\n      } else if (stepNumber === this.currentStep) {\n        status = StepStatus.ACTIVE;\n      } else {\n        status = StepStatus.PENDING;\n      }\n      \n      return {\n        ...step,\n        status,\n        stepNumber\n      };\n    });\n  }\n\n  /**\n   * 處理步驟點擊事件\n   * @param stepNumber 步驟編號（1-based）\n   * @param step 步驟配置\n   */\n  onStepClick(stepNumber: number, step: StepConfig): void {\n    // 檢查是否允許點擊導航\n    if (!this.allowClickNavigation) {\n      return;\n    }\n    \n    // 檢查步驟是否可點擊\n    if (step.clickable === false) {\n      return;\n    }\n    \n    // 發出步驟點擊事件\n    this.stepClick.emit(stepNumber);\n  }\n\n  /**\n   * 獲取步驟的CSS類名\n   * @param step 處理後的步驟數據\n   * @returns CSS類名字符串\n   */\n  getStepClasses(step: StepConfig & { status: StepStatus; stepNumber: number }): string {\n    const classes = ['step-item', step.status];\n    \n    // 添加可點擊樣式\n    if (this.allowClickNavigation && step.clickable !== false) {\n      classes.push('clickable');\n    }\n    \n    return classes.join(' ');\n  }\n\n  /**\n   * 檢查步驟是否可點擊\n   * @param step 步驟配置\n   * @returns 是否可點擊\n   */\n  isStepClickable(step: StepConfig): boolean {\n    return this.allowClickNavigation && step.clickable !== false;\n  }\n}\n", "<!-- 步驟導航條 -->\n<div class=\"step-navigator\" [ngClass]=\"customClass\">\n  <div class=\"step-nav\">\n    <div \n      *ngFor=\"let step of processedSteps; let i = index\"\n      class=\"step-wrapper\"\n      [class.clickable]=\"isStepClickable(step)\"\n      (click)=\"onStepClick(step.stepNumber, step)\">\n      \n      <!-- 步驟項目 -->\n      <div [ngClass]=\"getStepClasses(step)\">\n        <!-- 步驟圖標（如果有） -->\n        <nb-icon \n          *ngIf=\"step.icon\" \n          [icon]=\"step.icon\" \n          class=\"step-icon\">\n        </nb-icon>\n        \n        <!-- 步驟內容 -->\n        <div class=\"step-content\">\n          <!-- 步驟編號和標籤 -->\n          <span class=\"step-text\">\n            <span *ngIf=\"showStepNumber\" class=\"step-number\">{{ step.stepNumber }}.</span>\n            <span class=\"step-label\">{{ step.label }}</span>\n          </span>\n        </div>\n        \n        <!-- 完成狀態圖標 -->\n        <nb-icon \n          *ngIf=\"step.status === StepStatus.COMPLETED\" \n          icon=\"checkmark-outline\" \n          class=\"step-check-icon\">\n        </nb-icon>\n      </div>\n      \n      <!-- 步驟連接線（除了最後一個步驟） -->\n      <div \n        *ngIf=\"i < processedSteps.length - 1\" \n        class=\"step-connector\"\n        [ngClass]=\"{\n          'completed': step.status === StepStatus.COMPLETED,\n          'active': step.status === StepStatus.ACTIVE\n        }\">\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAkC,eAAe;AAChG,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAAqBC,UAAU,QAAQ,wCAAwC;;;;;;;;;;ICSvEC,EAAA,CAAAC,SAAA,kBAIU;;;;IAFRD,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAC,IAAA,CAAkB;;;;;IAQhBJ,EAAA,CAAAK,cAAA,eAAiD;IAAAL,EAAA,CAAAM,MAAA,GAAsB;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;IAA7BP,EAAA,CAAAQ,SAAA,EAAsB;IAAtBR,EAAA,CAAAS,kBAAA,KAAAN,OAAA,CAAAO,UAAA,MAAsB;;;;;IAM3EV,EAAA,CAAAC,SAAA,kBAIU;;;;;IAIZD,EAAA,CAAAC,SAAA,cAOM;;;;;IAJJD,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAW,eAAA,IAAAC,GAAA,EAAAT,OAAA,CAAAU,MAAA,KAAAC,MAAA,CAAAf,UAAA,CAAAgB,SAAA,EAAAZ,OAAA,CAAAU,MAAA,KAAAC,MAAA,CAAAf,UAAA,CAAAiB,MAAA,EAGE;;;;;;IAvCNhB,EAAA,CAAAK,cAAA,aAI+C;IAA7CL,EAAA,CAAAiB,UAAA,mBAAAC,2DAAA;MAAA,MAAAf,OAAA,GAAAH,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAU,WAAA,CAAArB,OAAA,CAAAO,UAAA,EAAAP,OAAA,CAAkC;IAAA,EAAC;IAG5CH,EAAA,CAAAK,cAAA,aAAsC;IAEpCL,EAAA,CAAAyB,UAAA,IAAAC,+CAAA,qBAGoB;IAMlB1B,EAFF,CAAAK,cAAA,aAA0B,cAEA;IACtBL,EAAA,CAAAyB,UAAA,IAAAE,4CAAA,kBAAiD;IACjD3B,EAAA,CAAAK,cAAA,cAAyB;IAAAL,EAAA,CAAAM,MAAA,GAAgB;IAE7CN,EAF6C,CAAAO,YAAA,EAAO,EAC3C,EACH;IAGNP,EAAA,CAAAyB,UAAA,IAAAG,+CAAA,sBAG0B;IAE5B5B,EAAA,CAAAO,YAAA,EAAM;IAGNP,EAAA,CAAAyB,UAAA,IAAAI,2CAAA,kBAMK;IAEP7B,EAAA,CAAAO,YAAA,EAAM;;;;;;IAtCJP,EAAA,CAAA8B,WAAA,cAAAhB,MAAA,CAAAiB,eAAA,CAAA5B,OAAA,EAAyC;IAIpCH,EAAA,CAAAQ,SAAA,EAAgC;IAAhCR,EAAA,CAAAE,UAAA,YAAAY,MAAA,CAAAkB,cAAA,CAAA7B,OAAA,EAAgC;IAGhCH,EAAA,CAAAQ,SAAA,EAAe;IAAfR,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAC,IAAA,CAAe;IASPJ,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAE,UAAA,SAAAY,MAAA,CAAAmB,cAAA,CAAoB;IACFjC,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAkC,iBAAA,CAAA/B,OAAA,CAAAgC,KAAA,CAAgB;IAM1CnC,EAAA,CAAAQ,SAAA,EAA0C;IAA1CR,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAU,MAAA,KAAAC,MAAA,CAAAf,UAAA,CAAAgB,SAAA,CAA0C;IAQ5Cf,EAAA,CAAAQ,SAAA,EAAmC;IAAnCR,EAAA,CAAAE,UAAA,SAAAkC,IAAA,GAAAtB,MAAA,CAAAuB,cAAA,CAAAC,MAAA,KAAmC;;;ADhC5C;;;;AAcA,OAAM,MAAOC,sBAAsB;EAVnCC,YAAA;IAWE;IACS,KAAAC,KAAK,GAAiB,EAAE;IAEjC;IACS,KAAAC,WAAW,GAAW,CAAC;IAEhC;IACS,KAAAC,oBAAoB,GAAY,KAAK;IAE9C;IACS,KAAAV,cAAc,GAAY,IAAI;IAEvC;IACS,KAAAW,WAAW,GAAW,EAAE;IAEjC;IACU,KAAAC,SAAS,GAAG,IAAIjD,YAAY,EAAU;IAEhD;IACA,KAAAG,UAAU,GAAGA,UAAU;IAEvB;IACA,KAAAsC,cAAc,GAAmE,EAAE;;EAEnFS,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,OAAO,CAAC,IAAIA,OAAO,CAAC,aAAa,CAAC,EAAE;MAC9C,IAAI,CAACC,YAAY,EAAE;IACrB;EACF;EAEA;;;EAGQA,YAAYA,CAAA;IAClB,IAAI,CAACX,cAAc,GAAG,IAAI,CAACI,KAAK,CAACQ,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;MACnD,MAAMzC,UAAU,GAAGyC,KAAK,GAAG,CAAC;MAC5B,IAAItC,MAAkB;MAEtB,IAAIH,UAAU,GAAG,IAAI,CAACgC,WAAW,EAAE;QACjC7B,MAAM,GAAGd,UAAU,CAACgB,SAAS;MAC/B,CAAC,MAAM,IAAIL,UAAU,KAAK,IAAI,CAACgC,WAAW,EAAE;QAC1C7B,MAAM,GAAGd,UAAU,CAACiB,MAAM;MAC5B,CAAC,MAAM;QACLH,MAAM,GAAGd,UAAU,CAACqD,OAAO;MAC7B;MAEA,OAAO;QACL,GAAGF,IAAI;QACPrC,MAAM;QACNH;OACD;IACH,CAAC,CAAC;EACJ;EAEA;;;;;EAKAc,WAAWA,CAACd,UAAkB,EAAEwC,IAAgB;IAC9C;IACA,IAAI,CAAC,IAAI,CAACP,oBAAoB,EAAE;MAC9B;IACF;IAEA;IACA,IAAIO,IAAI,CAACG,SAAS,KAAK,KAAK,EAAE;MAC5B;IACF;IAEA;IACA,IAAI,CAACR,SAAS,CAACS,IAAI,CAAC5C,UAAU,CAAC;EACjC;EAEA;;;;;EAKAsB,cAAcA,CAACkB,IAA6D;IAC1E,MAAMK,OAAO,GAAG,CAAC,WAAW,EAAEL,IAAI,CAACrC,MAAM,CAAC;IAE1C;IACA,IAAI,IAAI,CAAC8B,oBAAoB,IAAIO,IAAI,CAACG,SAAS,KAAK,KAAK,EAAE;MACzDE,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;IAC3B;IAEA,OAAOD,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;EAC1B;EAEA;;;;;EAKA1B,eAAeA,CAACmB,IAAgB;IAC9B,OAAO,IAAI,CAACP,oBAAoB,IAAIO,IAAI,CAACG,SAAS,KAAK,KAAK;EAC9D;;;uCAlGWd,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAmB,SAAA;MAAAC,MAAA;QAAAlB,KAAA;QAAAC,WAAA;QAAAC,oBAAA;QAAAV,cAAA;QAAAW,WAAA;MAAA;MAAAgB,OAAA;QAAAf,SAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GAAA9D,EAAA,CAAA+D,oBAAA,EAAA/D,EAAA,CAAAgE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjCtE,EADF,CAAAK,cAAA,aAAoD,aAC5B;UACpBL,EAAA,CAAAyB,UAAA,IAAA+C,qCAAA,kBAI+C;UAuCnDxE,EADE,CAAAO,YAAA,EAAM,EACF;;;UA7CsBP,EAAA,CAAAE,UAAA,YAAAqE,GAAA,CAAA3B,WAAA,CAAuB;UAG5B5C,EAAA,CAAAQ,SAAA,GAAmB;UAAnBR,EAAA,CAAAE,UAAA,YAAAqE,GAAA,CAAAlC,cAAA,CAAmB;;;qBDStCxC,YAAY,EAAA4E,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZ9E,YAAY,EAAA+E,EAAA,CAAAC,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}