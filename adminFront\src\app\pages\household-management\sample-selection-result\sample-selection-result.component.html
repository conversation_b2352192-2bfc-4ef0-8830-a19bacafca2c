<nb-card accent="success">
  <nb-card-header>
    <!-- <ngx-breadcrumb></ngx-breadcrumb> -->
    <div style="font-size: 32px;">戶別管理 / 客變確認圖說</div>
  </nb-card-header>
  <nb-card-body>
    <h1 class="font-bold text-[#818181]">您可與此檢視該戶別客戶對於選樣結果之簽認文件，並可選擇要將哪些客變圖面整合為一份圖面請客戶簽回確認。

      如果該位客戶有多份簽回檔案，於客戶端僅會顯示最新的一份文件。</h1>

    <div class="d-flex flex-wrap">

      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full">
          <button class="btn btn-info" (click)="addNew(dialogConfirmImage)">
            新增確認客變圖</button>
        </div>
      </div>
    </div>

    <div class="table-responsive mt-4">
      <table class="table" style="min-width: 1000px;">
        <thead class="table-header">
          <tr class="text-center">
            <th scope="col" class="col-1">類型</th>
            <th scope="col" class="col-1">文件名稱</th>
            <th scope="col" class="col-1">建立日期 </th>
            <th scope="col" class="col-1">簽回日期 </th>
            <th scope="col" class="col-1">狀態</th>
            <th scope="col" class="col-1">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of listFinalDoc ; let i = index" class="text-center">
            <td>{{ item.CIsChange === true ? '客變' : (item.CIsChange === false ? '選樣' :'') }}</td>
            <td>{{ item.CDocumentName}}</td>
            <td>{{ item.CCreateDt |date:'yyyy/MM/dd HH:mm:ss'}}</td>
            <td>{{ item.CSignDate ? (item.CSignDate | dateFormat) : ''}}</td>
            <td>{{ item.CDocumentStatus! | getDocumentStatus }}</td>
            <td class="w-32">
              <button class="btn btn-outline-primary btn-sm m-1" (click)="openPdfInNewTab(item)">檢視</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">
    <ngb-pagination [(page)]="pageIndex" [pageSize]="pageSize" [collectionSize]="totalRecords"
      (pageChange)="pageChanged($event)" aria-label="Pagination">
    </ngb-pagination>
  </nb-card-footer>
  <nb-card-footer>
    <div class="d-flex justify-content-center">
      <button class="btn btn-secondary btn-sm" (click)="goBack()">
        返回上一頁
      </button>
    </div>
  </nb-card-footer>
</nb-card>


<ng-template #dialogConfirmImage let-dialog let-ref="dialogRef">
  <nb-card style="width:1000px; max-height: 95vh">
    <nb-card-header>
      戶別管理 > 客變確認圖說 > {{houseByID.CHousehold}} {{houseByID.CFloor}}F
    </nb-card-header>
    <nb-card-body class="px-4">
      <h4>
        請確認要將哪些圖面整合為一份文件供客戶簽名確認。
      </h4>

      <div class="form-group">
        <label for="CDocumentName" class="required-field mr-4" style="min-width:75px" baseLabel>
          文件名稱
        </label>
        <input type="text" class="w-full" nbInput placeholder="文件名稱" [(ngModel)]="finalDoc.CDocumentName" />
      </div>

      <div class="form-group">
        <label for="isChecked" class="required-field mr-4" style="min-width:75px" baseLabel>
          選樣結果
        </label>
        <nb-checkbox status="basic" [(checked)]="isChecked" disabled>選樣結果
        </nb-checkbox>
      </div>

      <div class="form-group">
        <label for="客變圖" class="mr-4" style="min-width:75px" baseLabel>
          客變圖
        </label>
        <h4>僅能勾選已通過審核之圖面。</h4>

      </div>

      <table style="min-width: 600px;" class="table">
        <thead class="table-header">
          <tr>
            <td></td>
            <th>來源</th>
            <th>討論日期</th>
            <th>圖面名稱</th>
            <th>上傳日期</th>
            <th>通過日期</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let drawing of listSpecialChangeAvailable">

            <td>
              <nb-checkbox status="basic" [(checked)]="drawing.isChecked">
              </nb-checkbox>
            </td>
            <td>{{ drawing.CSource| specialChangeSource }}</td>
            <td>{{ formatDate(drawing.CChangeDate) }}</td>
            <td>{{ drawing.CDrawingName }}</td>
            <td>{{ formatDate(drawing.CCreateDT) }}</td>
            <td>{{ formatDate(drawing.CApproveDate) }}</td>
          </tr>
        </tbody>
      </table>

      <div class="form-group d-flex align-items-center">
        <label for="remark" baseLabel class="required-field align-self-start col-3">送審資訊
          <p style="color: red">內部審核人員查看</p>
        </label>

        <textarea name="remark" id="remark" rows="5" nbInput style="resize: none; max-width: none" class="w-full"
          [(ngModel)]="finalDoc.CApproveRemark">
        </textarea>
      </div>

      <div class="form-group d-flex align-items-center">
        <label for="CNote" baseLabel class="required-field align-self-start col-3">摘要註記
          <p style="color: red">客戶於文件中查看</p>
        </label>
        <textarea name="CNote" id="CNote" rows="5" nbInput style="resize: none; max-width: none" class="w-full"
          [(ngModel)]="finalDoc.CNote">
        </textarea>
      </div>
    </nb-card-body>
    <nb-card-footer class="d-flex justify-content-center">
      <button class="btn btn-outline-secondary m-2" (click)="onClose(ref)">取消</button>
      <button class="btn btn-success m-2" (click)="onCreateFinalDoc(ref)">確認送出審核</button>
    </nb-card-footer>
  </nb-card>
</ng-template>