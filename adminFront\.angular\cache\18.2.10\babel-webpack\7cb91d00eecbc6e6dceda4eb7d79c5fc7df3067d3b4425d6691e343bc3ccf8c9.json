{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { ProfitBarAnimationChartData } from '../data/profit-bar-animation-chart';\nimport * as i0 from \"@angular/core\";\nexport let ProfitBarAnimationChartService = /*#__PURE__*/(() => {\n  class ProfitBarAnimationChartService extends ProfitBarAnimationChartData {\n    constructor() {\n      super();\n      this.data = {\n        firstLine: this.getDataForFirstLine(),\n        secondLine: this.getDataForSecondLine()\n      };\n    }\n    getDataForFirstLine() {\n      return this.createEmptyArray(100).map((_, index) => {\n        const oneFifth = index / 5;\n        return (Math.sin(oneFifth) * (oneFifth - 10) + index / 6) * 5;\n      });\n    }\n    getDataForSecondLine() {\n      return this.createEmptyArray(100).map((_, index) => {\n        const oneFifth = index / 5;\n        return (Math.cos(oneFifth) * (oneFifth - 10) + index / 6) * 5;\n      });\n    }\n    createEmptyArray(nPoints) {\n      return Array.from(Array(nPoints));\n    }\n    getChartData() {\n      return observableOf(this.data);\n    }\n    static {\n      this.ɵfac = function ProfitBarAnimationChartService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ProfitBarAnimationChartService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ProfitBarAnimationChartService,\n        factory: ProfitBarAnimationChartService.ɵfac\n      });\n    }\n  }\n  return ProfitBarAnimationChartService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}