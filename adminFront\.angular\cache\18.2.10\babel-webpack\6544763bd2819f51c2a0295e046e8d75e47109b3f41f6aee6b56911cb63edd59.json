{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction TemplateViewerComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_19_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(2, \"i\", 29);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"small\", 31);\n    i0.ɵɵelement(2, \"i\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u627E\\u5230 \", ctx_r1.filteredTemplates.length, \" \\u500B\\u76F8\\u95DC\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"small\", 34);\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵtext(3, \"\\u672A\\u627E\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 4)(2, \"div\", 43)(3, \"span\", 44);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 45)(6, \"small\", 8);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A\\u7B2C \", (ctx_r1.templatePagination.currentPage - 1) * ctx_r1.templatePagination.pageSize + 1, \" - \", ctx_r1.Math.min(ctx_r1.templatePagination.currentPage * ctx_r1.templatePagination.pageSize, ctx_r1.templatePagination.totalItems), \" \\u9805\\uFF0C \\u5171 \", ctx_r1.templatePagination.totalItems, \" \\u9805\\u6A21\\u677F \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\\u7B2C \", ctx_r1.templatePagination.currentPage, \" / \", ctx_r1.templatePagination.totalPages, \" \\u9801\");\n  }\n}\nfunction TemplateViewerComponent_div_22_div_3_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_3_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const tpl_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(tpl_r4.TemplateID && ctx_r1.onDeleteTemplate(tpl_r4.TemplateID));\n    });\n    i0.ɵɵelement(1, \"i\", 57);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_22_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 48)(3, \"h6\", 49);\n    i0.ɵɵelement(4, \"i\", 50);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 52)(9, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_3_Template_button_click_9_listener() {\n      const tpl_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r4));\n    });\n    i0.ɵɵelement(10, \"i\", 54);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, TemplateViewerComponent_div_22_div_3_button_13_Template, 4, 0, \"button\", 55);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tpl_r4 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", tpl_r4.TemplateName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", tpl_r4.Description || \"\\u7121\\u63CF\\u8FF0\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", tpl_r4.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_div_22_div_4_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 16);\n  }\n}\nfunction TemplateViewerComponent_div_22_div_4_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 65);\n  }\n}\nfunction TemplateViewerComponent_div_22_div_4_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p\", 66);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5617\\u8A66\\u5176\\u4ED6\\u95DC\\u9375\\u5B57\\u6216 \");\n    i0.ɵɵelementStart(2, \"a\", 67);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_4_p_7_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(3, \"\\u6E05\\u9664\\u641C\\u5C0B\\u689D\\u4EF6\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_22_div_4_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 66);\n    i0.ɵɵtext(1, \" \\u76EE\\u524D\\u9084\\u6C92\\u6709\\u5EFA\\u7ACB\\u4EFB\\u4F55\\u6A21\\u677F\\uFF0C\\u8ACB\\u5148\\u5EFA\\u7ACB\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_22_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"div\", 60);\n    i0.ɵɵtemplate(3, TemplateViewerComponent_div_22_div_4_i_3_Template, 1, 0, \"i\", 61)(4, TemplateViewerComponent_div_22_div_4_i_4_Template, 1, 0, \"i\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, TemplateViewerComponent_div_22_div_4_p_7_Template, 4, 0, \"p\", 64)(8, TemplateViewerComponent_div_22_div_4_p_8_Template, 2, 0, \"p\", 64);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u66AB\\u7121\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_22_div_5_li_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 74)(1, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_5_li_13_Template_button_click_1_listener() {\n      const page_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(page_r9));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r9 === ctx_r1.templatePagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r9);\n  }\n}\nfunction TemplateViewerComponent_div_22_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69)(2, \"div\", 70)(3, \"span\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"nav\", 72)(6, \"ul\", 73)(7, \"li\", 74)(8, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_5_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(1));\n    });\n    i0.ɵɵelement(9, \"i\", 76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"li\", 74)(11, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_5_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage - 1));\n    });\n    i0.ɵɵelement(12, \"i\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, TemplateViewerComponent_div_22_div_5_li_13_Template, 3, 3, \"li\", 79);\n    i0.ɵɵelementStart(14, \"li\", 74)(15, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_5_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage + 1));\n    });\n    i0.ɵɵelement(16, \"i\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"li\", 74)(18, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_22_div_5_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.totalPages));\n    });\n    i0.ɵɵelement(19, \"i\", 83);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.templatePagination.currentPage, \" \\u9801\\uFF0C\\u5171 \", ctx_r1.templatePagination.totalPages, \" \\u9801 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getTemplatePageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_22_div_1_Template, 8, 5, \"div\", 37);\n    i0.ɵɵelementStart(2, \"div\", 38);\n    i0.ɵɵtemplate(3, TemplateViewerComponent_div_22_div_3_Template, 14, 3, \"div\", 39)(4, TemplateViewerComponent_div_22_div_4_Template, 9, 5, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TemplateViewerComponent_div_22_div_5_Template, 20, 15, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalItems > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedTemplates)(\"ngForTrackBy\", ctx_r1.trackByTemplateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.paginatedTemplates || ctx_r1.paginatedTemplates.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalPages > 1);\n  }\n}\nfunction TemplateViewerComponent_div_23_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedTemplate.Description, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_23_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"span\", 96);\n    i0.ɵɵtext(2, \"\\u9801\\u6578\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 97);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.detailPagination.currentPage, \" / \", ctx_r1.detailPagination.totalPages, \"\");\n  }\n}\nfunction TemplateViewerComponent_div_23_div_19_div_1_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 121);\n    i0.ɵɵelement(1, \"i\", 122);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r11.CGroupName);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_19_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 123);\n    i0.ɵɵelement(1, \"i\", 124);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r11.CCategory);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_19_div_1_div_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130)(1, \"span\", 131);\n    i0.ɵɵtext(2, \"\\u55AE\\u50F9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 132);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind1(5, 1, detail_r11.CUnitPrice), \"\");\n  }\n}\nfunction TemplateViewerComponent_div_23_div_19_div_1_div_21_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 133)(1, \"span\", 131);\n    i0.ɵɵtext(2, \"\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 134);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", detail_r11.CQuantity, \" \", detail_r11.CUnit, \"\");\n  }\n}\nfunction TemplateViewerComponent_div_23_div_19_div_1_div_21_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"div\", 136);\n    i0.ɵɵelement(2, \"i\", 137);\n    i0.ɵɵelementStart(3, \"span\", 138);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r11 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(detail_r11.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_19_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125)(1, \"div\", 126);\n    i0.ɵɵtemplate(2, TemplateViewerComponent_div_23_div_19_div_1_div_21_div_2_Template, 6, 3, \"div\", 127)(3, TemplateViewerComponent_div_23_div_19_div_1_div_21_div_3_Template, 5, 2, \"div\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TemplateViewerComponent_div_23_div_19_div_1_div_21_div_4_Template, 5, 1, \"div\", 129);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r11.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r11.CQuantity && detail_r11.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r11.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 106)(2, \"div\", 107)(3, \"div\", 108)(4, \"span\", 109);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 110)(7, \"h6\", 111);\n    i0.ɵɵelement(8, \"i\", 112);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 113)(11, \"span\", 114);\n    i0.ɵɵelement(12, \"i\", 115);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, TemplateViewerComponent_div_23_div_19_div_1_span_15_Template, 4, 1, \"span\", 116)(16, TemplateViewerComponent_div_23_div_19_div_1_span_16_Template, 4, 1, \"span\", 117);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 118)(18, \"span\", 119);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, TemplateViewerComponent_div_23_div_19_div_1_div_21_Template, 5, 3, \"div\", 120);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r12 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", detail_r11.CReleateName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(detail_r11.CReleateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r11.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r11.CCategory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 7, detail_r11.CCreateDt, \"MM/dd\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r11.CUnitPrice || detail_r11.CQuantity || detail_r11.CUnit || detail_r11.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_23_div_19_div_1_Template, 22, 10, \"div\", 104);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetailsData);\n  }\n}\nfunction TemplateViewerComponent_div_23_ng_template_20_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 145)(5, \"div\", 146)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 147);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r13 = ctx.$implicit;\n    const i_r14 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r14 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", detail_r13.FieldName, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", detail_r13.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_23_ng_template_20_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 140);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_23_ng_template_20_div_0_div_1_Template, 10, 3, \"div\", 141);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedDetails);\n  }\n}\nfunction TemplateViewerComponent_div_23_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TemplateViewerComponent_div_23_ng_template_20_div_0_Template, 2, 1, \"div\", 139);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const noDetails_r15 = i0.ɵɵreference(24);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetails.length > 0)(\"ngIfElse\", noDetails_r15);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_22_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 74)(1, \"button\", 152);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_23_div_22_li_6_Template_button_click_1_listener() {\n      const page_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(page_r18));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r18 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r18 === ctx_r1.detailPagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r18);\n  }\n}\nfunction TemplateViewerComponent_div_23_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 148)(1, \"nav\", 149)(2, \"ul\", 150)(3, \"li\", 74)(4, \"button\", 151);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_23_div_22_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_23_div_22_li_6_Template, 3, 3, \"li\", 79);\n    i0.ɵɵelementStart(7, \"li\", 74)(8, \"button\", 151);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_23_div_22_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 81);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getDetailPageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_23_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 153);\n    i0.ɵɵelement(1, \"i\", 154);\n    i0.ɵɵelementStart(2, \"p\", 155);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"div\", 87)(3, \"div\", 88)(4, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_23_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵelement(5, \"i\", 90);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 91)(7, \"h5\", 92);\n    i0.ɵɵelement(8, \"i\", 50);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, TemplateViewerComponent_div_23_p_10_Template, 2, 1, \"p\", 93);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 94)(12, \"div\", 95)(13, \"span\", 96);\n    i0.ɵɵtext(14, \"\\u9805\\u76EE\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 97);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, TemplateViewerComponent_div_23_div_17_Template, 5, 2, \"div\", 98);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 99);\n    i0.ɵɵtemplate(19, TemplateViewerComponent_div_23_div_19_Template, 2, 1, \"div\", 100)(20, TemplateViewerComponent_div_23_ng_template_20_Template, 1, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(22, TemplateViewerComponent_div_23_div_22_Template, 10, 7, \"div\", 101)(23, TemplateViewerComponent_div_23_ng_template_23_Template, 4, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const checkOldDetails_r19 = i0.ɵɵreference(21);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedTemplate.TemplateName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTemplate.Description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.currentTemplateDetailsData.length > 0 ? ctx_r1.detailPagination.totalItems : ctx_r1.currentTemplateDetails.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetailsData.length > 0)(\"ngIfElse\", checkOldDetails_r19);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.templateType = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 公開Math對象供模板使用\n    this.Math = Math;\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = []; // 保留用於向後相容\n    this.selectedTemplate = null;\n    // 新的詳情資料管理\n    this.currentTemplateDetailsData = [];\n    this.detailSearchKeyword = ''; // 明細專用搜尋關鍵字\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 分頁功能 - 模板列表\n    this.templatePagination = {\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedTemplates = [];\n    // 分頁功能 - 模板詳情\n    this.detailPagination = {\n      currentPage: 1,\n      pageSize: 5,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedDetails = [];\n  }\n  ngOnInit() {\n    this.loadTemplates();\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 載入模板列表 - 使用真實的 API 調用\n  loadTemplates() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      PageIndex: 1,\n      // 暫時載入第一頁\n      PageSize: 100,\n      // 暫時載入較多資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateList API\n    this.templateService.apiTemplateGetTemplateListPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // API 調用成功\n          // 轉換 API 回應為內部格式\n          this.templates = response.Entries.map(item => ({\n            TemplateID: item.CTemplateId,\n            TemplateName: item.CTemplateName || '',\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\n          }));\n          // 更新過濾列表\n          this.updateFilteredTemplates();\n          // 清空詳情資料，改為按需載入\n          this.templateDetails = [];\n          this.currentTemplateDetailsData = [];\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n          this.templateDetails = [];\n          this.updateFilteredTemplates();\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n        this.templates = [];\n        this.templateDetails = [];\n        this.updateFilteredTemplates();\n      }\n    });\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n    this.updateTemplatePagination();\n  }\n  // 更新模板分頁\n  updateTemplatePagination() {\n    this.templatePagination.totalItems = this.filteredTemplates.length;\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\n    // 確保當前頁面不超過總頁數\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\n    }\n    this.updatePaginatedTemplates();\n  }\n  // 更新分頁後的模板列表\n  updatePaginatedTemplates() {\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\n    const endIndex = startIndex + this.templatePagination.pageSize;\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\n  }\n  // 模板分頁導航\n  goToTemplatePage(page) {\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = page;\n      this.updatePaginatedTemplates();\n    }\n  }\n  // 獲取模板分頁頁碼數組\n  getTemplatePageNumbers() {\n    const pages = [];\n    const totalPages = this.templatePagination.totalPages;\n    const currentPage = this.templatePagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n    // 按需載入模板詳情\n    if (template.TemplateID) {\n      this.loadTemplateDetails(template.TemplateID);\n    }\n    this.updateDetailPagination();\n  }\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\n  loadTemplateDetails(templateId, pageIndex = 1, searchKeyword) {\n    const args = {\n      templateId: templateId\n    };\n    // 調用真實的 GetTemplateDetailById API\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\n          let allDetails = response.Entries.map(item => ({\n            CTemplateDetailId: item.CTemplateDetailId || 0,\n            CTemplateId: item.CTemplateId || templateId,\n            CReleateId: item.CReleateId || 0,\n            CReleateName: item.CReleateName || '',\n            CGroupName: item.CGroupName || '',\n            // 新增群組名稱欄位\n            CSort: undefined,\n            CRemark: undefined,\n            CCreateDt: new Date().toISOString(),\n            CCreator: '系統',\n            CCategory: undefined,\n            CUnitPrice: undefined,\n            CQuantity: undefined,\n            CUnit: undefined\n          }));\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\n          if (searchKeyword && searchKeyword.trim()) {\n            allDetails = allDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()));\n          }\n          // 前端分頁處理 (因為 API 不支援分頁參數)\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n          const endIndex = startIndex + this.detailPagination.pageSize;\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\n          // 更新詳情資料和分頁資訊\n          this.currentTemplateDetailsData = pagedDetails;\n          this.detailPagination.totalItems = allDetails.length;\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\n          this.detailPagination.currentPage = pageIndex;\n        } else {\n          this.currentTemplateDetailsData = [];\n          this.detailPagination.totalItems = 0;\n          this.detailPagination.totalPages = 0;\n          this.detailPagination.currentPage = 1;\n        }\n      },\n      error: () => {\n        this.currentTemplateDetailsData = [];\n        this.detailPagination.totalItems = 0;\n        this.detailPagination.totalPages = 0;\n        this.detailPagination.currentPage = 1;\n        // 如果 API 失敗，回退到模擬資料\n        this.loadTemplateDetailsMock(templateId, pageIndex, searchKeyword);\n      }\n    });\n  }\n  // 模擬載入模板詳情 (暫時使用，等待後端 API)\n  loadTemplateDetailsMock(templateId, pageIndex = 1, searchKeyword) {\n    // 模擬 API 延遲\n    setTimeout(() => {\n      // 生成模擬詳情資料\n      const mockDetails = [];\n      const itemsPerTemplate = 8; // 每個模板8個項目\n      for (let i = 1; i <= itemsPerTemplate; i++) {\n        const detail = {\n          CTemplateDetailId: templateId * 100 + i,\n          CTemplateId: templateId,\n          CReleateId: templateId * 1000 + i,\n          CReleateName: `工程項目${String.fromCharCode(64 + templateId % 26 + 1)}-${i}`,\n          CGroupName: i % 4 === 0 ? '結構工程' : i % 4 === 1 ? '機電工程' : i % 4 === 2 ? '裝修工程' : '其他工程',\n          // 新增群組名稱\n          CSort: i,\n          CRemark: i % 3 === 0 ? `備註說明 ${i}` : undefined,\n          CCreateDt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\n          CCreator: '系統管理員',\n          CCategory: i % 2 === 0 ? '基礎工程' : '進階工程',\n          CUnitPrice: Math.floor(Math.random() * 10000) + 1000,\n          CQuantity: Math.floor(Math.random() * 10) + 1,\n          CUnit: i % 3 === 0 ? '式' : i % 3 === 1 ? '個' : 'm²'\n        };\n        mockDetails.push(detail);\n      }\n      // 搜尋篩選\n      let filteredDetails = mockDetails;\n      if (searchKeyword && searchKeyword.trim()) {\n        filteredDetails = mockDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CRemark && detail.CRemark.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CCategory && detail.CCategory.toLowerCase().includes(searchKeyword.toLowerCase()));\n      }\n      // 分頁處理\n      const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n      const endIndex = startIndex + this.detailPagination.pageSize;\n      const pagedDetails = filteredDetails.slice(startIndex, endIndex);\n      // 更新資料\n      this.currentTemplateDetailsData = pagedDetails;\n      this.detailPagination.totalItems = filteredDetails.length;\n      this.detailPagination.totalPages = Math.ceil(filteredDetails.length / this.detailPagination.pageSize);\n      this.detailPagination.currentPage = pageIndex;\n    }, 300);\n  }\n  // 搜尋模板詳情 (明細專用搜尋)\n  searchTemplateDetails(keyword) {\n    this.detailSearchKeyword = keyword;\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\n    }\n  }\n  // 清除明細搜尋\n  clearDetailSearch() {\n    this.detailSearchKeyword = '';\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1);\n    }\n  }\n  // 更新詳情分頁 (保留向後相容)\n  updateDetailPagination() {\n    // 如果使用新的詳情資料，直接返回\n    if (this.currentTemplateDetailsData.length > 0) {\n      return;\n    }\n    // 向後相容：使用舊的詳情資料\n    const details = this.currentTemplateDetails;\n    this.detailPagination.totalItems = details.length;\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\n    this.detailPagination.currentPage = 1; // 重置到第一頁\n    this.updatePaginatedDetails();\n  }\n  // 更新分頁後的詳情列表\n  updatePaginatedDetails() {\n    const details = this.currentTemplateDetails;\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\n    const endIndex = startIndex + this.detailPagination.pageSize;\n    this.paginatedDetails = details.slice(startIndex, endIndex);\n  }\n  // 詳情分頁導航\n  goToDetailPage(page) {\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\n      // 如果使用新的詳情資料，重新載入\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page);\n      } else {\n        // 向後相容：使用舊的分頁邏輯\n        this.detailPagination.currentPage = page;\n        this.updatePaginatedDetails();\n      }\n    }\n  }\n  // 獲取詳情分頁頁碼數組\n  getDetailPageNumbers() {\n    const pages = [];\n    const totalPages = this.detailPagination.totalPages;\n    const currentPage = this.detailPagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // 刪除模板 - 使用真實的 API 調用\n  deleteTemplateById(templateID) {\n    // 準備 API 請求參數\n    const deleteTemplateArgs = {\n      CTemplateId: templateID\n    };\n    // 調用 DeleteTemplate API\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\n      body: deleteTemplateArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          // 重新載入模板列表\n          this.loadTemplates();\n          // 如果當前查看的模板被刪除，關閉詳情\n          if (this.selectedTemplate?.TemplateID === templateID) {\n            this.selectedTemplate = null;\n          }\n        } else {\n          // API 返回錯誤\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n      }\n    });\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/Template/GetTemplateList\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/Template/SaveTemplate\n   *    請求體: {\n   *      CTemplateName: string,\n   *      CTemplateType: number,  // 1=客變需求\n   *      CStatus: number,\n   *      Details: [{\n   *        CReleateId: number,        // 關聯主檔ID\n   *        CReleateName: string       // 關聯名稱\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    POST /api/Template/DeleteTemplate\n   *    請求體: { CTemplateId: number }\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        templateType: \"templateType\"\n      },\n      outputs: {\n        selectTemplate: \"selectTemplate\",\n        close: \"close\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 29,\n      vars: 7,\n      consts: [[\"checkOldDetails\", \"\"], [\"noDetails\", \"\"], [1, \"template-viewer-card\"], [1, \"template-viewer-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"header-title\"], [1, \"mb-0\"], [1, \"fas\", \"fa-layer-group\", \"mr-2\", \"text-primary\"], [1, \"text-muted\"], [1, \"header-actions\"], [1, \"badge\", \"badge-info\"], [1, \"template-viewer-body\"], [1, \"enhanced-search-container\", \"mb-4\"], [1, \"search-wrapper\"], [1, \"search-input-group\"], [1, \"search-icon\"], [1, \"fas\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u63CF\\u8FF0\\u6216\\u95DC\\u9375\\u5B57...\", 1, \"search-input\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [\"class\", \"search-actions\", 4, \"ngIf\"], [\"class\", \"search-suggestions\", 4, \"ngIf\"], [\"class\", \"search-no-results\", 4, \"ngIf\"], [\"class\", \"template-list-container\", 4, \"ngIf\"], [\"class\", \"template-detail-view\", 4, \"ngIf\"], [1, \"template-viewer-footer\"], [1, \"footer-actions\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [1, \"search-actions\"], [\"type\", \"button\", \"title\", \"\\u6E05\\u9664\\u641C\\u5C0B\", 1, \"clear-search-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"search-suggestions\"], [1, \"text-success\"], [1, \"fas\", \"fa-check-circle\", \"mr-1\"], [1, \"search-no-results\"], [1, \"text-warning\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"template-list-container\"], [\"class\", \"list-controls mb-3\", 4, \"ngIf\"], [1, \"template-cards-grid\"], [\"class\", \"template-card\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"empty-state-card\", 4, \"ngIf\"], [\"class\", \"enhanced-pagination-container mt-4\", 4, \"ngIf\"], [1, \"list-controls\", \"mb-3\"], [1, \"list-info\"], [1, \"info-text\"], [1, \"view-options\"], [1, \"template-card\"], [1, \"card-header\"], [1, \"template-info\"], [1, \"template-name\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\", \"text-primary\"], [1, \"template-description\"], [1, \"template-actions\"], [\"title\", \"\\u67E5\\u770B\\u8A73\\u60C5\", 1, \"action-btn\", \"view-btn\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"action-btn delete-btn\", \"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 1, \"action-btn\", \"delete-btn\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"empty-state-card\"], [1, \"empty-content\"], [1, \"empty-icon\"], [\"class\", \"fas fa-search\", 4, \"ngIf\"], [\"class\", \"fas fa-folder-open\", 4, \"ngIf\"], [1, \"empty-title\"], [\"class\", \"empty-description\", 4, \"ngIf\"], [1, \"fas\", \"fa-folder-open\"], [1, \"empty-description\"], [\"href\", \"javascript:void(0)\", 1, \"clear-link\", 3, \"click\"], [1, \"enhanced-pagination-container\", \"mt-4\"], [1, \"pagination-wrapper\"], [1, \"pagination-info\"], [1, \"page-info\"], [\"aria-label\", \"\\u6A21\\u677F\\u5217\\u8868\\u5206\\u9801\", 1, \"pagination-nav\"], [1, \"enhanced-pagination\"], [1, \"page-item\"], [\"title\", \"\\u7B2C\\u4E00\\u9801\", 1, \"page-btn\", \"first-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [\"title\", \"\\u4E0A\\u4E00\\u9801\", 1, \"page-btn\", \"prev-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"\\u4E0B\\u4E00\\u9801\", 1, \"page-btn\", \"next-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [\"title\", \"\\u6700\\u5F8C\\u4E00\\u9801\", 1, \"page-btn\", \"last-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"page-btn\", \"page-number\", 3, \"click\"], [1, \"template-detail-view\"], [1, \"detail-header\"], [1, \"detail-title-section\"], [1, \"back-button\"], [\"title\", \"\\u8FD4\\u56DE\\u6A21\\u677F\\u5217\\u8868\", 1, \"back-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"detail-title-info\"], [1, \"detail-title\"], [\"class\", \"detail-subtitle\", 4, \"ngIf\"], [1, \"detail-stats\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [1, \"detail-content\"], [\"class\", \"enhanced-detail-list\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"detail-pagination mt-3\", 4, \"ngIf\"], [1, \"detail-subtitle\"], [1, \"enhanced-detail-list\"], [\"class\", \"enhanced-detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"enhanced-detail-item\"], [1, \"detail-item-card\"], [1, \"detail-item-header\"], [1, \"item-index\"], [1, \"index-badge\"], [1, \"item-main-info\"], [1, \"item-name\"], [1, \"fas\", \"fa-cog\", \"mr-2\", \"text-secondary\"], [1, \"item-meta\"], [1, \"meta-item\", \"id-meta\"], [1, \"fas\", \"fa-hashtag\"], [\"class\", \"meta-item group-meta\", 4, \"ngIf\"], [\"class\", \"meta-item category-meta\", 4, \"ngIf\"], [1, \"item-actions\"], [1, \"create-date\"], [\"class\", \"detail-item-body\", 4, \"ngIf\"], [1, \"meta-item\", \"group-meta\"], [1, \"fas\", \"fa-layer-group\"], [1, \"meta-item\", \"category-meta\"], [1, \"fas\", \"fa-tag\"], [1, \"detail-item-body\"], [1, \"item-details-grid\"], [\"class\", \"detail-group price-group\", 4, \"ngIf\"], [\"class\", \"detail-group quantity-group\", 4, \"ngIf\"], [\"class\", \"item-remark\", 4, \"ngIf\"], [1, \"detail-group\", \"price-group\"], [1, \"detail-label\"], [1, \"detail-value\", \"price-value\"], [1, \"detail-group\", \"quantity-group\"], [1, \"detail-value\", \"quantity-value\"], [1, \"item-remark\"], [1, \"remark-content\"], [1, \"fas\", \"fa-comment-alt\", \"mr-2\", \"text-muted\"], [1, \"remark-text\"], [\"class\", \"detail-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-list\"], [\"class\", \"detail-item d-flex align-items-center py-2 border-bottom\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\", \"d-flex\", \"align-items-center\", \"py-2\", \"border-bottom\"], [1, \"detail-index\"], [1, \"badge\", \"badge-light\"], [1, \"detail-content\", \"flex-grow-1\", \"ml-2\"], [1, \"detail-field\"], [1, \"detail-value\", \"text-muted\"], [1, \"detail-pagination\", \"mt-3\"], [\"aria-label\", \"\\u6A21\\u677F\\u8A73\\u60C5\\u5206\\u9801\"], [1, \"pagination\", \"pagination-sm\", \"justify-content-center\", \"mb-0\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"py-3\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"h5\", 6);\n          i0.ɵɵelement(5, \"i\", 7);\n          i0.ɵɵtext(6, \"\\u6A21\\u677F\\u7BA1\\u7406 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"small\", 8);\n          i0.ɵɵtext(8, \"\\u7BA1\\u7406\\u548C\\u67E5\\u770B\\u5BA2\\u8B8A\\u9700\\u6C42\\u6A21\\u677F\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"span\", 10);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"nb-card-body\", 11)(13, \"div\", 12)(14, \"div\", 13)(15, \"div\", 14)(16, \"div\", 15);\n          i0.ɵɵelement(17, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_Template_input_ngModelChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function TemplateViewerComponent_Template_input_input_18_listener() {\n            return ctx.onSearch();\n          })(\"keyup.enter\", function TemplateViewerComponent_Template_input_keyup_enter_18_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, TemplateViewerComponent_div_19_Template, 3, 0, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, TemplateViewerComponent_div_20_Template, 4, 1, \"div\", 19)(21, TemplateViewerComponent_div_21_Template, 4, 0, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(22, TemplateViewerComponent_div_22_Template, 6, 5, \"div\", 21)(23, TemplateViewerComponent_div_23_Template, 25, 7, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"nb-card-footer\", 23)(25, \"div\", 24)(26, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_26_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelement(27, \"i\", 26);\n          i0.ɵɵtext(28, \"\\u95DC\\u9589 \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\"\", ctx.templatePagination.totalItems, \" \\u500B\\u6A21\\u677F\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword && ctx.filteredTemplates.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword && ctx.filteredTemplates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, i2.DatePipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, NbCardModule, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardFooterComponent, i4.NbCardHeaderComponent, NbButtonModule],\n      styles: [\".template-viewer-card[_ngcontent-%COMP%] {\\n  width: 90vw;\\n  max-width: 1200px;\\n  height: 80vh;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(174, 155, 102, 0.25);\\n  border: none;\\n  overflow: hidden;\\n  background: #FFFFFF;\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  border-bottom: none;\\n  padding: 1.5rem;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-bottom: 0.25rem;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  opacity: 0.9;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: #FFFFFF;\\n  font-size: 0.875rem;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.template-viewer-body[_ngcontent-%COMP%] {\\n  overflow: auto;\\n  padding: 1.5rem;\\n  background: #FEFCF8;\\n}\\n\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  background: #F8F9FA;\\n  border-radius: 8px;\\n  padding: 0.75rem 1rem;\\n  border: 2px solid transparent;\\n  transition: 0.3s ease;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]:focus-within {\\n  border-color: #B8A676;\\n  background: #FFFFFF;\\n  box-shadow: 0 0 0 3px rgba(184, 166, 118, 0.1);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  margin-right: 0.75rem;\\n  font-size: 1rem;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  background: transparent;\\n  outline: none;\\n  font-size: 1rem;\\n  color: #2C3E50;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder {\\n  color: #ADB5BD;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-search-btn[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  color: #FFFFFF;\\n  border: none;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.75rem;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-search-btn[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n  transform: scale(1.1);\\n}\\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-suggestions[_ngcontent-%COMP%], \\n.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-no-results[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  padding: 0.5rem 0;\\n}\\n\\n.template-list-container[_ngcontent-%COMP%]   .list-controls[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 1rem 1.5rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n}\\n.template-list-container[_ngcontent-%COMP%]   .list-controls[_ngcontent-%COMP%]   .list-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n\\n.template-cards-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1rem;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n  transition: 0.3s ease;\\n  border: 1px solid #E9ECEF;\\n  overflow: hidden;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 16px rgba(174, 155, 102, 0.2);\\n  border-color: #B8A676;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  font-size: 1.1rem;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  margin: 0;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  margin-left: 1rem;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 1rem;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(184, 166, 118, 0.3);\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  color: #FFFFFF;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .template-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n  transform: translateY(-1px);\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  padding: 3rem 2rem;\\n  text-align: center;\\n  border: 2px dashed #dee2e6;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #adb5bd;\\n  margin-bottom: 1rem;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin: 0;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]   .clear-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.template-cards-grid[_ngcontent-%COMP%]   .empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]   .clear-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.enhanced-pagination-container[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]   .page-info[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n  gap: 0.5rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 40px;\\n  height: 40px;\\n  border: none;\\n  border-radius: 8px;\\n  background: #F8F9FA;\\n  color: #6C757D;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  transform: translateY(-1px);\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.page-number[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.first-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.last-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.prev-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.next-page[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.3);\\n}\\n\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  margin-bottom: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  margin-bottom: 1rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border: none;\\n  border-radius: 8px;\\n  background: #F8F9FA;\\n  color: #6C757D;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  transform: translateY(-1px);\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-title[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-subtitle[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6C757D;\\n  margin-bottom: 0.25rem;\\n}\\n.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%] {\\n  background: #FFFFFF;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(174, 155, 102, 0.15);\\n  border: 1px solid #E9ECEF;\\n  overflow: hidden;\\n  transition: 0.3s ease;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 16px rgba(174, 155, 102, 0.2);\\n  border-color: #B8A676;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%] {\\n  padding: 1.25rem;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  border-bottom: 1px solid #F8F9FA;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-index[_ngcontent-%COMP%]   .index-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, #B8A676 0%, #AE9B66 100%);\\n  color: #FFFFFF;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 600;\\n  margin-bottom: 0.75rem;\\n  font-size: 1.1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.75rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 6px;\\n  font-size: 0.875rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.id-meta[_ngcontent-%COMP%] {\\n  background: rgba(184, 166, 118, 0.1);\\n  color: #9B8A5A;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.group-meta[_ngcontent-%COMP%] {\\n  background: rgba(184, 166, 118, 0.15);\\n  color: #AE9B66;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.category-meta[_ngcontent-%COMP%] {\\n  background: rgba(184, 166, 118, 0.2);\\n  color: #9B8A5A;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-actions[_ngcontent-%COMP%]   .create-date[_ngcontent-%COMP%] {\\n  color: #6C757D;\\n  font-size: 0.875rem;\\n  background: #F8F9FA;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 6px;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%] {\\n  padding: 1.25rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6C757D;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value.price-value[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1.1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value.quantity-value[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 1rem;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%]   .remark-content[_ngcontent-%COMP%] {\\n  background: #F8F9FA;\\n  border-left: 4px solid #B8A676;\\n  padding: 1rem;\\n  border-radius: 0 8px 8px 0;\\n}\\n.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%]   .remark-content[_ngcontent-%COMP%]   .remark-text[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-style: italic;\\n  line-height: 1.5;\\n}\\n\\n.template-viewer-footer[_ngcontent-%COMP%] {\\n  background: #F8F9FA;\\n  border-top: 1px solid #E9ECEF;\\n  padding: 1rem 1.5rem;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  background: #6C757D;\\n  color: #FFFFFF;\\n  border: none;\\n  border-radius: 8px;\\n  padding: 0.75rem 2rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: 0.3s ease;\\n}\\n.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background: #AE9B66;\\n  transform: translateY(-1px);\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  border: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: none;\\n  padding: 0.75rem 1rem;\\n  font-size: 0.95rem;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-style: italic;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  padding: 0.75rem 1rem;\\n  transition: all 0.2s ease;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n.search-results-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #007bff;\\n  padding-left: 0.75rem;\\n  background: #f8f9ff;\\n  border-radius: 4px;\\n}\\n\\n.pagination-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #28a745;\\n  padding-left: 0.75rem;\\n  background: #f8fff8;\\n  border-radius: 4px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  font-size: 0.9rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9ff;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  border-color: #f0f0f0;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto 1rem;\\n  opacity: 0.5;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: 0.5rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.template-detail-modal[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  background: #fff;\\n  border: 2px solid #e9ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  background: #f8f9ff;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  border-left: 4px solid #007bff;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9ff;\\n  border-radius: 6px;\\n  margin: 0 -0.5rem;\\n  padding-left: 0.5rem !important;\\n  padding-right: 0.5rem !important;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0.375rem 0.5rem;\\n  border-radius: 50%;\\n  min-width: 2rem;\\n  text-align: center;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n  word-break: break-word;\\n}\\n\\n.add-template-form[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .template-form[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e8ecef;\\n  border-radius: 12px;\\n  padding: 2rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .template-form[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  margin-left: 0.125rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  background: #ffffff;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .items-selector[_ngcontent-%COMP%] {\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  background: #f9fafb;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  padding: 2rem;\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n  padding: 0.875rem 1rem;\\n  cursor: pointer;\\n  margin: 0;\\n  width: 100%;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-top: 0.125rem;\\n  width: 1rem;\\n  height: 1rem;\\n  accent-color: #28a745;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.25rem;\\n  line-height: 1.4;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-desc[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 0.75rem;\\n  margin-top: 1.5rem;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%], \\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  padding: 0.625rem 1.25rem;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border: none;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  min-width: 80px;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%] {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover {\\n  background: #e5e7eb;\\n  transform: translateY(-1px);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.25);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n  color: #6c757d;\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  border-radius: 4px;\\n  margin: 0 0.125rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  color: #495057;\\n  transform: translateY(-1px);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  background-color: #fff;\\n  border-color: #dee2e6;\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n}\\n\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.8rem;\\n  border-radius: 3px;\\n  margin: 0 0.0625rem;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  border-color: #28a745;\\n  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  transition: background-color 0.2s;\\n}\\n.detail-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-name[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.95rem;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-remark[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n  border-left: 3px solid #dee2e6;\\n}\\n\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-radius: 0.25rem 0 0 0.25rem;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n  border-radius: 0 0.25rem 0.25rem 0;\\n}\\n.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_div_19_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "filteredTemplates", "length", "ɵɵtextInterpolate3", "templatePagination", "currentPage", "pageSize", "Math", "min", "totalItems", "ɵɵtextInterpolate2", "totalPages", "TemplateViewerComponent_div_22_div_3_button_13_Template_button_click_0_listener", "_r5", "tpl_r4", "$implicit", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_div_22_div_3_Template_button_click_9_listener", "_r3", "onSelectTemplate", "ɵɵtemplate", "TemplateViewerComponent_div_22_div_3_button_13_Template", "TemplateName", "Description", "ɵɵproperty", "TemplateViewerComponent_div_22_div_4_p_7_Template_a_click_2_listener", "_r6", "TemplateViewerComponent_div_22_div_4_i_3_Template", "TemplateViewerComponent_div_22_div_4_i_4_Template", "TemplateViewerComponent_div_22_div_4_p_7_Template", "TemplateViewerComponent_div_22_div_4_p_8_Template", "searchKeyword", "TemplateViewerComponent_div_22_div_5_li_13_Template_button_click_1_listener", "page_r9", "_r8", "goToTemplatePage", "ɵɵclassProp", "ɵɵtextInterpolate", "TemplateViewerComponent_div_22_div_5_Template_button_click_8_listener", "_r7", "TemplateViewerComponent_div_22_div_5_Template_button_click_11_listener", "TemplateViewerComponent_div_22_div_5_li_13_Template", "TemplateViewerComponent_div_22_div_5_Template_button_click_15_listener", "TemplateViewerComponent_div_22_div_5_Template_button_click_18_listener", "getTemplatePageNumbers", "TemplateViewerComponent_div_22_div_1_Template", "TemplateViewerComponent_div_22_div_3_Template", "TemplateViewerComponent_div_22_div_4_Template", "TemplateViewerComponent_div_22_div_5_Template", "paginatedTemplates", "trackByTemplateId", "selectedTemplate", "detailPagination", "detail_r11", "CGroupName", "CCategory", "ɵɵpipeBind1", "CUnitPrice", "CQuantity", "CUnit", "CRemark", "TemplateViewerComponent_div_23_div_19_div_1_div_21_div_2_Template", "TemplateViewerComponent_div_23_div_19_div_1_div_21_div_3_Template", "TemplateViewerComponent_div_23_div_19_div_1_div_21_div_4_Template", "TemplateViewerComponent_div_23_div_19_div_1_span_15_Template", "TemplateViewerComponent_div_23_div_19_div_1_span_16_Template", "TemplateViewerComponent_div_23_div_19_div_1_div_21_Template", "i_r12", "CReleateName", "CReleateId", "ɵɵpipeBind2", "CCreateDt", "TemplateViewerComponent_div_23_div_19_div_1_Template", "currentTemplateDetailsData", "i_r14", "detail_r13", "FieldName", "FieldValue", "TemplateViewerComponent_div_23_ng_template_20_div_0_div_1_Template", "paginatedDetails", "TemplateViewerComponent_div_23_ng_template_20_div_0_Template", "currentTemplateDetails", "noDetails_r15", "TemplateViewerComponent_div_23_div_22_li_6_Template_button_click_1_listener", "page_r18", "_r17", "goToDetailPage", "TemplateViewerComponent_div_23_div_22_Template_button_click_4_listener", "_r16", "TemplateViewerComponent_div_23_div_22_li_6_Template", "TemplateViewerComponent_div_23_div_22_Template_button_click_8_listener", "getDetailPageNumbers", "TemplateViewerComponent_div_23_Template_button_click_4_listener", "_r10", "closeTemplateDetail", "TemplateViewerComponent_div_23_p_10_Template", "TemplateViewerComponent_div_23_div_17_Template", "TemplateViewerComponent_div_23_div_19_Template", "TemplateViewerComponent_div_23_ng_template_20_Template", "ɵɵtemplateRefExtractor", "TemplateViewerComponent_div_23_div_22_Template", "TemplateViewerComponent_div_23_ng_template_23_Template", "checkOldDetails_r19", "TemplateViewerComponent", "constructor", "templateService", "templateType", "selectTemplate", "close", "templates", "templateDetails", "detailSearchKeyword", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "Date", "toLocaleDateString", "error", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "updateTemplatePagination", "ceil", "max", "updatePaginatedTemplates", "startIndex", "endIndex", "slice", "page", "pages", "startPage", "endPage", "i", "push", "onSearch", "emit", "loadTemplateDetails", "updateDetailPagination", "templateId", "pageIndex", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "allDetails", "CTemplateDetailId", "CSort", "undefined", "toISOString", "CCreator", "detail", "pagedDetails", "loadTemplateDetailsMock", "setTimeout", "mockDetails", "itemsPerTemplate", "String", "fromCharCode", "now", "random", "floor", "filteredDetails", "searchTemplateDetails", "clearDetailSearch", "details", "updatePaginatedDetails", "onClose", "templateID", "confirm", "deleteTemplateById", "deleteTemplateArgs", "apiTemplateDeleteTemplatePost$Json", "d", "index", "ɵɵdirectiveInject", "i1", "TemplateService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "TemplateViewerComponent_Template_input_ngModelChange_18_listener", "$event", "ɵɵtwoWayBindingSet", "TemplateViewerComponent_Template_input_input_18_listener", "TemplateViewerComponent_Template_input_keyup_enter_18_listener", "TemplateViewerComponent_div_19_Template", "TemplateViewerComponent_div_20_Template", "TemplateViewerComponent_div_21_Template", "TemplateViewerComponent_div_22_Template", "TemplateViewerComponent_div_23_Template", "TemplateViewerComponent_Template_button_click_26_listener", "ɵɵtwoWayProperty", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "DatePipe", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i4", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, GetTemplateByIdArgs, GetTemplateDetailByIdArgs } from 'src/services/api/models';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 公開Math對象供模板使用\r\n  Math = Math;\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = []; // 保留用於向後相容\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 新的詳情資料管理\r\n  currentTemplateDetailsData: TemplateDetailItem[] = [];\r\n  detailSearchKeyword = ''; // 明細專用搜尋關鍵字\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板列表\r\n  templatePagination = {\r\n    currentPage: 1,\r\n    pageSize: 10,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板詳情\r\n  detailPagination = {\r\n    currentPage: 1,\r\n    pageSize: 5,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedDetails: TemplateDetail[] = [];\r\n\r\n\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    this.loadTemplates();\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 載入模板列表 - 使用真實的 API 調用\r\n  loadTemplates() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      PageIndex: 1, // 暫時載入第一頁\r\n      PageSize: 100, // 暫時載入較多資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateList API\r\n    this.templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // API 調用成功\r\n\r\n          // 轉換 API 回應為內部格式\r\n          this.templates = response.Entries.map(item => ({\r\n            TemplateID: item.CTemplateId,\r\n            TemplateName: item.CTemplateName || '',\r\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\r\n          }));\r\n\r\n          // 更新過濾列表\r\n          this.updateFilteredTemplates();\r\n\r\n          // 清空詳情資料，改為按需載入\r\n          this.templateDetails = [];\r\n          this.currentTemplateDetailsData = [];\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n          this.templateDetails = [];\r\n          this.updateFilteredTemplates();\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n        this.templateDetails = [];\r\n        this.updateFilteredTemplates();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n    this.updateTemplatePagination();\r\n  }\r\n\r\n  // 更新模板分頁\r\n  updateTemplatePagination() {\r\n    this.templatePagination.totalItems = this.filteredTemplates.length;\r\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\r\n\r\n    // 確保當前頁面不超過總頁數\r\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\r\n    }\r\n\r\n    this.updatePaginatedTemplates();\r\n  }\r\n\r\n  // 更新分頁後的模板列表\r\n  updatePaginatedTemplates() {\r\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\r\n    const endIndex = startIndex + this.templatePagination.pageSize;\r\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 模板分頁導航\r\n  goToTemplatePage(page: number) {\r\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = page;\r\n      this.updatePaginatedTemplates();\r\n    }\r\n  }\r\n\r\n  // 獲取模板分頁頁碼數組\r\n  getTemplatePageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.templatePagination.totalPages;\r\n    const currentPage = this.templatePagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n\r\n    // 按需載入模板詳情\r\n    if (template.TemplateID) {\r\n      this.loadTemplateDetails(template.TemplateID);\r\n    }\r\n\r\n    this.updateDetailPagination();\r\n  }\r\n\r\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\r\n  loadTemplateDetails(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    // 調用真實的 GetTemplateDetailById API\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n\r\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\r\n          let allDetails = response.Entries.map(item => ({\r\n            CTemplateDetailId: item.CTemplateDetailId || 0,\r\n            CTemplateId: item.CTemplateId || templateId,\r\n            CReleateId: item.CReleateId || 0,\r\n            CReleateName: item.CReleateName || '',\r\n            CGroupName: item.CGroupName || '', // 新增群組名稱欄位\r\n            CSort: undefined,\r\n            CRemark: undefined,\r\n            CCreateDt: new Date().toISOString(),\r\n            CCreator: '系統',\r\n            CCategory: undefined,\r\n            CUnitPrice: undefined,\r\n            CQuantity: undefined,\r\n            CUnit: undefined\r\n          } as TemplateDetailItem));\r\n\r\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\r\n          if (searchKeyword && searchKeyword.trim()) {\r\n            allDetails = allDetails.filter(detail =>\r\n              detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n              (detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n            );\r\n          }\r\n\r\n          // 前端分頁處理 (因為 API 不支援分頁參數)\r\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n          const endIndex = startIndex + this.detailPagination.pageSize;\r\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\r\n\r\n          // 更新詳情資料和分頁資訊\r\n          this.currentTemplateDetailsData = pagedDetails;\r\n          this.detailPagination.totalItems = allDetails.length;\r\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\r\n          this.detailPagination.currentPage = pageIndex;\r\n        } else {\r\n          this.currentTemplateDetailsData = [];\r\n          this.detailPagination.totalItems = 0;\r\n          this.detailPagination.totalPages = 0;\r\n          this.detailPagination.currentPage = 1;\r\n        }\r\n      },\r\n      error: () => {\r\n        this.currentTemplateDetailsData = [];\r\n        this.detailPagination.totalItems = 0;\r\n        this.detailPagination.totalPages = 0;\r\n        this.detailPagination.currentPage = 1;\r\n\r\n        // 如果 API 失敗，回退到模擬資料\r\n        this.loadTemplateDetailsMock(templateId, pageIndex, searchKeyword);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 模擬載入模板詳情 (暫時使用，等待後端 API)\r\n  private loadTemplateDetailsMock(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    // 模擬 API 延遲\r\n    setTimeout(() => {\r\n      // 生成模擬詳情資料\r\n      const mockDetails: TemplateDetailItem[] = [];\r\n      const itemsPerTemplate = 8; // 每個模板8個項目\r\n\r\n      for (let i = 1; i <= itemsPerTemplate; i++) {\r\n        const detail: TemplateDetailItem = {\r\n          CTemplateDetailId: templateId * 100 + i,\r\n          CTemplateId: templateId,\r\n          CReleateId: templateId * 1000 + i,\r\n          CReleateName: `工程項目${String.fromCharCode(64 + (templateId % 26) + 1)}-${i}`,\r\n          CGroupName: i % 4 === 0 ? '結構工程' : (i % 4 === 1 ? '機電工程' : (i % 4 === 2 ? '裝修工程' : '其他工程')), // 新增群組名稱\r\n          CSort: i,\r\n          CRemark: i % 3 === 0 ? `備註說明 ${i}` : undefined,\r\n          CCreateDt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\r\n          CCreator: '系統管理員',\r\n          CCategory: i % 2 === 0 ? '基礎工程' : '進階工程',\r\n          CUnitPrice: Math.floor(Math.random() * 10000) + 1000,\r\n          CQuantity: Math.floor(Math.random() * 10) + 1,\r\n          CUnit: i % 3 === 0 ? '式' : (i % 3 === 1 ? '個' : 'm²')\r\n        };\r\n        mockDetails.push(detail);\r\n      }\r\n\r\n      // 搜尋篩選\r\n      let filteredDetails = mockDetails;\r\n      if (searchKeyword && searchKeyword.trim()) {\r\n        filteredDetails = mockDetails.filter(detail =>\r\n          detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n          (detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase())) ||\r\n          (detail.CRemark && detail.CRemark.toLowerCase().includes(searchKeyword.toLowerCase())) ||\r\n          (detail.CCategory && detail.CCategory.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n        );\r\n      }\r\n\r\n      // 分頁處理\r\n      const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n      const endIndex = startIndex + this.detailPagination.pageSize;\r\n      const pagedDetails = filteredDetails.slice(startIndex, endIndex);\r\n\r\n      // 更新資料\r\n      this.currentTemplateDetailsData = pagedDetails;\r\n      this.detailPagination.totalItems = filteredDetails.length;\r\n      this.detailPagination.totalPages = Math.ceil(filteredDetails.length / this.detailPagination.pageSize);\r\n      this.detailPagination.currentPage = pageIndex;\r\n    }, 300);\r\n  }\r\n\r\n  // 搜尋模板詳情 (明細專用搜尋)\r\n  searchTemplateDetails(keyword: string) {\r\n    this.detailSearchKeyword = keyword;\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\r\n    }\r\n  }\r\n\r\n  // 清除明細搜尋\r\n  clearDetailSearch() {\r\n    this.detailSearchKeyword = '';\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1);\r\n    }\r\n  }\r\n\r\n  // 更新詳情分頁 (保留向後相容)\r\n  updateDetailPagination() {\r\n    // 如果使用新的詳情資料，直接返回\r\n    if (this.currentTemplateDetailsData.length > 0) {\r\n      return;\r\n    }\r\n\r\n    // 向後相容：使用舊的詳情資料\r\n    const details = this.currentTemplateDetails;\r\n    this.detailPagination.totalItems = details.length;\r\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\r\n    this.detailPagination.currentPage = 1; // 重置到第一頁\r\n    this.updatePaginatedDetails();\r\n  }\r\n\r\n  // 更新分頁後的詳情列表\r\n  updatePaginatedDetails() {\r\n    const details = this.currentTemplateDetails;\r\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\r\n    const endIndex = startIndex + this.detailPagination.pageSize;\r\n    this.paginatedDetails = details.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 詳情分頁導航\r\n  goToDetailPage(page: number) {\r\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\r\n      // 如果使用新的詳情資料，重新載入\r\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\r\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page);\r\n      } else {\r\n        // 向後相容：使用舊的分頁邏輯\r\n        this.detailPagination.currentPage = page;\r\n        this.updatePaginatedDetails();\r\n      }\r\n    }\r\n  }\r\n\r\n  // 獲取詳情分頁頁碼數組\r\n  getDetailPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.detailPagination.totalPages;\r\n    const currentPage = this.detailPagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // 刪除模板 - 使用真實的 API 調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 準備 API 請求參數\r\n    const deleteTemplateArgs: GetTemplateByIdArgs = {\r\n      CTemplateId: templateID\r\n    };\r\n\r\n    // 調用 DeleteTemplate API\r\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\r\n      body: deleteTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          // 重新載入模板列表\r\n          this.loadTemplates();\r\n\r\n          // 如果當前查看的模板被刪除，關閉詳情\r\n          if (this.selectedTemplate?.TemplateID === templateID) {\r\n            this.selectedTemplate = null;\r\n          }\r\n        } else {\r\n          // API 返回錯誤\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n      }\r\n    });\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/Template/GetTemplateList\r\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/Template/SaveTemplate\r\n   *    請求體: {\r\n   *      CTemplateName: string,\r\n   *      CTemplateType: number,  // 1=客變需求\r\n   *      CStatus: number,\r\n   *      Details: [{\r\n   *        CReleateId: number,        // 關聯主檔ID\r\n   *        CReleateName: string       // 關聯名稱\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    POST /api/Template/DeleteTemplate\r\n   *    請求體: { CTemplateId: number }\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\r\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\r\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\r\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  CTemplateType: number; // 模板類型，1=客變需求\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n\r\n// 擴展的詳情項目結構 (基於 API 的 TemplateDetailItem 擴展)\r\nexport interface TemplateDetailItem {\r\n  CTemplateDetailId: number;\r\n  CTemplateId: number;\r\n  CReleateId: number;            // 關聯主檔ID\r\n  CReleateName: string;          // 關聯名稱\r\n  CGroupName?: string;           // 群組名稱 (API 新增欄位)\r\n  CSort?: number;                // 排序順序\r\n  CRemark?: string;              // 備註\r\n  CCreateDt: string;             // 建立時間\r\n  CCreator: string;              // 建立者\r\n  CUpdateDt?: string;            // 更新時間\r\n  CUpdator?: string;             // 更新者\r\n\r\n  // 業務相關欄位 (依需求添加，部分由 API 提供)\r\n  CUnitPrice?: number;           // 單價\r\n  CQuantity?: number;            // 數量\r\n  CUnit?: string;                // 單位\r\n  CCategory?: string;            // 分類\r\n}\r\n\r\n// 注意：GetTemplateDetailById API 使用的是 GetTemplateDetailByIdArgs 和 TemplateDetailItemListResponseBase\r\n// 這些 interface 已經在 API models 中定義，不需要重複定義\r\n", "<nb-card class=\"template-viewer-card\">\r\n  <nb-card-header class=\"template-viewer-header\">\r\n    <div class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"header-title\">\r\n        <h5 class=\"mb-0\">\r\n          <i class=\"fas fa-layer-group mr-2 text-primary\"></i>模板管理\r\n        </h5>\r\n        <small class=\"text-muted\">管理和查看客變需求模板</small>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <span class=\"badge badge-info\">{{ templatePagination.totalItems }} 個模板</span>\r\n      </div>\r\n    </div>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"template-viewer-body\">\r\n    <!-- 增強的搜尋功能 -->\r\n    <div class=\"enhanced-search-container mb-4\">\r\n      <div class=\"search-wrapper\">\r\n        <div class=\"search-input-group\">\r\n          <div class=\"search-icon\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </div>\r\n          <input type=\"text\" class=\"search-input\" placeholder=\"搜尋模板名稱、描述或關鍵字...\" [(ngModel)]=\"searchKeyword\"\r\n            (input)=\"onSearch()\" (keyup.enter)=\"onSearch()\">\r\n          <div class=\"search-actions\" *ngIf=\"searchKeyword\">\r\n            <button class=\"clear-search-btn\" type=\"button\" (click)=\"clearSearch()\" title=\"清除搜尋\">\r\n              <i class=\"fas fa-times\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"search-suggestions\" *ngIf=\"searchKeyword && filteredTemplates.length > 0\">\r\n          <small class=\"text-success\">\r\n            <i class=\"fas fa-check-circle mr-1\"></i>找到 {{ filteredTemplates.length }} 個相關模板\r\n          </small>\r\n        </div>\r\n        <div class=\"search-no-results\" *ngIf=\"searchKeyword && filteredTemplates.length === 0\">\r\n          <small class=\"text-warning\">\r\n            <i class=\"fas fa-exclamation-triangle mr-1\"></i>未找到符合條件的模板\r\n          </small>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 優化的模板列表（僅在未選擇模板細節時顯示） -->\r\n    <div class=\"template-list-container\" *ngIf=\"!selectedTemplate\">\r\n      <!-- 列表控制欄 -->\r\n      <div class=\"list-controls mb-3\" *ngIf=\"templatePagination.totalItems > 0\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"list-info\">\r\n            <span class=\"info-text\">\r\n              顯示第 {{ (templatePagination.currentPage - 1) * templatePagination.pageSize + 1 }} -\r\n              {{ Math.min(templatePagination.currentPage * templatePagination.pageSize, templatePagination.totalItems)\r\n              }} 項，\r\n              共 {{ templatePagination.totalItems }} 項模板\r\n            </span>\r\n          </div>\r\n          <div class=\"view-options\">\r\n            <small class=\"text-muted\">第 {{ templatePagination.currentPage }} / {{ templatePagination.totalPages }}\r\n              頁</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 卡片式模板列表 -->\r\n      <div class=\"template-cards-grid\">\r\n        <div class=\"template-card\" *ngFor=\"let tpl of paginatedTemplates; trackBy: trackByTemplateId\">\r\n          <div class=\"card-header\">\r\n            <div class=\"template-info\">\r\n              <h6 class=\"template-name\">\r\n                <i class=\"fas fa-file-alt mr-2 text-primary\"></i>\r\n                {{ tpl.TemplateName }}\r\n              </h6>\r\n              <p class=\"template-description\">\r\n                {{ tpl.Description || '無描述' }}\r\n              </p>\r\n            </div>\r\n            <div class=\"template-actions\">\r\n              <button class=\"action-btn view-btn\" (click)=\"onSelectTemplate(tpl)\" title=\"查看詳情\">\r\n                <i class=\"fas fa-eye\"></i>\r\n                <span>查看</span>\r\n              </button>\r\n              <button class=\"action-btn delete-btn\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n                *ngIf=\"tpl.TemplateID\" title=\"刪除模板\">\r\n                <i class=\"fas fa-trash\"></i>\r\n                <span>刪除</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空狀態 -->\r\n        <div class=\"empty-state-card\" *ngIf=\"!paginatedTemplates || paginatedTemplates.length === 0\">\r\n          <div class=\"empty-content\">\r\n            <div class=\"empty-icon\">\r\n              <i class=\"fas fa-search\" *ngIf=\"searchKeyword\"></i>\r\n              <i class=\"fas fa-folder-open\" *ngIf=\"!searchKeyword\"></i>\r\n            </div>\r\n            <h6 class=\"empty-title\">\r\n              {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}\r\n            </h6>\r\n            <p class=\"empty-description\" *ngIf=\"searchKeyword\">\r\n              請嘗試其他關鍵字或\r\n              <a href=\"javascript:void(0)\" (click)=\"clearSearch()\" class=\"clear-link\">清除搜尋條件</a>\r\n            </p>\r\n            <p class=\"empty-description\" *ngIf=\"!searchKeyword\">\r\n              目前還沒有建立任何模板，請先建立模板\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 優化的分頁控制器 -->\r\n      <div class=\"enhanced-pagination-container mt-4\" *ngIf=\"templatePagination.totalPages > 1\">\r\n        <div class=\"pagination-wrapper\">\r\n          <div class=\"pagination-info\">\r\n            <span class=\"page-info\">\r\n              第 {{ templatePagination.currentPage }} 頁，共 {{ templatePagination.totalPages }} 頁\r\n            </span>\r\n          </div>\r\n          <nav aria-label=\"模板列表分頁\" class=\"pagination-nav\">\r\n            <ul class=\"enhanced-pagination\">\r\n              <!-- 第一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === 1\">\r\n                <button class=\"page-btn first-page\" (click)=\"goToTemplatePage(1)\"\r\n                  [disabled]=\"templatePagination.currentPage === 1\" title=\"第一頁\">\r\n                  <i class=\"fas fa-angle-double-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 上一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === 1\">\r\n                <button class=\"page-btn prev-page\" (click)=\"goToTemplatePage(templatePagination.currentPage - 1)\"\r\n                  [disabled]=\"templatePagination.currentPage === 1\" title=\"上一頁\">\r\n                  <i class=\"fas fa-chevron-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 頁碼 -->\r\n              <li class=\"page-item\" *ngFor=\"let page of getTemplatePageNumbers()\"\r\n                [class.active]=\"page === templatePagination.currentPage\">\r\n                <button class=\"page-btn page-number\" (click)=\"goToTemplatePage(page)\">{{ page }}</button>\r\n              </li>\r\n\r\n              <!-- 下一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n                <button class=\"page-btn next-page\" (click)=\"goToTemplatePage(templatePagination.currentPage + 1)\"\r\n                  [disabled]=\"templatePagination.currentPage === templatePagination.totalPages\" title=\"下一頁\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 最後一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n                <button class=\"page-btn last-page\" (click)=\"goToTemplatePage(templatePagination.totalPages)\"\r\n                  [disabled]=\"templatePagination.currentPage === templatePagination.totalPages\" title=\"最後一頁\">\r\n                  <i class=\"fas fa-angle-double-right\"></i>\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 優化的模板詳情視圖 -->\r\n    <div *ngIf=\"selectedTemplate\" class=\"template-detail-view\">\r\n      <!-- 詳情標題欄 -->\r\n      <div class=\"detail-header\">\r\n        <div class=\"detail-title-section\">\r\n          <div class=\"back-button\">\r\n            <button class=\"back-btn\" (click)=\"closeTemplateDetail()\" title=\"返回模板列表\">\r\n              <i class=\"fas fa-arrow-left\"></i>\r\n            </button>\r\n          </div>\r\n          <div class=\"detail-title-info\">\r\n            <h5 class=\"detail-title\">\r\n              <i class=\"fas fa-file-alt mr-2 text-primary\"></i>\r\n              {{ selectedTemplate!.TemplateName }}\r\n            </h5>\r\n            <p class=\"detail-subtitle\" *ngIf=\"selectedTemplate.Description\">\r\n              {{ selectedTemplate.Description }}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div class=\"detail-stats\">\r\n          <div class=\"stat-item\">\r\n            <span class=\"stat-label\">項目數量</span>\r\n            <span class=\"stat-value\">{{ currentTemplateDetailsData.length > 0 ? detailPagination.totalItems :\r\n              currentTemplateDetails.length }}</span>\r\n          </div>\r\n          <div class=\"stat-item\" *ngIf=\"detailPagination.totalPages > 1\">\r\n            <span class=\"stat-label\">頁數</span>\r\n            <span class=\"stat-value\">{{ detailPagination.currentPage }} / {{ detailPagination.totalPages }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 詳情內容區域 -->\r\n      <div class=\"detail-content\">\r\n\r\n\r\n        <!-- 優化的詳情項目顯示 -->\r\n        <div *ngIf=\"currentTemplateDetailsData.length > 0; else checkOldDetails\" class=\"enhanced-detail-list\">\r\n          <div *ngFor=\"let detail of currentTemplateDetailsData; let i = index\" class=\"enhanced-detail-item\">\r\n            <div class=\"detail-item-card\">\r\n              <div class=\"detail-item-header\">\r\n                <div class=\"item-index\">\r\n                  <span class=\"index-badge\">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i + 1\r\n                    }}</span>\r\n                </div>\r\n                <div class=\"item-main-info\">\r\n                  <h6 class=\"item-name\">\r\n                    <i class=\"fas fa-cog mr-2 text-secondary\"></i>\r\n                    {{ detail.CReleateName }}\r\n                  </h6>\r\n                  <div class=\"item-meta\">\r\n                    <span class=\"meta-item id-meta\">\r\n                      <i class=\"fas fa-hashtag\"></i>\r\n                      <span>{{ detail.CReleateId }}</span>\r\n                    </span>\r\n                    <span class=\"meta-item group-meta\" *ngIf=\"detail.CGroupName\">\r\n                      <i class=\"fas fa-layer-group\"></i>\r\n                      <span>{{ detail.CGroupName }}</span>\r\n                    </span>\r\n                    <span class=\"meta-item category-meta\" *ngIf=\"detail.CCategory\">\r\n                      <i class=\"fas fa-tag\"></i>\r\n                      <span>{{ detail.CCategory }}</span>\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item-actions\">\r\n                  <span class=\"create-date\">{{ detail.CCreateDt | date:'MM/dd' }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"detail-item-body\"\r\n                *ngIf=\"detail.CUnitPrice || detail.CQuantity || detail.CUnit || detail.CRemark\">\r\n                <div class=\"item-details-grid\">\r\n                  <div class=\"detail-group price-group\" *ngIf=\"detail.CUnitPrice\">\r\n                    <span class=\"detail-label\">單價</span>\r\n                    <span class=\"detail-value price-value\">NT$ {{ detail.CUnitPrice | number }}</span>\r\n                  </div>\r\n                  <div class=\"detail-group quantity-group\" *ngIf=\"detail.CQuantity && detail.CUnit\">\r\n                    <span class=\"detail-label\">數量</span>\r\n                    <span class=\"detail-value quantity-value\">{{ detail.CQuantity }} {{ detail.CUnit }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item-remark\" *ngIf=\"detail.CRemark\">\r\n                  <div class=\"remark-content\">\r\n                    <i class=\"fas fa-comment-alt mr-2 text-muted\"></i>\r\n                    <span class=\"remark-text\">{{ detail.CRemark }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 向後相容：舊的詳情資料顯示 -->\r\n        <ng-template #checkOldDetails>\r\n          <div *ngIf=\"currentTemplateDetails.length > 0; else noDetails\" class=\"detail-list\">\r\n            <div *ngFor=\"let detail of paginatedDetails; let i = index\"\r\n              class=\"detail-item d-flex align-items-center py-2 border-bottom\">\r\n              <div class=\"detail-index\">\r\n                <span class=\"badge badge-light\">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i\r\n                  + 1 }}</span>\r\n              </div>\r\n              <div class=\"detail-content flex-grow-1 ml-2\">\r\n                <div class=\"detail-field\">\r\n                  <strong>{{ detail.FieldName }}:</strong>\r\n                </div>\r\n                <div class=\"detail-value text-muted\">\r\n                  {{ detail.FieldValue }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-template>\r\n\r\n        <!-- 詳情分頁控制器 -->\r\n        <div class=\"detail-pagination mt-3\" *ngIf=\"detailPagination.totalPages > 1\">\r\n          <nav aria-label=\"模板詳情分頁\">\r\n            <ul class=\"pagination pagination-sm justify-content-center mb-0\">\r\n              <!-- 上一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === 1\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage - 1)\"\r\n                  [disabled]=\"detailPagination.currentPage === 1\">\r\n                  <i class=\"fas fa-chevron-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 頁碼 -->\r\n              <li class=\"page-item\" *ngFor=\"let page of getDetailPageNumbers()\"\r\n                [class.active]=\"page === detailPagination.currentPage\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(page)\">{{ page }}</button>\r\n              </li>\r\n\r\n              <!-- 下一頁 -->\r\n              <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage + 1)\"\r\n                  [disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                  <i class=\"fas fa-chevron-right\"></i>\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n\r\n        <ng-template #noDetails>\r\n          <div class=\"text-center py-3\">\r\n            <i class=\"fas fa-inbox fa-2x text-muted mb-2\"></i>\r\n            <p class=\"text-muted mb-0\">此模板暫無內容</p>\r\n          </div>\r\n        </ng-template>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"template-viewer-footer\">\r\n    <div class=\"footer-actions\">\r\n      <button class=\"close-btn\" (click)=\"onClose()\">\r\n        <i class=\"fas fa-times mr-2\"></i>關閉\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;;;;;;;;;ICsBjDC,EADF,CAAAC,cAAA,cAAkD,iBACoC;IAArCD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACpET,EAAA,CAAAU,SAAA,YAA4B;IAEhCV,EADE,CAAAW,YAAA,EAAS,EACL;;;;;IAGNX,EADF,CAAAC,cAAA,cAAsF,gBACxD;IAC1BD,EAAA,CAAAU,SAAA,YAAwC;IAAAV,EAAA,CAAAY,MAAA,GAC1C;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;IAFsCX,EAAA,CAAAa,SAAA,GAC1C;IAD0Cb,EAAA,CAAAc,kBAAA,kBAAAR,MAAA,CAAAS,iBAAA,CAAAC,MAAA,qCAC1C;;;;;IAGAhB,EADF,CAAAC,cAAA,cAAuF,gBACzD;IAC1BD,EAAA,CAAAU,SAAA,YAAgD;IAAAV,EAAA,CAAAY,MAAA,oEAClD;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;;IAYFX,EAHN,CAAAC,cAAA,cAA0E,aACT,cACtC,eACG;IACtBD,EAAA,CAAAY,MAAA,GAIF;IACFZ,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAC,cAAA,cAA0B,eACE;IAAAD,EAAA,CAAAY,MAAA,GACvB;IAGTZ,EAHS,CAAAW,YAAA,EAAQ,EACP,EACF,EACF;;;;IAXEX,EAAA,CAAAa,SAAA,GAIF;IAJEb,EAAA,CAAAiB,kBAAA,0BAAAX,MAAA,CAAAY,kBAAA,CAAAC,WAAA,QAAAb,MAAA,CAAAY,kBAAA,CAAAE,QAAA,aAAAd,MAAA,CAAAe,IAAA,CAAAC,GAAA,CAAAhB,MAAA,CAAAY,kBAAA,CAAAC,WAAA,GAAAb,MAAA,CAAAY,kBAAA,CAAAE,QAAA,EAAAd,MAAA,CAAAY,kBAAA,CAAAK,UAAA,4BAAAjB,MAAA,CAAAY,kBAAA,CAAAK,UAAA,yBAIF;IAG0BvB,EAAA,CAAAa,SAAA,GACvB;IADuBb,EAAA,CAAAwB,kBAAA,YAAAlB,MAAA,CAAAY,kBAAA,CAAAC,WAAA,SAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,YACvB;;;;;;IAuBDzB,EAAA,CAAAC,cAAA,iBACsC;IADAD,EAAA,CAAAE,UAAA,mBAAAwB,gFAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAAC,MAAA,GAAA5B,EAAA,CAAAO,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAoB,MAAA,CAAAE,UAAA,IAA2BxB,MAAA,CAAAyB,gBAAA,CAAAH,MAAA,CAAAE,UAAA,CAAgC;IAAA,EAAC;IAEhG9B,EAAA,CAAAU,SAAA,YAA4B;IAC5BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IACVZ,EADU,CAAAW,YAAA,EAAO,EACR;;;;;;IAjBTX,EAHN,CAAAC,cAAA,cAA8F,cACnE,cACI,aACC;IACxBD,EAAA,CAAAU,SAAA,YAAiD;IACjDV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,YAAgC;IAC9BD,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAI,EACA;IAEJX,EADF,CAAAC,cAAA,cAA8B,iBACqD;IAA7CD,EAAA,CAAAE,UAAA,mBAAA8B,sEAAA;MAAA,MAAAJ,MAAA,GAAA5B,EAAA,CAAAI,aAAA,CAAA6B,GAAA,EAAAJ,SAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4B,gBAAA,CAAAN,MAAA,CAAqB;IAAA,EAAC;IACjE5B,EAAA,CAAAU,SAAA,aAA0B;IAC1BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,oBAAE;IACVZ,EADU,CAAAW,YAAA,EAAO,EACR;IACTX,EAAA,CAAAmC,UAAA,KAAAC,uDAAA,qBACsC;IAM5CpC,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;IAlBEX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAc,MAAA,CAAAS,YAAA,MACF;IAEErC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAc,MAAA,CAAAU,WAAA,8BACF;IAQGtC,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAuC,UAAA,SAAAX,MAAA,CAAAE,UAAA,CAAoB;;;;;IAYvB9B,EAAA,CAAAU,SAAA,YAAmD;;;;;IACnDV,EAAA,CAAAU,SAAA,YAAyD;;;;;;IAK3DV,EAAA,CAAAC,cAAA,YAAmD;IACjDD,EAAA,CAAAY,MAAA,+DACA;IAAAZ,EAAA,CAAAC,cAAA,YAAwE;IAA3CD,EAAA,CAAAE,UAAA,mBAAAsC,qEAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAAoBT,EAAA,CAAAY,MAAA,2CAAM;IAChFZ,EADgF,CAAAW,YAAA,EAAI,EAChF;;;;;IACJX,EAAA,CAAAC,cAAA,YAAoD;IAClDD,EAAA,CAAAY,MAAA,qHACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IAbJX,EAFJ,CAAAC,cAAA,cAA6F,cAChE,cACD;IAEtBD,EADA,CAAAmC,UAAA,IAAAO,iDAAA,gBAA+C,IAAAC,iDAAA,gBACM;IACvD3C,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,aAAwB;IACtBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAKLX,EAJA,CAAAmC,UAAA,IAAAS,iDAAA,gBAAmD,IAAAC,iDAAA,gBAIC;IAIxD7C,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAd0BX,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAwC,aAAA,CAAmB;IACd9C,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAAuC,UAAA,UAAAjC,MAAA,CAAAwC,aAAA,CAAoB;IAGnD9C,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAAwC,aAAA,oGACF;IAC8B9C,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAwC,aAAA,CAAmB;IAInB9C,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAAuC,UAAA,UAAAjC,MAAA,CAAAwC,aAAA,CAAoB;;;;;;IAoC9C9C,EAFF,CAAAC,cAAA,aAC2D,iBACa;IAAjCD,EAAA,CAAAE,UAAA,mBAAA6C,4EAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAAI,aAAA,CAAA6C,GAAA,EAAApB,SAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,gBAAA,CAAAF,OAAA,CAAsB;IAAA,EAAC;IAAChD,EAAA,CAAAY,MAAA,GAAU;IAClFZ,EADkF,CAAAW,YAAA,EAAS,EACtF;;;;;IAFHX,EAAA,CAAAmD,WAAA,WAAAH,OAAA,KAAA1C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,CAAwD;IACcnB,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAAoD,iBAAA,CAAAJ,OAAA,CAAU;;;;;;IAzBpFhD,EAHN,CAAAC,cAAA,cAA0F,cACxD,cACD,eACH;IACtBD,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAO,EACH;IAKAX,EAJN,CAAAC,cAAA,cAAgD,aACd,aAEgD,iBAEZ;IAD5BD,EAAA,CAAAE,UAAA,mBAAAmD,sEAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,gBAAA,CAAiB,CAAC,CAAC;IAAA,EAAC;IAE/DlD,EAAA,CAAAU,SAAA,YAAwC;IAE5CV,EADE,CAAAW,YAAA,EAAS,EACN;IAIHX,EADF,CAAAC,cAAA,cAA8E,kBAEZ;IAD7BD,EAAA,CAAAE,UAAA,mBAAAqD,uEAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,gBAAA,CAAA5C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAE/FnB,EAAA,CAAAU,SAAA,aAAmC;IAEvCV,EADE,CAAAW,YAAA,EAAS,EACN;IAGLX,EAAA,CAAAmC,UAAA,KAAAqB,mDAAA,iBAC2D;IAMzDxD,EADF,CAAAC,cAAA,cAA0G,kBAEZ;IADzDD,EAAA,CAAAE,UAAA,mBAAAuD,uEAAA;MAAAzD,EAAA,CAAAI,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,gBAAA,CAAA5C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAE/FnB,EAAA,CAAAU,SAAA,aAAoC;IAExCV,EADE,CAAAW,YAAA,EAAS,EACN;IAIHX,EADF,CAAAC,cAAA,cAA0G,kBAEX;IAD1DD,EAAA,CAAAE,UAAA,mBAAAwD,uEAAA;MAAA1D,EAAA,CAAAI,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,gBAAA,CAAA5C,MAAA,CAAAY,kBAAA,CAAAO,UAAA,CAA+C;IAAA,EAAC;IAE1FzB,EAAA,CAAAU,SAAA,aAAyC;IAMrDV,EALU,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACF,EACF;;;;IA7CEX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAwB,kBAAA,aAAAlB,MAAA,CAAAY,kBAAA,CAAAC,WAAA,0BAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,aACF;IAKwBzB,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,OAAuD;IAEzEnB,EAAA,CAAAa,SAAA,EAAiD;IAAjDb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAAY,kBAAA,CAAAC,WAAA,OAAiD;IAM/BnB,EAAA,CAAAa,SAAA,GAAuD;IAAvDb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,OAAuD;IAEzEnB,EAAA,CAAAa,SAAA,EAAiD;IAAjDb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAAY,kBAAA,CAAAC,WAAA,OAAiD;IAMdnB,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAuC,UAAA,YAAAjC,MAAA,CAAAqD,sBAAA,GAA2B;IAM5C3D,EAAA,CAAAa,SAAA,EAAmF;IAAnFb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,KAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,CAAmF;IAErGzB,EAAA,CAAAa,SAAA,EAA6E;IAA7Eb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAAY,kBAAA,CAAAC,WAAA,KAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,CAA6E;IAM3DzB,EAAA,CAAAa,SAAA,GAAmF;IAAnFb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAAY,kBAAA,CAAAC,WAAA,KAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,CAAmF;IAErGzB,EAAA,CAAAa,SAAA,EAA6E;IAA7Eb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAAY,kBAAA,CAAAC,WAAA,KAAAb,MAAA,CAAAY,kBAAA,CAAAO,UAAA,CAA6E;;;;;IA9G3FzB,EAAA,CAAAC,cAAA,cAA+D;IAE7DD,EAAA,CAAAmC,UAAA,IAAAyB,6CAAA,kBAA0E;IAkB1E5D,EAAA,CAAAC,cAAA,cAAiC;IA2B/BD,EA1BA,CAAAmC,UAAA,IAAA0B,6CAAA,mBAA8F,IAAAC,6CAAA,kBA0BD;IAkB/F9D,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAmC,UAAA,IAAA4B,6CAAA,oBAA0F;IAkD5F/D,EAAA,CAAAW,YAAA,EAAM;;;;IApH6BX,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAY,kBAAA,CAAAK,UAAA,KAAuC;IAmB3BvB,EAAA,CAAAa,SAAA,GAAuB;IAAAb,EAAvB,CAAAuC,UAAA,YAAAjC,MAAA,CAAA0D,kBAAA,CAAuB,iBAAA1D,MAAA,CAAA2D,iBAAA,CAA0B;IA0B7DjE,EAAA,CAAAa,SAAA,EAA4D;IAA5Db,EAAA,CAAAuC,UAAA,UAAAjC,MAAA,CAAA0D,kBAAA,IAAA1D,MAAA,CAAA0D,kBAAA,CAAAhD,MAAA,OAA4D;IAqB5ChB,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAAY,kBAAA,CAAAO,UAAA,KAAuC;;;;;IAmElFzB,EAAA,CAAAC,cAAA,aAAgE;IAC9DD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;IADFX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAA4D,gBAAA,CAAA5B,WAAA,MACF;;;;;IAUAtC,EADF,CAAAC,cAAA,cAA+D,eACpC;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAClCX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAY,MAAA,GAAsE;IACjGZ,EADiG,CAAAW,YAAA,EAAO,EAClG;;;;IADqBX,EAAA,CAAAa,SAAA,GAAsE;IAAtEb,EAAA,CAAAwB,kBAAA,KAAAlB,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,SAAAb,MAAA,CAAA6D,gBAAA,CAAA1C,UAAA,KAAsE;;;;;IA4BvFzB,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAU,SAAA,aAAkC;IAClCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IAC/BZ,EAD+B,CAAAW,YAAA,EAAO,EAC/B;;;;IADCX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAoD,iBAAA,CAAAgB,UAAA,CAAAC,UAAA,CAAuB;;;;;IAE/BrE,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAU,SAAA,aAA0B;IAC1BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAC9BZ,EAD8B,CAAAW,YAAA,EAAO,EAC9B;;;;IADCX,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAoD,iBAAA,CAAAgB,UAAA,CAAAE,SAAA,CAAsB;;;;;IAa9BtE,EADF,CAAAC,cAAA,eAAgE,gBACnC;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACpCX,EAAA,CAAAC,cAAA,gBAAuC;IAAAD,EAAA,CAAAY,MAAA,GAAoC;;IAC7EZ,EAD6E,CAAAW,YAAA,EAAO,EAC9E;;;;IADmCX,EAAA,CAAAa,SAAA,GAAoC;IAApCb,EAAA,CAAAc,kBAAA,SAAAd,EAAA,CAAAuE,WAAA,OAAAH,UAAA,CAAAI,UAAA,MAAoC;;;;;IAG3ExE,EADF,CAAAC,cAAA,eAAkF,gBACrD;IAAAD,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACpCX,EAAA,CAAAC,cAAA,gBAA0C;IAAAD,EAAA,CAAAY,MAAA,GAAyC;IACrFZ,EADqF,CAAAW,YAAA,EAAO,EACtF;;;;IADsCX,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAwB,kBAAA,KAAA4C,UAAA,CAAAK,SAAA,OAAAL,UAAA,CAAAM,KAAA,KAAyC;;;;;IAIrF1E,EADF,CAAAC,cAAA,eAAgD,eAClB;IAC1BD,EAAA,CAAAU,SAAA,aAAkD;IAClDV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IAElDZ,EAFkD,CAAAW,YAAA,EAAO,EACjD,EACF;;;;IAFwBX,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAoD,iBAAA,CAAAgB,UAAA,CAAAO,OAAA,CAAoB;;;;;IAblD3E,EAFF,CAAAC,cAAA,eACkF,eACjD;IAK7BD,EAJA,CAAAmC,UAAA,IAAAyC,iEAAA,mBAAgE,IAAAC,iEAAA,mBAIkB;IAIpF7E,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAmC,UAAA,IAAA2C,iEAAA,mBAAgD;IAMlD9E,EAAA,CAAAW,YAAA,EAAM;;;;IAfqCX,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAuC,UAAA,SAAA6B,UAAA,CAAAI,UAAA,CAAuB;IAIpBxE,EAAA,CAAAa,SAAA,EAAsC;IAAtCb,EAAA,CAAAuC,UAAA,SAAA6B,UAAA,CAAAK,SAAA,IAAAL,UAAA,CAAAM,KAAA,CAAsC;IAKxD1E,EAAA,CAAAa,SAAA,EAAoB;IAApBb,EAAA,CAAAuC,UAAA,SAAA6B,UAAA,CAAAO,OAAA,CAAoB;;;;;IAxC5C3E,EAJR,CAAAC,cAAA,eAAmG,eACnE,eACI,eACN,gBACI;IAAAD,EAAA,CAAAY,MAAA,GACtB;IACNZ,EADM,CAAAW,YAAA,EAAO,EACP;IAEJX,EADF,CAAAC,cAAA,eAA4B,cACJ;IACpBD,EAAA,CAAAU,SAAA,aAA8C;IAC9CV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAEHX,EADF,CAAAC,cAAA,gBAAuB,iBACW;IAC9BD,EAAA,CAAAU,SAAA,cAA8B;IAC9BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,IAAuB;IAC/BZ,EAD+B,CAAAW,YAAA,EAAO,EAC/B;IAKPX,EAJA,CAAAmC,UAAA,KAAA4C,4DAAA,oBAA6D,KAAAC,4DAAA,oBAIE;IAKnEhF,EADE,CAAAW,YAAA,EAAM,EACF;IAEJX,EADF,CAAAC,cAAA,gBAA0B,iBACE;IAAAD,EAAA,CAAAY,MAAA,IAAqC;;IAEnEZ,EAFmE,CAAAW,YAAA,EAAO,EAClE,EACF;IAENX,EAAA,CAAAmC,UAAA,KAAA8C,2DAAA,mBACkF;IAmBtFjF,EADE,CAAAW,YAAA,EAAM,EACF;;;;;;IAhD4BX,EAAA,CAAAa,SAAA,GACtB;IADsBb,EAAA,CAAAoD,iBAAA,EAAA9C,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,QAAAb,MAAA,CAAA6D,gBAAA,CAAA/C,QAAA,GAAA8D,KAAA,KACtB;IAKFlF,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAsD,UAAA,CAAAe,YAAA,MACF;IAIUnF,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAoD,iBAAA,CAAAgB,UAAA,CAAAgB,UAAA,CAAuB;IAEKpF,EAAA,CAAAa,SAAA,EAAuB;IAAvBb,EAAA,CAAAuC,UAAA,SAAA6B,UAAA,CAAAC,UAAA,CAAuB;IAIpBrE,EAAA,CAAAa,SAAA,EAAsB;IAAtBb,EAAA,CAAAuC,UAAA,SAAA6B,UAAA,CAAAE,SAAA,CAAsB;IAOrCtE,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAoD,iBAAA,CAAApD,EAAA,CAAAqF,WAAA,QAAAjB,UAAA,CAAAkB,SAAA,WAAqC;IAKhEtF,EAAA,CAAAa,SAAA,GAA6E;IAA7Eb,EAAA,CAAAuC,UAAA,SAAA6B,UAAA,CAAAI,UAAA,IAAAJ,UAAA,CAAAK,SAAA,IAAAL,UAAA,CAAAM,KAAA,IAAAN,UAAA,CAAAO,OAAA,CAA6E;;;;;IAlCtF3E,EAAA,CAAAC,cAAA,eAAsG;IACpGD,EAAA,CAAAmC,UAAA,IAAAoD,oDAAA,qBAAmG;IAqDrGvF,EAAA,CAAAW,YAAA,EAAM;;;;IArDoBX,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAAuC,UAAA,YAAAjC,MAAA,CAAAkF,0BAAA,CAA+B;;;;;IA6DjDxF,EAHJ,CAAAC,cAAA,eACmE,eACvC,gBACQ;IAAAD,EAAA,CAAAY,MAAA,GACxB;IACVZ,EADU,CAAAW,YAAA,EAAO,EACX;IAGFX,EAFJ,CAAAC,cAAA,eAA6C,eACjB,aAChB;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IACjCZ,EADiC,CAAAW,YAAA,EAAS,EACpC;IACNX,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAY,MAAA,GACF;IAEJZ,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;;;IAX8BX,EAAA,CAAAa,SAAA,GACxB;IADwBb,EAAA,CAAAoD,iBAAA,EAAA9C,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,QAAAb,MAAA,CAAA6D,gBAAA,CAAA/C,QAAA,GAAAqE,KAAA,KACxB;IAIEzF,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,kBAAA,KAAA4E,UAAA,CAAAC,SAAA,MAAuB;IAG/B3F,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA4E,UAAA,CAAAE,UAAA,MACF;;;;;IAbN5F,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAmC,UAAA,IAAA0D,kEAAA,oBACmE;IAcrE7F,EAAA,CAAAW,YAAA,EAAM;;;;IAfoBX,EAAA,CAAAa,SAAA,EAAqB;IAArBb,EAAA,CAAAuC,UAAA,YAAAjC,MAAA,CAAAwF,gBAAA,CAAqB;;;;;IAD/C9F,EAAA,CAAAmC,UAAA,IAAA4D,4DAAA,mBAAmF;;;;;;IAApC/F,EAAzC,CAAAuC,UAAA,SAAAjC,MAAA,CAAA0F,sBAAA,CAAAhF,MAAA,KAAyC,aAAAiF,aAAA,CAAc;;;;;;IAkCvDjG,EAFF,CAAAC,cAAA,aACyD,kBACE;IAA/BD,EAAA,CAAAE,UAAA,mBAAAgG,4EAAA;MAAA,MAAAC,QAAA,GAAAnG,EAAA,CAAAI,aAAA,CAAAgG,IAAA,EAAAvE,SAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+F,cAAA,CAAAF,QAAA,CAAoB;IAAA,EAAC;IAACnG,EAAA,CAAAY,MAAA,GAAU;IACrEZ,EADqE,CAAAW,YAAA,EAAS,EACzE;;;;;IAFHX,EAAA,CAAAmD,WAAA,WAAAgD,QAAA,KAAA7F,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,CAAsD;IACGnB,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAAoD,iBAAA,CAAA+C,QAAA,CAAU;;;;;;IATnEnG,EALR,CAAAC,cAAA,eAA4E,eACjD,cAC0C,aAEa,kBAExB;IADxBD,EAAA,CAAAE,UAAA,mBAAAoG,uEAAA;MAAAtG,EAAA,CAAAI,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+F,cAAA,CAAA/F,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElFnB,EAAA,CAAAU,SAAA,YAAmC;IAEvCV,EADE,CAAAW,YAAA,EAAS,EACN;IAGLX,EAAA,CAAAmC,UAAA,IAAAqE,mDAAA,iBACyD;IAMvDxG,EADF,CAAAC,cAAA,aAAsG,kBAExB;IADlDD,EAAA,CAAAE,UAAA,mBAAAuG,uEAAA;MAAAzG,EAAA,CAAAI,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+F,cAAA,CAAA/F,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElFnB,EAAA,CAAAU,SAAA,YAAoC;IAK9CV,EAJQ,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAtBsBX,EAAA,CAAAa,SAAA,GAAqD;IAArDb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,OAAqD;IAEvEnB,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,OAA+C;IAMZnB,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAuC,UAAA,YAAAjC,MAAA,CAAAoG,oBAAA,GAAyB;IAM1C1G,EAAA,CAAAa,SAAA,EAA+E;IAA/Eb,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,KAAAb,MAAA,CAAA6D,gBAAA,CAAA1C,UAAA,CAA+E;IAEjGzB,EAAA,CAAAa,SAAA,EAAyE;IAAzEb,EAAA,CAAAuC,UAAA,aAAAjC,MAAA,CAAA6D,gBAAA,CAAAhD,WAAA,KAAAb,MAAA,CAAA6D,gBAAA,CAAA1C,UAAA,CAAyE;;;;;IASjFzB,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAU,SAAA,aAAkD;IAClDV,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAY,MAAA,iDAAO;IACpCZ,EADoC,CAAAW,YAAA,EAAI,EAClC;;;;;;IA9IJX,EALR,CAAAC,cAAA,cAA2D,cAE9B,cACS,cACP,iBACiD;IAA/CD,EAAA,CAAAE,UAAA,mBAAAyG,gEAAA;MAAA3G,EAAA,CAAAI,aAAA,CAAAwG,IAAA;MAAA,MAAAtG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuG,mBAAA,EAAqB;IAAA,EAAC;IACtD7G,EAAA,CAAAU,SAAA,YAAiC;IAErCV,EADE,CAAAW,YAAA,EAAS,EACL;IAEJX,EADF,CAAAC,cAAA,cAA+B,aACJ;IACvBD,EAAA,CAAAU,SAAA,YAAiD;IACjDV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAmC,UAAA,KAAA2E,4CAAA,gBAAgE;IAIpE9G,EADE,CAAAW,YAAA,EAAM,EACF;IAGFX,EAFJ,CAAAC,cAAA,eAA0B,eACD,gBACI;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACpCX,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAY,MAAA,IACS;IACpCZ,EADoC,CAAAW,YAAA,EAAO,EACrC;IACNX,EAAA,CAAAmC,UAAA,KAAA4E,8CAAA,kBAA+D;IAKnE/G,EADE,CAAAW,YAAA,EAAM,EACF;IAGNX,EAAA,CAAAC,cAAA,eAA4B;IA8G1BD,EA1GA,CAAAmC,UAAA,KAAA6E,8CAAA,mBAAsG,KAAAC,sDAAA,gCAAAjH,EAAA,CAAAkH,sBAAA,CAyDxE,KAAAC,8CAAA,oBAqB8C,KAAAC,sDAAA,gCAAApH,EAAA,CAAAkH,sBAAA,CA4BpD;IAO5BlH,EADE,CAAAW,YAAA,EAAM,EACF;;;;;IA1IIX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAR,MAAA,CAAA4D,gBAAA,CAAA7B,YAAA,MACF;IAC4BrC,EAAA,CAAAa,SAAA,EAAkC;IAAlCb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAA4D,gBAAA,CAAA5B,WAAA,CAAkC;IAQrCtC,EAAA,CAAAa,SAAA,GACS;IADTb,EAAA,CAAAoD,iBAAA,CAAA9C,MAAA,CAAAkF,0BAAA,CAAAxE,MAAA,OAAAV,MAAA,CAAA6D,gBAAA,CAAA5C,UAAA,GAAAjB,MAAA,CAAA0F,sBAAA,CAAAhF,MAAA,CACS;IAEZhB,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAA6D,gBAAA,CAAA1C,UAAA,KAAqC;IAYzDzB,EAAA,CAAAa,SAAA,GAA6C;IAAAb,EAA7C,CAAAuC,UAAA,SAAAjC,MAAA,CAAAkF,0BAAA,CAAAxE,MAAA,KAA6C,aAAAqG,mBAAA,CAAoB;IA8ElCrH,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAuC,UAAA,SAAAjC,MAAA,CAAA6D,gBAAA,CAAA1C,UAAA,KAAqC;;;AD5QlF,OAAM,MAAO6F,uBAAuB;EAyClCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAxC1B,KAAAC,YAAY,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAC,cAAc,GAAG,IAAI/H,YAAY,EAAY;IAC7C,KAAAgI,KAAK,GAAG,IAAIhI,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAA0B,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAuG,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE,CAAC,CAAC;IACxC,KAAA3D,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAsB,0BAA0B,GAAyB,EAAE;IACrD,KAAAsC,mBAAmB,GAAG,EAAE,CAAC,CAAC;IAE1B;IACA,KAAAhF,aAAa,GAAG,EAAE;IAClB,KAAA/B,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAG,kBAAkB,GAAG;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE,CAAC;MACbE,UAAU,EAAE;KACb;IACD,KAAAuC,kBAAkB,GAAe,EAAE;IAEnC;IACA,KAAAG,gBAAgB,GAAG;MACjBhD,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXG,UAAU,EAAE,CAAC;MACbE,UAAU,EAAE;KACb;IACD,KAAAqE,gBAAgB,GAAqB,EAAE;EAIiB;EAExDiC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACA,MAAMG,mBAAmB,GAAwB;MAC/CC,aAAa,EAAE,IAAI,CAACX,YAAY;MAAE;MAClCY,SAAS,EAAE,CAAC;MAAE;MACdC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAACf,eAAe,CAACgB,mCAAmC,CAAC;MACvDC,IAAI,EAAEN;KACP,CAAC,CAACO,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UAEA;UACA,IAAI,CAAClB,SAAS,GAAGgB,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7ClH,UAAU,EAAEkH,IAAI,CAACC,WAAW;YAC5B5G,YAAY,EAAE2G,IAAI,CAACT,aAAa,IAAI,EAAE;YACtCjG,WAAW,EAAE,SAAS0G,IAAI,CAAC1D,SAAS,GAAG,IAAI4D,IAAI,CAACF,IAAI,CAAC1D,SAAS,CAAC,CAAC6D,kBAAkB,EAAE,GAAG,IAAI;WAC5F,CAAC,CAAC;UAEH;UACA,IAAI,CAAClB,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAACJ,eAAe,GAAG,EAAE;UACzB,IAAI,CAACrC,0BAA0B,GAAG,EAAE;QACtC,CAAC,MAAM;UACL;UACA,IAAI,CAACoC,SAAS,GAAG,EAAE;UACnB,IAAI,CAACC,eAAe,GAAG,EAAE;UACzB,IAAI,CAACI,uBAAuB,EAAE;QAChC;MACF,CAAC;MACDmB,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACxB,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACI,uBAAuB,EAAE;MAChC;KACD,CAAC;EACJ;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACnF,aAAa,CAACuG,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACtI,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAAC6G,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAM0B,OAAO,GAAG,IAAI,CAACxG,aAAa,CAACyG,WAAW,EAAE;MAChD,IAAI,CAACxI,iBAAiB,GAAG,IAAI,CAAC6G,SAAS,CAAC4B,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAACpH,YAAY,CAACkH,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAACnH,WAAW,IAAImH,QAAQ,CAACnH,WAAW,CAACiH,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;IACA,IAAI,CAACK,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,IAAI,CAACzI,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACR,iBAAiB,CAACC,MAAM;IAClE,IAAI,CAACE,kBAAkB,CAACO,UAAU,GAAGJ,IAAI,CAACuI,IAAI,CAAC,IAAI,CAAC1I,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACL,kBAAkB,CAACE,QAAQ,CAAC;IAErH;IACA,IAAI,IAAI,CAACF,kBAAkB,CAACC,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACO,UAAU,EAAE;MAC5E,IAAI,CAACP,kBAAkB,CAACC,WAAW,GAAGE,IAAI,CAACwI,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC3I,kBAAkB,CAACO,UAAU,CAAC;IACvF;IAEA,IAAI,CAACqI,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAAC7I,kBAAkB,CAACC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,kBAAkB,CAACE,QAAQ;IAC/F,MAAM4I,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC7I,kBAAkB,CAACE,QAAQ;IAC9D,IAAI,CAAC4C,kBAAkB,GAAG,IAAI,CAACjD,iBAAiB,CAACkJ,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACA9G,gBAAgBA,CAACgH,IAAY;IAC3B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAChJ,kBAAkB,CAACO,UAAU,EAAE;MAC3D,IAAI,CAACP,kBAAkB,CAACC,WAAW,GAAG+I,IAAI;MAC1C,IAAI,CAACJ,wBAAwB,EAAE;IACjC;EACF;EAEA;EACAnG,sBAAsBA,CAAA;IACpB,MAAMwG,KAAK,GAAa,EAAE;IAC1B,MAAM1I,UAAU,GAAG,IAAI,CAACP,kBAAkB,CAACO,UAAU;IACrD,MAAMN,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACC,WAAW;IAEvD;IACA,MAAMiJ,SAAS,GAAG/I,IAAI,CAACwI,GAAG,CAAC,CAAC,EAAE1I,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMkJ,OAAO,GAAGhJ,IAAI,CAACC,GAAG,CAACG,UAAU,EAAEN,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAImJ,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACAK,QAAQA,CAAA;IACN,IAAI,CAACvC,uBAAuB,EAAE;EAChC;EAEA;EACAxH,WAAWA,CAAA;IACT,IAAI,CAACqC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACmF,uBAAuB,EAAE;EAChC;EAQA;EACA/F,gBAAgBA,CAACuH,QAAkB;IACjC,IAAI,CAACvF,gBAAgB,GAAGuF,QAAQ;IAChC,IAAI,CAAC/B,cAAc,CAAC+C,IAAI,CAAChB,QAAQ,CAAC;IAElC;IACA,IAAIA,QAAQ,CAAC3H,UAAU,EAAE;MACvB,IAAI,CAAC4I,mBAAmB,CAACjB,QAAQ,CAAC3H,UAAU,CAAC;IAC/C;IAEA,IAAI,CAAC6I,sBAAsB,EAAE;EAC/B;EAEA;EACAD,mBAAmBA,CAACE,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAE/H,aAAsB;IACnF,MAAMgI,IAAI,GAA8B;MACtCF,UAAU,EAAEA;KACb;IAED;IACA,IAAI,CAACpD,eAAe,CAACuD,yCAAyC,CAAC;MAC7DtC,IAAI,EAAEqC;KACP,CAAC,CAACpC,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UAEjD;UACA,IAAIkC,UAAU,GAAGpC,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7CiC,iBAAiB,EAAEjC,IAAI,CAACiC,iBAAiB,IAAI,CAAC;YAC9ChC,WAAW,EAAED,IAAI,CAACC,WAAW,IAAI2B,UAAU;YAC3CxF,UAAU,EAAE4D,IAAI,CAAC5D,UAAU,IAAI,CAAC;YAChCD,YAAY,EAAE6D,IAAI,CAAC7D,YAAY,IAAI,EAAE;YACrCd,UAAU,EAAE2E,IAAI,CAAC3E,UAAU,IAAI,EAAE;YAAE;YACnC6G,KAAK,EAAEC,SAAS;YAChBxG,OAAO,EAAEwG,SAAS;YAClB7F,SAAS,EAAE,IAAI4D,IAAI,EAAE,CAACkC,WAAW,EAAE;YACnCC,QAAQ,EAAE,IAAI;YACd/G,SAAS,EAAE6G,SAAS;YACpB3G,UAAU,EAAE2G,SAAS;YACrB1G,SAAS,EAAE0G,SAAS;YACpBzG,KAAK,EAAEyG;WACe,EAAC;UAEzB;UACA,IAAIrI,aAAa,IAAIA,aAAa,CAACuG,IAAI,EAAE,EAAE;YACzC2B,UAAU,GAAGA,UAAU,CAACxB,MAAM,CAAC8B,MAAM,IACnCA,MAAM,CAACnG,YAAY,CAACoE,WAAW,EAAE,CAACG,QAAQ,CAAC5G,aAAa,CAACyG,WAAW,EAAE,CAAC,IACtE+B,MAAM,CAACjH,UAAU,IAAIiH,MAAM,CAACjH,UAAU,CAACkF,WAAW,EAAE,CAACG,QAAQ,CAAC5G,aAAa,CAACyG,WAAW,EAAE,CAAE,CAC7F;UACH;UAEA;UACA,MAAMQ,UAAU,GAAG,CAACc,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC1G,gBAAgB,CAAC/C,QAAQ;UACnE,MAAM4I,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC5F,gBAAgB,CAAC/C,QAAQ;UAC5D,MAAMmK,YAAY,GAAGP,UAAU,CAACf,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;UAE3D;UACA,IAAI,CAACxE,0BAA0B,GAAG+F,YAAY;UAC9C,IAAI,CAACpH,gBAAgB,CAAC5C,UAAU,GAAGyJ,UAAU,CAAChK,MAAM;UACpD,IAAI,CAACmD,gBAAgB,CAAC1C,UAAU,GAAGJ,IAAI,CAACuI,IAAI,CAACoB,UAAU,CAAChK,MAAM,GAAG,IAAI,CAACmD,gBAAgB,CAAC/C,QAAQ,CAAC;UAChG,IAAI,CAAC+C,gBAAgB,CAAChD,WAAW,GAAG0J,SAAS;QAC/C,CAAC,MAAM;UACL,IAAI,CAACrF,0BAA0B,GAAG,EAAE;UACpC,IAAI,CAACrB,gBAAgB,CAAC5C,UAAU,GAAG,CAAC;UACpC,IAAI,CAAC4C,gBAAgB,CAAC1C,UAAU,GAAG,CAAC;UACpC,IAAI,CAAC0C,gBAAgB,CAAChD,WAAW,GAAG,CAAC;QACvC;MACF,CAAC;MACDiI,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC5D,0BAA0B,GAAG,EAAE;QACpC,IAAI,CAACrB,gBAAgB,CAAC5C,UAAU,GAAG,CAAC;QACpC,IAAI,CAAC4C,gBAAgB,CAAC1C,UAAU,GAAG,CAAC;QACpC,IAAI,CAAC0C,gBAAgB,CAAChD,WAAW,GAAG,CAAC;QAErC;QACA,IAAI,CAACqK,uBAAuB,CAACZ,UAAU,EAAEC,SAAS,EAAE/H,aAAa,CAAC;MACpE;KACD,CAAC;EACJ;EAEA;EACQ0I,uBAAuBA,CAACZ,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAE/H,aAAsB;IAC/F;IACA2I,UAAU,CAAC,MAAK;MACd;MACA,MAAMC,WAAW,GAAyB,EAAE;MAC5C,MAAMC,gBAAgB,GAAG,CAAC,CAAC,CAAC;MAE5B,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIqB,gBAAgB,EAAErB,CAAC,EAAE,EAAE;QAC1C,MAAMgB,MAAM,GAAuB;UACjCL,iBAAiB,EAAEL,UAAU,GAAG,GAAG,GAAGN,CAAC;UACvCrB,WAAW,EAAE2B,UAAU;UACvBxF,UAAU,EAAEwF,UAAU,GAAG,IAAI,GAAGN,CAAC;UACjCnF,YAAY,EAAE,OAAOyG,MAAM,CAACC,YAAY,CAAC,EAAE,GAAIjB,UAAU,GAAG,EAAG,GAAG,CAAC,CAAC,IAAIN,CAAC,EAAE;UAC3EjG,UAAU,EAAEiG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,MAAQ;UAAE;UAC7FY,KAAK,EAAEZ,CAAC;UACR3F,OAAO,EAAE2F,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQA,CAAC,EAAE,GAAGa,SAAS;UAC9C7F,SAAS,EAAE,IAAI4D,IAAI,CAACA,IAAI,CAAC4C,GAAG,EAAE,GAAGzK,IAAI,CAAC0K,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACX,WAAW,EAAE;UACxFC,QAAQ,EAAE,OAAO;UACjB/G,SAAS,EAAEgG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM;UACxC9F,UAAU,EAAEnD,IAAI,CAAC2K,KAAK,CAAC3K,IAAI,CAAC0K,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI;UACpDtH,SAAS,EAAEpD,IAAI,CAAC2K,KAAK,CAAC3K,IAAI,CAAC0K,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;UAC7CrH,KAAK,EAAE4F,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG;SACjD;QACDoB,WAAW,CAACnB,IAAI,CAACe,MAAM,CAAC;MAC1B;MAEA;MACA,IAAIW,eAAe,GAAGP,WAAW;MACjC,IAAI5I,aAAa,IAAIA,aAAa,CAACuG,IAAI,EAAE,EAAE;QACzC4C,eAAe,GAAGP,WAAW,CAAClC,MAAM,CAAC8B,MAAM,IACzCA,MAAM,CAACnG,YAAY,CAACoE,WAAW,EAAE,CAACG,QAAQ,CAAC5G,aAAa,CAACyG,WAAW,EAAE,CAAC,IACtE+B,MAAM,CAACjH,UAAU,IAAIiH,MAAM,CAACjH,UAAU,CAACkF,WAAW,EAAE,CAACG,QAAQ,CAAC5G,aAAa,CAACyG,WAAW,EAAE,CAAE,IAC3F+B,MAAM,CAAC3G,OAAO,IAAI2G,MAAM,CAAC3G,OAAO,CAAC4E,WAAW,EAAE,CAACG,QAAQ,CAAC5G,aAAa,CAACyG,WAAW,EAAE,CAAE,IACrF+B,MAAM,CAAChH,SAAS,IAAIgH,MAAM,CAAChH,SAAS,CAACiF,WAAW,EAAE,CAACG,QAAQ,CAAC5G,aAAa,CAACyG,WAAW,EAAE,CAAE,CAC3F;MACH;MAEA;MACA,MAAMQ,UAAU,GAAG,CAACc,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC1G,gBAAgB,CAAC/C,QAAQ;MACnE,MAAM4I,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC5F,gBAAgB,CAAC/C,QAAQ;MAC5D,MAAMmK,YAAY,GAAGU,eAAe,CAAChC,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;MAEhE;MACA,IAAI,CAACxE,0BAA0B,GAAG+F,YAAY;MAC9C,IAAI,CAACpH,gBAAgB,CAAC5C,UAAU,GAAG0K,eAAe,CAACjL,MAAM;MACzD,IAAI,CAACmD,gBAAgB,CAAC1C,UAAU,GAAGJ,IAAI,CAACuI,IAAI,CAACqC,eAAe,CAACjL,MAAM,GAAG,IAAI,CAACmD,gBAAgB,CAAC/C,QAAQ,CAAC;MACrG,IAAI,CAAC+C,gBAAgB,CAAChD,WAAW,GAAG0J,SAAS;IAC/C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAqB,qBAAqBA,CAAC5C,OAAe;IACnC,IAAI,CAACxB,mBAAmB,GAAGwB,OAAO;IAClC,IAAI,IAAI,CAACpF,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACpC,UAAU,EAAE;MAC7D,IAAI,CAAC4I,mBAAmB,CAAC,IAAI,CAACxG,gBAAgB,CAACpC,UAAU,EAAE,CAAC,EAAEwH,OAAO,CAAC;IACxE;EACF;EAEA;EACA6C,iBAAiBA,CAAA;IACf,IAAI,CAACrE,mBAAmB,GAAG,EAAE;IAC7B,IAAI,IAAI,CAAC5D,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACpC,UAAU,EAAE;MAC7D,IAAI,CAAC4I,mBAAmB,CAAC,IAAI,CAACxG,gBAAgB,CAACpC,UAAU,EAAE,CAAC,CAAC;IAC/D;EACF;EAEA;EACA6I,sBAAsBA,CAAA;IACpB;IACA,IAAI,IAAI,CAACnF,0BAA0B,CAACxE,MAAM,GAAG,CAAC,EAAE;MAC9C;IACF;IAEA;IACA,MAAMoL,OAAO,GAAG,IAAI,CAACpG,sBAAsB;IAC3C,IAAI,CAAC7B,gBAAgB,CAAC5C,UAAU,GAAG6K,OAAO,CAACpL,MAAM;IACjD,IAAI,CAACmD,gBAAgB,CAAC1C,UAAU,GAAGJ,IAAI,CAACuI,IAAI,CAAC,IAAI,CAACzF,gBAAgB,CAAC5C,UAAU,GAAG,IAAI,CAAC4C,gBAAgB,CAAC/C,QAAQ,CAAC;IAC/G,IAAI,CAAC+C,gBAAgB,CAAChD,WAAW,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,CAACkL,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMD,OAAO,GAAG,IAAI,CAACpG,sBAAsB;IAC3C,MAAM+D,UAAU,GAAG,CAAC,IAAI,CAAC5F,gBAAgB,CAAChD,WAAW,GAAG,CAAC,IAAI,IAAI,CAACgD,gBAAgB,CAAC/C,QAAQ;IAC3F,MAAM4I,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC5F,gBAAgB,CAAC/C,QAAQ;IAC5D,IAAI,CAAC0E,gBAAgB,GAAGsG,OAAO,CAACnC,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC7D;EAEA;EACA3D,cAAcA,CAAC6D,IAAY;IACzB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC/F,gBAAgB,CAAC1C,UAAU,EAAE;MACzD;MACA,IAAI,IAAI,CAACyC,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACpC,UAAU,IAAI,IAAI,CAAC0D,0BAA0B,CAACxE,MAAM,GAAG,CAAC,EAAE;QAC3G,IAAI,CAAC0J,mBAAmB,CAAC,IAAI,CAACxG,gBAAgB,CAACpC,UAAU,EAAEoI,IAAI,CAAC;MAClE,CAAC,MAAM;QACL;QACA,IAAI,CAAC/F,gBAAgB,CAAChD,WAAW,GAAG+I,IAAI;QACxC,IAAI,CAACmC,sBAAsB,EAAE;MAC/B;IACF;EACF;EAEA;EACA3F,oBAAoBA,CAAA;IAClB,MAAMyD,KAAK,GAAa,EAAE;IAC1B,MAAM1I,UAAU,GAAG,IAAI,CAAC0C,gBAAgB,CAAC1C,UAAU;IACnD,MAAMN,WAAW,GAAG,IAAI,CAACgD,gBAAgB,CAAChD,WAAW;IAErD;IACA,MAAMiJ,SAAS,GAAG/I,IAAI,CAACwI,GAAG,CAAC,CAAC,EAAE1I,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMkJ,OAAO,GAAGhJ,IAAI,CAACC,GAAG,CAACG,UAAU,EAAEN,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAImJ,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACAmC,OAAOA,CAAA;IACL,IAAI,CAAC3E,KAAK,CAAC8C,IAAI,EAAE;EACnB;EAEA;EACA1I,gBAAgBA,CAACwK,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACA,MAAMG,kBAAkB,GAAwB;MAC9CzD,WAAW,EAAEsD;KACd;IAED;IACA,IAAI,CAAC/E,eAAe,CAACmF,kCAAkC,CAAC;MACtDlE,IAAI,EAAEiE;KACP,CAAC,CAAChE,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACA;UACA,IAAI,CAACb,aAAa,EAAE;UAEpB;UACA,IAAI,IAAI,CAAC9D,gBAAgB,EAAEpC,UAAU,KAAKyK,UAAU,EAAE;YACpD,IAAI,CAACrI,gBAAgB,GAAG,IAAI;UAC9B;QACF,CAAC,MAAM;UACL;QAAA;MAEJ,CAAC;MACDkF,KAAK,EAAEA,CAAA,KAAK;QACV;MAAA;KAEH,CAAC;EACJ;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA;EACAvC,mBAAmBA,CAAA;IACjB,IAAI,CAAC3C,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAI8B,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC9B,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAAC2D,eAAe,CAAC2B,MAAM,CAACoD,CAAC,IAAIA,CAAC,CAAC9K,UAAU,KAAK,IAAI,CAACoC,gBAAiB,CAACpC,UAAU,CAAC;EAC7F;EAEA;EACAmC,iBAAiBA,CAAC4I,KAAa,EAAEpD,QAAkB;IACjD,OAAOA,QAAQ,CAAC3H,UAAU,IAAI+K,KAAK;EACrC;;;uCAndWvF,uBAAuB,EAAAtH,EAAA,CAAA8M,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvB1F,uBAAuB;MAAA2F,SAAA;MAAAC,MAAA;QAAAzF,YAAA;MAAA;MAAA0F,OAAA;QAAAzF,cAAA;QAAAC,KAAA;MAAA;MAAAyF,UAAA;MAAAC,QAAA,GAAArN,EAAA,CAAAsN,oBAAA,EAAAtN,EAAA,CAAAuN,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAjE,QAAA,WAAAkE,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5B5N,EAJR,CAAAC,cAAA,iBAAsC,wBACW,aACkB,aACnC,YACP;UACfD,EAAA,CAAAU,SAAA,WAAoD;UAAAV,EAAA,CAAAY,MAAA,gCACtD;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACLX,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAY,MAAA,yEAAW;UACvCZ,EADuC,CAAAW,YAAA,EAAQ,EACzC;UAEJX,EADF,CAAAC,cAAA,aAA4B,gBACK;UAAAD,EAAA,CAAAY,MAAA,IAAuC;UAG5EZ,EAH4E,CAAAW,YAAA,EAAO,EACzE,EACF,EACS;UAMTX,EALR,CAAAC,cAAA,wBAA2C,eAEG,eACd,eACM,eACL;UACvBD,EAAA,CAAAU,SAAA,aAA6B;UAC/BV,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAC,cAAA,iBACkD;UADqBD,EAAA,CAAA8N,gBAAA,2BAAAC,iEAAAC,MAAA;YAAAhO,EAAA,CAAAiO,kBAAA,CAAAJ,GAAA,CAAA/K,aAAA,EAAAkL,MAAA,MAAAH,GAAA,CAAA/K,aAAA,GAAAkL,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAC3EhO,EAArB,CAAAE,UAAA,mBAAAgO,yDAAA;YAAA,OAASL,GAAA,CAAArD,QAAA,EAAU;UAAA,EAAC,yBAAA2D,+DAAA;YAAA,OAAgBN,GAAA,CAAArD,QAAA,EAAU;UAAA,EAAC;UADjDxK,EAAA,CAAAW,YAAA,EACkD;UAClDX,EAAA,CAAAmC,UAAA,KAAAiM,uCAAA,kBAAkD;UAKpDpO,EAAA,CAAAW,YAAA,EAAM;UAMNX,EALA,CAAAmC,UAAA,KAAAkM,uCAAA,kBAAsF,KAAAC,uCAAA,kBAKC;UAM3FtO,EADE,CAAAW,YAAA,EAAM,EACF;UA8HNX,EAzHA,CAAAmC,UAAA,KAAAoM,uCAAA,kBAA+D,KAAAC,uCAAA,mBAyHJ;UAuJ7DxO,EAAA,CAAAW,YAAA,EAAe;UAGXX,EAFJ,CAAAC,cAAA,0BAA+C,eACjB,kBACoB;UAApBD,EAAA,CAAAE,UAAA,mBAAAuO,0DAAA;YAAA,OAASZ,GAAA,CAAAvB,OAAA,EAAS;UAAA,EAAC;UAC3CtM,EAAA,CAAAU,SAAA,aAAiC;UAAAV,EAAA,CAAAY,MAAA,qBACnC;UAGNZ,EAHM,CAAAW,YAAA,EAAS,EACL,EACS,EACT;;;UA5T6BX,EAAA,CAAAa,SAAA,IAAuC;UAAvCb,EAAA,CAAAc,kBAAA,KAAA+M,GAAA,CAAA3M,kBAAA,CAAAK,UAAA,wBAAuC;UAYGvB,EAAA,CAAAa,SAAA,GAA2B;UAA3Bb,EAAA,CAAA0O,gBAAA,YAAAb,GAAA,CAAA/K,aAAA,CAA2B;UAErE9C,EAAA,CAAAa,SAAA,EAAmB;UAAnBb,EAAA,CAAAuC,UAAA,SAAAsL,GAAA,CAAA/K,aAAA,CAAmB;UAMjB9C,EAAA,CAAAa,SAAA,EAAmD;UAAnDb,EAAA,CAAAuC,UAAA,SAAAsL,GAAA,CAAA/K,aAAA,IAAA+K,GAAA,CAAA9M,iBAAA,CAAAC,MAAA,KAAmD;UAKpDhB,EAAA,CAAAa,SAAA,EAAqD;UAArDb,EAAA,CAAAuC,UAAA,SAAAsL,GAAA,CAAA/K,aAAA,IAAA+K,GAAA,CAAA9M,iBAAA,CAAAC,MAAA,OAAqD;UAWnDhB,EAAA,CAAAa,SAAA,EAAuB;UAAvBb,EAAA,CAAAuC,UAAA,UAAAsL,GAAA,CAAA3J,gBAAA,CAAuB;UAyHvDlE,EAAA,CAAAa,SAAA,EAAsB;UAAtBb,EAAA,CAAAuC,UAAA,SAAAsL,GAAA,CAAA3J,gBAAA,CAAsB;;;qBD3JpBtE,YAAY,EAAA+O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAAH,EAAA,CAAAI,QAAA,EAAElP,WAAW,EAAAmP,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAErP,YAAY,EAAAsP,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAEzP,cAAc;MAAA0P,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}