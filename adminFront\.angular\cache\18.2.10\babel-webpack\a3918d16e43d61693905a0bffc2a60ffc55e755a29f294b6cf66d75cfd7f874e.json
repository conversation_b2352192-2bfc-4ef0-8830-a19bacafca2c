{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiPreOrderSettingGetPreOrderSettingPost$Json } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-json';\nimport { apiPreOrderSettingGetPreOrderSettingPost$Plain } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-plain';\nimport { apiPreOrderSettingSavePreOrderSettingPost$Json } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-json';\nimport { apiPreOrderSettingSavePreOrderSettingPost$Plain } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let PreOrderSettingService = /*#__PURE__*/(() => {\n  class PreOrderSettingService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiPreOrderSettingGetPreOrderSettingPost()` */\n    static {\n      this.ApiPreOrderSettingGetPreOrderSettingPostPath = '/api/PreOrderSetting/GetPreOrderSetting';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPreOrderSettingGetPreOrderSettingPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPreOrderSettingGetPreOrderSettingPost$Plain$Response(params, context) {\n      return apiPreOrderSettingGetPreOrderSettingPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPreOrderSettingGetPreOrderSettingPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPreOrderSettingGetPreOrderSettingPost$Plain(params, context) {\n      return this.apiPreOrderSettingGetPreOrderSettingPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPreOrderSettingGetPreOrderSettingPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPreOrderSettingGetPreOrderSettingPost$Json$Response(params, context) {\n      return apiPreOrderSettingGetPreOrderSettingPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPreOrderSettingGetPreOrderSettingPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPreOrderSettingGetPreOrderSettingPost$Json(params, context) {\n      return this.apiPreOrderSettingGetPreOrderSettingPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiPreOrderSettingSavePreOrderSettingPost()` */\n    static {\n      this.ApiPreOrderSettingSavePreOrderSettingPostPath = '/api/PreOrderSetting/SavePreOrderSetting';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPreOrderSettingSavePreOrderSettingPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPreOrderSettingSavePreOrderSettingPost$Plain$Response(params, context) {\n      return apiPreOrderSettingSavePreOrderSettingPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPreOrderSettingSavePreOrderSettingPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPreOrderSettingSavePreOrderSettingPost$Plain(params, context) {\n      return this.apiPreOrderSettingSavePreOrderSettingPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPreOrderSettingSavePreOrderSettingPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPreOrderSettingSavePreOrderSettingPost$Json$Response(params, context) {\n      return apiPreOrderSettingSavePreOrderSettingPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPreOrderSettingSavePreOrderSettingPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPreOrderSettingSavePreOrderSettingPost$Json(params, context) {\n      return this.apiPreOrderSettingSavePreOrderSettingPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function PreOrderSettingService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PreOrderSettingService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PreOrderSettingService,\n        factory: PreOrderSettingService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PreOrderSettingService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}