{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function sample(notifier) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let lastValue = null;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      lastValue = value;\n    }));\n    innerFrom(notifier).subscribe(createOperatorSubscriber(subscriber, () => {\n      if (hasValue) {\n        hasValue = false;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    }, noop));\n  });\n}\n//# sourceMappingURL=sample.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}