{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nlet TemplateCreatorComponent = class TemplateCreatorComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.availableData = []; // 父元件傳入的可選資料\n    this.templateType = 1; // 模板類型，1=客變需求\n    this.close = new EventEmitter(); // 關閉事件\n    this.templateCreated = new EventEmitter(); // 模板創建成功事件\n    // 新增模板表單\n    this.newTemplate = {\n      name: ''\n    };\n    // 表單驗證狀態\n    this.isSubmitting = false;\n    this.validationErrors = {};\n  }\n  ngOnInit() {\n    // 初始化時重置所有選擇狀態\n    this.resetForm();\n  }\n  // 重置表單\n  resetForm() {\n    this.newTemplate = {\n      name: ''\n    };\n    this.validationErrors = {};\n    this.isSubmitting = false;\n    // 重置選擇狀態\n    if (this.availableData) {\n      this.availableData.forEach(item => item.selected = false);\n    }\n  }\n  // 驗證表單\n  validateForm() {\n    this.validationErrors = {};\n    let isValid = true;\n    // 驗證模板名稱\n    if (!this.newTemplate.name.trim()) {\n      this.validationErrors['name'] = '請輸入模板名稱';\n      isValid = false;\n    } else if (this.newTemplate.name.trim().length > 50) {\n      this.validationErrors['name'] = '模板名稱不能超過50個字符';\n      isValid = false;\n    }\n    // 驗證是否選擇了項目\n    const selectedItems = this.getSelectedItems();\n    if (selectedItems.length === 0) {\n      this.validationErrors['items'] = '請至少選擇一個項目';\n      isValid = false;\n    }\n    return isValid;\n  }\n  // 獲取選中的項目\n  getSelectedItems() {\n    if (!this.availableData) return [];\n    return this.availableData.filter(item => item.selected).map(item => ({\n      CGroupName: item.CGroupName || null,\n      CReleateName: item.CReleateName || item.CRequirement || item.name || null,\n      CReleateId: item.CReleateId || item.CRequirementID || item.ID || item.id || 0\n    }));\n  }\n  // 儲存新模板\n  saveTemplate() {\n    if (!this.validateForm()) {\n      return;\n    }\n    this.isSubmitting = true;\n    const selectedItems = this.getSelectedItems();\n    // 準備 API 請求資料\n    const saveTemplateArgs = {\n      CTemplateId: null,\n      // 新增時為 null\n      CTemplateName: this.newTemplate.name.trim(),\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      CStatus: 1,\n      // 啟用狀態\n      Details: selectedItems.map(item => ({\n        CTemplateDetailId: null,\n        // 新增時為 null\n        CReleateId: item.CReleateId,\n        // 關聯主檔ID\n        CReleateName: item.CReleateName,\n        // 關聯名稱\n        CGroupName: item.CGroupName // 群組名稱\n      }))\n    };\n    // 調用 SaveTemplate API\n    this.templateService.apiTemplateSaveTemplatePost$Json({\n      body: saveTemplateArgs\n    }).subscribe({\n      next: response => {\n        this.isSubmitting = false;\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          this.templateCreated.emit(); // 通知父組件模板創建成功\n          this.close.emit(); // 關閉對話框\n        } else {\n          // API 返回錯誤\n          this.validationErrors['api'] = response.Message || '保存失敗，請稍後重試';\n        }\n      },\n      error: error => {\n        this.isSubmitting = false;\n        this.validationErrors['api'] = '網絡錯誤，請檢查網絡連接後重試';\n        console.error('保存模板失敗:', error);\n      }\n    });\n  }\n  // 取消操作\n  cancel() {\n    this.close.emit();\n  }\n  // 獲取選中項目數量\n  getSelectedCount() {\n    return this.availableData ? this.availableData.filter(item => item.selected).length : 0;\n  }\n  // 切換項目選擇狀態\n  toggleItemSelection(item) {\n    item.selected = !item.selected;\n    // 清除項目選擇相關的驗證錯誤\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\n      delete this.validationErrors['items'];\n    }\n  }\n  // 全選/取消全選\n  toggleSelectAll() {\n    const hasUnselected = this.availableData.some(item => !item.selected);\n    this.availableData.forEach(item => item.selected = hasUnselected);\n    // 清除項目選擇相關的驗證錯誤\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\n      delete this.validationErrors['items'];\n    }\n  }\n  // 檢查是否全選\n  isAllSelected() {\n    return this.availableData && this.availableData.length > 0 && this.availableData.every(item => item.selected);\n  }\n  // 檢查是否部分選中\n  isIndeterminate() {\n    const selectedCount = this.getSelectedCount();\n    return selectedCount > 0 && selectedCount < this.availableData.length;\n  }\n};\n__decorate([Input()], TemplateCreatorComponent.prototype, \"availableData\", void 0);\n__decorate([Input()], TemplateCreatorComponent.prototype, \"templateType\", void 0);\n__decorate([Output()], TemplateCreatorComponent.prototype, \"close\", void 0);\n__decorate([Output()], TemplateCreatorComponent.prototype, \"templateCreated\", void 0);\nTemplateCreatorComponent = __decorate([Component({\n  selector: 'app-template-creator',\n  templateUrl: './template-creator.component.html',\n  styleUrls: ['./template-creator.component.scss'],\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule]\n})], TemplateCreatorComponent);\nexport { TemplateCreatorComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "TemplateCreatorComponent", "constructor", "templateService", "availableData", "templateType", "close", "templateCreated", "newTemplate", "name", "isSubmitting", "validationErrors", "ngOnInit", "resetForm", "for<PERSON>ach", "item", "selected", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "trim", "length", "selectedItems", "getSelectedItems", "filter", "map", "CGroupName", "CReleateName", "CRequirement", "CReleateId", "CRequirementID", "ID", "id", "saveTemplate", "saveTemplateArgs", "CTemplateId", "CTemplateName", "CTemplateType", "CStatus", "Details", "CTemplateDetailId", "apiTemplateSaveTemplatePost$Json", "body", "subscribe", "next", "response", "StatusCode", "emit", "Message", "error", "console", "cancel", "getSelectedCount", "toggleItemSelection", "toggleSelectAll", "has<PERSON><PERSON><PERSON>", "some", "isAllSelected", "every", "isIndeterminate", "selectedCount", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-creator\\template-creator.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { SaveTemplateArgs, SaveTemplateDetailArgs } from 'src/services/api/models';\r\n\r\n// 選中項目的介面定義\r\ninterface SelectedItem {\r\n  CGroupName: string | null;\r\n  CReleateName: string | null;\r\n  CReleateId: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-template-creator',\r\n  templateUrl: './template-creator.component.html',\r\n  styleUrls: ['./template-creator.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateCreatorComponent implements OnInit {\r\n  @Input() availableData: any[] = []; // 父元件傳入的可選資料\r\n  @Input() templateType: number = 1; // 模板類型，1=客變需求\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n  @Output() templateCreated = new EventEmitter<void>(); // 模板創建成功事件\r\n\r\n  // 新增模板表單\r\n  newTemplate = {\r\n    name: ''\r\n  };\r\n\r\n  // 表單驗證狀態\r\n  isSubmitting = false;\r\n  validationErrors: { [key: string]: string } = {};\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    // 初始化時重置所有選擇狀態\r\n    this.resetForm();\r\n  }\r\n\r\n  // 重置表單\r\n  resetForm() {\r\n    this.newTemplate = {\r\n      name: ''\r\n    };\r\n    this.validationErrors = {};\r\n    this.isSubmitting = false;\r\n\r\n    // 重置選擇狀態\r\n    if (this.availableData) {\r\n      this.availableData.forEach(item => item.selected = false);\r\n    }\r\n  }\r\n\r\n  // 驗證表單\r\n  validateForm(): boolean {\r\n    this.validationErrors = {};\r\n    let isValid = true;\r\n\r\n    // 驗證模板名稱\r\n    if (!this.newTemplate.name.trim()) {\r\n      this.validationErrors['name'] = '請輸入模板名稱';\r\n      isValid = false;\r\n    } else if (this.newTemplate.name.trim().length > 50) {\r\n      this.validationErrors['name'] = '模板名稱不能超過50個字符';\r\n      isValid = false;\r\n    }\r\n\r\n\r\n\r\n    // 驗證是否選擇了項目\r\n    const selectedItems = this.getSelectedItems();\r\n    if (selectedItems.length === 0) {\r\n      this.validationErrors['items'] = '請至少選擇一個項目';\r\n      isValid = false;\r\n    }\r\n\r\n    return isValid;\r\n  }\r\n\r\n  // 獲取選中的項目\r\n  getSelectedItems(): SelectedItem[] {\r\n    if (!this.availableData) return [];\r\n\r\n    return this.availableData\r\n      .filter(item => item.selected)\r\n      .map(item => ({\r\n        CGroupName: item.CGroupName || null,\r\n        CReleateName: item.CReleateName || item.CRequirement || item.name || null,\r\n        CReleateId: item.CReleateId || item.CRequirementID || item.ID || item.id || 0\r\n      }));\r\n  }\r\n\r\n  // 儲存新模板\r\n  saveTemplate() {\r\n    if (!this.validateForm()) {\r\n      return;\r\n    }\r\n\r\n    this.isSubmitting = true;\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    // 準備 API 請求資料\r\n    const saveTemplateArgs: SaveTemplateArgs = {\r\n      CTemplateId: null, // 新增時為 null\r\n      CTemplateName: this.newTemplate.name.trim(),\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      CStatus: 1, // 啟用狀態\r\n      Details: selectedItems.map(item => ({\r\n        CTemplateDetailId: null, // 新增時為 null\r\n        CReleateId: item.CReleateId, // 關聯主檔ID\r\n        CReleateName: item.CReleateName, // 關聯名稱\r\n        CGroupName: item.CGroupName // 群組名稱\r\n      } as SaveTemplateDetailArgs))\r\n    };\r\n\r\n    // 調用 SaveTemplate API\r\n    this.templateService.apiTemplateSaveTemplatePost$Json({\r\n      body: saveTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        this.isSubmitting = false;\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          this.templateCreated.emit(); // 通知父組件模板創建成功\r\n          this.close.emit(); // 關閉對話框\r\n        } else {\r\n          // API 返回錯誤\r\n          this.validationErrors['api'] = response.Message || '保存失敗，請稍後重試';\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isSubmitting = false;\r\n        this.validationErrors['api'] = '網絡錯誤，請檢查網絡連接後重試';\r\n        console.error('保存模板失敗:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 取消操作\r\n  cancel() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 獲取選中項目數量\r\n  getSelectedCount(): number {\r\n    return this.availableData ? this.availableData.filter(item => item.selected).length : 0;\r\n  }\r\n\r\n  // 切換項目選擇狀態\r\n  toggleItemSelection(item: any) {\r\n    item.selected = !item.selected;\r\n    // 清除項目選擇相關的驗證錯誤\r\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\r\n      delete this.validationErrors['items'];\r\n    }\r\n  }\r\n\r\n  // 全選/取消全選\r\n  toggleSelectAll() {\r\n    const hasUnselected = this.availableData.some(item => !item.selected);\r\n    this.availableData.forEach(item => item.selected = hasUnselected);\r\n\r\n    // 清除項目選擇相關的驗證錯誤\r\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\r\n      delete this.validationErrors['items'];\r\n    }\r\n  }\r\n\r\n  // 檢查是否全選\r\n  isAllSelected(): boolean {\r\n    return this.availableData && this.availableData.length > 0 &&\r\n      this.availableData.every(item => item.selected);\r\n  }\r\n\r\n  // 檢查是否部分選中\r\n  isIndeterminate(): boolean {\r\n    const selectedCount = this.getSelectedCount();\r\n    return selectedCount > 0 && selectedCount < this.availableData.length;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;AAkBtD,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAenCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAd1B,KAAAC,aAAa,GAAU,EAAE,CAAC,CAAC;IAC3B,KAAAC,YAAY,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAC,KAAK,GAAG,IAAIZ,YAAY,EAAQ,CAAC,CAAC;IAClC,KAAAa,eAAe,GAAG,IAAIb,YAAY,EAAQ,CAAC,CAAC;IAEtD;IACA,KAAAc,WAAW,GAAG;MACZC,IAAI,EAAE;KACP;IAED;IACA,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,gBAAgB,GAA8B,EAAE;EAEQ;EAExDC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;EACAA,SAASA,CAAA;IACP,IAAI,CAACL,WAAW,GAAG;MACjBC,IAAI,EAAE;KACP;IACD,IAAI,CAACE,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACD,YAAY,GAAG,KAAK;IAEzB;IACA,IAAI,IAAI,CAACN,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACU,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAC;IAC3D;EACF;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,CAACN,gBAAgB,GAAG,EAAE;IAC1B,IAAIO,OAAO,GAAG,IAAI;IAElB;IACA,IAAI,CAAC,IAAI,CAACV,WAAW,CAACC,IAAI,CAACU,IAAI,EAAE,EAAE;MACjC,IAAI,CAACR,gBAAgB,CAAC,MAAM,CAAC,GAAG,SAAS;MACzCO,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAI,IAAI,CAACV,WAAW,CAACC,IAAI,CAACU,IAAI,EAAE,CAACC,MAAM,GAAG,EAAE,EAAE;MACnD,IAAI,CAACT,gBAAgB,CAAC,MAAM,CAAC,GAAG,eAAe;MAC/CO,OAAO,GAAG,KAAK;IACjB;IAIA;IACA,MAAMG,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAC7C,IAAID,aAAa,CAACD,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACT,gBAAgB,CAAC,OAAO,CAAC,GAAG,WAAW;MAC5CO,OAAO,GAAG,KAAK;IACjB;IAEA,OAAOA,OAAO;EAChB;EAEA;EACAI,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAClB,aAAa,EAAE,OAAO,EAAE;IAElC,OAAO,IAAI,CAACA,aAAa,CACtBmB,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,CAC7BQ,GAAG,CAACT,IAAI,KAAK;MACZU,UAAU,EAAEV,IAAI,CAACU,UAAU,IAAI,IAAI;MACnCC,YAAY,EAAEX,IAAI,CAACW,YAAY,IAAIX,IAAI,CAACY,YAAY,IAAIZ,IAAI,CAACN,IAAI,IAAI,IAAI;MACzEmB,UAAU,EAAEb,IAAI,CAACa,UAAU,IAAIb,IAAI,CAACc,cAAc,IAAId,IAAI,CAACe,EAAE,IAAIf,IAAI,CAACgB,EAAE,IAAI;KAC7E,CAAC,CAAC;EACP;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACf,YAAY,EAAE,EAAE;MACxB;IACF;IAEA,IAAI,CAACP,YAAY,GAAG,IAAI;IACxB,MAAMW,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAE7C;IACA,MAAMW,gBAAgB,GAAqB;MACzCC,WAAW,EAAE,IAAI;MAAE;MACnBC,aAAa,EAAE,IAAI,CAAC3B,WAAW,CAACC,IAAI,CAACU,IAAI,EAAE;MAC3CiB,aAAa,EAAE,IAAI,CAAC/B,YAAY;MAAE;MAClCgC,OAAO,EAAE,CAAC;MAAE;MACZC,OAAO,EAAEjB,aAAa,CAACG,GAAG,CAACT,IAAI,KAAK;QAClCwB,iBAAiB,EAAE,IAAI;QAAE;QACzBX,UAAU,EAAEb,IAAI,CAACa,UAAU;QAAE;QAC7BF,YAAY,EAAEX,IAAI,CAACW,YAAY;QAAE;QACjCD,UAAU,EAAEV,IAAI,CAACU,UAAU,CAAC;OACF;KAC7B;IAED;IACA,IAAI,CAACtB,eAAe,CAACqC,gCAAgC,CAAC;MACpDC,IAAI,EAAER;KACP,CAAC,CAACS,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAClC,YAAY,GAAG,KAAK;QACzB,IAAIkC,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACA,IAAI,CAACtC,eAAe,CAACuC,IAAI,EAAE,CAAC,CAAC;UAC7B,IAAI,CAACxC,KAAK,CAACwC,IAAI,EAAE,CAAC,CAAC;QACrB,CAAC,MAAM;UACL;UACA,IAAI,CAACnC,gBAAgB,CAAC,KAAK,CAAC,GAAGiC,QAAQ,CAACG,OAAO,IAAI,YAAY;QACjE;MACF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,gBAAgB,CAAC,KAAK,CAAC,GAAG,iBAAiB;QAChDsC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MACjC;KACD,CAAC;EACJ;EAEA;EACAE,MAAMA,CAAA;IACJ,IAAI,CAAC5C,KAAK,CAACwC,IAAI,EAAE;EACnB;EAEA;EACAK,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC/C,aAAa,GAAG,IAAI,CAACA,aAAa,CAACmB,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,CAACI,MAAM,GAAG,CAAC;EACzF;EAEA;EACAgC,mBAAmBA,CAACrC,IAAS;IAC3BA,IAAI,CAACC,QAAQ,GAAG,CAACD,IAAI,CAACC,QAAQ;IAC9B;IACA,IAAI,IAAI,CAACL,gBAAgB,CAAC,OAAO,CAAC,IAAI,IAAI,CAACwC,gBAAgB,EAAE,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAACxC,gBAAgB,CAAC,OAAO,CAAC;IACvC;EACF;EAEA;EACA0C,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG,IAAI,CAAClD,aAAa,CAACmD,IAAI,CAACxC,IAAI,IAAI,CAACA,IAAI,CAACC,QAAQ,CAAC;IACrE,IAAI,CAACZ,aAAa,CAACU,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,GAAGsC,aAAa,CAAC;IAEjE;IACA,IAAI,IAAI,CAAC3C,gBAAgB,CAAC,OAAO,CAAC,IAAI,IAAI,CAACwC,gBAAgB,EAAE,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAACxC,gBAAgB,CAAC,OAAO,CAAC;IACvC;EACF;EAEA;EACA6C,aAAaA,CAAA;IACX,OAAO,IAAI,CAACpD,aAAa,IAAI,IAAI,CAACA,aAAa,CAACgB,MAAM,GAAG,CAAC,IACxD,IAAI,CAAChB,aAAa,CAACqD,KAAK,CAAC1C,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;EACnD;EAEA;EACA0C,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG,IAAI,CAACR,gBAAgB,EAAE;IAC7C,OAAOQ,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,IAAI,CAACvD,aAAa,CAACgB,MAAM;EACvE;CACD;AAjKUwC,UAAA,EAARjE,KAAK,EAAE,C,8DAA2B;AAC1BiE,UAAA,EAARjE,KAAK,EAAE,C,6DAA0B;AACxBiE,UAAA,EAAThE,MAAM,EAAE,C,sDAAkC;AACjCgE,UAAA,EAAThE,MAAM,EAAE,C,gEAA4C;AAJ1CK,wBAAwB,GAAA2D,UAAA,EAPpCnE,SAAS,CAAC;EACToE,QAAQ,EAAE,sBAAsB;EAChCC,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,CAAC,mCAAmC,CAAC;EAChDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACpE,YAAY,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc;CAClE,CAAC,C,EACWC,wBAAwB,CAkKpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}