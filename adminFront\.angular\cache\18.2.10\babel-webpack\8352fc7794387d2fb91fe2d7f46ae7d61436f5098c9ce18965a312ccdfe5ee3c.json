{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let PetternHelper = /*#__PURE__*/(() => {\n  class PetternHelper {\n    constructor() {\n      this._AccountPettern = '^[a-zA-Z0-9]{3,20}$';\n      this._PasswordPettern = '^[a-zA-Z0-9]{3,20}$';\n      this._MailPettern = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$';\n    }\n    get AccountPettern() {\n      return this._AccountPettern;\n    }\n    set AccountPettern(value) {\n      this._AccountPettern = value;\n    }\n    get PasswordPettern() {\n      return this._PasswordPettern;\n    }\n    set PasswordPettern(value) {\n      this._PasswordPettern = value;\n    }\n    get MailPettern() {\n      return this._MailPettern;\n    }\n    set MailPettern(value) {\n      this._MailPettern = value;\n    }\n    static {\n      this.ɵfac = function PetternHelper_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PetternHelper)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PetternHelper,\n        factory: PetternHelper.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PetternHelper;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}