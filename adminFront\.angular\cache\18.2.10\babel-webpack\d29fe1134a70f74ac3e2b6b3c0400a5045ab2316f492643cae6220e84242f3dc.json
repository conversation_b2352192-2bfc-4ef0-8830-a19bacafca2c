{"ast": null, "code": "import eachDayOfInterval from \"../eachDayOfInterval/index.js\";\nimport isSunday from \"../isSunday/index.js\";\nimport isWeekend from \"../isWeekend/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekendOfInterval\n * @category Interval Helpers\n * @summary List all the Saturdays and Sundays in the given date interval.\n *\n * @description\n * Get all the Saturdays and Sundays in the given date interval.\n *\n * @param {Interval} interval - the given interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} an array containing all the Saturdays and Sundays\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Lists all Saturdays and Sundays in the given date interval\n * const result = eachWeekendOfInterval({\n *   start: new Date(2018, 8, 17),\n *   end: new Date(2018, 8, 30)\n * })\n * //=> [\n * //   Sat Sep 22 2018 00:00:00,\n * //   Sun Sep 23 2018 00:00:00,\n * //   Sat Sep 29 2018 00:00:00,\n * //   Sun Sep 30 2018 00:00:00\n * // ]\n */\nexport default function eachWeekendOfInterval(interval) {\n  requiredArgs(1, arguments);\n  var dateInterval = eachDayOfInterval(interval);\n  var weekends = [];\n  var index = 0;\n  while (index < dateInterval.length) {\n    var date = dateInterval[index++];\n    if (isWeekend(date)) {\n      weekends.push(date);\n      if (isSunday(date)) index = index + 5;\n    }\n  }\n  return weekends;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}