{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiBaseFunctionGetFunctionPost$Json } from '../fn/base-function/api-base-function-get-function-post-json';\nimport { apiBaseFunctionGetFunctionPost$Plain } from '../fn/base-function/api-base-function-get-function-post-plain';\nimport { apiBaseFunctionGetStatusPost$Json } from '../fn/base-function/api-base-function-get-status-post-json';\nimport { apiBaseFunctionGetStatusPost$Plain } from '../fn/base-function/api-base-function-get-status-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let BaseFunctionService = /*#__PURE__*/(() => {\n  class BaseFunctionService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiBaseFunctionGetStatusPost()` */\n    static {\n      this.ApiBaseFunctionGetStatusPostPath = '/api/BaseFunction/GetStatus';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBaseFunctionGetStatusPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBaseFunctionGetStatusPost$Plain$Response(params, context) {\n      return apiBaseFunctionGetStatusPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBaseFunctionGetStatusPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBaseFunctionGetStatusPost$Plain(params, context) {\n      return this.apiBaseFunctionGetStatusPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBaseFunctionGetStatusPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBaseFunctionGetStatusPost$Json$Response(params, context) {\n      return apiBaseFunctionGetStatusPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBaseFunctionGetStatusPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBaseFunctionGetStatusPost$Json(params, context) {\n      return this.apiBaseFunctionGetStatusPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBaseFunctionGetFunctionPost()` */\n    static {\n      this.ApiBaseFunctionGetFunctionPostPath = '/api/BaseFunction/GetFunction';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBaseFunctionGetFunctionPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBaseFunctionGetFunctionPost$Plain$Response(params, context) {\n      return apiBaseFunctionGetFunctionPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBaseFunctionGetFunctionPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBaseFunctionGetFunctionPost$Plain(params, context) {\n      return this.apiBaseFunctionGetFunctionPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBaseFunctionGetFunctionPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBaseFunctionGetFunctionPost$Json$Response(params, context) {\n      return apiBaseFunctionGetFunctionPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBaseFunctionGetFunctionPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBaseFunctionGetFunctionPost$Json(params, context) {\n      return this.apiBaseFunctionGetFunctionPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function BaseFunctionService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BaseFunctionService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: BaseFunctionService,\n        factory: BaseFunctionService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return BaseFunctionService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}