import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NbCheckboxModule } from '@nebular/theme';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { FormItemService, MaterialService, RegularNoticeFileService, HouseService } from 'src/services/api/services';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'src/app/shared/services/message.service';
import { tap } from 'rxjs';
import { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq, GetMaterialListResponse } from 'src/services/api/models';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { SharedModule } from 'src/app/pages/components/shared.module';
import { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/pages/components/base/baseComponent';
import { EventService, EEvent } from 'src/app/shared/services/event.service';
import { Base64ImagePipe } from "../../../../@theme/pipes/base64-image.pipe";
import { EnumHouseType } from 'src/app/shared/enum/enumHouseType';


export interface ExtendedSaveListFormItemReq {
  CDesignFileUrl?: string | null;
  CMatrialUrl?: string[] | null;
  CFile?: FileViewModel,
  CFormItemHouseHold?: Array<string> | null;
  CFormItemId?: number;
  CItemName?: string;
  CFormID?: number;
  CPart?: string | null;
  CName?: string | null;
  CLocation?: string | null;
  CRemarkType?: string | null;
  CTotalAnswer?: number;
  CRequireAnswer?: number;
  CUiType?: number;
  selectedCUiType: any | null;
  selectedItems: { [key: string]: boolean }
  selectedRemarkType?: { [key: string]: boolean }
  allSelected: boolean,
  listPictures: any[],
  currentImageIndex?: number; // 當前顯示的圖片索引
  isModalOpen?: boolean; // 是否打開放大模態窗口
  selectedHouseholdsCached?: string[]; // 緩存已選戶別，避免重複計算
  isCollapsed?: boolean; // 是否收合狀態
}

@Component({
  selector: 'ngx-detail-content-management-sales-account',
  templateUrl: './detail-content-management-sales-account.component.html',
  styleUrls: ['./detail-content-management-sales-account.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, SharedModule, AppSharedModule, NbCheckboxModule, Base64ImagePipe],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit, OnDestroy {
  constructor(
    private _allow: AllowHelper,
    private route: ActivatedRoute,
    private message: MessageService,
    private _formItemService: FormItemService,
    private _regularNoticeFileService: RegularNoticeFileService,
    private _utilityService: UtilityService,
    private valid: ValidationHelper,
    private location: Location,
    private _materialService: MaterialService,
    private _eventService: EventService,
    private _houseService: HouseService,
    private cdr: ChangeDetectorRef
  ) {
    super(_allow)
  }
  typeContentManagementSalesAccount = {
    CFormType: 2,
    CNoticeType: 2
  }
  // 通知類型選項映射
  cNoticeTypeOptions = [
    { label: '地主戶', value: EnumHouseType.地主戶 },
    { label: '銷售戶', value: EnumHouseType.銷售戶 }
  ];
  // 動態獲取標題文字
  get dynamicTitle(): string {
    const option = this.cNoticeTypeOptions.find(option =>
      option.value === this.typeContentManagementSalesAccount.CNoticeType
    );
    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';
  }
  // 設置通知類型（可供外部調用）
  setCNoticeType(noticeType: EnumHouseType): void {
    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {
      this.typeContentManagementSalesAccount.CNoticeType = noticeType;
      // 同時設定 CFormType 以保持一致性
      this.typeContentManagementSalesAccount.CFormType = noticeType;
    }
  }

  CUiTypeOptions: any[] = [
    {
      value: 1, label: '建材選色'
    },
    {
      value: 2, label: '群組選樣_選色'
    }, {
      value: 3, label: '建材選樣'
    }];
  CRemarkTypeOptions = ["正常", "留料"];
  buildCaseId: number;
  isSubmitting: boolean = false;


  override ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      if (params) {
        const idParam = params.get('id');
        const id = idParam ? +idParam : 0;
        this.buildCaseId = id

        if (this.buildCaseId > 0) {
          this.getListRegularNoticeFileHouseHold()
        } else {
          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回
          console.error('Invalid buildCaseId:', this.buildCaseId);
          this.message.showErrorMSG("無效的建案ID，請重新選擇建案");
          this.goBack();
        }
      }
    });

    // 處理查詢參數中的戶型
    this.route.queryParams.subscribe(queryParams => {
      if (queryParams['houseType']) {
        const houseType = +queryParams['houseType'];
        this.setCNoticeType(houseType);
      }
    });
  }

  ngOnDestroy(): void {
    // 確保在組件銷毀時恢復body的滾動
    document.body.style.overflow = 'auto';
  }

  getItemByValue(value: any, options: any[]) {
    for (const item of options) {
      if (item.value === value) {
        return item;
      }
    }
    return null;
  }

  selectedItems: { [key: string]: boolean } = {};
  selectedRemarkType: { [key: string]: boolean } = {};

  // 新增：戶別選擇器相關屬性
  buildingData: any = {}; // 存放建築物戶別資料

  detectFiles(event: any, formItemReq_: any) {
    const file = event.target.files[0]
    if (file) {
      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        let base64Str: string = reader.result as string;
        if (!base64Str) {
          return;
        }
        if (formItemReq_.listPictures.length > 0) {
          formItemReq_.listPictures[0] = {
            id: new Date().getTime(),
            name: file.name.split('.')[0],
            data: base64Str,
            extension: this._utilityService.getFileExtension(file.name),
            CFile: file
          };
        } else {
          formItemReq_.listPictures.push({
            id: new Date().getTime(),
            name: file.name.split('.')[0],
            data: base64Str,
            extension: this._utilityService.getFileExtension(file.name),
            CFile: file
          });
        }
        event.target.value = null;
      };
    }
  }

  removeImage(pictureId: number, formItemReq_: any) {
    if (formItemReq_.listPictures.length) {
      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)
    }
  }
  renameFile(event: any, index: number, formItemReq_: any) {
    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);
    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });
    formItemReq_.listPictures[index].CFile = newFile
  }

  // 輪播功能方法
  nextImage(formItemReq: any) {
    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {
      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;
    }
  }

  prevImage(formItemReq: any) {
    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {
      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0
        ? formItemReq.CMatrialUrl.length - 1
        : formItemReq.currentImageIndex - 1;
    }
  }
  getCurrentImage(formItemReq: any): string | null {
    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {
      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];
    }
    return null;
  }

  // 放大功能方法
  openImageModal(formItemReq: any, imageIndex?: number) {
    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {
      if (imageIndex !== undefined) {
        formItemReq.currentImageIndex = imageIndex;
      }
      formItemReq.isModalOpen = true;
      // 防止背景滾動
      document.body.style.overflow = 'hidden';
    }
  }

  closeImageModal(formItemReq: any) {
    formItemReq.isModalOpen = false;
    // 恢復背景滾動
    document.body.style.overflow = 'auto';
  }

  // 模態窗口中的輪播方法
  nextImageModal(formItemReq: any) {
    this.nextImage(formItemReq);
  }

  prevImageModal(formItemReq: any) {
    this.prevImage(formItemReq);
  }

  // 鍵盤事件處理
  onKeydown(event: KeyboardEvent, formItemReq: any) {
    if (formItemReq.isModalOpen) {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          this.prevImageModal(formItemReq);
          break;
        case 'ArrowRight':
          event.preventDefault();
          this.nextImageModal(formItemReq);
          break;
        case 'Escape':
          event.preventDefault();
          this.closeImageModal(formItemReq);
          break;
      }
    }
  }

  // 新增：從 HouseholdItem 陣列中提取戶別代碼
  extractHouseholdCodes(households: any[]): string[] {
    if (!households || !Array.isArray(households)) {
      return [];
    }
    return households.map(h => h.code || h);
  }
  // 新增：處理戶別選擇變更
  onHouseholdSelectionChange(selectedHouseholds: string[], formItemReq: any) {
    // 重置所有戶別選擇狀態
    Object.keys(formItemReq.selectedItems).forEach(key => {
      formItemReq.selectedItems[key] = false;
    });

    // 設置選中的戶別
    selectedHouseholds.forEach(household => {
      formItemReq.selectedItems[household] = true;
    });

    // 更新全選狀態
    formItemReq.allSelected = this.houseHoldList.length > 0 &&
      this.houseHoldList.every(item => formItemReq.selectedItems[item]);

    // 更新緩存
    this.updateSelectedHouseholdsCache(formItemReq);
  }

  // 新增：取得已選戶別數組
  getSelectedHouseholds(formItemReq: any): string[] {
    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);
  }

  // 新增：更新已選戶別緩存
  private updateSelectedHouseholdsCache(formItemReq: any): void {
    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);
  }

  // 新增：更新所有項目的緩存
  private updateAllSelectedHouseholdsCache(): void {
    if (this.arrListFormItemReq) {
      this.arrListFormItemReq.forEach(formItemReq => {
        this.updateSelectedHouseholdsCache(formItemReq);
      });
    }
  }



  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {
    formItemReq_.selectedRemarkType[item] = checked;
  }

  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {
    const remarkObject: { [key: string]: boolean } = {};
    for (const option of CRemarkTypeOptions) {
      remarkObject[option] = false;
    }
    const remarkTypes = CRemarkType.split('-');
    for (const type of remarkTypes) {
      if (CRemarkTypeOptions.includes(type)) {
        remarkObject[type] = true;
      }
    }
    return remarkObject;
  }

  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {
    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();

    items.forEach(item => {
      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;
      if (map.has(key)) {
        const existing = map.get(key)!;
        existing.count += 1;
      } else {
        map.set(key, { item: { ...item }, count: 1 });
      }
    });

    return Array.from(map.values()).map(({ item, count }) => ({
      ...item,
      CTotalAnswer: count
    }));
  }


  getMaterialList() { // call when create
    this._materialService.apiMaterialGetMaterialListPost$Json({
      body: {
        CBuildCaseId: this.buildCaseId,
        CPagi: false
      }
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {


          this.arrListFormItemReq = res.Entries.map((o: GetMaterialListResponse) => {
            return {
              CDesignFileUrl: null,
              CFormItemHouseHold: null,
              CFormId: null,
              CLocation: o.CLocation,
              CName: o.CName,
              CPart: o.CPart,
              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,
              CRemarkType: null,
              CTotalAnswer: 0,
              CRequireAnswer: 1,
              CUiType: 0, selectedItems: {},
              selectedRemarkType: this.selectedRemarkType,
              allSelected: false,
              listPictures: [], selectedCUiType: this.CUiTypeOptions[0],
              currentImageIndex: 0,
              isModalOpen: false,
              isCollapsed: true, // 新項目默認收合
              CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64).filter((url: any) => url != null) as string[] : []
            }
          })
          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]

          // 初始化過濾列表
          this.updateFilteredList()
        }
      })
    ).subscribe();
  }

  listFormItem: GetListFormItemRes | null = null
  isNew: boolean = true

  getListFormItem() {
    this._formItemService.apiFormItemGetListFormItemPost$Json({
      body: {
        CBuildCaseId: this.buildCaseId,
        CFormType: this.typeContentManagementSalesAccount.CFormType,
        CIsPaging: false
      }
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.listFormItem = res.Entries
          this.isNew = res.Entries.formItems ? false : true

          if (res.Entries.formItems) {
            this.houseHoldList.forEach(item => this.selectedItems[item] = false);
            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);

            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {
              return {
                CFormId: this.listFormItem?.CFormId,
                CDesignFileUrl: o.CDesignFileUrl,
                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),
                CFile: o.CFile,
                CFormItemHouseHold: o.CFormItemHouseHold,
                CFormItemId: o.CFormItemId,
                CLocation: o.CLocation,
                CName: o.CName,
                CPart: o.CPart,
                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,
                CRemarkType: o.CRemarkType,
                CTotalAnswer: o.CTotalAnswer,
                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,
                CUiType: o.CUiType,
                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },
                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,
                listPictures: [], selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],
                currentImageIndex: 0,
                isModalOpen: false,
                isCollapsed: true, // 現有項目默認收合
                selectedHouseholdsCached: [] // 初始化緩存，稍後會更新
              }
            })

            // 初始化過濾列表
            this.updateFilteredList();

            // 手動觸發變更檢測
            this.cdr.detectChanges();
          } else {
            // 當無資料時，載入材料清單供新增使用
            this.getMaterialList();
          }

          // 初始化所有項目的緩存
          this.updateAllSelectedHouseholdsCache();

          // 最終觸發變更檢測
          this.cdr.detectChanges();
        } else {
          console.error('getListFormItem failed:', res);
        }
      })
    ).subscribe({
      error: (error) => {
        console.error('getListFormItem error:', error);
      }
    })
  }

  changeSelectCUiType(formItemReq: any) {
    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {
      formItemReq.CRequireAnswer = 1
    }
  }
  getHouseHoldListByNoticeType(data: any[]) {
    for (let item of data) {
      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {
        return item.CHouseHoldList;
      }
    }
    return [];
  }

  arrListFormItemReq: ExtendedSaveListFormItemReq[] = []
  filteredArrListFormItemReq: ExtendedSaveListFormItemReq[] = []
  searchQuery: string = ''

  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {"key": true } => ["key"]
    return Object.keys(obj).filter(key => obj[key]);
  }

  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {
    return Object.keys(obj)
      .filter(key => obj[key])
      .join('-');
  }

  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {
    if (selectedCUiType && selectedCUiType.value == 3) {
      return this.getKeysWithTrueValueJoined(selectedRemarkType)
    }
  }

  getStringAfterComma(inputString: string): string {
    const parts = inputString.split(',');
    if (parts.length > 1) {
      return parts[1];
    } else return ""
  }

  formatFile(listPictures: any) {
    if (listPictures && listPictures.length > 0) {
      return {
        Base64String: this.getStringAfterComma(listPictures[0].data) || null,
        FileExtension: listPictures[0].extension || null,
        FileName: listPictures[0].CFile.name || listPictures[0].name || null,
      }
    } else return undefined

  }


  validation() {
    this.valid.clear();
    let hasInvalidCUiType = false;
    let hasInvalidCRequireAnswer = false;
    let hasInvalidItemName = false;

    for (const item of this.saveListFormItemReq) {
      if (!hasInvalidCUiType && (!item.CUiType)) {
        hasInvalidCUiType = true;
      }
      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {
        hasInvalidCRequireAnswer = true;
      }
      if (item.CTotalAnswer && item.CRequireAnswer) {
        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {
          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);
        }
      }

      if (!hasInvalidItemName && (!item.CItemName)) {
        hasInvalidItemName = true;
      }
    }
    if (hasInvalidCUiType) {
      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');
    }
    if (hasInvalidCRequireAnswer) {
      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');
    }
    if (hasInvalidItemName) {
      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');
    }
  }

  saveListFormItemReq: SaveListFormItemReq[]

  onSubmit() {
    // 設置提交狀態
    this.isSubmitting = true;

    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {
      return {
        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,
        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,
        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),
        CFormItemId: e.CFormItemId ? e.CFormItemId : null,
        CFormID: this.isNew ? null : this.listFormItem?.CFormId,
        CName: e.CName,
        CPart: e.CPart,
        CLocation: e.CLocation,
        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,
        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,
        CTotalAnswer: e.CTotalAnswer,
        CRequireAnswer: e.CRequireAnswer,
        CUiType: e.selectedCUiType.value,
      }
    })
    this.validation()
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      // 滾動到第一個有錯誤的項目
      this.scrollToFirstErrorItem();
      this.isSubmitting = false;
      return
    }
    if (this.isNew) {
      this.createListFormItem()

    } else {
      this.saveListFormItem()
    }
  }

  saveListFormItem() {
    this._formItemService.apiFormItemSaveListFormItemPost$Json({
      body: this.saveListFormItemReq
    }).subscribe({
      next: (res) => {
        this.isSubmitting = false;
        if (res.StatusCode == 0) {
          this.message.showSucessMSG("執行成功");
          // this.getListFormItem()
          this.goBack()
        }
      },
      error: (error) => {
        this.isSubmitting = false;
        console.error('Save error:', error);
      }
    })
  }

  creatListFormItem: CreateListFormItem

  createListFormItem() {
    this.creatListFormItem = {
      CBuildCaseId: this.buildCaseId,
      CFormItem: this.saveListFormItemReq || null,
      CFormType: this.typeContentManagementSalesAccount.CFormType,
    }

    this._formItemService.apiFormItemCreateListFormItemPost$Json({
      body: this.creatListFormItem
    }).subscribe({
      next: (res) => {
        this.isSubmitting = false;
        if (res.StatusCode == 0) {
          this.message.showSucessMSG("執行成功");
          // this.getListFormItem()
          this.goBack()
        }
      },
      error: (error) => {
        this.isSubmitting = false;
        console.error('Create error:', error);
      }
    })
  }

  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {
    const c: { [key: string]: boolean } = {};
    for (const item of a) {
      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);
      c[item] = !!matchingItem;
    }
    return c;
  } //["House1", "House2", "House3"] => [{CHousehold: "House1", CIsSelect: true,... }, ... ]

  /**
   * 複製當前表單到新表單
   */
  copyToNewForm() {
    // 先取得當前有效的材料清單
    this._materialService.apiMaterialGetMaterialListPost$Json({
      body: {
        CBuildCaseId: this.buildCaseId,
        CPagi: false
      }
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          // 建立有效材料清單的鍵值對應
          const validMaterialKeys = new Set<string>();
          res.Entries.forEach((material: any) => {
            const key = `${material.CLocation}_${material.CName}_${material.CPart}`;
            validMaterialKeys.add(key);
          });

          // 篩選出仍然有效的表單項目
          const validFormItems = this.arrListFormItemReq.filter((item: any) => {
            const itemKey = `${item.CLocation}_${item.CName}_${item.CPart}`;
            return validMaterialKeys.has(itemKey);
          });

          if (validFormItems.length === 0) {
            this.message.showErrorMSG("沒有有效的表單項目可以複製");
            return;
          }

          // 準備複製的表單項目數據
          this.saveListFormItemReq = validFormItems.map((e: any) => {
            return {
              CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,
              CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,
              CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),
              CFormItemId: null, // 設為 null 以建立新項目
              CFormID: null, // 設為 null 以建立新表單
              CName: e.CName,
              CPart: e.CPart,
              CLocation: e.CLocation,
              CItemName: e.CItemName,
              CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,
              CTotalAnswer: e.CTotalAnswer,
              CRequireAnswer: e.CRequireAnswer,
              CUiType: e.selectedCUiType.value,
            }
          });

          // 執行驗證
          this.validation()
          if (this.valid.errorMessages.length > 0) {
            this.message.showErrorMSGs(this.valid.errorMessages);
            return
          }

          // 建立複製的表單
          this.creatListFormItem = {
            CBuildCaseId: this.buildCaseId,
            CFormItem: this.saveListFormItemReq || null,
            CFormType: this.typeContentManagementSalesAccount.CFormType,
          }

          this._formItemService.apiFormItemCreateListFormItemPost$Json({
            body: this.creatListFormItem
          }).subscribe(createRes => {
            if (createRes.StatusCode == 0) {
              this.message.showSucessMSG(`複製表單成功，已篩選 ${validFormItems.length} 個有效項目`);
              // 重新載入資料以顯示新的未鎖定表單
              this.getListFormItem()
            }
          })
        } else {
          this.message.showErrorMSG("無法取得材料清單，複製失敗");
        }
      })
    ).subscribe();
  }

  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)
  private loadBuildingDataFromAPI(): void {
    if (!this.buildCaseId) return;

    this._houseService.apiHouseGetDropDownPost$Json({ buildCaseId: this.buildCaseId }).subscribe({
      next: (response) => {
        console.log('GetDropDown API response:', response);
        if (response.Entries) {
          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);
          console.log('Converted buildingData:', this.buildingData);
        }
      },
      error: (error) => {
        console.error('Error loading building data from API:', error);
        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料
        if (this.houseHoldList && this.houseHoldList.length > 0) {
          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);
        }
      }
    });
  }

  // 新增：將 API 回應轉換為建築物資料格式
  private convertApiResponseToBuildingData(entries: any): any {
    const buildingData: any = {};

    Object.entries(entries).forEach(([building, houses]: [string, any]) => {
      buildingData[building] = houses.map((house: any) => ({
        code: house.HouseName,
        building: house.Building,
        floor: house.Floor,
        houseId: house.HouseId,
        houseName: house.HouseName,
        isSelected: false,
        isDisabled: false
      }));
    });

    return buildingData;
  }

  // 新增：將戶別清單轉換為建築物資料格式
  convertHouseHoldListToBuildingData(houseHoldList: string[]): any {
    if (!houseHoldList || houseHoldList.length === 0) {
      return {};
    }

    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組
    const buildingData: any = {};

    houseHoldList.forEach(household => {
      // 嘗試從戶別名稱中提取建築物代碼
      const buildingMatch = household.match(/^([A-Z]+)/);
      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';

      if (!buildingData[building]) {
        buildingData[building] = [];
      }

      // 計算樓層（假設每4戶為一層）
      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));
      const floor = Math.ceil(houseNumber / 4);

      buildingData[building].push({
        code: household,
        building: building,
        floor: `${floor}F`,
        isSelected: false,
        isDisabled: false
      });
    }); return buildingData;
  }

  houseHoldList: any[];

  getListRegularNoticeFileHouseHold() {
    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({
      body: this.buildCaseId
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)

          // 載入建築物資料 (只呼叫一次 GetDropDown API)
          this.loadBuildingDataFromAPI();

          this.getListFormItem()
        } else {
          console.error('getListRegularNoticeFileHouseHold failed:', res);
        }
      })
    ).subscribe({
      error: (error) => {
        console.error('getListRegularNoticeFileHouseHold error:', error);
      }
    })
  }
  goBack() {
    this._eventService.push({
      action: EEvent.GET_BUILDCASE,
      payload: this.buildCaseId
    })
    this.location.back()
  }

  // UI優化相關方法

  /**
   * 檢查項目是否已完成
   */
  isItemCompleted(formItemReq: ExtendedSaveListFormItemReq): boolean {
    // 檢查必填欄位是否都已填寫
    const hasItemName = !!formItemReq.CItemName && formItemReq.CItemName.trim() !== '';
    const hasUiType = !!formItemReq.selectedCUiType && formItemReq.selectedCUiType.value;
    const hasRequireAnswer = !!formItemReq.CRequireAnswer && formItemReq.CRequireAnswer > 0;

    // 如果是建材選樣類型，檢查是否有選擇備註類型
    let hasRemarkType = true;
    if (formItemReq.selectedCUiType?.value === 3) {
      hasRemarkType = !!formItemReq.selectedRemarkType &&
        Object.values(formItemReq.selectedRemarkType).some(selected => selected);
    }

    return hasItemName && hasUiType && hasRequireAnswer && hasRemarkType;
  }

  /**
   * 獲取已完成項目數量
   */
  getCompletedItemsCount(): number {
    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;
    return this.arrListFormItemReq.filter(item => this.isItemCompleted(item)).length;
  }

  /**
   * 獲取進度百分比
   */
  getProgressPercentage(): number {
    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;
    const completed = this.getCompletedItemsCount();
    return Math.round((completed / this.arrListFormItemReq.length) * 100);
  }

  /**
   * 滾動到指定項目
   */
  scrollToItem(index: number): void {
    const element = document.getElementById(`form-item-${index}`);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });

      // 添加高亮效果
      element.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-75');
      setTimeout(() => {
        element.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-75');
      }, 2000);
    }
  }

  /**
   * 滾動到第一個未完成的項目
   */
  scrollToFirstIncompleteItem(): void {
    if (!this.arrListFormItemReq) return;

    const firstIncompleteIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));
    if (firstIncompleteIndex !== -1) {
      this.scrollToItem(firstIncompleteIndex);
    }
  }

  /**
   * 滾動到第一個有錯誤的項目
   */
  scrollToFirstErrorItem(): void {
    if (!this.arrListFormItemReq) return;

    // 找到第一個有錯誤的項目
    const firstErrorIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));
    if (firstErrorIndex !== -1) {
      this.scrollToItem(firstErrorIndex);
    }
  }

  /**
   * 滾動到頂部
   */
  scrollToTop(): void {
    window.scrollTo(0, 0);
  }

  /**
   * 回到頂部
   */
  goToTop(): void {
    console.log('goToTop clicked - method called');

    // 優先滾動到頁面最頂部的header區塊
    const headerElement = document.querySelector('nb-card-header') ||
      document.querySelector('.card-header') ||
      document.querySelector('nb-card') ||
      document.querySelector('.header') ||
      document.querySelector('h1, h2, h3') ||
      document.body.firstElementChild;

    if (headerElement) {
      (headerElement as HTMLElement).scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
      console.log('Scrolled to header element:', headerElement.tagName);

      // 額外向上滾動一點，確保header完全可見
      setTimeout(() => {
        window.scrollBy({
          top: -50, // 向上滾動50px
          behavior: 'smooth'
        });
      }, 500);
      return;
    }

    // 備用方案：滾動到第一個表單項目的上方
    if (this.arrListFormItemReq && this.arrListFormItemReq.length > 0) {
      const firstElement = document.getElementById('form-item-0');
      if (firstElement) {
        firstElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });

        // 向上滾動更多距離，確保看到header
        setTimeout(() => {
          window.scrollBy({
            top: -200, // 向上滾動200px
            behavior: 'smooth'
          });
        }, 500);
        console.log('Scrolled to first form item with extra offset');
        return;
      }
    }

    // 最後的備用方法
    console.log('Using fallback scroll methods');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  /**
   * 滾動到底部 - 滾動到 footer 資訊區塊
   */
  scrollToBottom(): void {
    console.log('=== 至底功能被點擊 - 滾動到 footer 資訊區塊 ===');

    // 立即顯示一個簡短的視覺反饋
    const button = document.querySelector('button[title="到底部"]') as HTMLElement;
    if (button) {
      button.style.transform = 'scale(0.95)';
      setTimeout(() => {
        button.style.transform = '';
      }, 150);
    }

    // 滾動到 footer 資訊區塊
    setTimeout(() => {
      // 方法1: 滾動到 nb-card-footer 元素
      const footerElement = document.querySelector('nb-card-footer') as HTMLElement;
      if (footerElement) {
        console.log('找到 footer 元素，滾動到 footer 資訊區塊');
        footerElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center', // 改為 center 讓 footer 在畫面中央
          inline: 'nearest'
        });
        console.log('已滾動到 footer 資訊區塊');
        return;
      }

      // 方法2: 尋找包含統計資訊的 footer 區域（使用更精確的選擇器）
      const progressFooter = document.querySelector('nb-card-footer .flex.items-center.justify-center') as HTMLElement;
      if (progressFooter) {
        console.log('找到統計資訊 footer，滾動到該區域');
        progressFooter.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });
        console.log('已滾動到統計資訊區域');
        return;
      }

      // 方法3: 滾動到最後一個表單項目
      if (this.filteredArrListFormItemReq && this.filteredArrListFormItemReq.length > 0) {
        const lastIndex = this.filteredArrListFormItemReq.length - 1;
        const lastElement = document.getElementById(`form-item-${lastIndex}`);
        if (lastElement) {
          console.log(`找到最後一個表單項目: form-item-${lastIndex}`);
          lastElement.scrollIntoView({
            behavior: 'smooth',
            block: 'end',
            inline: 'nearest'
          });

          // 額外向下滾動到 footer 區域
          setTimeout(() => {
            window.scrollBy({
              top: 200, // 增加滾動距離以確保看到 footer
              behavior: 'smooth'
            });
          }, 500);

          console.log('已滾動到最後一個表單項目並向下偏移到 footer');
          return;
        }
      }

      // 備用方法: 滾動到頁面底部
      console.log('使用備用方法：滾動到頁面底部');
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: 'smooth'
      });
    }, 100);
  }

  /**
   * 切換項目收合狀態
   */
  toggleItemCollapse(formItemReq: ExtendedSaveListFormItemReq): void {
    formItemReq.isCollapsed = !formItemReq.isCollapsed;
  }

  /**
   * 全部展開
   */
  expandAll(): void {
    if (this.arrListFormItemReq) {
      this.arrListFormItemReq.forEach(item => {
        item.isCollapsed = false;
      });
      this.cdr.detectChanges();
    }
  }

  /**
   * 全部收合
   */
  collapseAll(): void {
    if (this.arrListFormItemReq) {
      this.arrListFormItemReq.forEach(item => {
        item.isCollapsed = true;
      });
      this.cdr.detectChanges();
    }
  }

  /**
   * 只展開未完成的項目
   */
  expandIncompleteOnly(): void {
    if (this.arrListFormItemReq) {
      this.arrListFormItemReq.forEach(item => {
        item.isCollapsed = this.isItemCompleted(item);
      });
      this.cdr.detectChanges();
    }
  }

  /**
   * 搜尋功能
   */
  onSearch(): void {
    if (!this.searchQuery.trim()) {
      this.filteredArrListFormItemReq = [...this.arrListFormItemReq];
    } else {
      const query = this.searchQuery.toLowerCase().trim();
      this.filteredArrListFormItemReq = this.arrListFormItemReq.filter(item => {
        return (
          item.CName?.toLowerCase().includes(query) ||
          item.CPart?.toLowerCase().includes(query) ||
          item.CLocation?.toLowerCase().includes(query) ||
          item.CItemName?.toLowerCase().includes(query)
        );
      });
    }
    this.cdr.detectChanges();
  }

  /**
   * 清除搜尋
   */
  clearSearch(): void {
    this.searchQuery = '';
    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];
    this.cdr.detectChanges();
  }

  /**
   * 更新過濾列表
   */
  private updateFilteredList(): void {
    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];
    if (this.searchQuery.trim()) {
      this.onSearch();
    }
  }

}
