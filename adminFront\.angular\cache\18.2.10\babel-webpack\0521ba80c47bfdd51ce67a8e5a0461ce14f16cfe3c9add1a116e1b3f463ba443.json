{"ast": null, "code": "import * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nexport let MomentPipe = /*#__PURE__*/(() => {\n  class MomentPipe {\n    transform(value, dateFormat) {\n      if (value == null || value === undefined) return null;\n      const date = moment(value).format(dateFormat);\n      const stillUtc = moment.utc(date).toDate();\n      return moment(stillUtc).local().format(dateFormat);\n    }\n    static {\n      this.ɵfac = function MomentPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || MomentPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"localDate\",\n        type: MomentPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return MomentPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}