{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nexport let RegularNoticeComponent = /*#__PURE__*/(() => {\n  class RegularNoticeComponent extends BaseComponent {\n    constructor(_allow) {\n      super(_allow);\n      this._allow = _allow;\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n    }\n    static {\n      this.ɵfac = function RegularNoticeComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RegularNoticeComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RegularNoticeComponent,\n        selectors: [[\"ngx-regular-notice\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 2,\n        vars: 0,\n        template: function RegularNoticeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\");\n            i0.ɵɵtext(1, \"regularNotice\");\n            i0.ɵɵelementEnd();\n          }\n        },\n        dependencies: [CommonModule, SharedModule]\n      });\n    }\n  }\n  return RegularNoticeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}