import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { tap } from 'rxjs/operators';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { BuildCaseService, FormItemService } from 'src/services/api/services';
import { MessageService } from 'src/app/shared/services/message.service';
import { GetListFormItemRes } from 'src/services/api/models';
import { SharedModule } from '../../components/shared.module';
import { BaseComponent } from '../../components/base/baseComponent';
import { EventService, IEvent, EEvent } from 'src/app/shared/services/event.service';
import { EnumHouseType } from 'src/app/shared/enum/enumHouseType';

export interface selectItem {
  label: string,
  value: number,
  key?: string
}

// 戶型選項介面
export interface HouseTypeOption {
  label: string;
  value: EnumHouseType;
}

@Component({
  selector: 'ngx-content-management-sales-account',
  templateUrl: './content-management-sales-account.component.html',
  styleUrls: ['./content-management-sales-account.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule,],
})
export class ContentManagementSalesAccountComponent extends BaseComponent implements OnInit {

  // 戶型選項
  houseTypeOptions: HouseTypeOption[] = [
    { label: '地主戶', value: EnumHouseType.地主戶 },
    { label: '銷售戶', value: EnumHouseType.銷售戶 }
  ];

  // 選中的戶型
  selectedHouseType: EnumHouseType = EnumHouseType.銷售戶;

  toggleSwitch(CIsLock: any) {
    if (CIsLock) {
      this.unLock()
    } else {
      this.onLock()
    }
  }

  tempBuildCaseID: number = -1
  selectedBuilding: any;
  buildingSelectedOptions: any[] = [{ value: '', label: '全部' }];

  formItems: any;
  listFormItem: GetListFormItemRes;
  override pageSize = 20;

  buildCaseId: number;
  cBuildCaseSelected: any;
  userBuildCaseOptions: any;

  constructor(
    private _allow: AllowHelper,
    private router: Router,
    private message: MessageService,
    private _buildCaseService: BuildCaseService,
    private _formItemService: FormItemService,
    private _eventService: EventService,
  ) {
    super(_allow);
    this._eventService.receive().pipe(
      tap((res: IEvent) => {
        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {
          this.tempBuildCaseID = res.payload
        }
      })
    ).subscribe()
  }

  override ngOnInit(): void {
    this.cBuildCaseSelected = null;
    this.getUserBuildCase()
  }

  getUserBuildCase() {
    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({
      body: {
        CBuildCaseId: this.buildCaseId
      }
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.userBuildCaseOptions = res.Entries.map(res => {
            return {
              CBuildCaseName: res.CBuildCaseName,
              cID: res.cID
            };
          }); if (this.tempBuildCaseID != -1) {
            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseID)
            if (index >= 0) {
              this.cBuildCaseSelected = this.userBuildCaseOptions[index]
            } else {
              this.cBuildCaseSelected = this.userBuildCaseOptions[0];
            }
          } else {
            this.cBuildCaseSelected = this.userBuildCaseOptions[0];
          }
          if (this.cBuildCaseSelected && this.cBuildCaseSelected.cID && this.cBuildCaseSelected.cID > 0) {
            this.getListFormItem();
          }
        }
      })
    ).subscribe();
  }
  // 移除硬編碼的類型配置，改為動態使用選中的戶型
  // typeContentManagementSalesAccount = {
  //   CFormType: 2,
  // }

  // 戶型選項變更處理
  onHouseTypeChange(): void {
    // 當戶型改變時，重新載入表單項目
    if (this.cBuildCaseSelected?.cID) {
      this.getListFormItem();
    }
  }
  getListFormItem() {
    this._formItemService.apiFormItemGetListFormItemPost$Json({
      body: {
        CBuildCaseId: this.cBuildCaseSelected.cID,
        CFormType: this.selectedHouseType, // 使用選中的戶型
        PageIndex: this.pageIndex,
        PageSize: this.pageSize,
        CIsPaging: true
      }
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.formItems = res.Entries.formItems;
          this.listFormItem = res.Entries;
          this.totalRecords = res.TotalItems ? res.TotalItems : 0
        }
      })
    ).subscribe();
  }

  onSelectionChangeBuildCase() {
    this.getListFormItem();
  }

  pageChanged(newPage: number) {
    this.pageIndex = newPage;
    this.getListFormItem();
  }

  onLock() {
    this._formItemService.apiFormItemLockFormItemPost$Json({
      body: {
        CBuildCaseId: this.cBuildCaseSelected.cID,
        CFormId: this.listFormItem.CFormId
      }
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        this.message.showSucessMSG("執行成功");
      } else {
        // this.message.showErrorMSG(res.Message!);
        this.message.showErrorMSG("無資料，不可鎖定");
      }
      this.getListFormItem();
    });
  }

  unLock() {
    this._formItemService.apiFormItemUnlockFormItemPost$Json({
      body: {
        CBuildCaseID: this.cBuildCaseSelected.cID,
        CFormId: this.listFormItem.CFormId
      }
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        this.message.showSucessMSG("執行成功");
      } else {
        this.message.showErrorMSG(res.Message!);
      } this.getListFormItem();
    });
  }
  navidateDetai() {
    // 檢查是否有選擇建案
    if (!this.cBuildCaseSelected || !this.cBuildCaseSelected.cID || this.cBuildCaseSelected.cID <= 0) {
      this.message.showErrorMSG("請先選擇建案");
      return;
    }

    // 導航到詳細頁面，使用建案ID、戶型和表單ID參數
    this.router.navigate([`pages/content-management-sales-account/${this.cBuildCaseSelected.cID}`], {
      queryParams: {
        houseType: this.selectedHouseType,
      }
    });
  }
}
