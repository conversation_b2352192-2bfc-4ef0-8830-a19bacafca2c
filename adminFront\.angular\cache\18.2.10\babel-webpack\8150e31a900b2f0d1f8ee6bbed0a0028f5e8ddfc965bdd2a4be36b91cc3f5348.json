{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { VisitorsAnalyticsData } from '../data/visitors-analytics';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./periods.service\";\nexport let VisitorsAnalyticsService = /*#__PURE__*/(() => {\n  class VisitorsAnalyticsService extends VisitorsAnalyticsData {\n    constructor(periodService) {\n      super();\n      this.periodService = periodService;\n      this.pieChartValue = 75;\n      this.innerLinePoints = [94, 188, 225, 244, 253, 254, 249, 235, 208, 173, 141, 118, 105, 97, 94, 96, 104, 121, 147, 183, 224, 265, 302, 333, 358, 375, 388, 395, 400, 400, 397, 390, 377, 360, 338, 310, 278, 241, 204, 166, 130, 98, 71, 49, 32, 20, 13, 9];\n      this.outerLinePoints = [85, 71, 59, 50, 45, 42, 41, 44, 58, 88, 136, 199, 267, 326, 367, 391, 400, 397, 376, 319, 200, 104, 60, 41, 36, 37, 44, 55, 74, 100, 131, 159, 180, 193, 199, 200, 195, 184, 164, 135, 103, 73, 50, 33, 22, 15, 11];\n    }\n    generateOutlineLineData() {\n      const months = this.periodService.getMonths();\n      const outerLinePointsLength = this.outerLinePoints.length;\n      const monthsLength = months.length;\n      return this.outerLinePoints.map((p, index) => {\n        const monthIndex = Math.round(index / 4);\n        const label = index % Math.round(outerLinePointsLength / monthsLength) === 0 ? months[monthIndex] : '';\n        return {\n          label,\n          value: p\n        };\n      });\n    }\n    getInnerLineChartData() {\n      return observableOf(this.innerLinePoints);\n    }\n    getOutlineLineChartData() {\n      return observableOf(this.generateOutlineLineData());\n    }\n    getPieChartData() {\n      return observableOf(this.pieChartValue);\n    }\n    static {\n      this.ɵfac = function VisitorsAnalyticsService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || VisitorsAnalyticsService)(i0.ɵɵinject(i1.PeriodsService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: VisitorsAnalyticsService,\n        factory: VisitorsAnalyticsService.ɵfac\n      });\n    }\n  }\n  return VisitorsAnalyticsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}