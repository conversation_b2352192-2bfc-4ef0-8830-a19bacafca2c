/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { apiPreOrderSettingGetPreOrderSettingPost$Json } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-json';
import { ApiPreOrderSettingGetPreOrderSettingPost$Json$Params } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-json';
import { apiPreOrderSettingGetPreOrderSettingPost$Plain } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-plain';
import { ApiPreOrderSettingGetPreOrderSettingPost$Plain$Params } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-plain';
import { apiPreOrderSettingSavePreOrderSettingPost$Json } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-json';
import { ApiPreOrderSettingSavePreOrderSettingPost$Json$Params } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-json';
import { apiPreOrderSettingSavePreOrderSettingPost$Plain } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-plain';
import { ApiPreOrderSettingSavePreOrderSettingPost$Plain$Params } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-plain';
import { GetPreOrderSettingResponseListResponseBase } from '../models/get-pre-order-setting-response-list-response-base';
import { StringResponseBase } from '../models/string-response-base';

@Injectable({ providedIn: 'root' })
export class PreOrderSettingService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `apiPreOrderSettingGetPreOrderSettingPost()` */
  static readonly ApiPreOrderSettingGetPreOrderSettingPostPath = '/api/PreOrderSetting/GetPreOrderSetting';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPreOrderSettingGetPreOrderSettingPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPreOrderSettingGetPreOrderSettingPost$Plain$Response(params?: ApiPreOrderSettingGetPreOrderSettingPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPreOrderSettingResponseListResponseBase>> {
    return apiPreOrderSettingGetPreOrderSettingPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPreOrderSettingGetPreOrderSettingPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPreOrderSettingGetPreOrderSettingPost$Plain(params?: ApiPreOrderSettingGetPreOrderSettingPost$Plain$Params, context?: HttpContext): Observable<GetPreOrderSettingResponseListResponseBase> {
    return this.apiPreOrderSettingGetPreOrderSettingPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetPreOrderSettingResponseListResponseBase>): GetPreOrderSettingResponseListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPreOrderSettingGetPreOrderSettingPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPreOrderSettingGetPreOrderSettingPost$Json$Response(params?: ApiPreOrderSettingGetPreOrderSettingPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPreOrderSettingResponseListResponseBase>> {
    return apiPreOrderSettingGetPreOrderSettingPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPreOrderSettingGetPreOrderSettingPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPreOrderSettingGetPreOrderSettingPost$Json(params?: ApiPreOrderSettingGetPreOrderSettingPost$Json$Params, context?: HttpContext): Observable<GetPreOrderSettingResponseListResponseBase> {
    return this.apiPreOrderSettingGetPreOrderSettingPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetPreOrderSettingResponseListResponseBase>): GetPreOrderSettingResponseListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiPreOrderSettingSavePreOrderSettingPost()` */
  static readonly ApiPreOrderSettingSavePreOrderSettingPostPath = '/api/PreOrderSetting/SavePreOrderSetting';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPreOrderSettingSavePreOrderSettingPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPreOrderSettingSavePreOrderSettingPost$Plain$Response(params?: ApiPreOrderSettingSavePreOrderSettingPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiPreOrderSettingSavePreOrderSettingPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPreOrderSettingSavePreOrderSettingPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPreOrderSettingSavePreOrderSettingPost$Plain(params?: ApiPreOrderSettingSavePreOrderSettingPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiPreOrderSettingSavePreOrderSettingPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiPreOrderSettingSavePreOrderSettingPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPreOrderSettingSavePreOrderSettingPost$Json$Response(params?: ApiPreOrderSettingSavePreOrderSettingPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiPreOrderSettingSavePreOrderSettingPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiPreOrderSettingSavePreOrderSettingPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiPreOrderSettingSavePreOrderSettingPost$Json(params?: ApiPreOrderSettingSavePreOrderSettingPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiPreOrderSettingSavePreOrderSettingPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

}
