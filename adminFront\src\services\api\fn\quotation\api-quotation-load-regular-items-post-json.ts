/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetQuotationListResponseBase } from '../../models/get-quotation-list-response-base';
import { LoadDefaultItemsRequest } from '../../models/load-default-items-request';

export interface ApiQuotationLoadRegularItemsPost$Json$Params {
      body?: LoadDefaultItemsRequest
}

export function apiQuotationLoadRegularItemsPost$Json(http: HttpClient, rootUrl: string, params?: ApiQuotationLoadRegularItemsPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiQuotationLoadRegularItemsPost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetQuotationListResponseBase>;
    })
  );
}

apiQuotationLoadRegularItemsPost$Json.PATH = '/api/Quotation/LoadRegularItems';
