<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb *ngIf="!type"></ngx-breadcrumb>
    <div style="font-size: 32px;" *ngIf="type == 1">待審核列表 / 洽談紀錄</div>
    <div style="font-size: 32px;" *ngIf="type == 2">待審核列表 / 客變確認圖說</div>
    <div style="font-size: 32px;" *ngIf="type == 3">待審核列表 / 建案公佈欄文件</div>
    <div style="font-size: 32px;" *ngIf="type == 4">待審核列表 / 客變原則</div>
    <div style="font-size: 32px;" *ngIf="type == 5">待審核列表 / 標準圖說</div>
  </nb-card-header>
  <nb-card-body class="bg-white">
    <div class="my-2 w-100">
      <div class="flex items-center w-full">
        <div class="w-full flex flex-col items-start mr-4">
          <span class="mb-3" for="classification">{{'分類'}}</span>
          <nb-select class="w-full" [(ngModel)]="CType" [disabled]="isReadOnly">
            <nb-option *ngFor="let option of TYPE_WAITING_APPROVE" [value]="option.value">
              {{option.name}}
            </nb-option>
          </nb-select>
        </div>
        <div class="w-full flex flex-col items-start mr-4">
          <span class="mb-3" for="classification">{{'送審開始時間'}}</span>
          <input nbInput placeholder="年/月/日 -- --:--" [(ngModel)]="CDateStart" [nbDatepicker]="dateStartpicker">
          <nb-date-timepicker [max]="CDateEnd" format="yyyy/MM/dd hh mm:ss" withSeconds
            #dateStartpicker></nb-date-timepicker>
        </div>
        <div class="w-full flex flex-col items-start">
          <span class="mb-3 mr-2" for="classification">{{'送審結束時間'}}</span>
          <input class="w-full" nbInput placeholder="年/月/日 -- --:--" [(ngModel)]="CDateEnd"
            [nbDatepicker]="dateEndpicker">
          <nb-date-timepicker [min]="CDateStart" format="yyyy/MM/dd hh mm:ss" withSeconds
            #dateEndpicker></nb-date-timepicker>
        </div>
      </div>
    </div>
    <div class="flex justify-between mt-6">
      <div class="w-full flex items-center">
        <span class="mr-4" for="classification">{{'建案'}}</span>
        <nb-select class="w-[40%]" *ngIf="!!listUserBuildCases" [(ngModel)]="buildCaseId">
          <nb-option *ngFor="let buildCaseData of listUserBuildCases" [value]="buildCaseData.cID">
            {{buildCaseData.CBuildCaseName}}
          </nb-option>
        </nb-select>
      </div>
      <div class="w-full flex items-center">
        <span class="mr-4" for="CNameQuery">{{'名稱'}}</span>
        <nb-form-field>
          <input type="text" id="CNameQuery" nbInput class="w-full" [(ngModel)]="queryCName">
        </nb-form-field>
      </div>
      <button class="w-[200px] btn btn-info ml-2" *ngIf="isRead" (click)="searchList()">
        <i class="fas fa-search mr-1"></i> {{"查詢"}}
      </button>
    </div>
    <div class="table-responsive mt-4">
      <table class="table table-striped border">
        <thead>
          <tr class="text-white" style="background-color: #27ae60">
            <th>{{'項次'}}</th>
            <th>{{'建案'}}</th>
            <th>{{'類別'}}</th>
            <th>{{'名稱'}}</th>
            <th>{{'送審時間'}}</th>
            <th>{{'送審帳號'}}</th>
            <th>{{'管理'}}</th>
          </tr>
        </thead>
        <tbody *ngIf="!!listApprovalWaitingList && listApprovalWaitingList.length > 0">
          <tr *ngFor="let item of listApprovalWaitingList; let i = index">
            <td>{{ item.CID }}</td>
            <td>{{ item.CBuildcaseName }}</td>
            <td>
              <span class="px-3 py-1 rounded-full text-sm font-medium" [ngClass]="{
                      'bg-purple-100 text-purple-800': item.CType === 1,
                      'bg-blue-100 text-blue-800': item.CType === 2,
                      'bg-green-100 text-green-800': item.CType === 3,
                      'bg-orange-100 text-orange-800': item.CType === 4,
                      'bg-red-100 text-red-800': item.CType === 5,
                      'bg-gray-100 text-gray-800': !item.CType || item.CType < 1 || item.CType > 5
                    }">
                {{item.CType! | getTypeApprovalWaiting}}
              </span>
            </td>
            <td>
              <div class="flex items-center">
                <i class="fas fa-folder-open text-blue-500 mr-2"></i>
                <span class="font-medium">{{item.CName}}</span>
              </div>
            </td>
            <td>
              {{item.CCreateDT | date:'yyyy-MM-dd HH:mm:ss'}}
            </td>
            <td>
              {{item.CCreator}}
            </td>
            <td class="d-flex">
              <button class="btn btn-sm btn-outline-success mr-2 flex items-center" *ngIf="isUpdate"
                (click)="edit(item)">
                <i class="fas fa-eye mr-1"></i>
                {{ "查看" }}
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <ngx-pagination [(CollectionSize)]="totalRecords" [(Page)]="pageIndex" [(PageSize)]="pageSize"
      (PageChange)="getListPageChange($event)"></ngx-pagination>
  </nb-card-body>
</nb-card>