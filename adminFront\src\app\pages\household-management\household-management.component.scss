// 金額計算區塊樣式
.card {
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    }
}

// 營業稅區塊特殊樣式
.tax-section {
    background-color: #f8f9fa !important;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s ease;
    position: relative;

    &:hover {
        background-color: #f3f4f6 !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background-color: #0891b2;
        border-radius: 4px 0 0 4px;
        transition: width 0.2s ease;
    }

    &:hover::before {
        width: 6px;
    }
}

// 營業稅圖標容器
.tax-icon-wrapper {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #0891b2;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(8, 145, 178, 0.3);
    transition: all 0.2s ease;

    i {
        font-size: 1.1rem;
        color: white !important;
    }

    &:hover {
        transform: scale(1.05);
        background-color: #0e7490;
        box-shadow: 0 4px 12px rgba(8, 145, 178, 0.4);
    }
}

// 營業稅百分比徽章
.tax-percentage {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

// 營業稅金額動畫
.tax-amount {
    transition: all 0.2s ease;
    font-weight: 600;

    &:hover {
        transform: scale(1.02);
        color: #0e7490 !important;
    }
}

// 金額文字樣式
.text-primary {
    color: #2563eb !important;
}

.text-info {
    color: #0891b2 !important;
}

// 分隔線樣式
hr {
    border-top: 1px solid #e5e7eb;
    opacity: 1;
    margin: 1rem 0;
}

// 小計和總金額的樣式優化
.h5,
.h6 {
    transition: all 0.2s ease;
    font-weight: 600;
}

// 總金額區塊特殊效果
.text-primary.fw-bold {
    color: #2563eb !important;
    font-weight: 700 !important;
    transition: all 0.2s ease;

    &:hover {
        transform: scale(1.01);
        color: #1d4ed8 !important;
    }
}

// 資訊圖標樣式
.fa-info-circle {
    opacity: 0.7;
    transition: opacity 0.2s ease;

    &:hover {
        opacity: 1;
    }
}

// 響應式調整
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem !important;
    }

    .h4,
    .h5,
    .h6 {
        font-size: 1rem !important;
    }

    .tax-icon-wrapper {
        width: 35px;
        height: 35px;

        i {
            font-size: 1rem;
        }
    }

    .tax-section {
        padding: 1rem !important;

        &::before {
            width: 3px;
        }

        &:hover::before {
            width: 4px;
        }
    }
}

// 模板匯入對話框樣式
.template-selection-section {
    .section-title {
        color: #495057;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 0.5rem;
    }

    .search-container {
        .input-group-text {
            background-color: #f8f9fa;
            border-color: #ced4da;
            color: #6c757d;
        }

        .form-control {
            border-color: #d1d5db;
            transition: all 0.2s ease;
            border-radius: 6px;

            &:focus {
                border-color: #2563eb;
                box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                outline: none;
            }
        }
    }

    .template-list {
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        background-color: #fff;
        min-height: 400px; // 調整為適合5個模板項目的高度
        max-height: 500px; // 設定最大高度
        overflow-y: auto; // 如果內容超出則顯示滾動條

        .template-item {
            padding: 0.75rem 1rem; // 減少上下內邊距，讓5個項目更好地適應容器
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;

            &:last-child {
                border-bottom: none;
            }

            &:hover {
                background-color: #f8f9fa;
                transform: translateX(4px);
            }

            &.selected {
                background-color: #eff6ff;
                border-left: 4px solid #2563eb;
                transform: translateX(4px);

                .template-name {
                    color: #2563eb;
                    font-weight: 600;
                }
            }

            .template-info {
                flex: 1;

                .template-name {
                    font-weight: 500;
                    margin-bottom: 0.25rem;
                    transition: color 0.3s ease;

                    i {
                        color: #6c757d;
                    }
                }

                .template-description {
                    font-size: 0.875rem;
                    line-height: 1.4;
                }
            }

            .template-actions {
                i {
                    font-size: 1.25rem;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                }
            }

            &.selected .template-actions i {
                opacity: 1;
            }
        }

        .empty-state {
            color: #6c757d;

            i {
                opacity: 0.5;
            }
        }
    }
}

.template-details-section {
    .section-title {
        color: #495057;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 0.5rem;
    }

    .details-list {
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        background-color: #fff;

        .detail-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            align-items: center;
            transition: background-color 0.3s ease;

            &:last-child {
                border-bottom: none;
            }

            &:hover {
                background-color: #f8f9fa;
            }

            .detail-checkbox {
                margin-right: 1rem;
                flex-shrink: 0;
            }

            .detail-content {
                flex: 1;

                .detail-name {
                    margin-bottom: 0.25rem;
                    color: #212529;
                }

                .detail-meta {
                    display: flex;
                    gap: 1rem;
                    flex-wrap: wrap;

                    .meta-item {
                        font-size: 0.75rem;
                        color: #6c757d;
                        display: flex;
                        align-items: center;

                        i {
                            margin-right: 0.25rem;
                            opacity: 0.7;
                        }
                    }
                }
            }
        }

        .empty-state {
            color: #6c757d;

            i {
                opacity: 0.5;
            }
        }
    }

    .bulk-actions {
        padding: 0.75rem;
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        border: 1px solid #e9ecef;

        .btn {
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-1px);
            }
        }
    }

    // 分頁控制樣式
    .pagination-container {
        .pagination-info {
            color: #6c757d;
            font-size: 0.875rem;
        }

        .pagination-controls {
            display: flex;
            align-items: center;

            .btn {
                transition: all 0.3s ease;
                border-radius: 0.375rem;

                &:hover:not(:disabled) {
                    transform: translateY(-1px);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                &:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }

                i {
                    font-size: 0.75rem;
                }
            }

            .pagination-current {
                font-weight: 500;
                color: #495057;
                font-size: 0.875rem;
                white-space: nowrap;
            }
        }
    }
}

// Icon 優化樣式
.btn {
    i {
        transition: all 0.2s ease;
        font-size: 0.875rem;
    }

    &:hover i {
        transform: scale(1.1);
    }

    // 主要功能按鈕的 icon 樣式
    &.btn-info i {
        color: rgba(255, 255, 255, 0.9);

        &:hover {
            color: white;
        }
    }

    // 表格操作按鈕的 icon 樣式
    &.btn-outline-success i {
        color: #28a745;
        margin-right: 0.25rem;
    }

    // 查詢按鈕的 icon 樣式
    &.btn-secondary i {
        color: rgba(255, 255, 255, 0.9);
    }

    // 危險操作按鈕的 icon 樣式
    &.btn-danger i {
        color: rgba(255, 255, 255, 0.9);
    }

    // 警告按鈕的 icon 樣式
    &.btn-warning i {
        color: rgba(255, 255, 255, 0.9);
    }

    // 主要按鈕的 icon 樣式
    &.btn-primary i {
        color: rgba(255, 255, 255, 0.9);
    }

    // 次要按鈕的 icon 樣式
    &.btn-outline-secondary i {
        color: #6c757d;
    }
}

// 表格操作按鈕容器優化
.table td {
    .btn {
        margin: 1px;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;

        i {
            font-size: 0.75rem;
            margin-right: 0.25rem;
        }
    }
}

// 功能按鈕組優化
.d-flex .btn {
    &.btn-info {
        box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
        }
    }
}

// 查詢按鈕特殊樣式
.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.2);
    transition: all 0.3s ease;

    &:hover {
        background-color: #5a6268;
        border-color: #545b62;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
    }
}

// 對話框按鈕樣式優化
nb-card-footer .btn {
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.3s ease;

    i {
        font-size: 0.875rem;
        margin-right: 0.5rem;
    }

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
}