{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { QUOTATION_TEMPLATE } from 'src/assets/template/quotation-template';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { CQuotationItemType } from 'src/app/models/quotation.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/services/event.service\";\nimport * as i10 from \"src/app/shared/services/utility.service\";\nimport * as i11 from \"src/app/services/quotation.service\";\nimport * as i12 from \"@angular/common\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i15 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i16 from \"../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction HouseholdManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r6.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r7.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r9.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_button_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_button_74_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogHouseholdMain_r12 = i0.ɵɵreference(114);\n      return i0.ɵɵresetView(ctx_r10.openModel(dialogHouseholdMain_r12));\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_108_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const item_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogUpdateHousehold_r16 = i0.ɵɵreference(112);\n      return i0.ɵɵresetView(ctx_r10.openModelDetail(dialogUpdateHousehold_r16, item_r15));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_108_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 54);\n    i0.ɵɵtemplate(20, HouseholdManagementComponent_tr_108_button_20_Template, 2, 0, \"button\", 55);\n    i0.ɵɵelementStart(21, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_21_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"customer-change-picture\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(22, \" \\u6D3D\\u8AC7\\u7D00\\u9304 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_23_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"sample-selection-result\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(24, \" \\u5BA2\\u8B8A\\u78BA\\u8A8D\\u5716\\u8AAA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_25_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"finaldochouse_management\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(26, \" \\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_27_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.resetSecureKey(item_r15));\n    });\n    i0.ɵɵtext(28, \" \\u91CD\\u7F6E\\u5BC6\\u78BC \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_29_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogQuotation_r17 = i0.ɵɵreference(116);\n      return i0.ɵɵresetView(ctx_r10.openQuotation(dialogQuotation_r17, item_r15));\n    });\n    i0.ɵɵtext(30, \" \\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", item_r15.CHouseType === 2 ? \"\\u92B7\\u552E\\u6236\" : \"\", \" \", item_r15.CHouseType === 1 ? \"\\u5730\\u4E3B\\u6236\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsChange === true ? \"\\u5BA2\\u8B8A\" : item_r15.CIsChange === false ? \"\\u6A19\\u6E96\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CProgressName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", item_r15.CPayStatus === 0 ? \"\\u672A\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 1 ? \"\\u5DF2\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 2 ? \"\\u7121\\u9808\\u4ED8\\u6B3E\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CSignStatus === 0 || item_r15.CSignStatus == null ? \"\\u672A\\u7C3D\\u56DE\" : \"\\u5DF2\\u7C3D\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r10.getQuotationStatusText(item_r15.CQuotationStatus));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsEnable ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isUpdate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r20);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r20.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r21);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r21.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r23.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"label\", 92);\n    i0.ɵɵtext(2, \" \\u4ED8\\u6B3E\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CPayStatusSelected, $event) || (ctx_r10.detailSelected.CPayStatusSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_nb_option_4_Template, 2, 2, \"nb-option\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CPayStatusSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.payStatusOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r25);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r25.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"label\", 94);\n    i0.ɵɵtext(2, \" \\u9032\\u5EA6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 95);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CProgressSelected, $event) || (ctx_r10.detailSelected.CProgressSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_nb_option_4_Template, 2, 2, \"nb-option\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CProgressSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.progressOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-body\", 62)(1, \"div\", 63)(2, \"label\", 64);\n    i0.ɵɵtext(3, \" \\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-select\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CBuildCaseSelected, $event) || (ctx_r10.detailSelected.CBuildCaseSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_5_Template, 2, 2, \"nb-option\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 63)(7, \"label\", 66);\n    i0.ɵɵtext(8, \" \\u6236\\u578B\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CHousehold, $event) || (ctx_r10.houseDetail.CHousehold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 63)(11, \"label\", 68);\n    i0.ɵɵtext(12, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CFloor, $event) || (ctx_r10.houseDetail.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 63)(15, \"label\", 70);\n    i0.ɵɵtext(16, \" \\u5BA2\\u6236\\u59D3\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CCustomerName, $event) || (ctx_r10.houseDetail.CCustomerName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 63)(19, \"label\", 72);\n    i0.ɵɵtext(20, \" \\u8EAB\\u5206\\u8B49\\u5B57\\u865F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CNationalId, $event) || (ctx_r10.houseDetail.CNationalId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 63)(23, \"label\", 74);\n    i0.ɵɵtext(24, \" \\u96FB\\u5B50\\u90F5\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CMail, $event) || (ctx_r10.houseDetail.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 63)(27, \"label\", 76);\n    i0.ɵɵtext(28, \" \\u806F\\u7D61\\u96FB\\u8A71 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"input\", 77);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CPhone, $event) || (ctx_r10.houseDetail.CPhone = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 63)(31, \"label\", 78);\n    i0.ɵɵtext(32, \" \\u6236\\u5225\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-select\", 79);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CHouseTypeSelected, $event) || (ctx_r10.detailSelected.CHouseTypeSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(34, HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_34_Template, 2, 2, \"nb-option\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template, 5, 2, \"div\", 80)(36, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template, 5, 2, \"div\", 80);\n    i0.ɵɵelementStart(37, \"div\", 63)(38, \"label\", 81);\n    i0.ɵɵtext(39, \" \\u662F\\u5426\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"nb-checkbox\", 82);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsChange, $event) || (ctx_r10.houseDetail.CIsChange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(41, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 63)(43, \"label\", 83);\n    i0.ɵɵtext(44, \" \\u662F\\u5426\\u555F\\u7528 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"nb-checkbox\", 82);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsEnable, $event) || (ctx_r10.houseDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(46, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 84)(48, \"label\", 85);\n    i0.ɵɵtext(49, \" \\u5BA2\\u8B8A\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 86)(51, \"nb-form-field\", 87);\n    i0.ɵɵelement(52, \"nb-icon\", 88);\n    i0.ɵɵelementStart(53, \"input\", 89);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeStartDate, $event) || (ctx_r10.houseDetail.changeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"nb-datepicker\", 90, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"nb-form-field\", 87);\n    i0.ɵɵelement(57, \"nb-icon\", 88);\n    i0.ɵɵelementStart(58, \"input\", 91);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_58_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeEndDate, $event) || (ctx_r10.houseDetail.changeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"nb-datepicker\", 90, 6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const StartDate_r26 = i0.ɵɵreference(55);\n    const EndDate_r27 = i0.ɵɵreference(60);\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CBuildCaseSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.userBuildCaseOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CHousehold);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CFloor);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CCustomerName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CNationalId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CMail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CPhone);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CHouseTypeSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.houseTypeOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isChangePayStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isChangeProgress);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsChange);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsEnable);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"nbDatepicker\", StartDate_r26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeStartDate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"nbDatepicker\", EndDate_r27);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeEndDate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_111_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ref_r28 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onSubmitDetail(ref_r28));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 58);\n    i0.ɵɵtemplate(1, HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template, 61, 18, \"nb-card-body\", 59);\n    i0.ɵɵelementStart(2, \"nb-card-footer\", 51)(3, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_111_Template_button_click_3_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r28));\n    });\n    i0.ɵɵtext(4, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_111_button_5_Template, 2, 0, \"button\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.houseDetail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_113_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_113_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ref_r31 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.addHouseHoldMain(ref_r31));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_113_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 58)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6236\\u5225\\u7BA1\\u7406 \\u300B\\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 62)(4, \"div\", 63)(5, \"label\", 97);\n    i0.ɵɵtext(6, \" \\u68DF\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CBuildingName, $event) || (ctx_r10.houseHoldMain.CBuildingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 63)(9, \"label\", 99);\n    i0.ɵɵtext(10, \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 100);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CHouseHoldCount, $event) || (ctx_r10.houseHoldMain.CHouseHoldCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 63)(13, \"label\", 101);\n    i0.ɵɵtext(14, \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 102);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CFloor, $event) || (ctx_r10.houseHoldMain.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"nb-card-footer\", 51)(17, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_113_Template_button_click_17_listener() {\n      const ref_r31 = i0.ɵɵrestoreView(_r30).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r31));\n    });\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, HouseholdManagementComponent_ng_template_113_button_19_Template, 2, 0, \"button\", 104);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CBuildingName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CHouseHoldCount);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CFloor);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(\"\\u95DC\\u9589\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      const dialogTemplateImport_r35 = i0.ɵɵreference(118);\n      return i0.ɵɵresetView(ctx_r10.openTemplateImportDialog(dialogTemplateImport_r35));\n    });\n    i0.ɵɵtext(2, \" + \\u5F9E\\u6A21\\u677F\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.loadDefaultItems());\n    });\n    i0.ɵɵtext(5, \" \\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.loadRegularItems());\n    });\n    i0.ɵɵtext(7, \" \\u8F09\\u5165\\u9078\\u6A23\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146);\n    i0.ɵɵelement(1, \"i\", 147);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" - \\u6B64\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\\uFF0C\\u7121\\u6CD5\\u9032\\u884C\\u4FEE\\u6539\\u3002\\u60A8\\u53EF\\u4EE5\\u5217\\u5370\\u6B64\\u5831\\u50F9\\u55AE\\u6216\\u7522\\u751F\\u65B0\\u7684\\u5831\\u50F9\\u55AE\\u3002 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const i_r39 = i0.ɵɵnextContext().index;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.removeQuotationItem(i_r39));\n    });\n    i0.ɵɵtext(1, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_25_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 157);\n    i0.ɵɵelement(1, \"i\", 158);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 148);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_2_listener($event) {\n      const item_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r37.cItemName, $event) || (item_r37.cItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"input\", 149);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_4_listener($event) {\n      const item_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r37.cUnitPrice, $event) || (item_r37.cUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\")(6, \"input\", 150);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_6_listener($event) {\n      const item_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r37.cUnit, $event) || (item_r37.cUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"input\", 151);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_8_listener($event) {\n      const item_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r37.cCount, $event) || (item_r37.cCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_8_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 152);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"span\", 153);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtemplate(15, HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template, 2, 0, \"button\", 154)(16, HouseholdManagementComponent_ng_template_115_tr_25_span_16_Template, 2, 0, \"span\", 155);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r37 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 1 || item_r37.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r37.cItemName);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 1 || item_r37.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r37.cUnitPrice);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 1 || item_r37.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r37.cUnit);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 1 || item_r37.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r37.cCount);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.formatCurrency(item_r37.cUnitPrice * item_r37.cCount), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"badge-primary\", item_r37.CQuotationItemType === 1)(\"badge-info\", item_r37.CQuotationItemType === 3)(\"badge-secondary\", item_r37.CQuotationItemType !== 1 && item_r37.CQuotationItemType !== 3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getQuotationTypeText(item_r37.CQuotationItemType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 159);\n    i0.ɵɵtext(2, \" \\u8ACB\\u9EDE\\u64CA\\u300C\\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE\\u300D\\u6216\\u300C\\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42\\u300D\\u958B\\u59CB\\u5EFA\\u7ACB\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_button_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 160);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_button_63_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.createNewQuotation());\n    });\n    i0.ɵɵelement(1, \"i\", 161);\n    i0.ɵɵtext(2, \" \\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_button_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_button_67_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ref_r41 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.lockQuotation(ref_r41));\n    });\n    i0.ɵɵtext(1, \" \\u9396\\u5B9A\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_button_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_button_68_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const ref_r41 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.saveQuotation(ref_r41));\n    });\n    i0.ɵɵtext(1, \" \\u5132\\u5B58\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 106)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\");\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_115_div_4_Template, 8, 0, \"div\", 107)(5, HouseholdManagementComponent_ng_template_115_div_5_Template, 5, 0, \"div\", 108);\n    i0.ɵɵelementStart(6, \"div\", 109)(7, \"table\", 110)(8, \"thead\")(9, \"tr\")(10, \"th\", 111);\n    i0.ɵɵtext(11, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 112);\n    i0.ɵɵtext(13, \"\\u55AE\\u50F9 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 113);\n    i0.ɵɵtext(15, \"\\u55AE\\u4F4D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 113);\n    i0.ɵɵtext(17, \"\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 114);\n    i0.ɵɵtext(19, \"\\u5C0F\\u8A08 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 115);\n    i0.ɵɵtext(21, \"\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 115);\n    i0.ɵɵtext(23, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"tbody\");\n    i0.ɵɵtemplate(25, HouseholdManagementComponent_ng_template_115_tr_25_Template, 17, 22, \"tr\", 50)(26, HouseholdManagementComponent_ng_template_115_tr_26_Template, 3, 0, \"tr\", 116);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 117)(28, \"div\", 118)(29, \"div\", 119)(30, \"div\", 120)(31, \"span\", 121);\n    i0.ɵɵtext(32, \"\\u5C0F\\u8A08\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\", 122);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 123)(36, \"div\", 124)(37, \"div\", 125);\n    i0.ɵɵelement(38, \"i\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\")(40, \"span\", 127);\n    i0.ɵɵtext(41, \"\\u71DF\\u696D\\u7A05\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\", 128);\n    i0.ɵɵtext(43, \"5%\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 129);\n    i0.ɵɵelement(45, \"i\", 130);\n    i0.ɵɵtext(46, \" \\u56FA\\u5B9A\\u70BA\\u5C0F\\u8A08\\u91D1\\u984D\\u76845% \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(47, \"div\", 131)(48, \"div\", 132);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 133);\n    i0.ɵɵtext(51, \"\\u542B\\u7A05\\u91D1\\u984D\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(52, \"hr\", 134);\n    i0.ɵɵelementStart(53, \"div\", 135)(54, \"span\", 122);\n    i0.ɵɵtext(55, \"\\u7E3D\\u91D1\\u984D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"span\", 136);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(58, \"nb-card-footer\", 137)(59, \"div\")(60, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_Template_button_click_60_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.printQuotation());\n    });\n    i0.ɵɵelement(61, \"i\", 139);\n    i0.ɵɵtext(62, \" \\u5217\\u5370\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(63, HouseholdManagementComponent_ng_template_115_button_63_Template, 3, 0, \"button\", 140);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"div\")(65, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_Template_button_click_65_listener() {\n      const ref_r41 = i0.ɵɵrestoreView(_r33).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r41));\n    });\n    i0.ɵɵtext(66, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(67, HouseholdManagementComponent_ng_template_115_button_67_Template, 2, 1, \"button\", 142)(68, HouseholdManagementComponent_ng_template_115_button_68_Template, 2, 1, \"button\", 143);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u5831\\u50F9\\u55AE - \", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CHouseHold, \" (\", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CFloor, \"\\u6A13) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.quotationItems);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.quotationItems.length === 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.totalAmount));\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.additionalFeeAmount));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.finalTotalAmount));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 187)(1, \"button\", 188);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_19_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.clearTemplateSearch());\n    });\n    i0.ɵɵelement(2, \"i\", 189);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_21_i_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 197);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 190);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_21_Template_div_click_0_listener() {\n      const template_r47 = i0.ɵɵrestoreView(_r46).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.selectTemplateForImport(template_r47));\n    });\n    i0.ɵɵelementStart(1, \"div\", 191)(2, \"div\", 192);\n    i0.ɵɵelement(3, \"i\", 193);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 194);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 195);\n    i0.ɵɵtemplate(8, HouseholdManagementComponent_ng_template_117_div_21_i_8_Template, 1, 0, \"i\", 196);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const template_r47 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", (ctx_r10.selectedTemplateForImport == null ? null : ctx_r10.selectedTemplateForImport.TemplateID) === template_r47.TemplateID);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", template_r47.TemplateName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", template_r47.Description || \"\\u7121\\u63CF\\u8FF0\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r10.selectedTemplateForImport == null ? null : ctx_r10.selectedTemplateForImport.TemplateID) === template_r47.TemplateID);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 198);\n    i0.ɵɵelement(1, \"i\", 199);\n    i0.ɵɵelementStart(2, \"p\", 200);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.templateSearchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u8F09\\u5165\\u4E2D...\", \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 201)(1, \"div\", 135)(2, \"div\", 202)(3, \"small\", 157);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 203)(6, \"button\", 204);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_23_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.previousTemplatePage());\n    });\n    i0.ɵɵelement(7, \"i\", 205);\n    i0.ɵɵtext(8, \" \\u4E0A\\u4E00\\u9801 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 206);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 207);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_23_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.nextTemplatePage());\n    });\n    i0.ɵɵtext(12, \" \\u4E0B\\u4E00\\u9801 \");\n    i0.ɵɵelement(13, \"i\", 208);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", ctx_r10.getTemplateStartIndex() + 1, \" - \", ctx_r10.getTemplateEndIndex(), \" \\u7B46\\uFF0C\\u5171 \", ctx_r10.templateTotalItems, \" \\u7B46\\u6A21\\u677F \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.templateCurrentPage === 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r10.templateCurrentPage, \" / \", ctx_r10.getTotalTemplatePages(), \" \\u9801 \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r10.templateCurrentPage === ctx_r10.getTotalTemplatePages());\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_24_div_7_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 222);\n    i0.ɵɵelement(1, \"i\", 224);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r50 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", detail_r50.CGroupName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_24_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 215)(1, \"div\", 216)(2, \"nb-checkbox\", 217);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_117_div_24_div_7_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const detail_r50 = i0.ɵɵrestoreView(_r49).$implicit;\n      i0.ɵɵtwoWayBindingSet(detail_r50.selected, $event) || (detail_r50.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function HouseholdManagementComponent_ng_template_117_div_24_div_7_Template_nb_checkbox_checkedChange_2_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r10.updateSelectedCount());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 218)(4, \"div\", 219)(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 220);\n    i0.ɵɵtemplate(8, HouseholdManagementComponent_ng_template_117_div_24_div_7_span_8_Template, 3, 1, \"span\", 221);\n    i0.ɵɵelementStart(9, \"span\", 222);\n    i0.ɵɵelement(10, \"i\", 223);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const detail_r50 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"checked\", detail_r50.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(detail_r50.CReleateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r50.CGroupName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", detail_r50.CReleateId, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_24_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 198);\n    i0.ɵɵelement(1, \"i\", 225);\n    i0.ɵɵelementStart(2, \"p\", 200);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_24_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 226)(1, \"button\", 227);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_24_div_9_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r10.selectAllTemplateItems());\n    });\n    i0.ɵɵelement(2, \"i\", 228);\n    i0.ɵɵtext(3, \"\\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 229);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_24_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r10.deselectAllTemplateItems());\n    });\n    i0.ɵɵelement(5, \"i\", 230);\n    i0.ɵɵtext(6, \"\\u53D6\\u6D88\\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 231);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate2(\" \\u5DF2\\u9078\\u64C7 \", ctx_r10.getSelectedTemplateItemsCount(), \" / \", ctx_r10.templateDetailList.length, \" \\u9805 \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 209)(1, \"h6\", 169);\n    i0.ɵɵelement(2, \"i\", 210);\n    i0.ɵɵtext(3, \"\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵelementStart(4, \"span\", 211);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 212);\n    i0.ɵɵtemplate(7, HouseholdManagementComponent_ng_template_117_div_24_div_7_Template, 12, 4, \"div\", 213)(8, HouseholdManagementComponent_ng_template_117_div_24_div_8_Template, 4, 0, \"div\", 179);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, HouseholdManagementComponent_ng_template_117_div_24_div_9_Template, 9, 2, \"div\", 214);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r10.templateDetailList.length, \" \\u9805\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.templateDetailList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.templateDetailList.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.templateDetailList.length > 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 232)(1, \"small\", 157);\n    i0.ɵɵelement(2, \"i\", 233);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5C07\\u532F\\u5165 \", ctx_r10.getSelectedTemplateItemsCount(), \" \\u500B\\u9805\\u76EE\\u5230\\u5831\\u50F9\\u55AE \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 164)(1, \"nb-card-header\")(2, \"div\", 135)(3, \"h5\", 165);\n    i0.ɵɵelement(4, \"i\", 166);\n    i0.ɵɵtext(5, \"\\u5F9E\\u6A21\\u677F\\u532F\\u5165\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 167);\n    i0.ɵɵtext(7, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u6A21\\u677F\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"nb-card-body\")(9, \"div\", 168)(10, \"h6\", 169);\n    i0.ɵɵelement(11, \"i\", 170);\n    i0.ɵɵtext(12, \"\\u9078\\u64C7\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 171)(14, \"div\", 172)(15, \"div\", 173)(16, \"span\", 174);\n    i0.ɵɵelement(17, \"i\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"input\", 175);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_117_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.templateSearchKeyword, $event) || (ctx_r10.templateSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdManagementComponent_ng_template_117_Template_input_input_18_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.filterTemplates());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, HouseholdManagementComponent_ng_template_117_div_19_Template, 3, 0, \"div\", 176);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 177);\n    i0.ɵɵtemplate(21, HouseholdManagementComponent_ng_template_117_div_21_Template, 9, 5, \"div\", 178)(22, HouseholdManagementComponent_ng_template_117_div_22_Template, 4, 1, \"div\", 179);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, HouseholdManagementComponent_ng_template_117_div_23_Template, 14, 7, \"div\", 180);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, HouseholdManagementComponent_ng_template_117_div_24_Template, 10, 4, \"div\", 181);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"nb-card-footer\", 137);\n    i0.ɵɵtemplate(26, HouseholdManagementComponent_ng_template_117_div_26_Template, 4, 1, \"div\", 182);\n    i0.ɵɵelementStart(27, \"div\", 183)(28, \"button\", 184);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_Template_button_click_28_listener() {\n      const ref_r52 = i0.ɵɵrestoreView(_r44).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r52));\n    });\n    i0.ɵɵtext(29, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 185);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_Template_button_click_30_listener() {\n      const ref_r52 = i0.ɵɵrestoreView(_r44).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.importSelectedTemplateItems(ref_r52));\n    });\n    i0.ɵɵelement(31, \"i\", 186);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.templateSearchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.templateSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.templateList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.templateList.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.templateTotalItems > 0 || ctx_r10.templateList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.selectedTemplateForImport);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.selectedTemplateForImport);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.selectedTemplateForImport || ctx_r10.getSelectedTemplateItemsCount() === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u532F\\u5165\\u9078\\u4E2D\\u9805\\u76EE (\", ctx_r10.getSelectedTemplateItemsCount(), \") \");\n  }\n}\nexport class HouseholdManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService, quotationService, templateService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._houseHoldMainService = _houseHoldMainService;\n    this._buildCaseService = _buildCaseService;\n    this.pettern = pettern;\n    this.router = router;\n    this._eventService = _eventService;\n    this._ultilityService = _ultilityService;\n    this.quotationService = quotationService;\n    this.templateService = templateService;\n    this.tempBuildCaseID = -1;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.cIsEnableOptions = [{\n      value: null,\n      key: 'all',\n      label: '全部'\n    }, {\n      value: true,\n      key: 'enable',\n      label: '啟用'\n    }, {\n      value: false,\n      key: 'deactivate',\n      label: '停用'\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.houseHoldOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.progressOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.houseTypeOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.payStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.signStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.quotationStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.options = {\n      progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\n      payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\n      houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\n      quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus)\n    };\n    this.initDetail = {\n      CHouseID: 0,\n      CMail: \"\",\n      CIsChange: false,\n      CPayStatus: 0,\n      CIsEnable: false,\n      CCustomerName: \"\",\n      CNationalID: \"\",\n      CProgress: \"\",\n      CHouseType: 0,\n      CHouseHold: \"\",\n      CPhone: \"\"\n    };\n    // 報價單相關\n    this.quotationItems = [];\n    this.totalAmount = 0;\n    // 新增：百分比費用設定\n    this.additionalFeeName = '營業稅'; // 固定名稱\n    this.additionalFeePercentage = 5; // 固定5%\n    this.additionalFeeAmount = 0; // 百分比費用金額\n    this.finalTotalAmount = 0; // 最終總金額（含百分比費用）\n    this.enableAdditionalFee = true; // 固定啟用營業稅\n    this.currentHouse = null;\n    this.currentQuotationId = 0;\n    this.isQuotationEditable = true; // 報價單是否可編輯\n    // 模板匯入相關屬性\n    this.templateList = [];\n    this.templateDetailList = [];\n    this.selectedTemplateForImport = null;\n    this.templateSearchKeyword = '';\n    // 模板分頁相關屬性\n    this.templateCurrentPage = 1;\n    this.templatePageSize = 1; // 每頁顯示1個模板（測試用）\n    this.paginatedTemplateList = [];\n    this.templateTotalItems = 0; // API 返回的總項目數\n    this.selectedFile = null;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(EnumHouseProgress)];\n    this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(EnumHouseType)];\n    this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(EnumPayStatus)];\n    this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(EnumSignStatus)];\n    this.quotationStatusOptions = [...this.quotationStatusOptions, ...this.enumHelper.getEnumOptions(EnumQuotationStatus)];\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\n        //   : this.buildingSelectedOptions[0],\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value) : this.houseHoldOptions[0],\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value) : this.houseTypeOptions[0],\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value) : this.payStatusOptions[0],\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value) : this.progressOptions[0],\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value) : this.signStatusOptions[0],\n        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value) : this.quotationStatusOptions[0],\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value) : this.cIsEnableOptions[0],\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined ? previous_search.CFrom : '',\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined ? previous_search.CTo : ''\n      };\n    } else {\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: this.buildingSelectedOptions[0],\n        CHouseHoldSelected: this.houseHoldOptions[0],\n        CHouseTypeSelected: this.houseTypeOptions[0],\n        CPayStatusSelected: this.payStatusOptions[0],\n        CProgressSelected: this.progressOptions[0],\n        CSignStatusSelected: this.signStatusOptions[0],\n        CQuotationStatusSelected: this.quotationStatusOptions[0],\n        CIsEnableSeleted: this.cIsEnableOptions[0],\n        CFrom: '',\n        CTo: ''\n      };\n    }\n    this.getListBuildCase();\n  }\n  onSearch() {\n    let sessionSave = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\n      CFrom: this.searchQuery.CFrom,\n      CTo: this.searchQuery.CTo,\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\n      CProgressSelected: this.searchQuery.CProgressSelected,\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected,\n      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\n    };\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\n    this.getHouseList().subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getHouseList().subscribe();\n  }\n  exportHouse() {\n    if (this.searchQuery.CBuildCaseSelected.cID) {\n      this._houseService.apiHouseExportHousePost$Json({\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  triggerFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      this.selectedFile = input.files[0];\n      this.importExcel();\n    }\n  }\n  importExcel() {\n    if (this.selectedFile) {\n      const formData = new FormData();\n      formData.append('CFile', this.selectedFile);\n      this._houseService.apiHouseImportHousePost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n          CFile: this.selectedFile\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(res.Message);\n          this.getHouseList().subscribe();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  getListHouseHold() {\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\n            let index = this.houseHoldOptions.findIndex(x => x.value == previous_search.CHouseHoldSelected.value);\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index];\n          } else {\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n          }\n        }\n      }\n    });\n  }\n  formatQuery() {\n    this.bodyRequest = {\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n      this.bodyRequest['CFloor'] = {\n        CFrom: this.searchQuery.CFrom,\n        CTo: this.searchQuery.CTo\n      };\n    }\n    if (this.searchQuery.CHouseHoldSelected) {\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n    }\n    if (this.searchQuery.CHouseTypeSelected.value) {\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n    }\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n    }\n    if (this.searchQuery.CPayStatusSelected.value) {\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n    }\n    if (this.searchQuery.CProgressSelected.value) {\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n    }\n    if (this.searchQuery.CSignStatusSelected.value) {\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n    }\n    if (this.searchQuery.CQuotationStatusSelected.value) {\n      this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value;\n    }\n    return this.bodyRequest;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: this.formatQuery()\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  onSelectionChangeBuildCase() {\n    // this.getListBuilding()\n    this.getListHouseHold();\n    this.getHouseList().subscribe();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        }) : [];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == previous_search.CBuildCaseSelected.cID);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n      }\n    }), tap(() => {\n      // this.getListBuilding()\n      this.getListHouseHold();\n      setTimeout(() => {\n        this.getHouseList().subscribe();\n      }, 500);\n    })).subscribe();\n  }\n  getHouseById(CID, ref) {\n    this.detailSelected = {};\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: CID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseDetail = {\n          ...res.Entries,\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n        };\n        if (res.Entries.CBuildCaseId) {\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n        }\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n        if (res.Entries.CHouseType) {\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n        } else {\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n        }\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n        if (res.Entries.CBuildCaseId) {\n          if (this.houseHoldMain) {\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n          }\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  findItemInArray(array, key, value) {\n    return array.find(item => item[key] === value);\n  }\n  openModelDetail(ref, item) {\n    this.getHouseById(item.CID, ref);\n  }\n  openModel(ref) {\n    this.houseHoldMain = {\n      CBuildingName: '',\n      CFloor: undefined,\n      CHouseHoldCount: undefined\n    };\n    this.dialogService.open(ref);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmitDetail(ref) {\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n      CChangeStartDate: this.houseDetail.CChangeStartDate,\n      CChangeEndDate: this.houseDetail.CChangeEndDate\n    };\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseService.apiHouseEditHousePost$Json({\n      body: this.editHouseArgsParam\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  onSubmit(ref) {\n    let bodyReq = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.houseDetail.CHouseType,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.houseDetail.CPayStatus,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.houseDetail.CProgress\n    };\n    this._houseService.apiHouseEditHousePost$Json({\n      body: bodyReq\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onNavidateId(type, id) {\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n    this.router.navigate([`/pages/household-management/${type}`, idURL]);\n  }\n  onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n  }\n  resetSecureKey(item) {\n    if (confirm(\"您想重設密碼嗎？\")) {\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\n        body: item.CID\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.houseDetail.CId);\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n    this.valid.required('[樓層]', this.houseDetail.CFloor);\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n    // if (this.editHouseArgsParam.CNationalID) {\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\n    // }\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n    if (this.houseDetail.CChangeStartDate) {\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n    }\n    if (this.houseDetail.CChangeEndDate) {\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n    }\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n  }\n  validationHouseHoldMain() {\n    this.valid.clear();\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n  }\n  addHouseHoldMain(ref) {\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n      body: this.houseHoldMain\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  } // 開啟報價單對話框\n  openQuotation(dialog, item) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.currentHouse = item;\n      _this.quotationItems = [];\n      _this.totalAmount = 0;\n      _this.currentQuotationId = 0; // 重置報價單ID\n      _this.isQuotationEditable = true; // 預設可編輯\n      // 重置百分比費用設定（固定營業稅5%）\n      _this.additionalFeeName = '營業稅';\n      _this.additionalFeePercentage = 5;\n      _this.additionalFeeAmount = 0;\n      _this.finalTotalAmount = 0;\n      _this.enableAdditionalFee = true;\n      // 載入現有報價資料\n      try {\n        const response = yield _this.quotationService.getQuotationByHouseId(item.CID).toPromise();\n        if (response && response.StatusCode === 0 && response.Entries) {\n          // 保存當前的報價單ID\n          _this.currentQuotationId = response.Entries.CQuotationVersionId || 0;\n          // 根據 cQuotationStatus 決定是否可編輯\n          if (response.Entries.CQuotationStatus === 2) {\n            // 2: 已報價\n            _this.isQuotationEditable = false;\n          } else {\n            _this.isQuotationEditable = true;\n          }\n          // 載入額外費用設定（固定營業稅5%，不從後端載入）\n          _this.enableAdditionalFee = true;\n          _this.additionalFeeName = '營業稅';\n          _this.additionalFeePercentage = 5;\n          // 檢查 Entries 是否有 Items 陣列\n          if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\n            // 將 API 回傳的資料轉換為 QuotationItem 格式\n            _this.quotationItems = response.Entries.Items.map(entry => ({\n              cHouseID: response.Entries.CHouseID || item.CID,\n              cQuotationID: response.Entries.CQuotationID,\n              cItemName: entry.CItemName || '',\n              cUnit: entry.CUnit || '',\n              cUnitPrice: entry.CUnitPrice || 0,\n              cCount: entry.CCount || 1,\n              cStatus: entry.CStatus || 1,\n              CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\n              cRemark: entry.CRemark || '',\n              cQuotationStatus: entry.CQuotationStatus\n            }));\n            _this.calculateTotal();\n          } else {}\n        } else {}\n      } catch (error) {\n        console.error('載入報價資料失敗:', error);\n      }\n      _this.dialogService.open(dialog, {\n        context: item,\n        closeOnBackdropClick: false\n      });\n    })();\n  }\n  // 產生新報價單\n  createNewQuotation() {\n    this.currentQuotationId = 0;\n    this.quotationItems = [];\n    this.isQuotationEditable = true;\n    this.totalAmount = 0;\n    this.finalTotalAmount = 0;\n    this.additionalFeeAmount = 0;\n    this.enableAdditionalFee = true;\n    // 顯示成功訊息\n    this.message.showSucessMSG('已產生新報價單，可開始編輯');\n  }\n  // 新增自定義報價項目\n  addQuotationItem() {\n    this.quotationItems.push({\n      cHouseID: this.currentHouse?.CID || 0,\n      cItemName: '',\n      cUnit: '',\n      cUnitPrice: 0,\n      cCount: 1,\n      cStatus: 1,\n      CQuotationItemType: CQuotationItemType.自定義,\n      cRemark: ''\n    });\n  }\n  // 載入客變需求\n  loadDefaultItems() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.currentHouse?.CID) {\n          _this2.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this2.searchQuery?.CBuildCaseSelected?.cID || 0,\n          CHouseID: _this2.currentHouse.CID\n        };\n        const response = yield _this2.quotationService.loadDefaultItems(request).toPromise();\n        if (response?.success && response.data) {\n          const defaultItems = response.data.map(x => ({\n            cQuotationID: x.CQuotationID,\n            cHouseID: _this2.currentHouse?.CID,\n            cItemName: x.CItemName,\n            cUnit: x.CUnit || '',\n            cUnitPrice: x.CUnitPrice,\n            cCount: x.CCount,\n            cStatus: x.CStatus,\n            CQuotationItemType: CQuotationItemType.客變需求,\n            cRemark: x.CRemark\n          }));\n          _this2.quotationItems.push(...defaultItems);\n          _this2.calculateTotal();\n          _this2.message.showSucessMSG('載入客變需求成功');\n        } else {\n          _this2.message.showErrorMSG(response?.message || '載入客變需求失敗');\n        }\n      } catch (error) {\n        console.error('載入客變需求錯誤:', error);\n        _this2.message.showErrorMSG('載入客變需求失敗');\n      }\n    })();\n  }\n  // 載入選樣資料\n  loadRegularItems() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this3.currentHouse?.CID) {\n          _this3.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this3.searchQuery?.CBuildCaseSelected?.cID || 0,\n          CHouseID: _this3.currentHouse.CID\n        };\n        const response = yield _this3.quotationService.loadRegularItems(request).toPromise();\n        if (response?.success && response.data) {\n          const regularItems = response.data.map(x => ({\n            cQuotationID: x.CQuotationID,\n            cHouseID: _this3.currentHouse?.CID,\n            cItemName: x.CItemName,\n            cUnit: x.CUnit || '',\n            cUnitPrice: x.CUnitPrice,\n            cCount: x.CCount,\n            cStatus: x.CStatus,\n            CQuotationItemType: CQuotationItemType.選樣,\n            // 選樣資料\n            cRemark: x.CRemark || ''\n          }));\n          _this3.quotationItems.push(...regularItems);\n          _this3.calculateTotal();\n          _this3.message.showSucessMSG('載入選樣資料成功');\n        } else {\n          _this3.message.showErrorMSG(response?.message || '載入選樣資料失敗');\n        }\n      } catch (error) {\n        console.error('載入選樣資料錯誤:', error);\n        _this3.message.showErrorMSG('載入選樣資料失敗');\n      }\n    })();\n  }\n  // 移除報價項目\n  removeQuotationItem(index) {\n    const item = this.quotationItems[index];\n    this.quotationItems.splice(index, 1);\n    this.calculateTotal();\n  }\n  // 計算總金額\n  calculateTotal() {\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\n      return sum + item.cUnitPrice * item.cCount;\n    }, 0);\n    this.calculateFinalTotal();\n  }\n  // 計算百分比費用和最終總金額（固定營業稅5%）\n  calculateFinalTotal() {\n    // 固定計算營業稅5%\n    this.additionalFeeAmount = Math.round(this.totalAmount * 0.05);\n    this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\n  }\n  // 格式化金額\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('zh-TW', {\n      style: 'currency',\n      currency: 'TWD',\n      minimumFractionDigits: 0\n    }).format(amount);\n  }\n  // 儲存報價單\n  saveQuotation(ref) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (_this4.quotationItems.length === 0) {\n        _this4.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      // 驗證必填欄位 (調整：允許單價和數量為負數)\n      const invalidItems = _this4.quotationItems.filter(item => !item.cItemName.trim());\n      if (invalidItems.length > 0) {\n        _this4.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\n        return;\n      }\n      try {\n        const request = {\n          houseId: _this4.currentHouse.CID,\n          items: _this4.quotationItems,\n          quotationId: _this4.currentQuotationId,\n          // 傳遞當前的報價單ID\n          // 額外費用相關欄位\n          cShowOther: _this4.enableAdditionalFee,\n          // 啟用額外費用\n          cOtherName: _this4.additionalFeeName,\n          // 額外費用名稱\n          cOtherPercent: _this4.additionalFeePercentage // 額外費用百分比\n        };\n        const response = yield _this4.quotationService.saveQuotation(request).toPromise();\n        if (response?.success) {\n          _this4.message.showSucessMSG('報價單儲存成功');\n          ref.close();\n        } else {\n          _this4.message.showErrorMSG(response?.message || '儲存失敗');\n        }\n      } catch (error) {\n        _this4.message.showErrorMSG('報價單儲存失敗');\n      }\n    })();\n  }\n  // 匯出報價單\n  exportQuotation() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield _this5.quotationService.exportQuotation(_this5.currentHouse.CID).toPromise();\n        if (blob) {\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `報價單_${_this5.currentHouse.CHouseHold}_${_this5.currentHouse.CFloor}樓.pdf`;\n          link.click();\n          window.URL.revokeObjectURL(url);\n        } else {\n          _this5.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\n        }\n      } catch (error) {\n        _this5.message.showErrorMSG('匯出報價單失敗');\n      }\n    })();\n  }\n  // 列印報價單\n  printQuotation() {\n    if (this.quotationItems.length === 0) {\n      this.message.showErrorMSG('沒有可列印的報價項目');\n      return;\n    }\n    try {\n      // 建立列印內容\n      const printContent = this.generatePrintContent();\n      // 建立新的視窗進行列印\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n      if (printWindow) {\n        printWindow.document.open();\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        // 等待內容載入完成後列印\n        printWindow.onload = function () {\n          setTimeout(() => {\n            printWindow.print();\n            // 列印後不自動關閉視窗，讓使用者可以預覽\n          }, 500);\n        };\n      } else {\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\n      }\n    } catch (error) {\n      console.error('列印報價單錯誤:', error);\n      this.message.showErrorMSG('列印報價單時發生錯誤');\n    }\n  }\n  // 產生列印內容\n  generatePrintContent() {\n    // 使用導入的模板\n    const template = QUOTATION_TEMPLATE;\n    // 準備數據\n    const currentDate = new Date().toLocaleDateString('zh-TW');\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\n    // 生成項目HTML\n    let itemsHtml = '';\n    this.quotationItems.forEach((item, index) => {\n      const subtotal = item.cUnitPrice * item.cCount;\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\n      const unit = item.cUnit || '';\n      itemsHtml += `\n          <tr>\n            <td class=\"text-center\">${index + 1}</td>\n            <td>${item.cItemName}</td>\n            <td class=\"text-right\">${this.formatCurrency(item.cUnitPrice)}</td>\n            <td class=\"text-center\">${unit}</td>\n            <td class=\"text-center\">${item.cCount}</td>\n            <td class=\"text-right\">${this.formatCurrency(subtotal)}</td>\n            <td class=\"text-center\">${quotationType}</td>\n          </tr>\n        `;\n    });\n    // 生成額外費用HTML\n    const additionalFeeHtml = this.enableAdditionalFee ? `\n        <div class=\"additional-fee\">\n          ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\n        </div>\n      ` : '';\n    // 替換模板中的占位符\n    const html = template.replace(/{{buildCaseName}}/g, buildCaseName).replace(/{{houseHold}}/g, this.currentHouse?.CHouseHold || '').replace(/{{floor}}/g, this.currentHouse?.CFloor || '').replace(/{{customerName}}/g, this.currentHouse?.CCustomerName || '').replace(/{{printDate}}/g, currentDate).replace(/{{itemsHtml}}/g, itemsHtml).replace(/{{subtotalAmount}}/g, this.formatCurrency(this.totalAmount)).replace(/{{additionalFeeHtml}}/g, additionalFeeHtml).replace(/{{totalAmount}}/g, this.formatCurrency(this.finalTotalAmount)).replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\n    return html;\n  }\n  // 鎖定報價單\n  lockQuotation(ref) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (_this6.quotationItems.length === 0) {\n        _this6.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      if (!_this6.currentQuotationId) {\n        _this6.message.showErrorMSG('無效的報價單ID');\n        return;\n      }\n      try {\n        const response = yield _this6.quotationService.lockQuotation(_this6.currentQuotationId).toPromise();\n        if (response.success) {\n          _this6.message.showSucessMSG('報價單已成功鎖定');\n          console.log('報價單鎖定成功:', {\n            quotationId: _this6.currentQuotationId,\n            message: response.message\n          });\n        } else {\n          _this6.message.showErrorMSG(response.message || '報價單鎖定失敗');\n          console.error('報價單鎖定失敗:', response.message);\n        }\n        ref.close();\n      } catch (error) {\n        _this6.message.showErrorMSG('報價單鎖定失敗');\n        console.error('鎖定報價單錯誤:', error);\n      }\n    })();\n  }\n  // 取得報價類型文字\n  getQuotationTypeText(quotationType) {\n    switch (quotationType) {\n      case CQuotationItemType.客變需求:\n        return '客變需求';\n      case CQuotationItemType.自定義:\n        return '自定義';\n      case CQuotationItemType.選樣:\n        return '選樣';\n      default:\n        return '未知';\n    }\n  }\n  getQuotationStatusText(status) {\n    switch (status) {\n      case EnumQuotationStatus.待報價:\n        return '待報價';\n      case EnumQuotationStatus.已報價:\n        return '已報價';\n      case EnumQuotationStatus.已簽回:\n        return '已簽回';\n      default:\n        return '未知';\n    }\n  }\n  // 模板匯入相關方法\n  openTemplateImportDialog(dialog) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      // 重置模板相關資料\n      _this7.templateList = [];\n      _this7.templateDetailList = [];\n      _this7.selectedTemplateForImport = null;\n      _this7.templateSearchKeyword = '';\n      // 重置分頁相關資料\n      _this7.templateCurrentPage = 1;\n      _this7.paginatedTemplateList = [];\n      _this7.templateTotalItems = 0;\n      // 載入模板列表\n      yield _this7.loadTemplateList();\n      // 開啟對話框\n      _this7.dialogService.open(dialog, {\n        closeOnBackdropClick: false\n      });\n    })();\n  }\n  // 載入模板列表\n  loadTemplateList() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const getTemplateListArgs = {\n          CTemplateType: 1,\n          // 1=客變需求\n          PageIndex: _this8.templateCurrentPage,\n          PageSize: _this8.templatePageSize,\n          CTemplateName: _this8.templateSearchKeyword || null\n        };\n        const response = yield _this8.templateService.apiTemplateGetTemplateListPost$Json({\n          body: getTemplateListArgs\n        }).toPromise();\n        if (response?.StatusCode === 0 && response.Entries) {\n          _this8.templateList = response.Entries.map(item => ({\n            TemplateID: item.CTemplateId,\n            TemplateName: item.CTemplateName || '',\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\n          }));\n          // 不再需要 filteredTemplateList，直接使用 templateList\n          // 保存 API 返回的總項目數\n          _this8.templateTotalItems = response.TotalItems || 0;\n          // 初始化分頁\n          _this8.updatePaginatedTemplateList();\n        } else {\n          _this8.templateList = [];\n          _this8.templateTotalItems = 0;\n          _this8.message.showErrorMSG('載入模板列表失敗');\n        }\n      } catch (error) {\n        console.error('載入模板列表錯誤:', error);\n        _this8.message.showErrorMSG('載入模板列表失敗');\n      }\n    })();\n  }\n  // 篩選模板\n  filterTemplates() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      // 重置到第一頁並重新載入資料\n      _this9.templateCurrentPage = 1;\n      yield _this9.loadTemplateList();\n    })();\n  }\n  // 清除模板搜尋\n  clearTemplateSearch() {\n    this.templateSearchKeyword = '';\n    this.filterTemplates();\n  }\n  // 選擇模板\n  selectTemplateForImport(template) {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      _this10.selectedTemplateForImport = template;\n      yield _this10.loadTemplateDetails(template.TemplateID);\n    })();\n  }\n  // 載入模板詳情\n  loadTemplateDetails(templateId) {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const args = {\n          templateId: templateId\n        };\n        const response = yield _this11.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n          body: args\n        }).toPromise();\n        if (response?.StatusCode === 0 && response.Entries) {\n          _this11.templateDetailList = response.Entries.map(item => ({\n            CTemplateDetailId: item.CTemplateDetailId || 0,\n            CTemplateId: item.CTemplateId || templateId,\n            CReleateId: item.CReleateId || 0,\n            CReleateName: item.CReleateName || '',\n            CGroupName: item.CGroupName || '',\n            selected: false // 預設不選中\n          }));\n        } else {\n          _this11.templateDetailList = [];\n          _this11.message.showErrorMSG('載入模板詳情失敗');\n        }\n      } catch (error) {\n        console.error('載入模板詳情錯誤:', error);\n        _this11.message.showErrorMSG('載入模板詳情失敗');\n      }\n    })();\n  }\n  // 更新選中項目計數\n  updateSelectedCount() {\n    // 這個方法在 HTML 中被調用，用於觸發變更檢測\n  }\n  // 全選模板項目\n  selectAllTemplateItems() {\n    this.templateDetailList.forEach(item => {\n      item.selected = true;\n    });\n  }\n  // 取消全選模板項目\n  deselectAllTemplateItems() {\n    this.templateDetailList.forEach(item => {\n      item.selected = false;\n    });\n  }\n  // 獲取選中的模板項目數量\n  getSelectedTemplateItemsCount() {\n    return this.templateDetailList.filter(item => item.selected).length;\n  }\n  // 匯入選中的模板項目\n  importSelectedTemplateItems(ref) {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      const selectedItems = _this12.templateDetailList.filter(item => item.selected);\n      if (selectedItems.length === 0) {\n        _this12.message.showErrorMSG('請選擇要匯入的項目');\n        return;\n      }\n      try {\n        // 將選中的模板項目轉換為報價單項目格式\n        const importedItems = selectedItems.map(item => ({\n          cHouseID: _this12.currentHouse?.CID || 0,\n          cItemName: item.CReleateName,\n          cUnit: '',\n          // 模板中沒有單位資訊，預設為空\n          cUnitPrice: 0,\n          // 模板中沒有價格資訊，預設為0\n          cCount: 1,\n          // 預設數量為1\n          cStatus: 1,\n          CQuotationItemType: CQuotationItemType.自定義,\n          // 從模板匯入的項目保持舊版設定\n          cRemark: item.CGroupName ? `群組: ${item.CGroupName}` : '' // 將群組名稱作為備註\n        }));\n        // 添加到報價單項目列表\n        _this12.quotationItems.push(...importedItems);\n        // 重新計算總金額\n        _this12.calculateTotal();\n        // 顯示成功訊息\n        _this12.message.showSucessMSG(`成功匯入 ${selectedItems.length} 個項目`);\n        // 關閉對話框\n        ref.close();\n      } catch (error) {\n        console.error('匯入模板項目錯誤:', error);\n        _this12.message.showErrorMSG('匯入模板項目失敗');\n      }\n    })();\n  }\n  // 模板分頁相關方法\n  updatePaginatedTemplateList() {\n    // 由於使用 API 分頁，直接使用當前載入的模板列表\n    this.paginatedTemplateList = [...this.templateList];\n  }\n  getTotalTemplatePages() {\n    const totalPages = Math.ceil(this.templateTotalItems / this.templatePageSize);\n    return Math.max(1, totalPages); // 至少返回1頁\n  }\n  getTemplateStartIndex() {\n    return (this.templateCurrentPage - 1) * this.templatePageSize;\n  }\n  getTemplateEndIndex() {\n    const endIndex = this.templateCurrentPage * this.templatePageSize;\n    return Math.min(endIndex, this.templateTotalItems);\n  }\n  previousTemplatePage() {\n    var _this13 = this;\n    return _asyncToGenerator(function* () {\n      if (_this13.templateCurrentPage > 1) {\n        _this13.templateCurrentPage--;\n        yield _this13.loadTemplateList();\n      }\n    })();\n  }\n  nextTemplatePage() {\n    var _this14 = this;\n    return _asyncToGenerator(function* () {\n      if (_this14.templateCurrentPage < _this14.getTotalTemplatePages()) {\n        _this14.templateCurrentPage++;\n        yield _this14.loadTemplateList();\n      }\n    })();\n  }\n  static {\n    this.ɵfac = function HouseholdManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i6.HouseHoldMainService), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i9.EventService), i0.ɵɵdirectiveInject(i10.UtilityService), i0.ɵɵdirectiveInject(i11.QuotationService), i0.ɵɵdirectiveInject(i6.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdManagementComponent,\n      selectors: [[\"ngx-household-management\"]],\n      viewQuery: function HouseholdManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 119,\n      vars: 23,\n      consts: [[\"fileInput\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialogHouseholdMain\", \"\"], [\"dialogQuotation\", \"\"], [\"dialogTemplateImport\", \"\"], [\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cHouseType\", 1, \"label\", \"col-3\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"text\", \"id\", \"CFrom\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"text\", \"id\", \"CTo\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPayStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7E73\\u6B3E\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cQuotationStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5831\\u50F9\\u55AE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [\"type\", \"file\", \"accept\", \".xlsx, .xls\", 2, \"display\", \"none\", 3, \"change\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"text-center\", \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [\"class\", \"px-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", \"px-8\", 3, \"click\"], [\"class\", \"btn btn-primary m-2 bg-[#169BD5] px-8\", 3, \"click\", 4, \"ngIf\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"cBuildCaseId\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6236\\u578B\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6A13\\u5C64\", \"min\", \"1\", \"max\", \"100\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cCustomerName\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5BA2\\u6236\\u59D3\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cNationalId\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8EAB\\u5206\\u8B49\\u5B57\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cMail\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPhone\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u806F\\u7D61\\u96FB\\u8A71\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHouseType\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u6236\\u5225\\u985E\\u578B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"form-group\", 4, \"ngIf\"], [\"for\", \"cIsChange\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"form-group\", \"flex\", \"flex-row\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", \"content-center\", 2, \"min-width\", \"75px\"], [1, \"max-w-xs\", \"flex\", \"flex-row\"], [1, \"w-1/2\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"mr-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"ml-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"cPayStatus\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u4ED8\\u6B3E\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"m-2\", \"bg-[#169BD5]\", \"px-8\", 3, \"click\"], [\"for\", \"cBuildingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u68DF\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CHouseHoldCount\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"mr-4\", 3, \"click\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [2, \"width\", \"1200px\", \"max-height\", \"95vh\"], [\"class\", \"mb-4 d-flex justify-content-between\", 4, \"ngIf\"], [\"class\", \"mb-4 alert alert-warning\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-bordered\"], [\"width\", \"25%\"], [\"width\", \"15%\"], [\"width\", \"8%\"], [\"width\", \"18%\"], [\"width\", \"10%\"], [4, \"ngIf\"], [1, \"mt-4\"], [1, \"card\", \"border-0\", \"shadow-sm\"], [1, \"card-body\", \"p-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"h6\", \"mb-0\", \"text-muted\"], [1, \"h5\", \"mb-0\", \"text-dark\", \"fw-bold\"], [1, \"tax-section\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\", \"p-3\", \"bg-light\", \"rounded\"], [1, \"d-flex\", \"align-items-center\"], [1, \"tax-icon-wrapper\", \"me-3\"], [1, \"fas\", \"fa-receipt\", \"text-info\"], [1, \"fw-medium\", \"text-dark\"], [1, \"tax-percentage\", \"ms-1\", \"badge\", \"bg-info\", \"text-white\"], [1, \"small\", \"text-muted\", \"mt-1\"], [1, \"fas\", \"fa-info-circle\", \"me-1\"], [1, \"text-end\"], [1, \"tax-amount\", \"h6\", \"mb-0\", \"text-info\", \"fw-bold\"], [1, \"small\", \"text-muted\"], [1, \"my-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"h4\", \"mb-0\", \"text-primary\", \"fw-bold\"], [1, \"d-flex\", \"justify-content-between\"], [\"title\", \"\\u5217\\u5370\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-print\", \"me-1\"], [\"class\", \"btn btn-outline-success btn-sm me-2\", \"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-warning m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"mb-4\", \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"mb-4\", \"alert\", \"alert-warning\"], [1, \"fas\", \"fa-lock\", \"me-2\"], [\"type\", \"text\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"min\", \"0\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"text-right\"], [1, \"badge\"], [\"class\", \"btn btn-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"text-muted\"], [1, \"fas\", \"fa-lock\"], [\"colspan\", \"7\", 1, \"text-center\", \"text-muted\", \"py-4\"], [\"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"btn\", \"btn-warning\", \"m-2\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-primary\", \"m-2\", 3, \"click\", \"disabled\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\"], [1, \"mb-0\"], [1, \"fas\", \"fa-download\", \"mr-2\", \"text-primary\"], [1, \"badge\", \"badge-info\"], [1, \"template-selection-section\", \"mb-4\"], [1, \"section-title\", \"mb-3\"], [1, \"fas\", \"fa-list\", \"mr-2\"], [1, \"search-container\", \"mb-3\"], [1, \"input-group\"], [1, \"input-group-prepend\"], [1, \"input-group-text\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"class\", \"input-group-append\", 4, \"ngIf\"], [1, \"template-list\"], [\"class\", \"template-item\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"empty-state text-center py-4\", 4, \"ngIf\"], [\"class\", \"pagination-container mt-3\", 4, \"ngIf\"], [\"class\", \"template-details-section\", 4, \"ngIf\"], [\"class\", \"import-info\", 4, \"ngIf\"], [1, \"actions\"], [1, \"btn\", \"btn-outline-secondary\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\", \"mr-1\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"template-item\", 3, \"click\"], [1, \"template-info\"], [1, \"template-name\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\"], [1, \"template-description\", \"text-muted\", \"small\"], [1, \"template-actions\"], [\"class\", \"fas fa-check-circle text-success\", 4, \"ngIf\"], [1, \"fas\", \"fa-check-circle\", \"text-success\"], [1, \"empty-state\", \"text-center\", \"py-4\"], [1, \"fas\", \"fa-search\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"], [1, \"pagination-container\", \"mt-3\"], [1, \"pagination-info\"], [1, \"pagination-controls\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"mr-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"pagination-current\", \"mx-2\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"template-details-section\"], [1, \"fas\", \"fa-list-ul\", \"mr-2\"], [1, \"badge\", \"badge-secondary\", \"ml-2\"], [1, \"details-list\", 2, \"max-height\", \"250px\", \"overflow-y\", \"auto\"], [\"class\", \"detail-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"bulk-actions mt-3\", 4, \"ngIf\"], [1, \"detail-item\"], [1, \"detail-checkbox\"], [3, \"checkedChange\", \"checked\"], [1, \"detail-content\"], [1, \"detail-name\"], [1, \"detail-meta\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [1, \"meta-item\"], [1, \"fas\", \"fa-hashtag\", \"mr-1\"], [1, \"fas\", \"fa-layer-group\", \"mr-1\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"bulk-actions\", \"mt-3\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-check-square\", \"mr-1\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-square\", \"mr-1\"], [1, \"ml-3\", \"text-muted\"], [1, \"import-info\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"]],\n      template: function HouseholdManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 7)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 8);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 9)(7, \"div\", 10)(8, \"div\", 11)(9, \"label\", 12);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function HouseholdManagementComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n          });\n          i0.ɵɵtemplate(12, HouseholdManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11)(15, \"label\", 15);\n          i0.ɵɵtext(16, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseTypeSelected, $event) || (ctx.searchQuery.CHouseTypeSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(18, HouseholdManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 10)(20, \"div\", 17)(21, \"label\", 18);\n          i0.ɵɵtext(22, \"\\u6A13 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-form-field\", 19)(24, \"input\", 20);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"label\", 21);\n          i0.ɵɵtext(26, \"~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-form-field\", 22)(28, \"input\", 23);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(29, \"div\", 10);\n          i0.ɵɵelementStart(30, \"div\", 10)(31, \"div\", 11)(32, \"label\", 24);\n          i0.ɵɵtext(33, \" \\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"nb-select\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseHoldSelected, $event) || (ctx.searchQuery.CHouseHoldSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(35, HouseholdManagementComponent_nb_option_35_Template, 2, 2, \"nb-option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 10)(37, \"div\", 11)(38, \"label\", 26);\n          i0.ɵɵtext(39, \" \\u7E73\\u6B3E\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"nb-select\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CPayStatusSelected, $event) || (ctx.searchQuery.CPayStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(41, HouseholdManagementComponent_nb_option_41_Template, 2, 2, \"nb-option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 10)(43, \"div\", 11)(44, \"label\", 28);\n          i0.ɵɵtext(45, \" \\u9032\\u5EA6 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"nb-select\", 29);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CProgressSelected, $event) || (ctx.searchQuery.CProgressSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(47, HouseholdManagementComponent_nb_option_47_Template, 2, 2, \"nb-option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 10)(49, \"div\", 11)(50, \"label\", 30);\n          i0.ɵɵtext(51, \" \\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"nb-select\", 31);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CIsEnableSeleted, $event) || (ctx.searchQuery.CIsEnableSeleted = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(53, HouseholdManagementComponent_nb_option_53_Template, 2, 2, \"nb-option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 10)(55, \"div\", 11)(56, \"label\", 32);\n          i0.ɵɵtext(57, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"nb-select\", 33);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CSignStatusSelected, $event) || (ctx.searchQuery.CSignStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(59, HouseholdManagementComponent_nb_option_59_Template, 2, 2, \"nb-option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"div\", 10)(61, \"div\", 11)(62, \"label\", 34);\n          i0.ɵɵtext(63, \" \\u5831\\u50F9\\u55AE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"nb-select\", 35);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_64_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CQuotationStatusSelected, $event) || (ctx.searchQuery.CQuotationStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(65, HouseholdManagementComponent_nb_option_65_Template, 2, 2, \"nb-option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(66, \"div\", 10);\n          i0.ɵɵelementStart(67, \"div\", 36)(68, \"div\", 37)(69, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_69_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵtext(70, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(71, \"i\", 39);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(72, \"div\", 36)(73, \"div\", 40);\n          i0.ɵɵtemplate(74, HouseholdManagementComponent_button_74_Template, 2, 0, \"button\", 41);\n          i0.ɵɵelementStart(75, \"button\", 42);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_75_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNavidateId(\"modify-floor-plan\"));\n          });\n          i0.ɵɵtext(76, \" \\u4FEE\\u6539\\u6A13\\u5C64\\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"button\", 42);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_77_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportHouse());\n          });\n          i0.ɵɵtext(78, \" \\u532F\\u51FA\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"input\", 43, 0);\n          i0.ɵɵlistener(\"change\", function HouseholdManagementComponent_Template_input_change_79_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_81_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.triggerFileInput());\n          });\n          i0.ɵɵtext(82, \" \\u532F\\u5165\\u66F4\\u65B0\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(83, \"div\", 45)(84, \"table\", 46)(85, \"thead\")(86, \"tr\", 47)(87, \"th\", 48);\n          i0.ɵɵtext(88, \"\\u6236\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"th\", 48);\n          i0.ɵɵtext(90, \"\\u6A13\\u5C64\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"th\", 48);\n          i0.ɵɵtext(92, \"\\u6236\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"th\", 48);\n          i0.ɵɵtext(94, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"th\", 48);\n          i0.ɵɵtext(96, \"\\u9032\\u5EA6\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"th\", 48);\n          i0.ɵɵtext(98, \"\\u7E73\\u6B3E\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"th\", 48);\n          i0.ɵɵtext(100, \"\\u7C3D\\u56DE\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"th\", 48);\n          i0.ɵɵtext(102, \"\\u5831\\u50F9\\u55AE\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"th\", 48);\n          i0.ɵɵtext(104, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"th\", 49);\n          i0.ɵɵtext(106, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(107, \"tbody\");\n          i0.ɵɵtemplate(108, HouseholdManagementComponent_tr_108_Template, 31, 13, \"tr\", 50);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(109, \"nb-card-footer\", 51)(110, \"ngb-pagination\", 52);\n          i0.ɵɵtwoWayListener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_110_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_110_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(111, HouseholdManagementComponent_ng_template_111_Template, 6, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(113, HouseholdManagementComponent_ng_template_113_Template, 20, 5, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(115, HouseholdManagementComponent_ng_template_115_Template, 69, 13, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(117, HouseholdManagementComponent_ng_template_117_Template, 33, 9, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseTypeSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseHoldSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseHoldOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CPayStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.payStatusOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CProgressSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.progressOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CIsEnableSeleted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.cIsEnableOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CSignStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.signStatusOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CQuotationStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.quotationStatusOptions);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i12.NgForOf, i12.NgIf, SharedModule, i13.DefaultValueAccessor, i13.NumberValueAccessor, i13.NgControlStatus, i13.MaxLengthValidator, i13.MinValidator, i13.MaxValidator, i13.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, i3.NbCheckboxComponent, i3.NbInputDirective, i3.NbSelectComponent, i3.NbOptionComponent, i3.NbFormFieldComponent, i3.NbPrefixDirective, i3.NbIconComponent, i3.NbDatepickerDirective, i3.NbDatepickerComponent, i14.NgbPagination, i15.BreadcrumbComponent, i16.BaseLabelDirective, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\".card[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;\\n}\\n\\n.tax-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;\\n  border: 1px solid #dee2e6;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.tax-section[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.tax-section[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(to bottom, #0dcaf0, #0aa2c0);\\n  transition: width 0.3s ease;\\n}\\n.tax-section[_ngcontent-%COMP%]:hover::before {\\n  width: 6px;\\n}\\n\\n.tax-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #0dcaf0, #0aa2c0);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 8px rgba(13, 202, 240, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: white !important;\\n}\\n.tax-icon-wrapper[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 4px 12px rgba(13, 202, 240, 0.4);\\n}\\n\\n.tax-percentage[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n.tax-amount[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.tax-amount[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  color: #0aa2c0 !important;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #0d6efd !important;\\n}\\n\\n.text-info[_ngcontent-%COMP%] {\\n  color: #0dcaf0 !important;\\n}\\n\\nhr[_ngcontent-%COMP%] {\\n  border-top: 2px solid #dee2e6;\\n  opacity: 0.5;\\n}\\n\\n.h5[_ngcontent-%COMP%], \\n.h6[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n.text-primary.fw-bold[_ngcontent-%COMP%] {\\n  text-shadow: 0 1px 2px rgba(13, 110, 253, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.text-primary.fw-bold[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n  text-shadow: 0 2px 4px rgba(13, 110, 253, 0.2);\\n}\\n\\n.fa-info-circle[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  transition: opacity 0.2s ease;\\n}\\n.fa-info-circle[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n@media (max-width: 768px) {\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 1.5rem !important;\\n  }\\n  .h4[_ngcontent-%COMP%], \\n   .h5[_ngcontent-%COMP%], \\n   .h6[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n  .tax-icon-wrapper[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .tax-section[_ngcontent-%COMP%] {\\n    padding: 1rem !important;\\n  }\\n  .tax-section[_ngcontent-%COMP%]::before {\\n    width: 3px;\\n  }\\n  .tax-section[_ngcontent-%COMP%]:hover::before {\\n    width: 4px;\\n  }\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  border-bottom: 2px solid #e9ecef;\\n  padding-bottom: 0.5rem;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-color: #ced4da;\\n  color: #6c757d;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-color: #ced4da;\\n  transition: all 0.3s ease;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #0d6efd;\\n  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%] {\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  min-height: 200px;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-bottom: 1px solid #f1f3f4;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateX(4px);\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%] {\\n  background-color: #e7f3ff;\\n  border-left: 4px solid #0d6efd;\\n  transform: translateX(4px);\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  color: #0d6efd;\\n  font-weight: 600;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n  transition: color 0.3s ease;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n}\\n\\n.template-details-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  border-bottom: 2px solid #e9ecef;\\n  padding-bottom: 0.5rem;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%] {\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border-bottom: 1px solid #f1f3f4;\\n  display: flex;\\n  align-items: center;\\n  transition: background-color 0.3s ease;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-checkbox[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n  flex-shrink: 0;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-name[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n  color: #212529;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  display: flex;\\n  align-items: center;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n  opacity: 0.7;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.375rem;\\n  border: 1px solid #e9ecef;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.875rem;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border-radius: 0.375rem;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .pagination-current[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  font-size: 0.875rem;\\n  white-space: nowrap;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "QUOTATION_TEMPLATE", "NbDatepickerModule", "BaseComponent", "concatMap", "tap", "NbDateFnsDateModule", "moment", "EEvent", "EnumHouseProgress", "EnumHouseType", "EnumPayStatus", "EnumSignStatus", "EnumQuotationStatus", "LocalStorageService", "STORAGE_KEY", "CQuotationItemType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "case_r3", "label", "case_r4", "case_r5", "case_r6", "case_r7", "case_r8", "case_r9", "ɵɵlistener", "HouseholdManagementComponent_button_74_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r10", "ɵɵnextContext", "dialogHouseholdMain_r12", "ɵɵreference", "ɵɵresetView", "openModel", "HouseholdManagementComponent_tr_108_button_20_Template_button_click_0_listener", "_r14", "item_r15", "$implicit", "dialogUpdateHousehold_r16", "openModelDetail", "ɵɵtemplate", "HouseholdManagementComponent_tr_108_button_20_Template", "HouseholdManagementComponent_tr_108_Template_button_click_21_listener", "_r13", "onNavidateBuildCaseIdHouseId", "searchQuery", "CBuildCaseSelected", "cID", "CID", "HouseholdManagementComponent_tr_108_Template_button_click_23_listener", "HouseholdManagementComponent_tr_108_Template_button_click_25_listener", "HouseholdManagementComponent_tr_108_Template_button_click_27_listener", "resetSecureKey", "HouseholdManagementComponent_tr_108_Template_button_click_29_listener", "dialogQuotation_r17", "openQuotation", "ɵɵtextInterpolate", "CHouseHold", "CFloor", "ɵɵtextInterpolate2", "CHouseType", "CIsChange", "CProgressName", "ɵɵtextInterpolate3", "CPayStatus", "CSignStatus", "getQuotationStatusText", "CQuotationStatus", "CIsEnable", "isUpdate", "status_r20", "status_r21", "status_r23", "ɵɵtwoWayListener", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener", "$event", "_r22", "ɵɵtwoWayBindingSet", "detailSelected", "CPayStatusSelected", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_nb_option_4_Template", "ɵɵtwoWayProperty", "options", "payStatusOptions", "status_r25", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener", "_r24", "CProgressSelected", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_nb_option_4_Template", "progressOptions", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_4_listener", "_r19", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_5_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_9_listener", "houseDetail", "CHousehold", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_13_listener", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_17_listener", "CCustomerName", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_21_listener", "CNationalId", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_25_listener", "CMail", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_29_listener", "CPhone", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_33_listener", "CHouseTypeSelected", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_34_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener", "ɵɵelement", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_53_listener", "changeStartDate", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_58_listener", "changeEndDate", "userBuildCaseOptions", "houseTypeOptions", "isChangePayStatus", "isChangeProgress", "StartDate_r26", "EndDate_r27", "HouseholdManagementComponent_ng_template_111_button_5_Template_button_click_0_listener", "_r29", "ref_r28", "dialogRef", "onSubmitDetail", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template", "HouseholdManagementComponent_ng_template_111_Template_button_click_3_listener", "_r18", "onClose", "HouseholdManagementComponent_ng_template_111_button_5_Template", "isCreate", "HouseholdManagementComponent_ng_template_113_button_19_Template_button_click_0_listener", "_r32", "ref_r31", "addHouseHoldMain", "HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_7_listener", "_r30", "houseHoldMain", "CBuildingName", "HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_11_listener", "CHouseHoldCount", "HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_15_listener", "HouseholdManagementComponent_ng_template_113_Template_button_click_17_listener", "HouseholdManagementComponent_ng_template_113_button_19_Template", "HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_1_listener", "_r34", "dialogTemplateImport_r35", "openTemplateImportDialog", "HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_4_listener", "loadDefaultItems", "HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_6_listener", "loadRegularItems", "HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template_button_click_0_listener", "_r38", "i_r39", "index", "removeQuotationItem", "HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_2_listener", "item_r37", "_r36", "cItemName", "HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_4_listener", "cUnitPrice", "calculateTotal", "HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_6_listener", "cUnit", "HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_8_listener", "cCount", "HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template", "HouseholdManagementComponent_ng_template_115_tr_25_span_16_Template", "ɵɵclassProp", "isQuotationEditable", "formatCurrency", "getQuotationTypeText", "HouseholdManagementComponent_ng_template_115_button_63_Template_button_click_0_listener", "_r40", "createNewQuotation", "HouseholdManagementComponent_ng_template_115_button_67_Template_button_click_0_listener", "_r42", "ref_r41", "lockQuotation", "quotationItems", "length", "HouseholdManagementComponent_ng_template_115_button_68_Template_button_click_0_listener", "_r43", "saveQuotation", "HouseholdManagementComponent_ng_template_115_div_4_Template", "HouseholdManagementComponent_ng_template_115_div_5_Template", "HouseholdManagementComponent_ng_template_115_tr_25_Template", "HouseholdManagementComponent_ng_template_115_tr_26_Template", "HouseholdManagementComponent_ng_template_115_Template_button_click_60_listener", "_r33", "printQuotation", "HouseholdManagementComponent_ng_template_115_button_63_Template", "HouseholdManagementComponent_ng_template_115_Template_button_click_65_listener", "HouseholdManagementComponent_ng_template_115_button_67_Template", "HouseholdManagementComponent_ng_template_115_button_68_Template", "currentHouse", "totalAmount", "additionalFeeAmount", "finalTotalAmount", "HouseholdManagementComponent_ng_template_117_div_19_Template_button_click_1_listener", "_r45", "clearTemplateSearch", "HouseholdManagementComponent_ng_template_117_div_21_Template_div_click_0_listener", "template_r47", "_r46", "selectTemplateForImport", "HouseholdManagementComponent_ng_template_117_div_21_i_8_Template", "selectedTemplateForImport", "TemplateID", "TemplateName", "Description", "templateSearchKeyword", "HouseholdManagementComponent_ng_template_117_div_23_Template_button_click_6_listener", "_r48", "previousTemplatePage", "HouseholdManagementComponent_ng_template_117_div_23_Template_button_click_11_listener", "nextTemplatePage", "getTemplateStartIndex", "getTemplateEndIndex", "templateTotalItems", "templateCurrentPage", "getTotalTemplatePages", "detail_r50", "CGroupName", "HouseholdManagementComponent_ng_template_117_div_24_div_7_Template_nb_checkbox_checkedChange_2_listener", "_r49", "selected", "updateSelectedCount", "HouseholdManagementComponent_ng_template_117_div_24_div_7_span_8_Template", "CReleateName", "CReleateId", "HouseholdManagementComponent_ng_template_117_div_24_div_9_Template_button_click_1_listener", "_r51", "selectAllTemplateItems", "HouseholdManagementComponent_ng_template_117_div_24_div_9_Template_button_click_4_listener", "deselectAllTemplateItems", "getSelectedTemplateItemsCount", "templateDetailList", "HouseholdManagementComponent_ng_template_117_div_24_div_7_Template", "HouseholdManagementComponent_ng_template_117_div_24_div_8_Template", "HouseholdManagementComponent_ng_template_117_div_24_div_9_Template", "HouseholdManagementComponent_ng_template_117_Template_input_ngModelChange_18_listener", "_r44", "HouseholdManagementComponent_ng_template_117_Template_input_input_18_listener", "filterTemplates", "HouseholdManagementComponent_ng_template_117_div_19_Template", "HouseholdManagementComponent_ng_template_117_div_21_Template", "HouseholdManagementComponent_ng_template_117_div_22_Template", "HouseholdManagementComponent_ng_template_117_div_23_Template", "HouseholdManagementComponent_ng_template_117_div_24_Template", "HouseholdManagementComponent_ng_template_117_div_26_Template", "HouseholdManagementComponent_ng_template_117_Template_button_click_28_listener", "ref_r52", "HouseholdManagementComponent_ng_template_117_Template_button_click_30_listener", "importSelectedTemplateItems", "templateList", "HouseholdManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "_houseService", "_houseHoldMainService", "_buildCaseService", "pettern", "router", "_eventService", "_ultilityService", "quotationService", "templateService", "tempBuildCaseID", "pageFirst", "pageSize", "pageIndex", "totalRecords", "statusOptions", "value", "key", "cIsEnableOptions", "buildCaseOptions", "houseHoldOptions", "signStatusOptions", "quotationStatusOptions", "getEnumOptions", "initDetail", "CHouseID", "CNationalID", "CProgress", "additionalFeeName", "additionalFeePercentage", "enableAdditionalFee", "currentQuotationId", "templatePageSize", "paginatedTemplateList", "selectedFile", "buildingSelectedOptions", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "GetSessionStorage", "HOUSE_SEARCH", "undefined", "previous_search", "JSON", "parse", "CHouseHoldSelected", "find", "x", "CSignStatusSelected", "CQuotationStatusSelected", "CIsEnableSeleted", "CFrom", "CTo", "getListBuildCase", "onSearch", "sessionSave", "AddSessionStorage", "stringify", "getHouseList", "pageChanged", "newPage", "exportHouse", "apiHouseExportHousePost$Json", "CBuildCaseID", "Entries", "StatusCode", "downloadExcelFile", "showErrorMSG", "Message", "triggerFileInput", "fileInput", "nativeElement", "click", "onFileSelected", "event", "input", "target", "files", "importExcel", "formData", "FormData", "append", "apiHouseImportHousePost$Json", "body", "CFile", "showSucessMSG", "getListHouseHold", "apiHouseGetListHouseHoldPost$Json", "map", "e", "findIndex", "formatQuery", "bodyRequest", "PageIndex", "PageSize", "sortByFloorDescending", "arr", "sort", "a", "b", "apiHouseGetHouseListPost$Json", "houseList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "CIsPagi", "CStatus", "setTimeout", "getHouseById", "ref", "apiHouseGetHouseByIdPost$Json", "CChangeStartDate", "Date", "CChangeEndDate", "CBuildCaseId", "findItemInArray", "open", "array", "item", "formatDate", "CChangeDate", "format", "editHouseArgsParam", "CId", "validation", "errorMessages", "showErrorMSGs", "apiHouseEditHousePost$Json", "close", "onSubmit", "bodyReq", "onNavidateId", "type", "id", "idURL", "navigate", "buildCaseId", "houseId", "confirm", "apiHouseResetHouseSecureKeyPost$Json", "clear", "required", "isStringMaxLength", "pattern", "MailPettern", "isPhoneNumber", "checkStartBeforeEnd", "validationHouseHoldMain", "isNaturalNumberInRange", "apiHouseHoldMainAddHouseHoldMainPost$Json", "dialog", "_this", "_asyncToGenerator", "response", "getQuotationByHouseId", "to<PERSON>romise", "CQuotationVersionId", "Items", "Array", "isArray", "entry", "cHouseID", "cQuotationID", "CQuotationID", "CItemName", "CUnit", "CUnitPrice", "CCount", "cStatus", "自定義", "cRemark", "CRemark", "cQuotationStatus", "error", "console", "context", "closeOnBackdropClick", "addQuotationItem", "push", "_this2", "request", "success", "data", "defaultItems", "客變需求", "_this3", "regularItems", "選樣", "splice", "reduce", "sum", "calculateFinalTotal", "Math", "round", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "_this4", "invalidItems", "filter", "trim", "items", "quotationId", "cShowOther", "cOtherName", "cOtherPercent", "exportQuotation", "_this5", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "revokeObjectURL", "printContent", "generatePrintContent", "printWindow", "write", "onload", "print", "template", "currentDate", "toLocaleDateString", "buildCaseName", "itemsHtml", "for<PERSON>ach", "subtotal", "quotationType", "unit", "additionalFeeHtml", "html", "replace", "toLocaleString", "_this6", "log", "status", "待報價", "已報價", "已簽回", "_this7", "loadTemplateList", "_this8", "getTemplateListArgs", "CTemplateType", "CTemplateName", "apiTemplateGetTemplateListPost$Json", "CTemplateId", "CCreateDt", "updatePaginatedTemplateList", "_this9", "_this10", "loadTemplateDetails", "templateId", "_this11", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "CTemplateDetailId", "_this12", "selectedItems", "importedItems", "totalPages", "ceil", "max", "endIndex", "min", "_this13", "_this14", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "HouseService", "HouseHoldMainService", "BuildCaseService", "i7", "PetternHelper", "i8", "Router", "i9", "EventService", "i10", "UtilityService", "i11", "QuotationService", "TemplateService", "selectors", "viewQuery", "HouseholdManagementComponent_Query", "rf", "ctx", "HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "HouseholdManagementComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "HouseholdManagementComponent_nb_option_12_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener", "HouseholdManagementComponent_nb_option_18_Template", "HouseholdManagementComponent_Template_input_ngModelChange_24_listener", "HouseholdManagementComponent_Template_input_ngModelChange_28_listener", "HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener", "HouseholdManagementComponent_nb_option_35_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener", "HouseholdManagementComponent_nb_option_41_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener", "HouseholdManagementComponent_nb_option_47_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener", "HouseholdManagementComponent_nb_option_53_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener", "HouseholdManagementComponent_nb_option_59_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_64_listener", "HouseholdManagementComponent_nb_option_65_Template", "HouseholdManagementComponent_Template_button_click_69_listener", "HouseholdManagementComponent_button_74_Template", "HouseholdManagementComponent_Template_button_click_75_listener", "HouseholdManagementComponent_Template_button_click_77_listener", "HouseholdManagementComponent_Template_input_change_79_listener", "HouseholdManagementComponent_Template_button_click_81_listener", "HouseholdManagementComponent_tr_108_Template", "HouseholdManagementComponent_Template_ngb_pagination_pageChange_110_listener", "HouseholdManagementComponent_ng_template_111_Template", "ɵɵtemplateRefExtractor", "HouseholdManagementComponent_ng_template_113_Template", "HouseholdManagementComponent_ng_template_115_Template", "HouseholdManagementComponent_ng_template_117_Template", "i12", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i13", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "MinValidator", "MaxValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i14", "NgbPagination", "i15", "BreadcrumbComponent", "i16", "BaseLabelDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { QUOTATION_TEMPLATE } from 'src/assets/template/quotation-template';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseHoldMainService, HouseService, TemplateService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\n// import { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { AddHouseHoldMain, EditHouseArgs, GetHouseListArgs, GetHouseListRes, TblHouse, TemplateGetListArgs, TemplateDetailItem, GetTemplateDetailByIdArgs } from 'src/services/api/models';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\r\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\r\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { QuotationItem, CQuotationItemType } from 'src/app/models/quotation.model';\r\nimport { QuotationService } from 'src/app/services/quotation.service';\r\n\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n// interface HouseDetailExtension {\r\n//   changeStartDate: string;\r\n//   changeEndDate: string;\r\n// }\r\nexport interface SearchQuery {\r\n  CBuildCaseSelected?: any | null;\r\n  CHouseTypeSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CHouseHoldSelected?: any | null;\r\n  CPayStatusSelected?: any | null;\r\n  CProgressSelected?: any | null;\r\n  CSignStatusSelected?: any | null;\r\n  CQuotationStatusSelected?: any | null;\r\n  CIsEnableSeleted?: any | null;\r\n  CFrom?: any | null;\r\n  CTo?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-household-management',\r\n  templateUrl: './household-management.component.html',\r\n  styleUrls: ['./household-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule,],\r\n})\r\n\r\nexport class HouseholdManagementComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseID: number = -1\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _houseHoldMainService: HouseHoldMainService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private _eventService: EventService,\r\n    private _ultilityService: UtilityService,\r\n    private quotationService: QuotationService,\r\n    private templateService: TemplateService\r\n  ) {\r\n    super(_allow)\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  cIsEnableOptions = [\r\n    {\r\n      value: null,\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: true,\r\n      key: 'enable',\r\n      label: '啟用',\r\n    },\r\n    {\r\n      value: false,\r\n      key: 'deactivate',\r\n      label: '停用',\r\n    }\r\n  ]\r\n\r\n  searchQuery: SearchQuery\r\n  detailSelected: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n  houseHoldOptions: any[] = [{ label: '全部', value: '' }]\r\n  progressOptions: any[] = [{ label: '全部', value: -1 }]\r\n  houseTypeOptions: any[] = [{ label: '全部', value: -1 }]\r\n  payStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  signStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  quotationStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n\r\n  options = {\r\n    progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\r\n    payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\r\n    houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\r\n    quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus),\r\n  }\r\n\r\n  userBuildCaseOptions: any\r\n  initDetail = {\r\n    CHouseID: 0,\r\n    CMail: \"\",\r\n    CIsChange: false,\r\n    CPayStatus: 0,\r\n    CIsEnable: false,\r\n    CCustomerName: \"\",\r\n    CNationalID: \"\",\r\n    CProgress: \"\",\r\n    CHouseType: 0,\r\n    CHouseHold: \"\",\r\n    CPhone: \"\"\r\n  }\r\n  // 報價單相關\r\n  quotationItems: QuotationItem[] = [];\r\n  totalAmount: number = 0;\r\n  // 新增：百分比費用設定\r\n  additionalFeeName: string = '營業稅';  // 固定名稱\r\n  additionalFeePercentage: number = 5;   // 固定5%\r\n  additionalFeeAmount: number = 0;       // 百分比費用金額\r\n  finalTotalAmount: number = 0;          // 最終總金額（含百分比費用）\r\n  enableAdditionalFee: boolean = true;   // 固定啟用營業稅\r\n  currentHouse: any = null;\r\n  currentQuotationId: number = 0;\r\n  isQuotationEditable: boolean = true; // 報價單是否可編輯\r\n\r\n  // 模板匯入相關屬性\r\n  templateList: any[] = [];\r\n  templateDetailList: any[] = [];\r\n  selectedTemplateForImport: any = null;\r\n  templateSearchKeyword: string = '';\r\n\r\n  // 模板分頁相關屬性\r\n  templateCurrentPage: number = 1;\r\n  templatePageSize: number = 1; // 每頁顯示1個模板（測試用）\r\n  paginatedTemplateList: any[] = [];\r\n  templateTotalItems: number = 0; // API 返回的總項目數\r\n\r\n  override ngOnInit(): void {\r\n    this.progressOptions = [\r\n      ...this.progressOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseProgress)\r\n    ]\r\n    this.houseTypeOptions = [\r\n      ...this.houseTypeOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseType)\r\n    ]\r\n    this.payStatusOptions = [\r\n      ...this.payStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumPayStatus)\r\n    ]\r\n    this.signStatusOptions = [\r\n      ...this.signStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumSignStatus)\r\n    ]\r\n    this.quotationStatusOptions = [\r\n      ...this.quotationStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumQuotationStatus)\r\n    ]\r\n\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\r\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\r\n        //   : this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined\r\n          ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value)\r\n          : this.houseHoldOptions[0],\r\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined\r\n          ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value)\r\n          : this.houseTypeOptions[0],\r\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined\r\n          ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value)\r\n          : this.payStatusOptions[0],\r\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined\r\n          ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value)\r\n          : this.progressOptions[0],\r\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined\r\n          ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value)\r\n          : this.signStatusOptions[0],\r\n        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined\r\n          ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value)\r\n          : this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined\r\n          ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value)\r\n          : this.cIsEnableOptions[0],\r\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined\r\n          ? previous_search.CFrom\r\n          : '',\r\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined\r\n          ? previous_search.CTo\r\n          : ''\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: this.houseHoldOptions[0],\r\n        CHouseTypeSelected: this.houseTypeOptions[0],\r\n        CPayStatusSelected: this.payStatusOptions[0],\r\n        CProgressSelected: this.progressOptions[0],\r\n        CSignStatusSelected: this.signStatusOptions[0],\r\n        CQuotationStatusSelected: this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: this.cIsEnableOptions[0],\r\n        CFrom: '',\r\n        CTo: ''\r\n      }\r\n    }\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  onSearch() {\r\n    let sessionSave = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\r\n      CFrom: this.searchQuery.CFrom,\r\n      CTo: this.searchQuery.CTo,\r\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\r\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\r\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\r\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\r\n      CProgressSelected: this.searchQuery.CProgressSelected,\r\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected,\r\n      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  exportHouse() {\r\n    if (this.searchQuery.CBuildCaseSelected.cID) {\r\n      this._houseService.apiHouseExportHousePost$Json({\r\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(\r\n            res.Entries, '戶別資訊範本'\r\n          )\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  selectedFile: File | null = null;\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n\r\n  triggerFileInput(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.selectedFile = input.files[0];\r\n      this.importExcel();\r\n    }\r\n  }\r\n\r\n  importExcel(): void {\r\n    if (this.selectedFile) {\r\n      const formData = new FormData();\r\n      formData.append('CFile', this.selectedFile);\r\n      this._houseService.apiHouseImportHousePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n          CFile: this.selectedFile\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(res.Message!);\r\n          this.getHouseList().subscribe()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getListHouseHold() {\r\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\r\n            let index = this.houseHoldOptions.findIndex((x: any) => x.value == previous_search.CHouseHoldSelected.value)\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index]\r\n          } else {\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0]\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  formatQuery() {\r\n    this.bodyRequest = {\r\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\r\n      this.bodyRequest['CFloor'] = { CFrom: this.searchQuery.CFrom, CTo: this.searchQuery.CTo }\r\n    }\r\n    if (this.searchQuery.CHouseHoldSelected) {\r\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value\r\n    }\r\n    if (this.searchQuery.CHouseTypeSelected.value) {\r\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value\r\n    }\r\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\r\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value\r\n    }\r\n    if (this.searchQuery.CPayStatusSelected.value) {\r\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CProgressSelected.value) {\r\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value\r\n    }\r\n    if (this.searchQuery.CSignStatusSelected.value) {\r\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CQuotationStatusSelected.value) {\r\n      this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value\r\n    }\r\n\r\n    return this.bodyRequest\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: this.formatQuery()\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n\r\n  userBuildCaseSelected: any\r\n  onSelectionChangeBuildCase() {\r\n    // this.getListBuilding()\r\n    this.getListHouseHold()\r\n    this.getHouseList().subscribe()\r\n  }\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            }\r\n          }) : []\r\n\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == previous_search.CBuildCaseSelected.cID)\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n          }\r\n        }\r\n      }),\r\n      tap(() => {\r\n        // this.getListBuilding()\r\n        this.getListHouseHold()\r\n        setTimeout(() => {\r\n          this.getHouseList().subscribe();\r\n        }, 500)\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  buildingSelected: any\r\n\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  // getListBuilding() {\r\n  //   this._houseService.apiHouseGetListBuildingPost$Json({\r\n  //     body: {\r\n  //       CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n  //     }\r\n  //   }).subscribe(res => {\r\n  //     if (res.Entries && res.StatusCode == 0) {\r\n  //       this.buildingSelectedOptions = [{\r\n  //         value: '', label: '全部'\r\n  //       }, ...res.Entries.map(e => {\r\n  //         return { value: e, label: e }\r\n  //       })]\r\n  //       if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n  //         let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n  //         if (previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined) {\r\n  //           let index = this.buildingSelectedOptions.findIndex((x: any) => x.value == previous_search.CBuildingNameSelected.value)\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[index]\r\n  //         } else {\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //         }\r\n  //       }\r\n  //       else {\r\n  //         this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //       }\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  houseDetail: TblHouse & {\r\n    changeStartDate?: any;\r\n    changeEndDate?: any\r\n  }\r\n\r\n  getHouseById(CID: any, ref: any) {\r\n    this.detailSelected = {}\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: CID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseDetail = {\r\n          ...res.Entries,\r\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\r\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined,\r\n        }\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId)\r\n        }\r\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus)\r\n        if (res.Entries.CHouseType) {\r\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType)\r\n        } else {\r\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1]\r\n        }\r\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress)\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          if (this.houseHoldMain) {\r\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId\r\n          }\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n\r\n    })\r\n  }\r\n\r\n\r\n  findItemInArray(array: any[], key: string, value: any) {\r\n    return array.find(item => item[key] === value);\r\n  }\r\n\r\n\r\n  openModelDetail(ref: any, item: any) {\r\n    this.getHouseById(item.CID, ref)\r\n  }\r\n\r\n  openModel(ref: any) {\r\n    this.houseHoldMain = {\r\n      CBuildingName: '',\r\n      CFloor: undefined,\r\n      CHouseHoldCount: undefined\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  editHouseArgsParam: EditHouseArgs\r\n\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmitDetail(ref: any) {\r\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '',\r\n      this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '',\r\n\r\n      this.editHouseArgsParam = {\r\n        CCustomerName: this.houseDetail.CCustomerName,\r\n        CHouseHold: this.houseDetail.CHousehold,\r\n        CHouseID: this.houseDetail.CId,\r\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\r\n        CIsChange: this.houseDetail.CIsChange,\r\n        CIsEnable: this.houseDetail.CIsEnable,\r\n        CMail: this.houseDetail.CMail,\r\n        CNationalID: this.houseDetail.CNationalId,\r\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\r\n        CPhone: this.houseDetail.CPhone,\r\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\r\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\r\n        CChangeEndDate: this.houseDetail.CChangeEndDate\r\n      }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: this.editHouseArgsParam\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  onSubmit(ref: any) {\r\n    let bodyReq: EditHouseArgs = {\r\n      CCustomerName: this.houseDetail.CCustomerName,\r\n      CHouseHold: this.houseDetail.CHousehold,\r\n      CHouseID: this.houseDetail.CId,\r\n      CHouseType: this.houseDetail.CHouseType,\r\n      CIsChange: this.houseDetail.CIsChange,\r\n      CIsEnable: this.houseDetail.CIsEnable,\r\n      CMail: this.houseDetail.CMail,\r\n      CNationalID: this.houseDetail.CNationalId,\r\n      CPayStatus: this.houseDetail.CPayStatus,\r\n      CPhone: this.houseDetail.CPhone,\r\n      CProgress: this.houseDetail.CProgress,\r\n    }\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavidateId(type: any, id?: any) {\r\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID\r\n    this.router.navigate([`/pages/household-management/${type}`, idURL])\r\n  }\r\n\r\n  onNavidateBuildCaseIdHouseId(type: any, buildCaseId: any, houseId: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId])\r\n  }\r\n\r\n  resetSecureKey(item: any) {\r\n    if (confirm(\"您想重設密碼嗎？\")) {\r\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\r\n        body: item.CID\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  houseHoldMain: AddHouseHoldMain\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.houseDetail.CId)\r\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold)\r\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50)\r\n    this.valid.required('[樓層]', this.houseDetail.CFloor)\r\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50)\r\n    // if (this.editHouseArgsParam.CNationalID) {\r\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\r\n    // }\r\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern)\r\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone)\r\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress)\r\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value)\r\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value)\r\n    if (this.houseDetail.CChangeStartDate) {\r\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate)\r\n    }\r\n    if (this.houseDetail.CChangeEndDate) {\r\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate)\r\n    }\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '')\r\n  }\r\n\r\n  validationHouseHoldMain() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID)\r\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName)\r\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10)\r\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100)\r\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100)\r\n  }\r\n\r\n\r\n  addHouseHoldMain(ref: any) {\r\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID,\r\n      this.validationHouseHoldMain()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\r\n      body: this.houseHoldMain\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }  // 開啟報價單對話框\r\n  async openQuotation(dialog: any, item: any) {\r\n    this.currentHouse = item;\r\n    this.quotationItems = [];\r\n    this.totalAmount = 0;\r\n    this.currentQuotationId = 0; // 重置報價單ID\r\n    this.isQuotationEditable = true; // 預設可編輯\r\n    // 重置百分比費用設定（固定營業稅5%）\r\n    this.additionalFeeName = '營業稅';\r\n    this.additionalFeePercentage = 5;\r\n    this.additionalFeeAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.enableAdditionalFee = true;\r\n\r\n    // 載入現有報價資料\r\n    try {\r\n      const response = await this.quotationService.getQuotationByHouseId(item.CID).toPromise();\r\n\r\n      if (response && response.StatusCode === 0 && response.Entries) {\r\n        // 保存當前的報價單ID\r\n        this.currentQuotationId = response.Entries.CQuotationVersionId || 0;\r\n        // 根據 cQuotationStatus 決定是否可編輯\r\n        if (response.Entries.CQuotationStatus === 2) { // 2: 已報價\r\n          this.isQuotationEditable = false;\r\n        } else {\r\n          this.isQuotationEditable = true;\r\n        }\r\n\r\n        // 載入額外費用設定（固定營業稅5%，不從後端載入）\r\n        this.enableAdditionalFee = true;\r\n        this.additionalFeeName = '營業稅';\r\n        this.additionalFeePercentage = 5;\r\n\r\n        // 檢查 Entries 是否有 Items 陣列\r\n        if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\r\n          // 將 API 回傳的資料轉換為 QuotationItem 格式\r\n          this.quotationItems = response.Entries.Items.map((entry: any) => ({\r\n            cHouseID: response.Entries.CHouseID || item.CID,\r\n            cQuotationID: response.Entries.CQuotationID,\r\n            cItemName: entry.CItemName || '',\r\n            cUnit: entry.CUnit || '',\r\n            cUnitPrice: entry.CUnitPrice || 0,\r\n            cCount: entry.CCount || 1,\r\n            cStatus: entry.CStatus || 1,\r\n            CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\r\n            cRemark: entry.CRemark || '',\r\n            cQuotationStatus: entry.CQuotationStatus\r\n          }));\r\n          this.calculateTotal();\r\n        } else {\r\n\r\n        }\r\n      } else {\r\n\r\n      }\r\n    } catch (error) {\r\n      console.error('載入報價資料失敗:', error);\r\n    }\r\n\r\n    this.dialogService.open(dialog, {\r\n      context: item,\r\n      closeOnBackdropClick: false\r\n    });\r\n  }\r\n\r\n  // 產生新報價單\r\n  createNewQuotation() {\r\n    this.currentQuotationId = 0;\r\n    this.quotationItems = [];\r\n    this.isQuotationEditable = true;\r\n    this.totalAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.additionalFeeAmount = 0;\r\n    this.enableAdditionalFee = true;\r\n\r\n    // 顯示成功訊息\r\n    this.message.showSucessMSG('已產生新報價單，可開始編輯');\r\n  }\r\n  // 新增自定義報價項目\r\n  addQuotationItem() {\r\n    this.quotationItems.push({\r\n      cHouseID: this.currentHouse?.CID || 0,\r\n      cItemName: '',\r\n      cUnit: '',\r\n      cUnitPrice: 0,\r\n      cCount: 1,\r\n      cStatus: 1,\r\n      CQuotationItemType: CQuotationItemType.自定義,\r\n      cRemark: ''\r\n    });\r\n  }\r\n  // 載入客變需求\r\n  async loadDefaultItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.searchQuery?.CBuildCaseSelected?.cID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadDefaultItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const defaultItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnit: x.CUnit || '',\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          CQuotationItemType: CQuotationItemType.客變需求,\r\n          cRemark: x.CRemark\r\n        }));\r\n        this.quotationItems.push(...defaultItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入客變需求成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入客變需求失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入客變需求錯誤:', error);\r\n      this.message.showErrorMSG('載入客變需求失敗');\r\n    }\r\n  }\r\n\r\n  // 載入選樣資料\r\n  async loadRegularItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.searchQuery?.CBuildCaseSelected?.cID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadRegularItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const regularItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnit: x.CUnit || '',\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          CQuotationItemType: CQuotationItemType.選樣, // 選樣資料\r\n          cRemark: x.CRemark || ''\r\n        }));\r\n        this.quotationItems.push(...regularItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入選樣資料成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入選樣資料失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入選樣資料錯誤:', error);\r\n      this.message.showErrorMSG('載入選樣資料失敗');\r\n    }\r\n  }\r\n\r\n  // 移除報價項目\r\n  removeQuotationItem(index: number) {\r\n    const item = this.quotationItems[index];\r\n    this.quotationItems.splice(index, 1);\r\n    this.calculateTotal();\r\n  }\r\n\r\n  // 計算總金額\r\n  calculateTotal() {\r\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\r\n      return sum + (item.cUnitPrice * item.cCount);\r\n    }, 0);\r\n    this.calculateFinalTotal();\r\n  }\r\n\r\n  // 計算百分比費用和最終總金額（固定營業稅5%）\r\n  calculateFinalTotal() {\r\n    // 固定計算營業稅5%\r\n    this.additionalFeeAmount = Math.round(this.totalAmount * 0.05);\r\n    this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\r\n  }\r\n\r\n  // 格式化金額\r\n  formatCurrency(amount: number): string {\r\n    return new Intl.NumberFormat('zh-TW', {\r\n      style: 'currency',\r\n      currency: 'TWD',\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  }\r\n\r\n\r\n\r\n  // 儲存報價單\r\n  async saveQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    // 驗證必填欄位 (調整：允許單價和數量為負數)\r\n    const invalidItems = this.quotationItems.filter(item =>\r\n      !item.cItemName.trim()\r\n    );\r\n\r\n    if (invalidItems.length > 0) {\r\n      this.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\r\n      return;\r\n    } try {\r\n      const request = {\r\n        houseId: this.currentHouse.CID,\r\n        items: this.quotationItems,\r\n        quotationId: this.currentQuotationId, // 傳遞當前的報價單ID\r\n        // 額外費用相關欄位\r\n        cShowOther: this.enableAdditionalFee, // 啟用額外費用\r\n        cOtherName: this.additionalFeeName,   // 額外費用名稱\r\n        cOtherPercent: this.additionalFeePercentage // 額外費用百分比\r\n      };\r\n\r\n      const response = await this.quotationService.saveQuotation(request).toPromise();\r\n      if (response?.success) {\r\n        this.message.showSucessMSG('報價單儲存成功');\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '儲存失敗');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單儲存失敗');\r\n    }\r\n  }\r\n\r\n  // 匯出報價單\r\n  async exportQuotation() {\r\n    try {\r\n      const blob: Blob | undefined = await this.quotationService.exportQuotation(this.currentHouse.CID).toPromise();\r\n      if (blob) {\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = `報價單_${this.currentHouse.CHouseHold}_${this.currentHouse.CFloor}樓.pdf`;\r\n        link.click();\r\n        window.URL.revokeObjectURL(url);\r\n      } else {\r\n        this.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('匯出報價單失敗');\r\n    }\r\n  }\r\n\r\n  // 列印報價單\r\n  printQuotation() {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('沒有可列印的報價項目');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 建立列印內容\r\n      const printContent = this.generatePrintContent();\r\n\r\n      // 建立新的視窗進行列印\r\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\r\n      if (printWindow) {\r\n        printWindow.document.open();\r\n        printWindow.document.write(printContent);\r\n        printWindow.document.close();\r\n\r\n        // 等待內容載入完成後列印\r\n        printWindow.onload = function () {\r\n          setTimeout(() => {\r\n            printWindow.print();\r\n            // 列印後不自動關閉視窗，讓使用者可以預覽\r\n          }, 500);\r\n        };\r\n      } else {\r\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\r\n      }\r\n    } catch (error) {\r\n      console.error('列印報價單錯誤:', error);\r\n      this.message.showErrorMSG('列印報價單時發生錯誤');\r\n    }\r\n  }\r\n\r\n  // 產生列印內容\r\n  private generatePrintContent(): string {\r\n    // 使用導入的模板\r\n    const template = QUOTATION_TEMPLATE;\r\n\r\n    // 準備數據\r\n    const currentDate = new Date().toLocaleDateString('zh-TW');\r\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\r\n\r\n    // 生成項目HTML\r\n    let itemsHtml = '';\r\n    this.quotationItems.forEach((item, index) => {\r\n      const subtotal = item.cUnitPrice * item.cCount;\r\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\r\n      const unit = item.cUnit || '';\r\n      itemsHtml += `\r\n          <tr>\r\n            <td class=\"text-center\">${index + 1}</td>\r\n            <td>${item.cItemName}</td>\r\n            <td class=\"text-right\">${this.formatCurrency(item.cUnitPrice)}</td>\r\n            <td class=\"text-center\">${unit}</td>\r\n            <td class=\"text-center\">${item.cCount}</td>\r\n            <td class=\"text-right\">${this.formatCurrency(subtotal)}</td>\r\n            <td class=\"text-center\">${quotationType}</td>\r\n          </tr>\r\n        `;\r\n    });\r\n\r\n    // 生成額外費用HTML\r\n    const additionalFeeHtml = this.enableAdditionalFee ? `\r\n        <div class=\"additional-fee\">\r\n          ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\r\n        </div>\r\n      ` : '';\r\n\r\n    // 替換模板中的占位符\r\n    const html = template\r\n      .replace(/{{buildCaseName}}/g, buildCaseName)\r\n      .replace(/{{houseHold}}/g, this.currentHouse?.CHouseHold || '')\r\n      .replace(/{{floor}}/g, this.currentHouse?.CFloor || '')\r\n      .replace(/{{customerName}}/g, this.currentHouse?.CCustomerName || '')\r\n      .replace(/{{printDate}}/g, currentDate)\r\n      .replace(/{{itemsHtml}}/g, itemsHtml)\r\n      .replace(/{{subtotalAmount}}/g, this.formatCurrency(this.totalAmount))\r\n      .replace(/{{additionalFeeHtml}}/g, additionalFeeHtml)\r\n      .replace(/{{totalAmount}}/g, this.formatCurrency(this.finalTotalAmount))\r\n      .replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\r\n\r\n    return html;\r\n  }\r\n\r\n\r\n  // 鎖定報價單\r\n  async lockQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    if (!this.currentQuotationId) {\r\n      this.message.showErrorMSG('無效的報價單ID');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await this.quotationService.lockQuotation(this.currentQuotationId).toPromise();\r\n\r\n      if (response.success) {\r\n        this.message.showSucessMSG('報價單已成功鎖定');\r\n        console.log('報價單鎖定成功:', {\r\n          quotationId: this.currentQuotationId,\r\n          message: response.message\r\n        });\r\n      } else {\r\n        this.message.showErrorMSG(response.message || '報價單鎖定失敗');\r\n        console.error('報價單鎖定失敗:', response.message);\r\n      }\r\n\r\n      ref.close();\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單鎖定失敗');\r\n      console.error('鎖定報價單錯誤:', error);\r\n    }\r\n  }\r\n\r\n  // 取得報價類型文字\r\n  getQuotationTypeText(quotationType: CQuotationItemType): string {\r\n    switch (quotationType) {\r\n      case CQuotationItemType.客變需求:\r\n        return '客變需求';\r\n      case CQuotationItemType.自定義:\r\n        return '自定義';\r\n      case CQuotationItemType.選樣:\r\n        return '選樣';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  getQuotationStatusText(status: number): string {\r\n    switch (status) {\r\n      case EnumQuotationStatus.待報價:\r\n        return '待報價';\r\n      case EnumQuotationStatus.已報價:\r\n        return '已報價';\r\n      case EnumQuotationStatus.已簽回:\r\n        return '已簽回';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  // 模板匯入相關方法\r\n  async openTemplateImportDialog(dialog: any) {\r\n    // 重置模板相關資料\r\n    this.templateList = [];\r\n    this.templateDetailList = [];\r\n    this.selectedTemplateForImport = null;\r\n    this.templateSearchKeyword = '';\r\n\r\n    // 重置分頁相關資料\r\n    this.templateCurrentPage = 1;\r\n    this.paginatedTemplateList = [];\r\n    this.templateTotalItems = 0;\r\n\r\n    // 載入模板列表\r\n    await this.loadTemplateList();\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(dialog, {\r\n      closeOnBackdropClick: false\r\n    });\r\n  }\r\n\r\n  // 載入模板列表\r\n  async loadTemplateList() {\r\n    try {\r\n      const getTemplateListArgs: TemplateGetListArgs = {\r\n        CTemplateType: 1, // 1=客變需求\r\n        PageIndex: this.templateCurrentPage,\r\n        PageSize: this.templatePageSize,\r\n        CTemplateName: this.templateSearchKeyword || null\r\n      };\r\n\r\n      const response = await this.templateService.apiTemplateGetTemplateListPost$Json({\r\n        body: getTemplateListArgs\r\n      }).toPromise();\r\n\r\n      if (response?.StatusCode === 0 && response.Entries) {\r\n        this.templateList = response.Entries.map(item => ({\r\n          TemplateID: item.CTemplateId,\r\n          TemplateName: item.CTemplateName || '',\r\n          Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\r\n        }));\r\n        // 不再需要 filteredTemplateList，直接使用 templateList\r\n        // 保存 API 返回的總項目數\r\n        this.templateTotalItems = response.TotalItems || 0;\r\n        // 初始化分頁\r\n        this.updatePaginatedTemplateList();\r\n      } else {\r\n        this.templateList = [];\r\n        this.templateTotalItems = 0;\r\n        this.message.showErrorMSG('載入模板列表失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入模板列表錯誤:', error);\r\n      this.message.showErrorMSG('載入模板列表失敗');\r\n    }\r\n  }\r\n\r\n  // 篩選模板\r\n  async filterTemplates() {\r\n    // 重置到第一頁並重新載入資料\r\n    this.templateCurrentPage = 1;\r\n    await this.loadTemplateList();\r\n  }\r\n\r\n  // 清除模板搜尋\r\n  clearTemplateSearch() {\r\n    this.templateSearchKeyword = '';\r\n    this.filterTemplates();\r\n  }\r\n\r\n  // 選擇模板\r\n  async selectTemplateForImport(template: any) {\r\n    this.selectedTemplateForImport = template;\r\n    await this.loadTemplateDetails(template.TemplateID);\r\n  }\r\n\r\n  // 載入模板詳情\r\n  async loadTemplateDetails(templateId: number) {\r\n    try {\r\n      const args: GetTemplateDetailByIdArgs = {\r\n        templateId: templateId\r\n      };\r\n\r\n      const response = await this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n        body: args\r\n      }).toPromise();\r\n\r\n      if (response?.StatusCode === 0 && response.Entries) {\r\n        this.templateDetailList = response.Entries.map(item => ({\r\n          CTemplateDetailId: item.CTemplateDetailId || 0,\r\n          CTemplateId: item.CTemplateId || templateId,\r\n          CReleateId: item.CReleateId || 0,\r\n          CReleateName: item.CReleateName || '',\r\n          CGroupName: item.CGroupName || '',\r\n          selected: false // 預設不選中\r\n        }));\r\n      } else {\r\n        this.templateDetailList = [];\r\n        this.message.showErrorMSG('載入模板詳情失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入模板詳情錯誤:', error);\r\n      this.message.showErrorMSG('載入模板詳情失敗');\r\n    }\r\n  }\r\n\r\n  // 更新選中項目計數\r\n  updateSelectedCount() {\r\n    // 這個方法在 HTML 中被調用，用於觸發變更檢測\r\n  }\r\n\r\n  // 全選模板項目\r\n  selectAllTemplateItems() {\r\n    this.templateDetailList.forEach(item => {\r\n      item.selected = true;\r\n    });\r\n  }\r\n\r\n  // 取消全選模板項目\r\n  deselectAllTemplateItems() {\r\n    this.templateDetailList.forEach(item => {\r\n      item.selected = false;\r\n    });\r\n  }\r\n\r\n  // 獲取選中的模板項目數量\r\n  getSelectedTemplateItemsCount(): number {\r\n    return this.templateDetailList.filter(item => item.selected).length;\r\n  }\r\n\r\n  // 匯入選中的模板項目\r\n  async importSelectedTemplateItems(ref: any) {\r\n    const selectedItems = this.templateDetailList.filter(item => item.selected);\r\n\r\n    if (selectedItems.length === 0) {\r\n      this.message.showErrorMSG('請選擇要匯入的項目');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 將選中的模板項目轉換為報價單項目格式\r\n      const importedItems = selectedItems.map(item => ({\r\n        cHouseID: this.currentHouse?.CID || 0,\r\n        cItemName: item.CReleateName,\r\n        cUnit: '', // 模板中沒有單位資訊，預設為空\r\n        cUnitPrice: 0, // 模板中沒有價格資訊，預設為0\r\n        cCount: 1, // 預設數量為1\r\n        cStatus: 1,\r\n        CQuotationItemType: CQuotationItemType.自定義, // 從模板匯入的項目保持舊版設定\r\n        cRemark: item.CGroupName ? `群組: ${item.CGroupName}` : '' // 將群組名稱作為備註\r\n      }));\r\n\r\n      // 添加到報價單項目列表\r\n      this.quotationItems.push(...importedItems);\r\n\r\n      // 重新計算總金額\r\n      this.calculateTotal();\r\n\r\n      // 顯示成功訊息\r\n      this.message.showSucessMSG(`成功匯入 ${selectedItems.length} 個項目`);\r\n\r\n      // 關閉對話框\r\n      ref.close();\r\n    } catch (error) {\r\n      console.error('匯入模板項目錯誤:', error);\r\n      this.message.showErrorMSG('匯入模板項目失敗');\r\n    }\r\n  }\r\n\r\n  // 模板分頁相關方法\r\n  updatePaginatedTemplateList() {\r\n    // 由於使用 API 分頁，直接使用當前載入的模板列表\r\n    this.paginatedTemplateList = [...this.templateList];\r\n  }\r\n\r\n  getTotalTemplatePages(): number {\r\n    const totalPages = Math.ceil(this.templateTotalItems / this.templatePageSize);\r\n    return Math.max(1, totalPages); // 至少返回1頁\r\n  }\r\n\r\n  getTemplateStartIndex(): number {\r\n    return (this.templateCurrentPage - 1) * this.templatePageSize;\r\n  }\r\n\r\n  getTemplateEndIndex(): number {\r\n    const endIndex = this.templateCurrentPage * this.templatePageSize;\r\n    return Math.min(endIndex, this.templateTotalItems);\r\n  }\r\n\r\n  async previousTemplatePage() {\r\n    if (this.templateCurrentPage > 1) {\r\n      this.templateCurrentPage--;\r\n      await this.loadTemplateList();\r\n    }\r\n  }\r\n\r\n  async nextTemplatePage() {\r\n    if (this.templateCurrentPage < this.getTotalTemplatePages()) {\r\n      this.templateCurrentPage++;\r\n      await this.loadTemplateList();\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHouseType\" class=\"label col-3\">類型</label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CHouseTypeSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseTypeOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"text\" id=\"CFrom\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"text\" id=\"CTo\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">\r\n            棟別\r\n          </label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of buildingSelectedOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div> -->\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHousehold\" class=\"label col-3\">\r\n            戶型\r\n          </label>\r\n          <nb-select placeholder=\"戶型\" [(ngModel)]=\"searchQuery.CHouseHoldSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseHoldOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cPayStatus\" class=\"label col-3\">\r\n            繳款狀態\r\n          </label>\r\n          <nb-select placeholder=\"繳款狀態\" [(ngModel)]=\"searchQuery.CPayStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of payStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cProgress\" class=\"label col-3\">\r\n            進度\r\n          </label>\r\n          <nb-select placeholder=\"進度\" [(ngModel)]=\"searchQuery.CProgressSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of progressOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cStatus\" class=\"label col-3\">\r\n            狀態\r\n          </label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CIsEnableSeleted\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of cIsEnableOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cSignStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <nb-select placeholder=\"簽回狀態\" [(ngModel)]=\"searchQuery.CSignStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of signStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cQuotationStatus\" class=\"label col-3\">\r\n            報價單狀態\r\n          </label>\r\n          <nb-select placeholder=\"報價單狀態\" [(ngModel)]=\"searchQuery.CQuotationStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of quotationStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢按鈕移到這裡，放在搜尋條件的右下角 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openModel(dialogHouseholdMain)\">\r\n            批次新增戶別資料\r\n          </button>\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('modify-floor-plan')\">\r\n            修改樓層戶型\r\n          </button>\r\n          <!-- <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('standard-house-plan')\">\r\n            3.設定戶型標準圖\r\n          </button> -->\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"exportHouse()\">\r\n            匯出戶別明細檔\r\n          </button>\r\n          <input type=\"file\" #fileInput style=\"display:none\" (change)=\"onFileSelected($event)\" accept=\".xlsx, .xls\" />\r\n          <button class=\"btn btn-info btn-sm\" (click)=\"triggerFileInput()\">\r\n            匯入更新戶別明細檔\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <!-- <th scope=\"col\" class=\"col-1\">棟別</th> -->\r\n            <th scope=\"col\" class=\"col-1\">戶型</th>\r\n            <th scope=\"col\" class=\"col-1\">樓層</th>\r\n            <th scope=\"col\" class=\"col-1\">戶別</th>\r\n            <th scope=\"col\" class=\"col-1\">類型</th>\r\n            <th scope=\"col\" class=\"col-1\">進度</th>\r\n            <th scope=\"col\" class=\"col-1\">繳款狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">簽回狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">報價單狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-4\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of houseList ; let i = index\">\r\n            <!-- <td>{{ item.CBuildingName}}</td> -->\r\n            <td>{{ item.CHouseHold}}</td>\r\n            <td>{{ item.CFloor}}</td>\r\n            <td>\r\n              {{ item.CHouseType === 2 ? '銷售戶' : ''}}\r\n              {{item.CHouseType === 1 ? '地主戶' : ''}}\r\n            </td>\r\n            <td>{{ item.CIsChange === true ? '客變' : (item.CIsChange === false ? '標準' :'') }}</td>\r\n            <td>{{ item.CProgressName}}</td>\r\n            <td>\r\n              {{item.CPayStatus === 0 ? '未付款': ''}}\r\n              {{item.CPayStatus === 1 ? '已付款': ''}}\r\n              {{item.CPayStatus === 2 ? '無須付款': ''}}\r\n            </td>\r\n            <td>{{ (item.CSignStatus === 0 || item.CSignStatus == null) ? '未簽回' : '已簽回' }}</td>\r\n            <td>{{ getQuotationStatusText(item.CQuotationStatus) }}</td>\r\n            <td>{{ item.CIsEnable ? '啟用' : '停用'}}</td>\r\n            <td class=\"text-center w-32 px-0\">\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-success btn-sm text-left m-[2px]\"\r\n                (click)=\"openModelDetail(dialogUpdateHousehold, item)\">\r\n                編輯\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('customer-change-picture', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                洽談紀錄\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('sample-selection-result', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                客變確認圖說\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('finaldochouse_management', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                簽署文件歷程\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"resetSecureKey(item)\">\r\n                重置密碼\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"openQuotation(dialogQuotation, item)\">\r\n                報價單\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <!-- <nb-card-header>\r\n    </nb-card-header> -->\r\n    <nb-card-body class=\"px-4\" *ngIf=\"houseDetail\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildCaseId\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          建案名稱\r\n        </label>\r\n        <nb-select placeholder=\"建案名稱\" [(ngModel)]=\"detailSelected.CBuildCaseSelected\" class=\"w-full\" disabled=\"true\">\r\n          <nb-option *ngFor=\"let status of userBuildCaseOptions\" [value]=\"status\">\r\n            {{ status.CBuildCaseName }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHousehold\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶型名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"戶型名稱\" [(ngModel)]=\"houseDetail.CHousehold\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"樓層\" [(ngModel)]=\"houseDetail.CFloor\" min=\"1\" max=\"100\"\r\n          disabled=\"true\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cCustomerName\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          客戶姓名\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"客戶姓名\" [(ngModel)]=\"houseDetail.CCustomerName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cNationalId\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          身分證字號\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"身分證字號\" [(ngModel)]=\"houseDetail.CNationalId\"\r\n          maxlength=\"20\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cMail\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          電子郵件\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"電子郵件\" [(ngModel)]=\"houseDetail.CMail\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cPhone\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          聯絡電話\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"聯絡電話\" [(ngModel)]=\"houseDetail.CPhone\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHouseType\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶別類型\r\n        </label>\r\n        <nb-select placeholder=\"戶別類型\" [(ngModel)]=\"detailSelected.CHouseTypeSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.houseTypeOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\" *ngIf=\"isChangePayStatus\">\r\n        <label for=\"cPayStatus\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          付款狀態\r\n        </label>\r\n        <nb-select placeholder=\"付款狀態\" [(ngModel)]=\"detailSelected.CPayStatusSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.payStatusOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group\" *ngIf=\"isChangeProgress\">\r\n        <label for=\"cProgress\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          進度\r\n        </label>\r\n        <nb-select placeholder=\"進度\" [(ngModel)]=\"detailSelected.CProgressSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.progressOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsChange\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否客變\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsChange\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否啟用\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsEnable\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group flex flex-row\">\r\n        <label for=\"cIsEnable\" class=\"mr-4 content-center\" style=\"min-width:75px\" baseLabel>\r\n          客變時段\r\n        </label>\r\n        <div class=\"max-w-xs flex flex-row\">\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"StartDate\"\r\n              class=\"w-[42%] mr-2\" [(ngModel)]=\"houseDetail.changeStartDate\">\r\n            <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"EndDate\"\r\n              class=\"w-[42%] ml-2\" [(ngModel)]=\"houseDetail.changeEndDate\">\r\n            <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-outline-secondary m-2 px-8\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary m-2 bg-[#169BD5] px-8\" *ngIf=\"isCreate\" (click)=\"onSubmitDetail(ref)\">送出</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogHouseholdMain let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 》批次新增戶別資料\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          棟別\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"棟別\" [(ngModel)]=\"houseHoldMain.CBuildingName\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CHouseHoldCount\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>當層最多戶數\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"當層最多戶數\" [(ngModel)]=\"houseHoldMain.CHouseHoldCount\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>本棠總樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"本棠總樓層\" [(ngModel)]=\"houseHoldMain.CFloor\" />\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-primary mr-4\" (click)=\"onClose(ref)\">{{ '關閉'}}</button>\r\n      <button class=\"btn btn-primary\" *ngIf=\"isCreate\" (click)=\"addHouseHoldMain(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 報價單對話框 -->\r\n<ng-template #dialogQuotation let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:1200px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      報價單 - {{ currentHouse?.CHouseHold }} ({{ currentHouse?.CFloor }}樓)\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 只有報價單可編輯時才顯示操作按鈕 -->\r\n      <div *ngIf=\"isQuotationEditable\" class=\"mb-4 d-flex justify-content-between\">\r\n        <button class=\"btn btn-info btn-sm\" (click)=\"openTemplateImportDialog(dialogTemplateImport)\">\r\n          + 從模板匯入\r\n        </button>\r\n        <div>\r\n          <button class=\"btn btn-secondary btn-sm me-2\" (click)=\"loadDefaultItems()\">\r\n            載入客變需求\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"loadRegularItems()\">\r\n            載入選樣資料\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 報價單已鎖定時的提示 -->\r\n      <div *ngIf=\"!isQuotationEditable\" class=\"mb-4 alert alert-warning\">\r\n        <i class=\"fas fa-lock me-2\"></i>\r\n        <strong>報價單已鎖定</strong> - 此報價單已鎖定，無法進行修改。您可以列印此報價單或產生新的報價單。\r\n      </div>\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-bordered\">\r\n          <thead>\r\n            <tr>\r\n              <th width=\"25%\">項目名稱</th>\r\n              <th width=\"15%\">單價 (元)</th>\r\n              <th width=\"8%\">單位</th>\r\n              <th width=\"8%\">數量</th>\r\n              <th width=\"18%\">小計 (元)</th>\r\n              <th width=\"10%\">類型</th>\r\n              <th width=\"10%\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of quotationItems; let i = index\">\r\n              <td>\r\n                <input type=\"text\" nbInput [(ngModel)]=\"item.cItemName\"\r\n                  [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\"\r\n                  class=\"w-full\"\r\n                  [class.bg-light]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cUnitPrice\" (ngModelChange)=\"calculateTotal()\"\r\n                  class=\"w-full\" min=\"0\" step=\"0.01\" [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"text\" nbInput [(ngModel)]=\"item.cUnit\"\r\n                  [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\"\r\n                  class=\"w-full\" placeholder=\"單位\"\r\n                  [class.bg-light]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cCount\" (ngModelChange)=\"calculateTotal()\" class=\"w-full\"\r\n                  step=\"0.01\" [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td class=\"text-right\">\r\n                {{ formatCurrency(item.cUnitPrice * item.cCount) }}\r\n              </td>\r\n              <td>\r\n                <span class=\"badge\" [class.badge-primary]=\"item.CQuotationItemType === 1\"\r\n                  [class.badge-info]=\"item.CQuotationItemType === 3\"\r\n                  [class.badge-secondary]=\"item.CQuotationItemType !== 1 && item.CQuotationItemType !== 3\">\r\n                  {{ getQuotationTypeText(item.CQuotationItemType) }}\r\n                </span>\r\n              </td>\r\n              <td>\r\n                <button *ngIf=\"isQuotationEditable\" class=\"btn btn-danger btn-sm\" (click)=\"removeQuotationItem(i)\">\r\n                  刪除\r\n                </button>\r\n                <span *ngIf=\"!isQuotationEditable\" class=\"text-muted\">\r\n                  <i class=\"fas fa-lock\"></i>\r\n                </span>\r\n              </td>\r\n            </tr>\r\n            <tr *ngIf=\"quotationItems.length === 0\">\r\n              <td colspan=\"7\" class=\"text-center text-muted py-4\">\r\n                請點擊「新增自定義項目」或「載入客變需求」開始建立報價單\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 金額計算區塊 -->\r\n      <div class=\"mt-4\">\r\n        <div class=\"card border-0 shadow-sm\">\r\n          <div class=\"card-body p-4\">\r\n            <!-- 小計 -->\r\n            <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n              <span class=\"h6 mb-0 text-muted\">小計</span>\r\n              <span class=\"h5 mb-0 text-dark fw-bold\">{{ formatCurrency(totalAmount) }}</span>\r\n            </div>\r\n\r\n            <!-- 營業稅 -->\r\n            <div class=\"tax-section d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded\">\r\n              <div class=\"d-flex align-items-center\">\r\n                <div class=\"tax-icon-wrapper me-3\">\r\n                  <i class=\"fas fa-receipt text-info\"></i>\r\n                </div>\r\n                <div>\r\n                  <span class=\"fw-medium text-dark\">營業稅</span>\r\n                  <span class=\"tax-percentage ms-1 badge bg-info text-white\">5%</span>\r\n                  <div class=\"small text-muted mt-1\">\r\n                    <i class=\"fas fa-info-circle me-1\"></i>\r\n                    固定為小計金額的5%\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"text-end\">\r\n                <div class=\"tax-amount h6 mb-0 text-info fw-bold\">{{ formatCurrency(additionalFeeAmount) }}</div>\r\n                <div class=\"small text-muted\">含稅金額</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 分隔線 -->\r\n            <hr class=\"my-3\">\r\n\r\n            <!-- 總金額 -->\r\n            <div class=\"d-flex justify-content-between align-items-center\">\r\n              <span class=\"h5 mb-0 text-dark fw-bold\">總金額</span>\r\n              <span class=\"h4 mb-0 text-primary fw-bold\">{{ formatCurrency(finalTotalAmount) }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <div>\r\n        <button class=\"btn btn-outline-info btn-sm me-2\" (click)=\"printQuotation()\"\r\n          [disabled]=\"quotationItems.length === 0\" title=\"列印報價單\">\r\n          <i class=\"fas fa-print me-1\"></i> 列印報價單\r\n        </button>\r\n        <!-- 報價單已鎖定時才顯示 -->\r\n        <button *ngIf=\"!isQuotationEditable\" class=\"btn btn-outline-success btn-sm me-2\" (click)=\"createNewQuotation()\"\r\n          title=\"產生新報價單\">\r\n          <i class=\"fas fa-plus me-1\"></i> 產生新報價單\r\n        </button>\r\n        <!-- <button class=\"btn btn-outline-info btn-sm\" (click)=\"exportQuotation()\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          匯出報價單\r\n        </button> -->\r\n      </div>\r\n      <div>\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <!-- 只有在報價單可編輯時才顯示鎖定和儲存按鈕 -->\r\n        <button *ngIf=\"isQuotationEditable\" class=\"btn btn-warning m-2\" (click)=\"lockQuotation(ref)\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          鎖定報價單\r\n        </button>\r\n        <button *ngIf=\"isQuotationEditable\" class=\"btn btn-primary m-2\" (click)=\"saveQuotation(ref)\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          儲存報價單\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 模板匯入對話框 -->\r\n<ng-template #dialogTemplateImport let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:800px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      <div class=\"d-flex justify-content-between align-items-center\">\r\n        <h5 class=\"mb-0\">\r\n          <i class=\"fas fa-download mr-2 text-primary\"></i>從模板匯入項目\r\n        </h5>\r\n        <span class=\"badge badge-info\">客變需求模板</span>\r\n      </div>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 模板選擇區域 -->\r\n      <div class=\"template-selection-section mb-4\">\r\n        <h6 class=\"section-title mb-3\">\r\n          <i class=\"fas fa-list mr-2\"></i>選擇模板\r\n        </h6>\r\n\r\n        <!-- 搜尋框 -->\r\n        <div class=\"search-container mb-3\">\r\n          <div class=\"input-group\">\r\n            <div class=\"input-group-prepend\">\r\n              <span class=\"input-group-text\">\r\n                <i class=\"fas fa-search\"></i>\r\n              </span>\r\n            </div>\r\n            <input type=\"text\" class=\"form-control\" placeholder=\"搜尋模板名稱...\" [(ngModel)]=\"templateSearchKeyword\"\r\n              (input)=\"filterTemplates()\">\r\n            <div class=\"input-group-append\" *ngIf=\"templateSearchKeyword\">\r\n              <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"clearTemplateSearch()\">\r\n                <i class=\"fas fa-times\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板列表 -->\r\n        <div class=\"template-list\">\r\n          <div class=\"template-item\" *ngFor=\"let template of templateList\"\r\n            [class.selected]=\"selectedTemplateForImport?.TemplateID === template.TemplateID\"\r\n            (click)=\"selectTemplateForImport(template)\">\r\n            <div class=\"template-info\">\r\n              <div class=\"template-name\">\r\n                <i class=\"fas fa-file-alt mr-2\"></i>\r\n                {{ template.TemplateName }}\r\n              </div>\r\n              <div class=\"template-description text-muted small\">\r\n                {{ template.Description || '無描述' }}\r\n              </div>\r\n            </div>\r\n            <div class=\"template-actions\">\r\n              <i class=\"fas fa-check-circle text-success\"\r\n                *ngIf=\"selectedTemplateForImport?.TemplateID === template.TemplateID\"></i>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 空狀態 -->\r\n          <div class=\"empty-state text-center py-4\" *ngIf=\"templateList.length === 0\">\r\n            <i class=\"fas fa-search fa-2x text-muted mb-2\"></i>\r\n            <p class=\"text-muted mb-0\">\r\n              {{ templateSearchKeyword ? '找不到符合條件的模板' : '載入中...' }}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 分頁控制 -->\r\n        <div class=\"pagination-container mt-3\" *ngIf=\"templateTotalItems > 0 || templateList.length > 0\">\r\n          <div class=\"d-flex justify-content-between align-items-center\">\r\n            <div class=\"pagination-info\">\r\n              <small class=\"text-muted\">\r\n                顯示 {{ getTemplateStartIndex() + 1 }} - {{ getTemplateEndIndex() }} 筆，共 {{ templateTotalItems }} 筆模板\r\n              </small>\r\n            </div>\r\n            <div class=\"pagination-controls\">\r\n              <button class=\"btn btn-outline-secondary btn-sm mr-2\" (click)=\"previousTemplatePage()\"\r\n                [disabled]=\"templateCurrentPage === 1\">\r\n                <i class=\"fas fa-chevron-left\"></i> 上一頁\r\n              </button>\r\n              <span class=\"pagination-current mx-2\">\r\n                第 {{ templateCurrentPage }} / {{ getTotalTemplatePages() }} 頁\r\n              </span>\r\n              <button class=\"btn btn-outline-secondary btn-sm ml-2\" (click)=\"nextTemplatePage()\"\r\n                [disabled]=\"templateCurrentPage === getTotalTemplatePages()\">\r\n                下一頁 <i class=\"fas fa-chevron-right\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 模板詳情區域 -->\r\n      <div class=\"template-details-section\" *ngIf=\"selectedTemplateForImport\">\r\n        <h6 class=\"section-title mb-3\">\r\n          <i class=\"fas fa-list-ul mr-2\"></i>模板項目\r\n          <span class=\"badge badge-secondary ml-2\">{{ templateDetailList.length }} 項</span>\r\n        </h6>\r\n\r\n        <!-- 項目列表 -->\r\n        <div class=\"details-list\" style=\"max-height: 250px; overflow-y: auto;\">\r\n          <div class=\"detail-item\" *ngFor=\"let detail of templateDetailList; let i = index\">\r\n            <div class=\"detail-checkbox\">\r\n              <nb-checkbox [(checked)]=\"detail.selected\" (checkedChange)=\"updateSelectedCount()\">\r\n              </nb-checkbox>\r\n            </div>\r\n            <div class=\"detail-content\">\r\n              <div class=\"detail-name\">\r\n                <strong>{{ detail.CReleateName }}</strong>\r\n              </div>\r\n              <div class=\"detail-meta\">\r\n                <span class=\"meta-item\" *ngIf=\"detail.CGroupName\">\r\n                  <i class=\"fas fa-layer-group mr-1\"></i>{{ detail.CGroupName }}\r\n                </span>\r\n                <span class=\"meta-item\">\r\n                  <i class=\"fas fa-hashtag mr-1\"></i>ID: {{ detail.CReleateId }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 空狀態 -->\r\n          <div class=\"empty-state text-center py-4\" *ngIf=\"templateDetailList.length === 0\">\r\n            <i class=\"fas fa-inbox fa-2x text-muted mb-2\"></i>\r\n            <p class=\"text-muted mb-0\">此模板暫無項目</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 批量選擇操作 -->\r\n        <div class=\"bulk-actions mt-3\" *ngIf=\"templateDetailList.length > 0\">\r\n          <button class=\"btn btn-outline-primary btn-sm mr-2\" (click)=\"selectAllTemplateItems()\">\r\n            <i class=\"fas fa-check-square mr-1\"></i>全選\r\n          </button>\r\n          <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"deselectAllTemplateItems()\">\r\n            <i class=\"fas fa-square mr-1\"></i>取消全選\r\n          </button>\r\n          <span class=\"ml-3 text-muted\">\r\n            已選擇 {{ getSelectedTemplateItemsCount() }} / {{ templateDetailList.length }} 項\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <div class=\"import-info\" *ngIf=\"selectedTemplateForImport\">\r\n        <small class=\"text-muted\">\r\n          <i class=\"fas fa-info-circle mr-1\"></i>\r\n          將匯入 {{ getSelectedTemplateItemsCount() }} 個項目到報價單\r\n        </small>\r\n      </div>\r\n      <div class=\"actions\">\r\n        <button class=\"btn btn-outline-secondary mr-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <button class=\"btn btn-primary\" (click)=\"importSelectedTemplateItems(ref)\"\r\n          [disabled]=\"!selectedTemplateForImport || getSelectedTemplateItemsCount() === 0\">\r\n          <i class=\"fas fa-download mr-1\"></i>\r\n          匯入選中項目 ({{ getSelectedTemplateItemsCount() }})\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AACA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,kBAAkB,QAAyB,gBAAgB;AAOpE,SAASC,aAAa,QAAQ,kCAAkC;AAGhE,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AACrC,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,MAAM,QAA8B,uCAAuC;AAEpF,SAASC,iBAAiB,QAAQ,uCAAuC;AAEzE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAAwBC,kBAAkB,QAAQ,gCAAgC;;;;;;;;;;;;;;;;;;;;;ICftEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IAQAR,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAK,OAAA,CAAc;IAC7DT,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,OAAA,CAAAC,KAAA,MACF;;;;;IAuCAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAO,OAAA,CAAc;IAC7DX,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,OAAA,CAAAD,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAQ,OAAA,CAAc;IAC7DZ,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAK,OAAA,CAAAF,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAS,OAAA,CAAc;IAC5Db,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAM,OAAA,CAAAH,KAAA,MACF;;;;;IAUAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAU,OAAA,CAAc;IAC7Dd,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAO,OAAA,CAAAJ,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAW,OAAA,CAAc;IAC9Df,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAQ,OAAA,CAAAL,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAAY,OAAA,CAAc;IACnEhB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAS,OAAA,CAAAN,KAAA,MACF;;;;;;IAoBFV,EAAA,CAAAC,cAAA,iBAAmG;IAAzCD,EAAA,CAAAiB,UAAA,mBAAAC,wEAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAAC,uBAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAK,SAAA,CAAAH,uBAAA,CAA8B;IAAA,EAAC;IAChGvB,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAuDLH,EAAA,CAAAC,cAAA,iBACyD;IAAvDD,EAAA,CAAAiB,UAAA,mBAAAU,+EAAA;MAAA3B,EAAA,CAAAmB,aAAA,CAAAS,IAAA;MAAA,MAAAC,QAAA,GAAA7B,EAAA,CAAAsB,aAAA,GAAAQ,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAAS,yBAAA,GAAA/B,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAW,eAAA,CAAAD,yBAAA,EAAAF,QAAA,CAA4C;IAAA,EAAC;IACtD7B,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IApBXH,EAFF,CAAAC,cAAA,SAAmD,SAE7C;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA4E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrFH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAE,MAAA,IAGF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA0E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnFH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAiC,UAAA,KAAAC,sDAAA,qBACyD;IAGzDlC,EAAA,CAAAC,cAAA,kBACmH;IAAjHD,EAAA,CAAAiB,UAAA,mBAAAkB,sEAAA;MAAA,MAAAN,QAAA,GAAA7B,EAAA,CAAAmB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAgB,4BAAA,CAA6B,yBAAyB,EAAAhB,OAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IAChHzC,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACmH;IAAjHD,EAAA,CAAAiB,UAAA,mBAAAyB,sEAAA;MAAA,MAAAb,QAAA,GAAA7B,EAAA,CAAAmB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAgB,4BAAA,CAA6B,yBAAyB,EAAAhB,OAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IAChHzC,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACoH;IAAlHD,EAAA,CAAAiB,UAAA,mBAAA0B,sEAAA;MAAA,MAAAd,QAAA,GAAA7B,EAAA,CAAAmB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAgB,4BAAA,CAA6B,0BAA0B,EAAAhB,OAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IACjHzC,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsF;IAA/BD,EAAA,CAAAiB,UAAA,mBAAA2B,sEAAA;MAAA,MAAAf,QAAA,GAAA7B,EAAA,CAAAmB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAwB,cAAA,CAAAhB,QAAA,CAAoB;IAAA,EAAC;IACnF7B,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsG;IAA/CD,EAAA,CAAAiB,UAAA,mBAAA6B,sEAAA;MAAA,MAAAjB,QAAA,GAAA7B,EAAA,CAAAmB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAAyB,mBAAA,GAAA/C,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA2B,aAAA,CAAAD,mBAAA,EAAAlB,QAAA,CAAoC;IAAA,EAAC;IACnG7B,EAAA,CAAAE,MAAA,4BACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;IAxCCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAAqB,UAAA,CAAoB;IACpBlD,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAAsB,MAAA,CAAgB;IAElBnD,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAAoD,kBAAA,MAAAvB,QAAA,CAAAwB,UAAA,yCAAAxB,QAAA,CAAAwB,UAAA,wCAEF;IACIrD,EAAA,CAAAM,SAAA,GAA4E;IAA5EN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAAyB,SAAA,6BAAAzB,QAAA,CAAAyB,SAAA,iCAA4E;IAC5EtD,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAA0B,aAAA,CAAuB;IAEzBvD,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAwD,kBAAA,MAAA3B,QAAA,CAAA4B,UAAA,yCAAA5B,QAAA,CAAA4B,UAAA,yCAAA5B,QAAA,CAAA4B,UAAA,8CAGF;IACIzD,EAAA,CAAAM,SAAA,GAA0E;IAA1EN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAA6B,WAAA,UAAA7B,QAAA,CAAA6B,WAAA,uDAA0E;IAC1E1D,EAAA,CAAAM,SAAA,GAAmD;IAAnDN,EAAA,CAAAiD,iBAAA,CAAA5B,OAAA,CAAAsC,sBAAA,CAAA9B,QAAA,CAAA+B,gBAAA,EAAmD;IACnD5D,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAAgC,SAAA,mCAAiC;IAE1B7D,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAyC,QAAA,CAAc;;;;;IA6C3B9D,EAAA,CAAAC,cAAA,oBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAA2D,UAAA,CAAgB;IACrE/D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAwD,UAAA,CAAAvD,cAAA,MACF;;;;;IAoDAR,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAA4D,UAAA,CAAgB;IACzEhE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAyD,UAAA,CAAAtD,KAAA,MACF;;;;;IASAV,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAA6D,UAAA,CAAgB;IACzEjE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAA0D,UAAA,CAAAvD,KAAA,MACF;;;;;;IANFV,EADF,CAAAC,cAAA,cAAkD,gBACqC;IACnFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA6F;IAA/DD,EAAA,CAAAkE,gBAAA,2BAAAC,+GAAAC,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkD,cAAA,CAAAC,kBAAA,EAAAJ,MAAA,MAAA/C,OAAA,CAAAkD,cAAA,CAAAC,kBAAA,GAAAJ,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA+C;IAC3EpE,EAAA,CAAAiC,UAAA,IAAAwC,uFAAA,wBAA4E;IAIhFzE,EADE,CAAAG,YAAA,EAAY,EACR;;;;IAL0BH,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkD,cAAA,CAAAC,kBAAA,CAA+C;IAC7CxE,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAsD,OAAA,CAAAC,gBAAA,CAA2B;;;;;IAUzD5E,EAAA,CAAAC,cAAA,oBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAyE,UAAA,CAAgB;IACxE7E,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAsE,UAAA,CAAAnE,KAAA,MACF;;;;;;IANFV,EADF,CAAAC,cAAA,cAAiD,gBACqC;IAClFD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA0F;IAA9DD,EAAA,CAAAkE,gBAAA,2BAAAY,+GAAAV,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAA4D,IAAA;MAAA,MAAA1D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkD,cAAA,CAAAS,iBAAA,EAAAZ,MAAA,MAAA/C,OAAA,CAAAkD,cAAA,CAAAS,iBAAA,GAAAZ,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA8C;IACxEpE,EAAA,CAAAiC,UAAA,IAAAgD,uFAAA,wBAA2E;IAI/EjF,EADE,CAAAG,YAAA,EAAY,EACR;;;;IALwBH,EAAA,CAAAM,SAAA,GAA8C;IAA9CN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkD,cAAA,CAAAS,iBAAA,CAA8C;IAC1ChF,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAsD,OAAA,CAAAO,eAAA,CAA0B;;;;;;IA/E1DlF,EAFJ,CAAAC,cAAA,uBAA+C,cACrB,gBACiE;IACrFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA6G;IAA/ED,EAAA,CAAAkE,gBAAA,2BAAAiB,wGAAAf,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkD,cAAA,CAAAhC,kBAAA,EAAA6B,MAAA,MAAA/C,OAAA,CAAAkD,cAAA,CAAAhC,kBAAA,GAAA6B,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA+C;IAC3EpE,EAAA,CAAAiC,UAAA,IAAAoD,gFAAA,wBAAwE;IAI5ErF,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,cAAwB,gBAC+D;IACnFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAoG;IAAvCD,EAAA,CAAAkE,gBAAA,2BAAAoB,oGAAAlB,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAC,UAAA,EAAApB,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAC,UAAA,GAAApB,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAoC;IACnGpE,EADE,CAAAG,YAAA,EAAoG,EAChG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2D;IAC/ED,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACoB;IADyCD,EAAA,CAAAkE,gBAAA,2BAAAuB,qGAAArB,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAApC,MAAA,EAAAiB,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAApC,MAAA,GAAAiB,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAgC;IAE/FpE,EAFE,CAAAG,YAAA,EACoB,EAChB;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACmD;IACvED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAuG;IAA1CD,EAAA,CAAAkE,gBAAA,2BAAAwB,qGAAAtB,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAI,aAAA,EAAAvB,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAI,aAAA,GAAAvB,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAuC;IACtGpE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACiD;IACrED,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACmB;IAD2CD,EAAA,CAAAkE,gBAAA,2BAAA0B,qGAAAxB,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAM,WAAA,EAAAzB,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAM,WAAA,GAAAzB,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAqC;IAErGpE,EAFE,CAAAG,YAAA,EACmB,EACf;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2C;IAC/DD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA+F;IAAlCD,EAAA,CAAAkE,gBAAA,2BAAA4B,qGAAA1B,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAQ,KAAA,EAAA3B,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAQ,KAAA,GAAA3B,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA+B;IAC9FpE,EADE,CAAAG,YAAA,EAA+F,EAC3F;IAEJH,EADF,CAAAC,cAAA,eAAwB,iBAC4C;IAChED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAgG;IAAnCD,EAAA,CAAAkE,gBAAA,2BAAA8B,qGAAA5B,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAU,MAAA,EAAA7B,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAU,MAAA,GAAA7B,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAgC;IAC/FpE,EADE,CAAAG,YAAA,EAAgG,EAC5F;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+D;IACnFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAA6F;IAA/DD,EAAA,CAAAkE,gBAAA,2BAAAgC,yGAAA9B,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkD,cAAA,CAAA4B,kBAAA,EAAA/B,MAAA,MAAA/C,OAAA,CAAAkD,cAAA,CAAA4B,kBAAA,GAAA/B,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA+C;IAC3EpE,EAAA,CAAAiC,UAAA,KAAAmE,iFAAA,wBAA4E;IAIhFpG,EADE,CAAAG,YAAA,EAAY,EACR;IAYNH,EAVA,CAAAiC,UAAA,KAAAoE,2EAAA,kBAAkD,KAAAC,2EAAA,kBAUD;IAY/CtG,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAAkE,gBAAA,2BAAAqC,2GAAAnC,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAjC,SAAA,EAAAc,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAjC,SAAA,GAAAc,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAmC;IAACpE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAAkE,gBAAA,2BAAAsC,2GAAApC,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAA1B,SAAA,EAAAO,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAA1B,SAAA,GAAAO,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAmC;IAACpE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAsC,iBACgD;IAClFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAENH,EADF,CAAAC,cAAA,eAAoC,yBACL;IAC3BD,EAAA,CAAAyG,SAAA,mBAAoD;IACpDzG,EAAA,CAAAC,cAAA,iBACiE;IAA1CD,EAAA,CAAAkE,gBAAA,2BAAAwC,qGAAAtC,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAoB,eAAA,EAAAvC,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAoB,eAAA,GAAAvC,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAyC;IADhEpE,EAAA,CAAAG,YAAA,EACiE;IACjEH,EAAA,CAAAyG,SAAA,4BAA8D;IAChEzG,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,yBAA6B;IAC3BD,EAAA,CAAAyG,SAAA,mBAAoD;IACpDzG,EAAA,CAAAC,cAAA,iBAC+D;IAAxCD,EAAA,CAAAkE,gBAAA,2BAAA0C,qGAAAxC,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAsB,aAAA,EAAAzC,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAsB,aAAA,GAAAzC,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAuC;IAD9DpE,EAAA,CAAAG,YAAA,EAC+D;IAC/DH,EAAA,CAAAyG,SAAA,4BAA4D;IAIpEzG,EAHM,CAAAG,YAAA,EAAgB,EACZ,EACF,EACO;;;;;;IArHmBH,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkD,cAAA,CAAAhC,kBAAA,CAA+C;IAC7CvC,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAyF,oBAAA,CAAuB;IAUM9G,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAC,UAAA,CAAoC;IAOpCxF,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAApC,MAAA,CAAgC;IAQhCnD,EAAA,CAAAM,SAAA,GAAuC;IAAvCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAI,aAAA,CAAuC;IAOtC3F,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAM,WAAA,CAAqC;IAQtC7F,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAQ,KAAA,CAA+B;IAM/B/F,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAU,MAAA,CAAgC;IAO/DjG,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkD,cAAA,CAAA4B,kBAAA,CAA+C;IAC7CnG,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAsD,OAAA,CAAAoC,gBAAA,CAA2B;IAMpC/G,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA2F,iBAAA,CAAuB;IAUvBhH,EAAA,CAAAM,SAAA,EAAsB;IAAtBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA4F,gBAAA,CAAsB;IAejBjH,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAjC,SAAA,CAAmC;IAQnCtD,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAA1B,SAAA,CAAmC;IAWQ7D,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,iBAAA8G,aAAA,CAA0B;IACtElH,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAoB,eAAA,CAAyC;IAKC3G,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,iBAAA+G,WAAA,CAAwB;IAClEnH,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAsB,aAAA,CAAuC;;;;;;IAQpE7G,EAAA,CAAAC,cAAA,iBAAqG;IAA9BD,EAAA,CAAAiB,UAAA,mBAAAmG,uFAAA;MAAApH,EAAA,CAAAmB,aAAA,CAAAkG,IAAA;MAAA,MAAAC,OAAA,GAAAtH,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAmG,cAAA,CAAAF,OAAA,CAAmB;IAAA,EAAC;IAACtH,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAhIpHH,EAAA,CAAAC,cAAA,kBAA+C;IAG7CD,EAAA,CAAAiC,UAAA,IAAAwF,oEAAA,6BAA+C;IA4H7CzH,EADF,CAAAC,cAAA,yBAAsD,iBACsB;IAAvBD,EAAA,CAAAiB,UAAA,mBAAAyG,8EAAA;MAAA,MAAAJ,OAAA,GAAAtH,EAAA,CAAAmB,aAAA,CAAAwG,IAAA,EAAAJ,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuG,OAAA,CAAAN,OAAA,CAAY;IAAA,EAAC;IAACtH,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrFH,EAAA,CAAAiC,UAAA,IAAA4F,8DAAA,qBAAqG;IAEzG7H,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IA/HoBH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAkE,WAAA,CAAiB;IA6HYvF,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAyG,QAAA,CAAc;;;;;;IA8BrE9H,EAAA,CAAAC,cAAA,kBAAiF;IAAhCD,EAAA,CAAAiB,UAAA,mBAAA8G,wFAAA;MAAA/H,EAAA,CAAAmB,aAAA,CAAA6G,IAAA;MAAA,MAAAC,OAAA,GAAAjI,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA6G,gBAAA,CAAAD,OAAA,CAAqB;IAAA,EAAC;IAACjI,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAvB9FH,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,wFACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,uBAA2B,cACD,gBACkE;IACtFD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAuG;IAA5CD,EAAA,CAAAkE,gBAAA,2BAAAiE,qFAAA/D,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiH,IAAA;MAAA,MAAA/G,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAgH,aAAA,CAAAC,aAAA,EAAAlE,MAAA,MAAA/C,OAAA,CAAAgH,aAAA,CAAAC,aAAA,GAAAlE,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAyC;IACtGpE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAEJH,EADF,CAAAC,cAAA,cAAwB,gBACoE;IAAAD,EAAA,CAAAE,MAAA,6CAC1F;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,kBAA+G;IAA9CD,EAAA,CAAAkE,gBAAA,2BAAAqE,sFAAAnE,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiH,IAAA;MAAA,MAAA/G,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAgH,aAAA,CAAAG,eAAA,EAAApE,MAAA,MAAA/C,OAAA,CAAAgH,aAAA,CAAAG,eAAA,GAAApE,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA2C;IAC9GpE,EADE,CAAAG,YAAA,EAA+G,EAC3G;IAEJH,EADF,CAAAC,cAAA,eAAwB,kBAC2D;IAAAD,EAAA,CAAAE,MAAA,uCACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,kBAAqG;IAArCD,EAAA,CAAAkE,gBAAA,2BAAAuE,sFAAArE,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiH,IAAA;MAAA,MAAA/G,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAgH,aAAA,CAAAlF,MAAA,EAAAiB,MAAA,MAAA/C,OAAA,CAAAgH,aAAA,CAAAlF,MAAA,GAAAiB,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAkC;IAEtGpE,EAFI,CAAAG,YAAA,EAAqG,EACjG,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,mBACQ;IAAvBD,EAAA,CAAAiB,UAAA,mBAAAyH,+EAAA;MAAA,MAAAT,OAAA,GAAAjI,EAAA,CAAAmB,aAAA,CAAAiH,IAAA,EAAAb,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuG,OAAA,CAAAK,OAAA,CAAY;IAAA,EAAC;IAACjI,EAAA,CAAAE,MAAA,IAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9EH,EAAA,CAAAiC,UAAA,KAAA0G,+DAAA,sBAAiF;IAErF3I,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IAjBuDH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAgH,aAAA,CAAAC,aAAA,CAAyC;IAKnCtI,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAgH,aAAA,CAAAG,eAAA,CAA2C;IAK5CxI,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAgH,aAAA,CAAAlF,MAAA,CAAkC;IAIxCnD,EAAA,CAAAM,SAAA,GAAS;IAATN,EAAA,CAAAiD,iBAAA,gBAAS;IACpCjD,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAyG,QAAA,CAAc;;;;;;IAc7C9H,EADF,CAAAC,cAAA,eAA6E,iBACkB;IAAzDD,EAAA,CAAAiB,UAAA,mBAAA2H,oFAAA;MAAA5I,EAAA,CAAAmB,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAAwH,wBAAA,GAAA9I,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA0H,wBAAA,CAAAD,wBAAA,CAA8C;IAAA,EAAC;IAC1F9I,EAAA,CAAAE,MAAA,yCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,UAAK,kBACwE;IAA7BD,EAAA,CAAAiB,UAAA,mBAAA+H,oFAAA;MAAAhJ,EAAA,CAAAmB,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA4H,gBAAA,EAAkB;IAAA,EAAC;IACxEjJ,EAAA,CAAAE,MAAA,6CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAsE;IAA7BD,EAAA,CAAAiB,UAAA,mBAAAiI,oFAAA;MAAAlJ,EAAA,CAAAmB,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA8H,gBAAA,EAAkB;IAAA,EAAC;IACnEnJ,EAAA,CAAAE,MAAA,6CACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAGNH,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAyG,SAAA,aAAgC;IAChCzG,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,iNAC1B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAgDIH,EAAA,CAAAC,cAAA,kBAAmG;IAAjCD,EAAA,CAAAiB,UAAA,mBAAAmI,8FAAA;MAAApJ,EAAA,CAAAmB,aAAA,CAAAkI,IAAA;MAAA,MAAAC,KAAA,GAAAtJ,EAAA,CAAAsB,aAAA,GAAAiI,KAAA;MAAA,MAAAlI,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAmI,mBAAA,CAAAF,KAAA,CAAsB;IAAA,EAAC;IAChGtJ,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IACTH,EAAA,CAAAC,cAAA,gBAAsD;IACpDD,EAAA,CAAAyG,SAAA,aAA2B;IAC7BzG,EAAA,CAAAG,YAAA,EAAO;;;;;;IAnCPH,EAFJ,CAAAC,cAAA,SAAuD,SACjD,iBAI0G;IAHjFD,EAAA,CAAAkE,gBAAA,2BAAAuF,2FAAArF,MAAA;MAAA,MAAAsF,QAAA,GAAA1J,EAAA,CAAAmB,aAAA,CAAAwI,IAAA,EAAA7H,SAAA;MAAA9B,EAAA,CAAAsE,kBAAA,CAAAoF,QAAA,CAAAE,SAAA,EAAAxF,MAAA,MAAAsF,QAAA,CAAAE,SAAA,GAAAxF,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA4B;IAIzDpE,EAJE,CAAAG,YAAA,EAG4G,EACzG;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAEsG;IAD3ED,EAAA,CAAAkE,gBAAA,2BAAA2F,2FAAAzF,MAAA;MAAA,MAAAsF,QAAA,GAAA1J,EAAA,CAAAmB,aAAA,CAAAwI,IAAA,EAAA7H,SAAA;MAAA9B,EAAA,CAAAsE,kBAAA,CAAAoF,QAAA,CAAAI,UAAA,EAAA1F,MAAA,MAAAsF,QAAA,CAAAI,UAAA,GAAA1F,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA6B;IAACpE,EAAA,CAAAiB,UAAA,2BAAA4I,2FAAA;MAAA7J,EAAA,CAAAmB,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAAiBJ,OAAA,CAAA0I,cAAA,EAAgB;IAAA,EAAC;IAE/F/J,EAFE,CAAAG,YAAA,EACwG,EACrG;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAI0G;IAHjFD,EAAA,CAAAkE,gBAAA,2BAAA8F,2FAAA5F,MAAA;MAAA,MAAAsF,QAAA,GAAA1J,EAAA,CAAAmB,aAAA,CAAAwI,IAAA,EAAA7H,SAAA;MAAA9B,EAAA,CAAAsE,kBAAA,CAAAoF,QAAA,CAAAO,KAAA,EAAA7F,MAAA,MAAAsF,QAAA,CAAAO,KAAA,GAAA7F,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAwB;IAIrDpE,EAJE,CAAAG,YAAA,EAG4G,EACzG;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAE+E;IADpDD,EAAA,CAAAkE,gBAAA,2BAAAgG,2FAAA9F,MAAA;MAAA,MAAAsF,QAAA,GAAA1J,EAAA,CAAAmB,aAAA,CAAAwI,IAAA,EAAA7H,SAAA;MAAA9B,EAAA,CAAAsE,kBAAA,CAAAoF,QAAA,CAAAS,MAAA,EAAA/F,MAAA,MAAAsF,QAAA,CAAAS,MAAA,GAAA/F,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAyB;IAACpE,EAAA,CAAAiB,UAAA,2BAAAiJ,2FAAA;MAAAlK,EAAA,CAAAmB,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAAiBJ,OAAA,CAAA0I,cAAA,EAAgB;IAAA,EAAC;IAE3F/J,EAFE,CAAAG,YAAA,EACiF,EAC9E;IACLH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,UAAI,iBAGyF;IACzFD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IACLH,EAAA,CAAAC,cAAA,UAAI;IAIFD,EAHA,CAAAiC,UAAA,KAAAmI,qEAAA,sBAAmG,KAAAC,mEAAA,oBAG7C;IAI1DrK,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAlCCH,EAAA,CAAAM,SAAA,GAAyG;IAAzGN,EAAA,CAAAsK,WAAA,cAAAjJ,OAAA,CAAAkJ,mBAAA,IAAAb,QAAA,CAAA3J,kBAAA,UAAA2J,QAAA,CAAA3J,kBAAA,OAAyG;IAHhFC,EAAA,CAAA0E,gBAAA,YAAAgF,QAAA,CAAAE,SAAA,CAA4B;IACrD5J,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAkJ,mBAAA,IAAAb,QAAA,CAAA3J,kBAAA,UAAA2J,QAAA,CAAA3J,kBAAA,OAAmG;IAKxEC,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAA0E,gBAAA,YAAAgF,QAAA,CAAAI,UAAA,CAA6B;IACrB9J,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAkJ,mBAAA,IAAAb,QAAA,CAAA3J,kBAAA,OAAkE;IAMrGC,EAAA,CAAAM,SAAA,GAAyG;IAAzGN,EAAA,CAAAsK,WAAA,cAAAjJ,OAAA,CAAAkJ,mBAAA,IAAAb,QAAA,CAAA3J,kBAAA,UAAA2J,QAAA,CAAA3J,kBAAA,OAAyG;IAHhFC,EAAA,CAAA0E,gBAAA,YAAAgF,QAAA,CAAAO,KAAA,CAAwB;IACjDjK,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAkJ,mBAAA,IAAAb,QAAA,CAAA3J,kBAAA,UAAA2J,QAAA,CAAA3J,kBAAA,OAAmG;IAKxEC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA0E,gBAAA,YAAAgF,QAAA,CAAAS,MAAA,CAAyB;IACxCnK,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAkJ,mBAAA,IAAAb,QAAA,CAAA3J,kBAAA,OAAkE;IAGhFC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAc,OAAA,CAAAmJ,cAAA,CAAAd,QAAA,CAAAI,UAAA,GAAAJ,QAAA,CAAAS,MAAA,OACF;IAEsBnK,EAAA,CAAAM,SAAA,GAAqD;IAEvEN,EAFkB,CAAAsK,WAAA,kBAAAZ,QAAA,CAAA3J,kBAAA,OAAqD,eAAA2J,QAAA,CAAA3J,kBAAA,OACrB,oBAAA2J,QAAA,CAAA3J,kBAAA,UAAA2J,QAAA,CAAA3J,kBAAA,OACsC;IACxFC,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAc,OAAA,CAAAoJ,oBAAA,CAAAf,QAAA,CAAA3J,kBAAA,OACF;IAGSC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAkJ,mBAAA,CAAyB;IAG3BvK,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAiB,OAAA,CAAAkJ,mBAAA,CAA0B;;;;;IAMnCvK,EADF,CAAAC,cAAA,SAAwC,cACc;IAClDD,EAAA,CAAAE,MAAA,iLACF;IACFF,EADE,CAAAG,YAAA,EAAK,EACF;;;;;;IAuDTH,EAAA,CAAAC,cAAA,kBACiB;IADgED,EAAA,CAAAiB,UAAA,mBAAAyJ,wFAAA;MAAA1K,EAAA,CAAAmB,aAAA,CAAAwJ,IAAA;MAAA,MAAAtJ,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuJ,kBAAA,EAAoB;IAAA,EAAC;IAE7G5K,EAAA,CAAAyG,SAAA,aAAgC;IAACzG,EAAA,CAAAE,MAAA,6CACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IASTH,EAAA,CAAAC,cAAA,kBAC2C;IADqBD,EAAA,CAAAiB,UAAA,mBAAA4J,wFAAA;MAAA7K,EAAA,CAAAmB,aAAA,CAAA2J,IAAA;MAAA,MAAAC,OAAA,GAAA/K,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA2J,aAAA,CAAAD,OAAA,CAAkB;IAAA,EAAC;IAE1F/K,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAA4J,cAAA,CAAAC,MAAA,OAAwC;;;;;;IAG1ClL,EAAA,CAAAC,cAAA,kBAC2C;IADqBD,EAAA,CAAAiB,UAAA,mBAAAkK,wFAAA;MAAAnL,EAAA,CAAAmB,aAAA,CAAAiK,IAAA;MAAA,MAAAL,OAAA,GAAA/K,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAgK,aAAA,CAAAN,OAAA,CAAkB;IAAA,EAAC;IAE1F/K,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAA4J,cAAA,CAAAC,MAAA,OAAwC;;;;;;IA3J9ClL,EADF,CAAAC,cAAA,mBAAgD,qBAC9B;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,mBAAc;IAiBZD,EAfA,CAAAiC,UAAA,IAAAqJ,2DAAA,mBAA6E,IAAAC,2DAAA,mBAeV;IAS3DvL,EAJR,CAAAC,cAAA,eAA8B,iBACQ,YAC3B,SACD,eACc;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAAe;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,eAAe;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEtBF,EAFsB,CAAAG,YAAA,EAAK,EACpB,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IAyCLD,EAxCA,CAAAiC,UAAA,KAAAuJ,2DAAA,mBAAuD,KAAAC,2DAAA,kBAwCf;IAO9CzL,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAQEH,EALR,CAAAC,cAAA,gBAAkB,gBACqB,gBACR,gBAE2C,iBACjC;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,iBAAwC;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAC3EF,EAD2E,CAAAG,YAAA,EAAO,EAC5E;IAKFH,EAFJ,CAAAC,cAAA,gBAAqG,gBAC5D,gBACF;IACjCD,EAAA,CAAAyG,SAAA,cAAwC;IAC1CzG,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,WAAK,iBAC+B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,iBAA2D;IAAAD,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpEH,EAAA,CAAAC,cAAA,gBAAmC;IACjCD,EAAA,CAAAyG,SAAA,cAAuC;IACvCzG,EAAA,CAAAE,MAAA,4DACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAEJH,EADF,CAAAC,cAAA,gBAAsB,gBAC8B;IAAAD,EAAA,CAAAE,MAAA,IAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjGH,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAEtCF,EAFsC,CAAAG,YAAA,EAAM,EACpC,EACF;IAGNH,EAAA,CAAAyG,SAAA,eAAiB;IAIfzG,EADF,CAAAC,cAAA,gBAA+D,iBACrB;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,iBAA2C;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAK3FF,EAL2F,CAAAG,YAAA,EAAO,EACpF,EACF,EACF,EACF,EACO;IAGXH,EAFJ,CAAAC,cAAA,2BAAuD,WAChD,mBAEsD;IADRD,EAAA,CAAAiB,UAAA,mBAAAyK,+EAAA;MAAA1L,EAAA,CAAAmB,aAAA,CAAAwK,IAAA;MAAA,MAAAtK,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuK,cAAA,EAAgB;IAAA,EAAC;IAEzE5L,EAAA,CAAAyG,SAAA,cAAiC;IAACzG,EAAA,CAAAE,MAAA,wCACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAiC,UAAA,KAAA4J,+DAAA,sBACiB;IAOnB7L,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,WAAK,mBACkE;IAAvBD,EAAA,CAAAiB,UAAA,mBAAA6K,+EAAA;MAAA,MAAAf,OAAA,GAAA/K,EAAA,CAAAmB,aAAA,CAAAwK,IAAA,EAAApE,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuG,OAAA,CAAAmD,OAAA,CAAY;IAAA,EAAC;IAAC/K,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMhFH,EAJA,CAAAiC,UAAA,KAAA8J,+DAAA,sBAC2C,KAAAC,+DAAA,sBAIA;IAKjDhM,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;;IA/JNH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAoD,kBAAA,2BAAA/B,OAAA,CAAA4K,YAAA,kBAAA5K,OAAA,CAAA4K,YAAA,CAAA/I,UAAA,QAAA7B,OAAA,CAAA4K,YAAA,kBAAA5K,OAAA,CAAA4K,YAAA,CAAA9I,MAAA,aACF;IAGQnD,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAkJ,mBAAA,CAAyB;IAezBvK,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAiB,OAAA,CAAAkJ,mBAAA,CAA0B;IAmBLvK,EAAA,CAAAM,SAAA,IAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAA4J,cAAA,CAAmB;IAwCnCjL,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA4J,cAAA,CAAAC,MAAA,OAAiC;IAgBIlL,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAiD,iBAAA,CAAA5B,OAAA,CAAAmJ,cAAA,CAAAnJ,OAAA,CAAA6K,WAAA,EAAiC;IAmBrBlM,EAAA,CAAAM,SAAA,IAAyC;IAAzCN,EAAA,CAAAiD,iBAAA,CAAA5B,OAAA,CAAAmJ,cAAA,CAAAnJ,OAAA,CAAA8K,mBAAA,EAAyC;IAWlDnM,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAAiD,iBAAA,CAAA5B,OAAA,CAAAmJ,cAAA,CAAAnJ,OAAA,CAAA+K,gBAAA,EAAsC;IASrFpM,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAA4J,cAAA,CAAAC,MAAA,OAAwC;IAIjClL,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAiB,OAAA,CAAAkJ,mBAAA,CAA0B;IAY1BvK,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAkJ,mBAAA,CAAyB;IAIzBvK,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAkJ,mBAAA,CAAyB;;;;;;IAsC5BvK,EADF,CAAAC,cAAA,eAA8D,kBAC4B;IAAhCD,EAAA,CAAAiB,UAAA,mBAAAoL,qFAAA;MAAArM,EAAA,CAAAmB,aAAA,CAAAmL,IAAA;MAAA,MAAAjL,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAkL,mBAAA,EAAqB;IAAA,EAAC;IACrFvM,EAAA,CAAAyG,SAAA,aAA4B;IAEhCzG,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAmBJH,EAAA,CAAAyG,SAAA,aAC4E;;;;;;IAdhFzG,EAAA,CAAAC,cAAA,eAE8C;IAA5CD,EAAA,CAAAiB,UAAA,mBAAAuL,kFAAA;MAAA,MAAAC,YAAA,GAAAzM,EAAA,CAAAmB,aAAA,CAAAuL,IAAA,EAAA5K,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAsL,uBAAA,CAAAF,YAAA,CAAiC;IAAA,EAAC;IAEzCzM,EADF,CAAAC,cAAA,eAA2B,eACE;IACzBD,EAAA,CAAAyG,SAAA,aAAoC;IACpCzG,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAmD;IACjDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAiC,UAAA,IAAA2K,gEAAA,iBACwE;IAE5E5M,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAfJH,EAAA,CAAAsK,WAAA,cAAAjJ,OAAA,CAAAwL,yBAAA,kBAAAxL,OAAA,CAAAwL,yBAAA,CAAAC,UAAA,MAAAL,YAAA,CAAAK,UAAA,CAAgF;IAK5E9M,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAkM,YAAA,CAAAM,YAAA,MACF;IAEE/M,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAkM,YAAA,CAAAO,WAAA,8BACF;IAIGhN,EAAA,CAAAM,SAAA,GAAmE;IAAnEN,EAAA,CAAAI,UAAA,UAAAiB,OAAA,CAAAwL,yBAAA,kBAAAxL,OAAA,CAAAwL,yBAAA,CAAAC,UAAA,MAAAL,YAAA,CAAAK,UAAA,CAAmE;;;;;IAK1E9M,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAyG,SAAA,aAAmD;IACnDzG,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;;;;IAFFH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAc,OAAA,CAAA4L,qBAAA,iGACF;;;;;;IAQEjN,EAHN,CAAAC,cAAA,eAAiG,eAChC,eAChC,iBACD;IACxBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;IAEJH,EADF,CAAAC,cAAA,eAAiC,kBAEU;IADaD,EAAA,CAAAiB,UAAA,mBAAAiM,qFAAA;MAAAlN,EAAA,CAAAmB,aAAA,CAAAgM,IAAA;MAAA,MAAA9L,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA+L,oBAAA,EAAsB;IAAA,EAAC;IAEpFpN,EAAA,CAAAyG,SAAA,aAAmC;IAACzG,EAAA,CAAAE,MAAA,2BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,gBAAsC;IACpCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,mBAC+D;IADTD,EAAA,CAAAiB,UAAA,mBAAAoM,sFAAA;MAAArN,EAAA,CAAAmB,aAAA,CAAAgM,IAAA;MAAA,MAAA9L,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAiM,gBAAA,EAAkB;IAAA,EAAC;IAEhFtN,EAAA,CAAAE,MAAA,4BAAI;IAAAF,EAAA,CAAAyG,SAAA,cAAoC;IAIhDzG,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAjBEH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAwD,kBAAA,mBAAAnC,OAAA,CAAAkM,qBAAA,eAAAlM,OAAA,CAAAmM,mBAAA,4BAAAnM,OAAA,CAAAoM,kBAAA,yBACF;IAIEzN,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAAqM,mBAAA,OAAsC;IAItC1N,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAoD,kBAAA,aAAA/B,OAAA,CAAAqM,mBAAA,SAAArM,OAAA,CAAAsM,qBAAA,eACF;IAEE3N,EAAA,CAAAM,SAAA,EAA4D;IAA5DN,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAAqM,mBAAA,KAAArM,OAAA,CAAAsM,qBAAA,GAA4D;;;;;IA2B5D3N,EAAA,CAAAC,cAAA,gBAAkD;IAChDD,EAAA,CAAAyG,SAAA,aAAuC;IAAAzG,EAAA,CAAAE,MAAA,GACzC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADkCH,EAAA,CAAAM,SAAA,GACzC;IADyCN,EAAA,CAAAO,kBAAA,KAAAqN,UAAA,CAAAC,UAAA,MACzC;;;;;;IAVF7N,EAFJ,CAAAC,cAAA,eAAkF,eACnD,uBACwD;IAAtED,EAAA,CAAAkE,gBAAA,2BAAA4J,wGAAA1J,MAAA;MAAA,MAAAwJ,UAAA,GAAA5N,EAAA,CAAAmB,aAAA,CAAA4M,IAAA,EAAAjM,SAAA;MAAA9B,EAAA,CAAAsE,kBAAA,CAAAsJ,UAAA,CAAAI,QAAA,EAAA5J,MAAA,MAAAwJ,UAAA,CAAAI,QAAA,GAAA5J,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA6B;IAACpE,EAAA,CAAAiB,UAAA,2BAAA6M,wGAAA;MAAA9N,EAAA,CAAAmB,aAAA,CAAA4M,IAAA;MAAA,MAAA1M,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAAiBJ,OAAA,CAAA4M,mBAAA,EAAqB;IAAA,EAAC;IAEpFjO,EADE,CAAAG,YAAA,EAAc,EACV;IAGFH,EAFJ,CAAAC,cAAA,eAA4B,eACD,aACf;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACnCF,EADmC,CAAAG,YAAA,EAAS,EACtC;IACNH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAiC,UAAA,IAAAiM,yEAAA,oBAAkD;IAGlDlO,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAyG,SAAA,cAAmC;IAAAzG,EAAA,CAAAE,MAAA,IACrC;IAGNF,EAHM,CAAAG,YAAA,EAAO,EACH,EACF,EACF;;;;IAhBWH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAA0E,gBAAA,YAAAkJ,UAAA,CAAAI,QAAA,CAA6B;IAKhChO,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAiD,iBAAA,CAAA2K,UAAA,CAAAO,YAAA,CAAyB;IAGRnO,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAI,UAAA,SAAAwN,UAAA,CAAAC,UAAA,CAAuB;IAIX7N,EAAA,CAAAM,SAAA,GACrC;IADqCN,EAAA,CAAAO,kBAAA,SAAAqN,UAAA,CAAAQ,UAAA,MACrC;;;;;IAMNpO,EAAA,CAAAC,cAAA,eAAkF;IAChFD,EAAA,CAAAyG,SAAA,aAAkD;IAClDzG,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IACpCF,EADoC,CAAAG,YAAA,EAAI,EAClC;;;;;;IAKNH,EADF,CAAAC,cAAA,eAAqE,kBACoB;IAAnCD,EAAA,CAAAiB,UAAA,mBAAAoN,2FAAA;MAAArO,EAAA,CAAAmB,aAAA,CAAAmN,IAAA;MAAA,MAAAjN,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAkN,sBAAA,EAAwB;IAAA,EAAC;IACpFvO,EAAA,CAAAyG,SAAA,aAAwC;IAAAzG,EAAA,CAAAE,MAAA,oBAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsF;IAArCD,EAAA,CAAAiB,UAAA,mBAAAuN,2FAAA;MAAAxO,EAAA,CAAAmB,aAAA,CAAAmN,IAAA;MAAA,MAAAjN,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAoN,wBAAA,EAA0B;IAAA,EAAC;IACnFzO,EAAA,CAAAyG,SAAA,aAAkC;IAAAzG,EAAA,CAAAE,MAAA,gCACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,gBAA8B;IAC5BD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAoD,kBAAA,yBAAA/B,OAAA,CAAAqN,6BAAA,WAAArN,OAAA,CAAAsN,kBAAA,CAAAzD,MAAA,aACF;;;;;IA5CFlL,EADF,CAAAC,cAAA,eAAwE,cACvC;IAC7BD,EAAA,CAAAyG,SAAA,aAAmC;IAAAzG,EAAA,CAAAE,MAAA,gCACnC;IAAAF,EAAA,CAAAC,cAAA,gBAAyC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAC5EF,EAD4E,CAAAG,YAAA,EAAO,EAC9E;IAGLH,EAAA,CAAAC,cAAA,eAAuE;IAsBrED,EArBA,CAAAiC,UAAA,IAAA2M,kEAAA,oBAAkF,IAAAC,kEAAA,mBAqBA;IAIpF7O,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAiC,UAAA,IAAA6M,kEAAA,mBAAqE;IAWvE9O,EAAA,CAAAG,YAAA,EAAM;;;;IA5CuCH,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAO,kBAAA,KAAAc,OAAA,CAAAsN,kBAAA,CAAAzD,MAAA,YAAiC;IAK9BlL,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAsN,kBAAA,CAAuB;IAqBxB3O,EAAA,CAAAM,SAAA,EAAqC;IAArCN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAsN,kBAAA,CAAAzD,MAAA,OAAqC;IAOlDlL,EAAA,CAAAM,SAAA,EAAmC;IAAnCN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAsN,kBAAA,CAAAzD,MAAA,KAAmC;;;;;IAenElL,EADF,CAAAC,cAAA,eAA2D,iBAC/B;IACxBD,EAAA,CAAAyG,SAAA,aAAuC;IACvCzG,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;;;;IAFFH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,yBAAAc,OAAA,CAAAqN,6BAAA,mDACF;;;;;;IA5IA1O,EAHN,CAAAC,cAAA,mBAA+C,qBAC7B,eACiD,cAC5C;IACfD,EAAA,CAAAyG,SAAA,aAAiD;IAAAzG,EAAA,CAAAE,MAAA,kDACnD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAEzCF,EAFyC,CAAAG,YAAA,EAAO,EACxC,EACS;IAIbH,EAHJ,CAAAC,cAAA,mBAAc,eAEiC,eACZ;IAC7BD,EAAA,CAAAyG,SAAA,cAAgC;IAAAzG,EAAA,CAAAE,MAAA,iCAClC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAMCH,EAHN,CAAAC,cAAA,gBAAmC,gBACR,gBACU,iBACA;IAC7BD,EAAA,CAAAyG,SAAA,aAA6B;IAEjCzG,EADE,CAAAG,YAAA,EAAO,EACH;IACNH,EAAA,CAAAC,cAAA,kBAC8B;IADkCD,EAAA,CAAAkE,gBAAA,2BAAA6K,sFAAA3K,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAA6N,IAAA;MAAA,MAAA3N,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAA4L,qBAAA,EAAA7I,MAAA,MAAA/C,OAAA,CAAA4L,qBAAA,GAAA7I,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAmC;IACjGpE,EAAA,CAAAiB,UAAA,mBAAAgO,8EAAA;MAAAjP,EAAA,CAAAmB,aAAA,CAAA6N,IAAA;MAAA,MAAA3N,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA6N,eAAA,EAAiB;IAAA,EAAC;IAD7BlP,EAAA,CAAAG,YAAA,EAC8B;IAC9BH,EAAA,CAAAiC,UAAA,KAAAkN,4DAAA,mBAA8D;IAMlEnP,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,gBAA2B;IAoBzBD,EAnBA,CAAAiC,UAAA,KAAAmN,4DAAA,mBAE8C,KAAAC,4DAAA,mBAiB8B;IAM9ErP,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAiC,UAAA,KAAAqN,4DAAA,oBAAiG;IAsBnGtP,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAiC,UAAA,KAAAsN,4DAAA,oBAAwE;IAgD1EvP,EAAA,CAAAG,YAAA,EAAe;IACfH,EAAA,CAAAC,cAAA,2BAAuD;IACrDD,EAAA,CAAAiC,UAAA,KAAAuN,4DAAA,mBAA2D;IAOzDxP,EADF,CAAAC,cAAA,gBAAqB,mBACmD;IAAvBD,EAAA,CAAAiB,UAAA,mBAAAwO,+EAAA;MAAA,MAAAC,OAAA,GAAA1P,EAAA,CAAAmB,aAAA,CAAA6N,IAAA,EAAAzH,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuG,OAAA,CAAA8H,OAAA,CAAY;IAAA,EAAC;IAAC1P,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACjFH,EAAA,CAAAC,cAAA,mBACmF;IADnDD,EAAA,CAAAiB,UAAA,mBAAA0O,+EAAA;MAAA,MAAAD,OAAA,GAAA1P,EAAA,CAAAmB,aAAA,CAAA6N,IAAA,EAAAzH,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuO,2BAAA,CAAAF,OAAA,CAAgC;IAAA,EAAC;IAExE1P,EAAA,CAAAyG,SAAA,cAAoC;IACpCzG,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;;;;IAlIgEH,EAAA,CAAAM,SAAA,IAAmC;IAAnCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAA4L,qBAAA,CAAmC;IAElEjN,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA4L,qBAAA,CAA2B;IAUdjN,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAwO,YAAA,CAAe;IAmBpB7P,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAwO,YAAA,CAAA3E,MAAA,OAA+B;IASpClL,EAAA,CAAAM,SAAA,EAAuD;IAAvDN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAoM,kBAAA,QAAApM,OAAA,CAAAwO,YAAA,CAAA3E,MAAA,KAAuD;IAyB1DlL,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAwL,yBAAA,CAA+B;IAkD5C7M,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAwL,yBAAA,CAA+B;IASrD7M,EAAA,CAAAM,SAAA,GAAgF;IAAhFN,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAwL,yBAAA,IAAAxL,OAAA,CAAAqN,6BAAA,SAAgF;IAEhF1O,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,4CAAAc,OAAA,CAAAqN,6BAAA,SACF;;;ADrpBR,OAAM,MAAOoB,4BAA6B,SAAQ5Q,aAAa;EAE7D6Q,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,qBAA2C,EAC3CC,iBAAmC,EACnCC,OAAsB,EACtBC,MAAc,EACdC,aAA2B,EAC3BC,gBAAgC,EAChCC,gBAAkC,EAClCC,eAAgC;IAExC,KAAK,CAACb,MAAM,CAAC;IAfL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IAfzB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA2BnB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZ3Q,KAAK,EAAE;KACR,EACD;MACE0Q,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClB3Q,KAAK,EAAE;KACR,CACF;IAED,KAAA4Q,gBAAgB,GAAG,CACjB;MACEF,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,KAAK;MACV3Q,KAAK,EAAE;KACR,EACD;MACE0Q,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,QAAQ;MACb3Q,KAAK,EAAE;KACR,EACD;MACE0Q,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,YAAY;MACjB3Q,KAAK,EAAE;KACR,CACF;IAKD,KAAA6Q,gBAAgB,GAAU,CAAC;MAAE7Q,KAAK,EAAE,IAAI;MAAE0Q,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAI,gBAAgB,GAAU,CAAC;MAAE9Q,KAAK,EAAE,IAAI;MAAE0Q,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAlM,eAAe,GAAU,CAAC;MAAExE,KAAK,EAAE,IAAI;MAAE0Q,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACrD,KAAArK,gBAAgB,GAAU,CAAC;MAAErG,KAAK,EAAE,IAAI;MAAE0Q,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAxM,gBAAgB,GAAU,CAAC;MAAElE,KAAK,EAAE,IAAI;MAAE0Q,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAK,iBAAiB,GAAU,CAAC;MAAE/Q,KAAK,EAAE,IAAI;MAAE0Q,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACvD,KAAAM,sBAAsB,GAAU,CAAC;MAAEhR,KAAK,EAAE,IAAI;MAAE0Q,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IAE5D,KAAAzM,OAAO,GAAG;MACRO,eAAe,EAAE,IAAI,CAAC+K,UAAU,CAAC0B,cAAc,CAACnS,iBAAiB,CAAC;MAClEoF,gBAAgB,EAAE,IAAI,CAACqL,UAAU,CAAC0B,cAAc,CAACjS,aAAa,CAAC;MAC/DqH,gBAAgB,EAAE,IAAI,CAACkJ,UAAU,CAAC0B,cAAc,CAAClS,aAAa,CAAC;MAC/DiS,sBAAsB,EAAE,IAAI,CAACzB,UAAU,CAAC0B,cAAc,CAAC/R,mBAAmB;KAC3E;IAGD,KAAAgS,UAAU,GAAG;MACXC,QAAQ,EAAE,CAAC;MACX9L,KAAK,EAAE,EAAE;MACTzC,SAAS,EAAE,KAAK;MAChBG,UAAU,EAAE,CAAC;MACbI,SAAS,EAAE,KAAK;MAChB8B,aAAa,EAAE,EAAE;MACjBmM,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACb1O,UAAU,EAAE,CAAC;MACbH,UAAU,EAAE,EAAE;MACd+C,MAAM,EAAE;KACT;IACD;IACA,KAAAgF,cAAc,GAAoB,EAAE;IACpC,KAAAiB,WAAW,GAAW,CAAC;IACvB;IACA,KAAA8F,iBAAiB,GAAW,KAAK,CAAC,CAAE;IACpC,KAAAC,uBAAuB,GAAW,CAAC,CAAC,CAAG;IACvC,KAAA9F,mBAAmB,GAAW,CAAC,CAAC,CAAO;IACvC,KAAAC,gBAAgB,GAAW,CAAC,CAAC,CAAU;IACvC,KAAA8F,mBAAmB,GAAY,IAAI,CAAC,CAAG;IACvC,KAAAjG,YAAY,GAAQ,IAAI;IACxB,KAAAkG,kBAAkB,GAAW,CAAC;IAC9B,KAAA5H,mBAAmB,GAAY,IAAI,CAAC,CAAC;IAErC;IACA,KAAAsF,YAAY,GAAU,EAAE;IACxB,KAAAlB,kBAAkB,GAAU,EAAE;IAC9B,KAAA9B,yBAAyB,GAAQ,IAAI;IACrC,KAAAI,qBAAqB,GAAW,EAAE;IAElC;IACA,KAAAS,mBAAmB,GAAW,CAAC;IAC/B,KAAA0E,gBAAgB,GAAW,CAAC,CAAC,CAAC;IAC9B,KAAAC,qBAAqB,GAAU,EAAE;IACjC,KAAA5E,kBAAkB,GAAW,CAAC,CAAC,CAAC;IAuHhC,KAAA6E,YAAY,GAAgB,IAAI;IAuKhC,KAAAC,uBAAuB,GAAU,CAC/B;MACEnB,KAAK,EAAE,EAAE;MAAE1Q,KAAK,EAAE;KACnB,CACF;IAtYC,IAAI,CAACgQ,aAAa,CAAC8B,OAAO,EAAE,CAACC,IAAI,CAC/BrT,GAAG,CAAEsT,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAAC9B,eAAe,GAAG4B,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EA+FSC,QAAQA,CAAA;IACf,IAAI,CAAC5N,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAG,IAAI,CAAC+K,UAAU,CAAC0B,cAAc,CAACnS,iBAAiB,CAAC,CACrD;IACD,IAAI,CAACuH,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAACkJ,UAAU,CAAC0B,cAAc,CAAClS,aAAa,CAAC,CACjD;IACD,IAAI,CAACmF,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAACqL,UAAU,CAAC0B,cAAc,CAACjS,aAAa,CAAC,CACjD;IACD,IAAI,CAAC+R,iBAAiB,GAAG,CACvB,GAAG,IAAI,CAACA,iBAAiB,EACzB,GAAG,IAAI,CAACxB,UAAU,CAAC0B,cAAc,CAAChS,cAAc,CAAC,CAClD;IACD,IAAI,CAAC+R,sBAAsB,GAAG,CAC5B,GAAG,IAAI,CAACA,sBAAsB,EAC9B,GAAG,IAAI,CAACzB,UAAU,CAAC0B,cAAc,CAAC/R,mBAAmB,CAAC,CACvD;IAED,IAAIC,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,IAAI,IAAI,IACtEnT,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,IAAIC,SAAS,IAC5EpT,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,IAAI,EAAE,EAAE;MAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACvT,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,CAAC;MACjG,IAAI,CAAC1Q,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACA;QACA;QACA8Q,kBAAkB,EAAEH,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAIJ,SAAS,GAC7G,IAAI,CAACzB,gBAAgB,CAAC8B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnC,KAAK,IAAI8B,eAAe,CAACG,kBAAkB,CAACjC,KAAK,CAAC,GACpF,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC;QAC5BrL,kBAAkB,EAAE+M,eAAe,CAAC/M,kBAAkB,IAAI,IAAI,IAAI+M,eAAe,CAAC/M,kBAAkB,IAAI8M,SAAS,GAC7G,IAAI,CAAClM,gBAAgB,CAACuM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnC,KAAK,IAAI8B,eAAe,CAAC/M,kBAAkB,CAACiL,KAAK,CAAC,GACpF,IAAI,CAACrK,gBAAgB,CAAC,CAAC,CAAC;QAC5BvC,kBAAkB,EAAE0O,eAAe,CAAC1O,kBAAkB,IAAI,IAAI,IAAI0O,eAAe,CAAC1O,kBAAkB,IAAIyO,SAAS,GAC7G,IAAI,CAACrO,gBAAgB,CAAC0O,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnC,KAAK,IAAI8B,eAAe,CAAC1O,kBAAkB,CAAC4M,KAAK,CAAC,GACpF,IAAI,CAACxM,gBAAgB,CAAC,CAAC,CAAC;QAC5BI,iBAAiB,EAAEkO,eAAe,CAAClO,iBAAiB,IAAI,IAAI,IAAIkO,eAAe,CAAClO,iBAAiB,IAAIiO,SAAS,GAC1G,IAAI,CAAC/N,eAAe,CAACoO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnC,KAAK,IAAI8B,eAAe,CAAClO,iBAAiB,CAACoM,KAAK,CAAC,GAClF,IAAI,CAAClM,eAAe,CAAC,CAAC,CAAC;QAC3BsO,mBAAmB,EAAEN,eAAe,CAACM,mBAAmB,IAAI,IAAI,IAAIN,eAAe,CAACM,mBAAmB,IAAIP,SAAS,GAChH,IAAI,CAACxB,iBAAiB,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnC,KAAK,IAAI8B,eAAe,CAACM,mBAAmB,CAACpC,KAAK,CAAC,GACtF,IAAI,CAACK,iBAAiB,CAAC,CAAC,CAAC;QAC7BgC,wBAAwB,EAAEP,eAAe,CAACO,wBAAwB,IAAI,IAAI,IAAIP,eAAe,CAACO,wBAAwB,IAAIR,SAAS,GAC/H,IAAI,CAACvB,sBAAsB,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnC,KAAK,IAAI8B,eAAe,CAACO,wBAAwB,CAACrC,KAAK,CAAC,GAChG,IAAI,CAACM,sBAAsB,CAAC,CAAC,CAAC;QAClCgC,gBAAgB,EAAER,eAAe,CAACQ,gBAAgB,IAAI,IAAI,IAAIR,eAAe,CAACQ,gBAAgB,IAAIT,SAAS,GACvG,IAAI,CAAC3B,gBAAgB,CAACgC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnC,KAAK,IAAI8B,eAAe,CAACQ,gBAAgB,CAACtC,KAAK,CAAC,GAClF,IAAI,CAACE,gBAAgB,CAAC,CAAC,CAAC;QAC5BqC,KAAK,EAAET,eAAe,CAACS,KAAK,IAAI,IAAI,IAAIT,eAAe,CAACS,KAAK,IAAIV,SAAS,GACtEC,eAAe,CAACS,KAAK,GACrB,EAAE;QACNC,GAAG,EAAEV,eAAe,CAACU,GAAG,IAAI,IAAI,IAAIV,eAAe,CAACU,GAAG,IAAIX,SAAS,GAChEC,eAAe,CAACU,GAAG,GACnB;OACL;IACH,CAAC,MACI;MACH,IAAI,CAACtR,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACA8Q,kBAAkB,EAAE,IAAI,CAAC7B,gBAAgB,CAAC,CAAC,CAAC;QAC5CrL,kBAAkB,EAAE,IAAI,CAACY,gBAAgB,CAAC,CAAC,CAAC;QAC5CvC,kBAAkB,EAAE,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC;QAC5CI,iBAAiB,EAAE,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;QAC1CsO,mBAAmB,EAAE,IAAI,CAAC/B,iBAAiB,CAAC,CAAC,CAAC;QAC9CgC,wBAAwB,EAAE,IAAI,CAAC/B,sBAAsB,CAAC,CAAC,CAAC;QACxDgC,gBAAgB,EAAE,IAAI,CAACpC,gBAAgB,CAAC,CAAC,CAAC;QAC1CqC,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE;OACN;IACH;IACA,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAC,QAAQA,CAAA;IACN,IAAIC,WAAW,GAAG;MAChBxR,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvD;MACAoR,KAAK,EAAE,IAAI,CAACrR,WAAW,CAACqR,KAAK;MAC7BC,GAAG,EAAE,IAAI,CAACtR,WAAW,CAACsR,GAAG;MACzBP,kBAAkB,EAAE,IAAI,CAAC/Q,WAAW,CAAC+Q,kBAAkB;MACvDlN,kBAAkB,EAAE,IAAI,CAAC7D,WAAW,CAAC6D,kBAAkB;MACvDuN,gBAAgB,EAAE,IAAI,CAACpR,WAAW,CAACoR,gBAAgB;MACnDlP,kBAAkB,EAAE,IAAI,CAAClC,WAAW,CAACkC,kBAAkB;MACvDQ,iBAAiB,EAAE,IAAI,CAAC1C,WAAW,CAAC0C,iBAAiB;MACrDwO,mBAAmB,EAAE,IAAI,CAAClR,WAAW,CAACkR,mBAAmB;MACzDC,wBAAwB,EAAE,IAAI,CAACnR,WAAW,CAACmR;KAC5C;IACD5T,mBAAmB,CAACmU,iBAAiB,CAAClU,WAAW,CAACkT,YAAY,EAAEG,IAAI,CAACc,SAAS,CAACF,WAAW,CAAC,CAAC;IAC5F,IAAI,CAACG,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EAEAsB,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACnD,SAAS,GAAGmD,OAAO;IACxB,IAAI,CAACF,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EAEAwB,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC/R,WAAW,CAACC,kBAAkB,CAACC,GAAG,EAAE;MAC3C,IAAI,CAAC6N,aAAa,CAACiE,4BAA4B,CAAC;QAC9CC,YAAY,EAAE,IAAI,CAACjS,WAAW,CAACC,kBAAkB,CAACC;OACnD,CAAC,CAACqQ,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;UACtC,IAAI,CAAC9D,gBAAgB,CAAC+D,iBAAiB,CACrChC,GAAG,CAAC8B,OAAO,EAAE,QAAQ,CACtB;QACH,CAAC,MAAM;UACL,IAAI,CAACrE,OAAO,CAACwE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAMAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEAC,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACnK,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACoH,YAAY,GAAG6C,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAACC,WAAW,EAAE;IACpB;EACF;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChD,YAAY,EAAE;MACrB,MAAMiD,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAACnD,YAAY,CAAC;MAC3C,IAAI,CAACjC,aAAa,CAACqF,4BAA4B,CAAC;QAC9CC,IAAI,EAAE;UACJpB,YAAY,EAAE,IAAI,CAACjS,WAAW,CAACC,kBAAkB,CAACC,GAAG;UACrDoT,KAAK,EAAE,IAAI,CAACtD;;OAEf,CAAC,CAACO,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACtE,OAAO,CAAC0F,aAAa,CAACnD,GAAG,CAACkC,OAAQ,CAAC;UACxC,IAAI,CAACV,YAAY,EAAE,CAACrB,SAAS,EAAE;QACjC,CAAC,MAAM;UACL,IAAI,CAAC1C,OAAO,CAACwE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAGAkB,gBAAgBA,CAAA;IACd,IAAI,CAACzF,aAAa,CAAC0F,iCAAiC,CAAC;MACnDJ,IAAI,EAAE;QAAEpB,YAAY,EAAE,IAAI,CAACjS,WAAW,CAACC,kBAAkB,CAACC;MAAG;KAC9D,CAAC,CAACqQ,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACjD,gBAAgB,GAAG,CAAC;UACvBJ,KAAK,EAAE,EAAE;UAAE1Q,KAAK,EAAE;SACnB,EAAE,GAAGgS,GAAG,CAAC8B,OAAO,CAACwB,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAE7E,KAAK,EAAE6E,CAAC;YAAEvV,KAAK,EAAEuV;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAIpW,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,IAAI,IAAI,IACtEnT,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,IAAIC,SAAS,IAC5EpT,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACvT,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAIJ,SAAS,EAAE;YACjG,IAAI1J,KAAK,GAAG,IAAI,CAACiI,gBAAgB,CAAC0E,SAAS,CAAE3C,CAAM,IAAKA,CAAC,CAACnC,KAAK,IAAI8B,eAAe,CAACG,kBAAkB,CAACjC,KAAK,CAAC;YAC5G,IAAI,CAAC9O,WAAW,CAAC+Q,kBAAkB,GAAG,IAAI,CAAC7B,gBAAgB,CAACjI,KAAK,CAAC;UACpE,CAAC,MAAM;YACL,IAAI,CAACjH,WAAW,CAAC+Q,kBAAkB,GAAG,IAAI,CAAC7B,gBAAgB,CAAC,CAAC,CAAC;UAChE;QACF;MACF;IACF,CAAC,CAAC;EACJ;EAKA2E,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,GAAG;MACjB7B,YAAY,EAAE,IAAI,CAACjS,WAAW,CAACC,kBAAkB,CAACC,GAAG;MACrD6T,SAAS,EAAE,IAAI,CAACpF,SAAS;MACzBqF,QAAQ,EAAE,IAAI,CAACtF;KAChB;IACD,IAAI,IAAI,CAAC1O,WAAW,CAACqR,KAAK,IAAI,IAAI,CAACrR,WAAW,CAACsR,GAAG,EAAE;MAClD,IAAI,CAACwC,WAAW,CAAC,QAAQ,CAAC,GAAG;QAAEzC,KAAK,EAAE,IAAI,CAACrR,WAAW,CAACqR,KAAK;QAAEC,GAAG,EAAE,IAAI,CAACtR,WAAW,CAACsR;MAAG,CAAE;IAC3F;IACA,IAAI,IAAI,CAACtR,WAAW,CAAC+Q,kBAAkB,EAAE;MACvC,IAAI,CAAC+C,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC9T,WAAW,CAAC+Q,kBAAkB,CAACjC,KAAK;IAC5E;IACA,IAAI,IAAI,CAAC9O,WAAW,CAAC6D,kBAAkB,CAACiL,KAAK,EAAE;MAC7C,IAAI,CAACgF,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC9T,WAAW,CAAC6D,kBAAkB,CAACiL,KAAK;IAC5E;IACA,IAAI,OAAO,IAAI,CAAC9O,WAAW,CAACoR,gBAAgB,CAACtC,KAAK,KAAK,SAAS,EAAE;MAChE,IAAI,CAACgF,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC9T,WAAW,CAACoR,gBAAgB,CAACtC,KAAK;IACzE;IACA,IAAI,IAAI,CAAC9O,WAAW,CAACkC,kBAAkB,CAAC4M,KAAK,EAAE;MAC7C,IAAI,CAACgF,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC9T,WAAW,CAACkC,kBAAkB,CAAC4M,KAAK;IAC5E;IACA,IAAI,IAAI,CAAC9O,WAAW,CAAC0C,iBAAiB,CAACoM,KAAK,EAAE;MAC5C,IAAI,CAACgF,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC9T,WAAW,CAAC0C,iBAAiB,CAACoM,KAAK;IAC1E;IACA,IAAI,IAAI,CAAC9O,WAAW,CAACkR,mBAAmB,CAACpC,KAAK,EAAE;MAC9C,IAAI,CAACgF,WAAW,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC9T,WAAW,CAACkR,mBAAmB,CAACpC,KAAK;IAC9E;IACA,IAAI,IAAI,CAAC9O,WAAW,CAACmR,wBAAwB,CAACrC,KAAK,EAAE;MACnD,IAAI,CAACgF,WAAW,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC9T,WAAW,CAACmR,wBAAwB,CAACrC,KAAK;IACxF;IAEA,OAAO,IAAI,CAACgF,WAAW;EACzB;EAEAG,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACxT,MAAM,IAAI,CAAC,KAAKuT,CAAC,CAACvT,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA+Q,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC7D,aAAa,CAACuG,6BAA6B,CAAC;MACtDjB,IAAI,EAAE,IAAI,CAACQ,WAAW;KACvB,CAAC,CAAC1D,IAAI,CACLrT,GAAG,CAACsT,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACoC,SAAS,GAAGnE,GAAG,CAAC8B,OAAO;QAC5B,IAAI,CAACtD,YAAY,GAAGwB,GAAG,CAACoE,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAIAC,0BAA0BA,CAAA;IACxB;IACA,IAAI,CAACjB,gBAAgB,EAAE;IACvB,IAAI,CAAC5B,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EACAgB,gBAAgBA,CAAA;IACd,IAAI,CAACtD,iBAAiB,CAACyG,6CAA6C,CAAC;MACnErB,IAAI,EAAE;QACJsB,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CAACzE,IAAI,CACLrT,GAAG,CAACsT,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC3N,oBAAoB,GAAG4L,GAAG,CAAC8B,OAAO,EAAEtJ,MAAM,GAAGwH,GAAG,CAAC8B,OAAO,CAACwB,GAAG,CAACtD,GAAG,IAAG;UACtE,OAAO;YACLlS,cAAc,EAAEkS,GAAG,CAAClS,cAAc;YAClCgC,GAAG,EAAEkQ,GAAG,CAAClQ;WACV;QACH,CAAC,CAAC,GAAG,EAAE;QAEP,IAAI3C,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,IAAI,IAAI,IACtEnT,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,IAAIC,SAAS,IAC5EpT,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACvT,mBAAmB,CAACkT,iBAAiB,CAACjT,WAAW,CAACkT,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAAC3Q,kBAAkB,IAAI,IAAI,IAAI2Q,eAAe,CAAC3Q,kBAAkB,IAAI0Q,SAAS,EAAE;YACjG,IAAI1J,KAAK,GAAG,IAAI,CAACzC,oBAAoB,CAACoP,SAAS,CAAE3C,CAAM,IAAKA,CAAC,CAAC/Q,GAAG,IAAI0Q,eAAe,CAAC3Q,kBAAkB,CAACC,GAAG,CAAC;YAC5G,IAAI,CAACF,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuE,oBAAoB,CAACyC,KAAK,CAAC;UACxE,CAAC,MAAM;YACL,IAAI,CAACjH,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuE,oBAAoB,CAAC,CAAC,CAAC;UACpE;QACF,CAAC,MACI;UACH,IAAI,CAACxE,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuE,oBAAoB,CAAC,CAAC,CAAC;QACpE;MACF;IACF,CAAC,CAAC,EACF1H,GAAG,CAAC,MAAK;MACP;MACA,IAAI,CAAC0W,gBAAgB,EAAE;MACvBqB,UAAU,CAAC,MAAK;QACd,IAAI,CAACjD,YAAY,EAAE,CAACrB,SAAS,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CAACA,SAAS,EAAE;EACf;EA6CAuE,YAAYA,CAAC3U,GAAQ,EAAE4U,GAAQ;IAC7B,IAAI,CAAC9S,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC8L,aAAa,CAACiH,6BAA6B,CAAC;MAC/C3B,IAAI,EAAE;QAAE9D,QAAQ,EAAEpP;MAAG;KACtB,CAAC,CAACoQ,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAClP,WAAW,GAAG;UACjB,GAAGmN,GAAG,CAAC8B,OAAO;UACd7N,eAAe,EAAE+L,GAAG,CAAC8B,OAAO,CAAC+C,gBAAgB,GAAG,IAAIC,IAAI,CAAC9E,GAAG,CAAC8B,OAAO,CAAC+C,gBAAgB,CAAC,GAAGtE,SAAS;UAClGpM,aAAa,EAAE6L,GAAG,CAAC8B,OAAO,CAACiD,cAAc,GAAG,IAAID,IAAI,CAAC9E,GAAG,CAAC8B,OAAO,CAACiD,cAAc,CAAC,GAAGxE;SACpF;QAED,IAAIP,GAAG,CAAC8B,OAAO,CAACkD,YAAY,EAAE;UAC5B,IAAI,CAACnT,cAAc,CAAChC,kBAAkB,GAAG,IAAI,CAACoV,eAAe,CAAC,IAAI,CAAC7Q,oBAAoB,EAAE,KAAK,EAAE4L,GAAG,CAAC8B,OAAO,CAACkD,YAAY,CAAC;QAC3H;QACA,IAAI,CAACnT,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAACmT,eAAe,CAAC,IAAI,CAAChT,OAAO,CAACC,gBAAgB,EAAE,OAAO,EAAE8N,GAAG,CAAC8B,OAAO,CAAC/Q,UAAU,CAAC;QAC7H,IAAIiP,GAAG,CAAC8B,OAAO,CAACnR,UAAU,EAAE;UAC1B,IAAI,CAACkB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAACwR,eAAe,CAAC,IAAI,CAAChT,OAAO,CAACoC,gBAAgB,EAAE,OAAO,EAAE2L,GAAG,CAAC8B,OAAO,CAACnR,UAAU,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAACkB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAACxB,OAAO,CAACoC,gBAAgB,CAAC,CAAC,CAAC;QAC3E;QACA,IAAI,CAACxC,cAAc,CAACS,iBAAiB,GAAG,IAAI,CAAC2S,eAAe,CAAC,IAAI,CAAChT,OAAO,CAACO,eAAe,EAAE,OAAO,EAAEwN,GAAG,CAAC8B,OAAO,CAACzC,SAAS,CAAC;QAE1H,IAAIW,GAAG,CAAC8B,OAAO,CAACkD,YAAY,EAAE;UAC5B,IAAI,IAAI,CAACrP,aAAa,EAAE;YACtB,IAAI,CAACA,aAAa,CAACkM,YAAY,GAAG7B,GAAG,CAAC8B,OAAO,CAACkD,YAAY;UAC5D;QACF;QACA,IAAI,CAACxH,aAAa,CAAC0H,IAAI,CAACP,GAAG,CAAC;MAC9B;IAEF,CAAC,CAAC;EACJ;EAGAM,eAAeA,CAACE,KAAY,EAAExG,GAAW,EAAED,KAAU;IACnD,OAAOyG,KAAK,CAACvE,IAAI,CAACwE,IAAI,IAAIA,IAAI,CAACzG,GAAG,CAAC,KAAKD,KAAK,CAAC;EAChD;EAGApP,eAAeA,CAACqV,GAAQ,EAAES,IAAS;IACjC,IAAI,CAACV,YAAY,CAACU,IAAI,CAACrV,GAAG,EAAE4U,GAAG,CAAC;EAClC;EAEA3V,SAASA,CAAC2V,GAAQ;IAChB,IAAI,CAAChP,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBnF,MAAM,EAAE8P,SAAS;MACjBzK,eAAe,EAAEyK;KAClB;IACD,IAAI,CAAC/C,aAAa,CAAC0H,IAAI,CAACP,GAAG,CAAC;EAC9B;EAKAU,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO1Y,MAAM,CAAC0Y,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAzQ,cAAcA,CAAC6P,GAAQ;IACrB,IAAI,CAAC9R,WAAW,CAACgS,gBAAgB,GAAG,IAAI,CAAChS,WAAW,CAACoB,eAAe,GAAG,IAAI,CAACoR,UAAU,CAAC,IAAI,CAACxS,WAAW,CAACoB,eAAe,CAAC,GAAG,EAAE,EAC3H,IAAI,CAACpB,WAAW,CAACkS,cAAc,GAAG,IAAI,CAAClS,WAAW,CAACsB,aAAa,GAAG,IAAI,CAACkR,UAAU,CAAC,IAAI,CAACxS,WAAW,CAACsB,aAAa,CAAC,GAAG,EAAE,EAEvH,IAAI,CAACqR,kBAAkB,GAAG;MACxBvS,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7CzC,UAAU,EAAE,IAAI,CAACqC,WAAW,CAACC,UAAU;MACvCqM,QAAQ,EAAE,IAAI,CAACtM,WAAW,CAAC4S,GAAG;MAC9B9U,UAAU,EAAE,IAAI,CAACkB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAAC5B,cAAc,CAAC4B,kBAAkB,CAACiL,KAAK,GAAG,IAAI;MACxG9N,SAAS,EAAE,IAAI,CAACiC,WAAW,CAACjC,SAAS;MACrCO,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAAC1B,SAAS;MACrCkC,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7B+L,WAAW,EAAE,IAAI,CAACvM,WAAW,CAACM,WAAW;MACzCpC,UAAU,EAAE,IAAI,CAACc,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAACD,cAAc,CAACC,kBAAkB,CAAC4M,KAAK,GAAG,IAAI;MACxGnL,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/B8L,SAAS,EAAE,IAAI,CAACxN,cAAc,CAACS,iBAAiB,GAAG,IAAI,CAACT,cAAc,CAACS,iBAAiB,CAACoM,KAAK,GAAG,IAAI;MACrGmG,gBAAgB,EAAE,IAAI,CAAChS,WAAW,CAACgS,gBAAgB;MACnDE,cAAc,EAAE,IAAI,CAAClS,WAAW,CAACkS;KAClC;IACH,IAAI,CAACW,UAAU,EAAE;IACjB,IAAI,IAAI,CAAChI,KAAK,CAACiI,aAAa,CAACnN,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACiF,OAAO,CAACmI,aAAa,CAAC,IAAI,CAAClI,KAAK,CAACiI,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAChI,aAAa,CAACkI,0BAA0B,CAAC;MAC5C5C,IAAI,EAAE,IAAI,CAACuC;KACZ,CAAC,CAACzF,IAAI,CACLrT,GAAG,CAACsT,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtE,OAAO,CAAC0F,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACrI,OAAO,CAACwE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACvCyC,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFrZ,SAAS,CAAC,MAAM,IAAI,CAAC+U,YAAY,EAAE,CAAC,CACrC,CAACrB,SAAS,EAAE;EACf;EAGA4F,QAAQA,CAACpB,GAAQ;IACf,IAAIqB,OAAO,GAAkB;MAC3B/S,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7CzC,UAAU,EAAE,IAAI,CAACqC,WAAW,CAACC,UAAU;MACvCqM,QAAQ,EAAE,IAAI,CAACtM,WAAW,CAAC4S,GAAG;MAC9B9U,UAAU,EAAE,IAAI,CAACkC,WAAW,CAAClC,UAAU;MACvCC,SAAS,EAAE,IAAI,CAACiC,WAAW,CAACjC,SAAS;MACrCO,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAAC1B,SAAS;MACrCkC,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7B+L,WAAW,EAAE,IAAI,CAACvM,WAAW,CAACM,WAAW;MACzCpC,UAAU,EAAE,IAAI,CAAC8B,WAAW,CAAC9B,UAAU;MACvCwC,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/B8L,SAAS,EAAE,IAAI,CAACxM,WAAW,CAACwM;KAC7B;IACD,IAAI,CAAC1B,aAAa,CAACkI,0BAA0B,CAAC;MAC5C5C,IAAI,EAAE+C;KACP,CAAC,CAAC7F,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtE,OAAO,CAAC0F,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EAEJ;EAEA5Q,OAAOA,CAACyP,GAAQ;IACdA,GAAG,CAACmB,KAAK,EAAE;EACb;EAEAG,YAAYA,CAACC,IAAS,EAAEC,EAAQ;IAC9B,MAAMC,KAAK,GAAGD,EAAE,GAAGA,EAAE,GAAG,IAAI,CAACvW,WAAW,CAACC,kBAAkB,CAACC,GAAG;IAC/D,IAAI,CAACiO,MAAM,CAACsI,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEE,KAAK,CAAC,CAAC;EACtE;EAEAzW,4BAA4BA,CAACuW,IAAS,EAAEI,WAAgB,EAAEC,OAAY;IACpE,IAAI,CAACxI,MAAM,CAACsI,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEI,WAAW,EAAEC,OAAO,CAAC,CAAC;EACrF;EAEApW,cAAcA,CAACiV,IAAS;IACtB,IAAIoB,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAAC7I,aAAa,CAAC8I,oCAAoC,CAAC;QACtDxD,IAAI,EAAEmC,IAAI,CAACrV;OACZ,CAAC,CAACoQ,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACtE,OAAO,CAAC0F,aAAa,CAAC,MAAM,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;EACF;EAIAuC,UAAUA,CAAA;IACR,IAAI,CAAChI,KAAK,CAACgJ,KAAK,EAAE;IAClB,IAAI,CAAChJ,KAAK,CAACiJ,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9T,WAAW,CAAC4S,GAAG,CAAC;IACnD,IAAI,CAAC/H,KAAK,CAACiJ,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACnB,kBAAkB,CAAChV,UAAU,CAAC;IACjE,IAAI,CAACkN,KAAK,CAACkJ,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACpB,kBAAkB,CAAChV,UAAU,EAAE,EAAE,CAAC;IAC5E,IAAI,CAACkN,KAAK,CAACiJ,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC9T,WAAW,CAACpC,MAAM,CAAC;IACpD,IAAI,CAACiN,KAAK,CAACkJ,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACpB,kBAAkB,CAACvS,aAAa,EAAE,EAAE,CAAC;IACjF;IACA;IACA;IACA,IAAI,CAACyK,KAAK,CAACmJ,OAAO,CAAC,QAAQ,EAAE,IAAI,CAACrB,kBAAkB,CAACnS,KAAK,EAAE,IAAI,CAACyK,OAAO,CAACgJ,WAAW,CAAC;IACrF,IAAI,CAACpJ,KAAK,CAACqJ,aAAa,CAAC,QAAQ,EAAE,IAAI,CAACvB,kBAAkB,CAACjS,MAAM,CAAC;IAClE,IAAI,CAACmK,KAAK,CAACiJ,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACnB,kBAAkB,CAACnG,SAAS,CAAC;IAC9D,IAAI,CAAC3B,KAAK,CAACiJ,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9U,cAAc,CAAC4B,kBAAkB,CAACiL,KAAK,CAAC;IAC3E,IAAI,CAAChB,KAAK,CAACiJ,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9U,cAAc,CAACC,kBAAkB,CAAC4M,KAAK,CAAC;IAC3E,IAAI,IAAI,CAAC7L,WAAW,CAACgS,gBAAgB,EAAE;MACrC,IAAI,CAACnH,KAAK,CAACiJ,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC9T,WAAW,CAACkS,cAAc,CAAC;IAClE;IACA,IAAI,IAAI,CAAClS,WAAW,CAACkS,cAAc,EAAE;MACnC,IAAI,CAACrH,KAAK,CAACiJ,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC9T,WAAW,CAACgS,gBAAgB,CAAC;IACpE;IACA,IAAI,CAACnH,KAAK,CAACsJ,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACnU,WAAW,CAACgS,gBAAgB,GAAG,IAAI,CAAChS,WAAW,CAACgS,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAAChS,WAAW,CAACkS,cAAc,GAAG,IAAI,CAAClS,WAAW,CAACkS,cAAc,GAAG,EAAE,CAAC;EAC9L;EAEAkC,uBAAuBA,CAAA;IACrB,IAAI,CAACvJ,KAAK,CAACgJ,KAAK,EAAE;IAClB,IAAI,CAAChJ,KAAK,CAACiJ,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAChR,aAAa,CAACkM,YAAY,CAAC;IAC5D,IAAI,CAACnE,KAAK,CAACiJ,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAChR,aAAa,CAACC,aAAa,CAAC;IAC7D,IAAI,CAAC8H,KAAK,CAACkJ,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACjR,aAAa,CAACC,aAAa,EAAE,EAAE,CAAC;IAC1E,IAAI,CAAC8H,KAAK,CAACwJ,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAACvR,aAAa,CAAClF,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC;IAChF,IAAI,CAACiN,KAAK,CAACwJ,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAACvR,aAAa,CAACG,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1F;EAGAN,gBAAgBA,CAACmP,GAAQ;IACvB,IAAI,CAAChP,aAAa,CAACkM,YAAY,GAAG,IAAI,CAACjS,WAAW,CAACC,kBAAkB,CAACC,GAAG,EACvE,IAAI,CAACmX,uBAAuB,EAAE;IAChC,IAAI,IAAI,CAACvJ,KAAK,CAACiI,aAAa,CAACnN,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACiF,OAAO,CAACmI,aAAa,CAAC,IAAI,CAAClI,KAAK,CAACiI,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAAC/H,qBAAqB,CAACuJ,yCAAyC,CAAC;MACnElE,IAAI,EAAE,IAAI,CAACtN;KACZ,CAAC,CAACoK,IAAI,CACLrT,GAAG,CAACsT,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtE,OAAO,CAAC0F,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFrZ,SAAS,CAAC,MAAM,IAAI,CAAC+U,YAAY,EAAE,CAAC,CACrC,CAACrB,SAAS,EAAE;EACf,CAAC,CAAE;EACG7P,aAAaA,CAAC8W,MAAW,EAAEhC,IAAS;IAAA,IAAAiC,KAAA;IAAA,OAAAC,iBAAA;MACxCD,KAAI,CAAC9N,YAAY,GAAG6L,IAAI;MACxBiC,KAAI,CAAC9O,cAAc,GAAG,EAAE;MACxB8O,KAAI,CAAC7N,WAAW,GAAG,CAAC;MACpB6N,KAAI,CAAC5H,kBAAkB,GAAG,CAAC,CAAC,CAAC;MAC7B4H,KAAI,CAACxP,mBAAmB,GAAG,IAAI,CAAC,CAAC;MACjC;MACAwP,KAAI,CAAC/H,iBAAiB,GAAG,KAAK;MAC9B+H,KAAI,CAAC9H,uBAAuB,GAAG,CAAC;MAChC8H,KAAI,CAAC5N,mBAAmB,GAAG,CAAC;MAC5B4N,KAAI,CAAC3N,gBAAgB,GAAG,CAAC;MACzB2N,KAAI,CAAC7H,mBAAmB,GAAG,IAAI;MAE/B;MACA,IAAI;QACF,MAAM+H,QAAQ,SAASF,KAAI,CAACnJ,gBAAgB,CAACsJ,qBAAqB,CAACpC,IAAI,CAACrV,GAAG,CAAC,CAAC0X,SAAS,EAAE;QAExF,IAAIF,QAAQ,IAAIA,QAAQ,CAACxF,UAAU,KAAK,CAAC,IAAIwF,QAAQ,CAACzF,OAAO,EAAE;UAC7D;UACAuF,KAAI,CAAC5H,kBAAkB,GAAG8H,QAAQ,CAACzF,OAAO,CAAC4F,mBAAmB,IAAI,CAAC;UACnE;UACA,IAAIH,QAAQ,CAACzF,OAAO,CAAC5Q,gBAAgB,KAAK,CAAC,EAAE;YAAE;YAC7CmW,KAAI,CAACxP,mBAAmB,GAAG,KAAK;UAClC,CAAC,MAAM;YACLwP,KAAI,CAACxP,mBAAmB,GAAG,IAAI;UACjC;UAEA;UACAwP,KAAI,CAAC7H,mBAAmB,GAAG,IAAI;UAC/B6H,KAAI,CAAC/H,iBAAiB,GAAG,KAAK;UAC9B+H,KAAI,CAAC9H,uBAAuB,GAAG,CAAC;UAEhC;UACA,IAAIgI,QAAQ,CAACzF,OAAO,CAAC6F,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACzF,OAAO,CAAC6F,KAAK,CAAC,EAAE;YACnE;YACAN,KAAI,CAAC9O,cAAc,GAAGgP,QAAQ,CAACzF,OAAO,CAAC6F,KAAK,CAACrE,GAAG,CAAEwE,KAAU,KAAM;cAChEC,QAAQ,EAAER,QAAQ,CAACzF,OAAO,CAAC3C,QAAQ,IAAIiG,IAAI,CAACrV,GAAG;cAC/CiY,YAAY,EAAET,QAAQ,CAACzF,OAAO,CAACmG,YAAY;cAC3C/Q,SAAS,EAAE4Q,KAAK,CAACI,SAAS,IAAI,EAAE;cAChC3Q,KAAK,EAAEuQ,KAAK,CAACK,KAAK,IAAI,EAAE;cACxB/Q,UAAU,EAAE0Q,KAAK,CAACM,UAAU,IAAI,CAAC;cACjC3Q,MAAM,EAAEqQ,KAAK,CAACO,MAAM,IAAI,CAAC;cACzBC,OAAO,EAAER,KAAK,CAACtD,OAAO,IAAI,CAAC;cAC3BnX,kBAAkB,EAAEya,KAAK,CAACza,kBAAkB,IAAIya,KAAK,CAACza,kBAAkB,GAAG,CAAC,GAAGya,KAAK,CAACza,kBAAkB,GAAGA,kBAAkB,CAACkb,GAAG;cAChIC,OAAO,EAAEV,KAAK,CAACW,OAAO,IAAI,EAAE;cAC5BC,gBAAgB,EAAEZ,KAAK,CAAC5W;aACzB,CAAC,CAAC;YACHmW,KAAI,CAAChQ,cAAc,EAAE;UACvB,CAAC,MAAM,CAEP;QACF,CAAC,MAAM,CAEP;MACF,CAAC,CAAC,OAAOsR,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;MAEAtB,KAAI,CAAC7J,aAAa,CAAC0H,IAAI,CAACkC,MAAM,EAAE;QAC9ByB,OAAO,EAAEzD,IAAI;QACb0D,oBAAoB,EAAE;OACvB,CAAC;IAAC;EACL;EAEA;EACA5Q,kBAAkBA,CAAA;IAChB,IAAI,CAACuH,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAAClH,cAAc,GAAG,EAAE;IACxB,IAAI,CAACV,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC2B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACE,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACD,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAAC+F,mBAAmB,GAAG,IAAI;IAE/B;IACA,IAAI,CAAC/B,OAAO,CAAC0F,aAAa,CAAC,eAAe,CAAC;EAC7C;EACA;EACA4F,gBAAgBA,CAAA;IACd,IAAI,CAACxQ,cAAc,CAACyQ,IAAI,CAAC;MACvBjB,QAAQ,EAAE,IAAI,CAACxO,YAAY,EAAExJ,GAAG,IAAI,CAAC;MACrCmH,SAAS,EAAE,EAAE;MACbK,KAAK,EAAE,EAAE;MACTH,UAAU,EAAE,CAAC;MACbK,MAAM,EAAE,CAAC;MACT6Q,OAAO,EAAE,CAAC;MACVjb,kBAAkB,EAAEA,kBAAkB,CAACkb,GAAG;MAC1CC,OAAO,EAAE;KACV,CAAC;EACJ;EACA;EACMjS,gBAAgBA,CAAA;IAAA,IAAA0S,MAAA;IAAA,OAAA3B,iBAAA;MACpB,IAAI;QACF,IAAI,CAAC2B,MAAI,CAAC1P,YAAY,EAAExJ,GAAG,EAAE;UAC3BkZ,MAAI,CAACxL,OAAO,CAACwE,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAMiH,OAAO,GAAG;UACdrH,YAAY,EAAEoH,MAAI,CAACrZ,WAAW,EAAEC,kBAAkB,EAAEC,GAAG,IAAI,CAAC;UAC5DqP,QAAQ,EAAE8J,MAAI,CAAC1P,YAAY,CAACxJ;SAC7B;QAED,MAAMwX,QAAQ,SAAS0B,MAAI,CAAC/K,gBAAgB,CAAC3H,gBAAgB,CAAC2S,OAAO,CAAC,CAACzB,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAE4B,OAAO,IAAI5B,QAAQ,CAAC6B,IAAI,EAAE;UACtC,MAAMC,YAAY,GAAG9B,QAAQ,CAAC6B,IAAI,CAAC9F,GAAG,CAAEzC,CAAM,KAAM;YAClDmH,YAAY,EAAEnH,CAAC,CAACoH,YAAY;YAC5BF,QAAQ,EAAEkB,MAAI,CAAC1P,YAAY,EAAExJ,GAAG;YAChCmH,SAAS,EAAE2J,CAAC,CAACqH,SAAS;YACtB3Q,KAAK,EAAEsJ,CAAC,CAACsH,KAAK,IAAI,EAAE;YACpB/Q,UAAU,EAAEyJ,CAAC,CAACuH,UAAU;YACxB3Q,MAAM,EAAEoJ,CAAC,CAACwH,MAAM;YAChBC,OAAO,EAAEzH,CAAC,CAAC2D,OAAO;YAClBnX,kBAAkB,EAAEA,kBAAkB,CAACic,IAAI;YAC3Cd,OAAO,EAAE3H,CAAC,CAAC4H;WACZ,CAAC,CAAC;UACHQ,MAAI,CAAC1Q,cAAc,CAACyQ,IAAI,CAAC,GAAGK,YAAY,CAAC;UACzCJ,MAAI,CAAC5R,cAAc,EAAE;UACrB4R,MAAI,CAACxL,OAAO,CAAC0F,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACL8F,MAAI,CAACxL,OAAO,CAACwE,YAAY,CAACsF,QAAQ,EAAE9J,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOkL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCM,MAAI,CAACxL,OAAO,CAACwE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACMxL,gBAAgBA,CAAA;IAAA,IAAA8S,MAAA;IAAA,OAAAjC,iBAAA;MACpB,IAAI;QACF,IAAI,CAACiC,MAAI,CAAChQ,YAAY,EAAExJ,GAAG,EAAE;UAC3BwZ,MAAI,CAAC9L,OAAO,CAACwE,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAMiH,OAAO,GAAG;UACdrH,YAAY,EAAE0H,MAAI,CAAC3Z,WAAW,EAAEC,kBAAkB,EAAEC,GAAG,IAAI,CAAC;UAC5DqP,QAAQ,EAAEoK,MAAI,CAAChQ,YAAY,CAACxJ;SAC7B;QAED,MAAMwX,QAAQ,SAASgC,MAAI,CAACrL,gBAAgB,CAACzH,gBAAgB,CAACyS,OAAO,CAAC,CAACzB,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAE4B,OAAO,IAAI5B,QAAQ,CAAC6B,IAAI,EAAE;UACtC,MAAMI,YAAY,GAAGjC,QAAQ,CAAC6B,IAAI,CAAC9F,GAAG,CAAEzC,CAAM,KAAM;YAClDmH,YAAY,EAAEnH,CAAC,CAACoH,YAAY;YAC5BF,QAAQ,EAAEwB,MAAI,CAAChQ,YAAY,EAAExJ,GAAG;YAChCmH,SAAS,EAAE2J,CAAC,CAACqH,SAAS;YACtB3Q,KAAK,EAAEsJ,CAAC,CAACsH,KAAK,IAAI,EAAE;YACpB/Q,UAAU,EAAEyJ,CAAC,CAACuH,UAAU;YACxB3Q,MAAM,EAAEoJ,CAAC,CAACwH,MAAM;YAChBC,OAAO,EAAEzH,CAAC,CAAC2D,OAAO;YAClBnX,kBAAkB,EAAEA,kBAAkB,CAACoc,EAAE;YAAE;YAC3CjB,OAAO,EAAE3H,CAAC,CAAC4H,OAAO,IAAI;WACvB,CAAC,CAAC;UACHc,MAAI,CAAChR,cAAc,CAACyQ,IAAI,CAAC,GAAGQ,YAAY,CAAC;UACzCD,MAAI,CAAClS,cAAc,EAAE;UACrBkS,MAAI,CAAC9L,OAAO,CAAC0F,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACLoG,MAAI,CAAC9L,OAAO,CAACwE,YAAY,CAACsF,QAAQ,EAAE9J,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOkL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCY,MAAI,CAAC9L,OAAO,CAACwE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACAnL,mBAAmBA,CAACD,KAAa;IAC/B,MAAMuO,IAAI,GAAG,IAAI,CAAC7M,cAAc,CAAC1B,KAAK,CAAC;IACvC,IAAI,CAAC0B,cAAc,CAACmR,MAAM,CAAC7S,KAAK,EAAE,CAAC,CAAC;IACpC,IAAI,CAACQ,cAAc,EAAE;EACvB;EAEA;EACAA,cAAcA,CAAA;IACZ,IAAI,CAACmC,WAAW,GAAG,IAAI,CAACjB,cAAc,CAACoR,MAAM,CAAC,CAACC,GAAG,EAAExE,IAAI,KAAI;MAC1D,OAAOwE,GAAG,GAAIxE,IAAI,CAAChO,UAAU,GAAGgO,IAAI,CAAC3N,MAAO;IAC9C,CAAC,EAAE,CAAC,CAAC;IACL,IAAI,CAACoS,mBAAmB,EAAE;EAC5B;EAEA;EACAA,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACpQ,mBAAmB,GAAGqQ,IAAI,CAACC,KAAK,CAAC,IAAI,CAACvQ,WAAW,GAAG,IAAI,CAAC;IAC9D,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACF,WAAW,GAAG,IAAI,CAACC,mBAAmB;EACrE;EAEA;EACA3B,cAAcA,CAACkS,MAAc;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAAC9E,MAAM,CAACyE,MAAM,CAAC;EACnB;EAIA;EACMrR,aAAaA,CAACgM,GAAQ;IAAA,IAAA2F,MAAA;IAAA,OAAAhD,iBAAA;MAC1B,IAAIgD,MAAI,CAAC/R,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC8R,MAAI,CAAC7M,OAAO,CAACwE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA;MACA,MAAMsI,YAAY,GAAGD,MAAI,CAAC/R,cAAc,CAACiS,MAAM,CAACpF,IAAI,IAClD,CAACA,IAAI,CAAClO,SAAS,CAACuT,IAAI,EAAE,CACvB;MAED,IAAIF,YAAY,CAAC/R,MAAM,GAAG,CAAC,EAAE;QAC3B8R,MAAI,CAAC7M,OAAO,CAACwE,YAAY,CAAC,iBAAiB,CAAC;QAC5C;MACF;MAAE,IAAI;QACJ,MAAMiH,OAAO,GAAG;UACd3C,OAAO,EAAE+D,MAAI,CAAC/Q,YAAY,CAACxJ,GAAG;UAC9B2a,KAAK,EAAEJ,MAAI,CAAC/R,cAAc;UAC1BoS,WAAW,EAAEL,MAAI,CAAC7K,kBAAkB;UAAE;UACtC;UACAmL,UAAU,EAAEN,MAAI,CAAC9K,mBAAmB;UAAE;UACtCqL,UAAU,EAAEP,MAAI,CAAChL,iBAAiB;UAAI;UACtCwL,aAAa,EAAER,MAAI,CAAC/K,uBAAuB,CAAC;SAC7C;QAED,MAAMgI,QAAQ,SAAS+C,MAAI,CAACpM,gBAAgB,CAACvF,aAAa,CAACuQ,OAAO,CAAC,CAACzB,SAAS,EAAE;QAC/E,IAAIF,QAAQ,EAAE4B,OAAO,EAAE;UACrBmB,MAAI,CAAC7M,OAAO,CAAC0F,aAAa,CAAC,SAAS,CAAC;UACrCwB,GAAG,CAACmB,KAAK,EAAE;QACb,CAAC,MAAM;UACLwE,MAAI,CAAC7M,OAAO,CAACwE,YAAY,CAACsF,QAAQ,EAAE9J,OAAO,IAAI,MAAM,CAAC;QACxD;MACF,CAAC,CAAC,OAAOkL,KAAK,EAAE;QACd2B,MAAI,CAAC7M,OAAO,CAACwE,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACM8I,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1D,iBAAA;MACnB,IAAI;QACF,MAAM2D,IAAI,SAA2BD,MAAI,CAAC9M,gBAAgB,CAAC6M,eAAe,CAACC,MAAI,CAACzR,YAAY,CAACxJ,GAAG,CAAC,CAAC0X,SAAS,EAAE;QAC7G,IAAIwD,IAAI,EAAE;UACR,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAG,OAAOV,MAAI,CAACzR,YAAY,CAAC/I,UAAU,IAAIwa,MAAI,CAACzR,YAAY,CAAC9I,MAAM,OAAO;UACtF6a,IAAI,CAAChJ,KAAK,EAAE;UACZ6I,MAAM,CAACC,GAAG,CAACO,eAAe,CAACT,GAAG,CAAC;QACjC,CAAC,MAAM;UACLF,MAAI,CAACvN,OAAO,CAACwE,YAAY,CAAC,iBAAiB,CAAC;QAC9C;MACF,CAAC,CAAC,OAAO0G,KAAK,EAAE;QACdqC,MAAI,CAACvN,OAAO,CAACwE,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACA/I,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACX,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAACiF,OAAO,CAACwE,YAAY,CAAC,YAAY,CAAC;MACvC;IACF;IAEA,IAAI;MACF;MACA,MAAM2J,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;MAEhD;MACA,MAAMC,WAAW,GAAGX,MAAM,CAACjG,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,mDAAmD,CAAC;MAClG,IAAI4G,WAAW,EAAE;QACfA,WAAW,CAACP,QAAQ,CAACrG,IAAI,EAAE;QAC3B4G,WAAW,CAACP,QAAQ,CAACQ,KAAK,CAACH,YAAY,CAAC;QACxCE,WAAW,CAACP,QAAQ,CAACzF,KAAK,EAAE;QAE5B;QACAgG,WAAW,CAACE,MAAM,GAAG;UACnBvH,UAAU,CAAC,MAAK;YACdqH,WAAW,CAACG,KAAK,EAAE;YACnB;UACF,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACxO,OAAO,CAACwE,YAAY,CAAC,yBAAyB,CAAC;MACtD;IACF,CAAC,CAAC,OAAO0G,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,IAAI,CAAClL,OAAO,CAACwE,YAAY,CAAC,YAAY,CAAC;IACzC;EACF;EAEA;EACQ4J,oBAAoBA,CAAA;IAC1B;IACA,MAAMK,QAAQ,GAAG5f,kBAAkB;IAEnC;IACA,MAAM6f,WAAW,GAAG,IAAIrH,IAAI,EAAE,CAACsH,kBAAkB,CAAC,OAAO,CAAC;IAC1D,MAAMC,aAAa,GAAG,IAAI,CAACzc,WAAW,CAACC,kBAAkB,EAAE/B,cAAc,IAAI,EAAE;IAE/E;IACA,IAAIwe,SAAS,GAAG,EAAE;IAClB,IAAI,CAAC/T,cAAc,CAACgU,OAAO,CAAC,CAACnH,IAAI,EAAEvO,KAAK,KAAI;MAC1C,MAAM2V,QAAQ,GAAGpH,IAAI,CAAChO,UAAU,GAAGgO,IAAI,CAAC3N,MAAM;MAC9C,MAAMgV,aAAa,GAAG,IAAI,CAAC1U,oBAAoB,CAACqN,IAAI,CAAC/X,kBAAkB,CAAC;MACxE,MAAMqf,IAAI,GAAGtH,IAAI,CAAC7N,KAAK,IAAI,EAAE;MAC7B+U,SAAS,IAAI;;sCAEmBzV,KAAK,GAAG,CAAC;kBAC7BuO,IAAI,CAAClO,SAAS;qCACK,IAAI,CAACY,cAAc,CAACsN,IAAI,CAAChO,UAAU,CAAC;sCACnCsV,IAAI;sCACJtH,IAAI,CAAC3N,MAAM;qCACZ,IAAI,CAACK,cAAc,CAAC0U,QAAQ,CAAC;sCAC5BC,aAAa;;SAE1C;IACL,CAAC,CAAC;IAEF;IACA,MAAME,iBAAiB,GAAG,IAAI,CAACnN,mBAAmB,GAAG;;YAE7C,IAAI,CAACF,iBAAiB,KAAK,IAAI,CAACC,uBAAuB,MAAM,IAAI,CAACzH,cAAc,CAAC,IAAI,CAAC2B,mBAAmB,CAAC;;OAE/G,GAAG,EAAE;IAER;IACA,MAAMmT,IAAI,GAAGV,QAAQ,CAClBW,OAAO,CAAC,oBAAoB,EAAER,aAAa,CAAC,CAC5CQ,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAACtT,YAAY,EAAE/I,UAAU,IAAI,EAAE,CAAC,CAC9Dqc,OAAO,CAAC,YAAY,EAAE,IAAI,CAACtT,YAAY,EAAE9I,MAAM,IAAI,EAAE,CAAC,CACtDoc,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAACtT,YAAY,EAAEtG,aAAa,IAAI,EAAE,CAAC,CACpE4Z,OAAO,CAAC,gBAAgB,EAAEV,WAAW,CAAC,CACtCU,OAAO,CAAC,gBAAgB,EAAEP,SAAS,CAAC,CACpCO,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC/U,cAAc,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAAC,CACrEqT,OAAO,CAAC,wBAAwB,EAAEF,iBAAiB,CAAC,CACpDE,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC/U,cAAc,CAAC,IAAI,CAAC4B,gBAAgB,CAAC,CAAC,CACvEmT,OAAO,CAAC,oBAAoB,EAAE,IAAI/H,IAAI,EAAE,CAACgI,cAAc,CAAC,OAAO,CAAC,CAAC;IAEpE,OAAOF,IAAI;EACb;EAGA;EACMtU,aAAaA,CAACqM,GAAQ;IAAA,IAAAoI,MAAA;IAAA,OAAAzF,iBAAA;MAC1B,IAAIyF,MAAI,CAACxU,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpCuU,MAAI,CAACtP,OAAO,CAACwE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA,IAAI,CAAC8K,MAAI,CAACtN,kBAAkB,EAAE;QAC5BsN,MAAI,CAACtP,OAAO,CAACwE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA,IAAI;QACF,MAAMsF,QAAQ,SAASwF,MAAI,CAAC7O,gBAAgB,CAAC5F,aAAa,CAACyU,MAAI,CAACtN,kBAAkB,CAAC,CAACgI,SAAS,EAAE;QAE/F,IAAIF,QAAQ,CAAC4B,OAAO,EAAE;UACpB4D,MAAI,CAACtP,OAAO,CAAC0F,aAAa,CAAC,UAAU,CAAC;UACtCyF,OAAO,CAACoE,GAAG,CAAC,UAAU,EAAE;YACtBrC,WAAW,EAAEoC,MAAI,CAACtN,kBAAkB;YACpChC,OAAO,EAAE8J,QAAQ,CAAC9J;WACnB,CAAC;QACJ,CAAC,MAAM;UACLsP,MAAI,CAACtP,OAAO,CAACwE,YAAY,CAACsF,QAAQ,CAAC9J,OAAO,IAAI,SAAS,CAAC;UACxDmL,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEpB,QAAQ,CAAC9J,OAAO,CAAC;QAC7C;QAEAkH,GAAG,CAACmB,KAAK,EAAE;MACb,CAAC,CAAC,OAAO6C,KAAK,EAAE;QACdoE,MAAI,CAACtP,OAAO,CAACwE,YAAY,CAAC,SAAS,CAAC;QACpC2G,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IAAC;EACH;EAEA;EACA5Q,oBAAoBA,CAAC0U,aAAiC;IACpD,QAAQA,aAAa;MACnB,KAAKpf,kBAAkB,CAACic,IAAI;QAC1B,OAAO,MAAM;MACf,KAAKjc,kBAAkB,CAACkb,GAAG;QACzB,OAAO,KAAK;MACd,KAAKlb,kBAAkB,CAACoc,EAAE;QACxB,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EAEAxY,sBAAsBA,CAACgc,MAAc;IACnC,QAAQA,MAAM;MACZ,KAAK/f,mBAAmB,CAACggB,GAAG;QAC1B,OAAO,KAAK;MACd,KAAKhgB,mBAAmB,CAACigB,GAAG;QAC1B,OAAO,KAAK;MACd,KAAKjgB,mBAAmB,CAACkgB,GAAG;QAC1B,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACM/W,wBAAwBA,CAAC+Q,MAAW;IAAA,IAAAiG,MAAA;IAAA,OAAA/F,iBAAA;MACxC;MACA+F,MAAI,CAAClQ,YAAY,GAAG,EAAE;MACtBkQ,MAAI,CAACpR,kBAAkB,GAAG,EAAE;MAC5BoR,MAAI,CAAClT,yBAAyB,GAAG,IAAI;MACrCkT,MAAI,CAAC9S,qBAAqB,GAAG,EAAE;MAE/B;MACA8S,MAAI,CAACrS,mBAAmB,GAAG,CAAC;MAC5BqS,MAAI,CAAC1N,qBAAqB,GAAG,EAAE;MAC/B0N,MAAI,CAACtS,kBAAkB,GAAG,CAAC;MAE3B;MACA,MAAMsS,MAAI,CAACC,gBAAgB,EAAE;MAE7B;MACAD,MAAI,CAAC7P,aAAa,CAAC0H,IAAI,CAACkC,MAAM,EAAE;QAC9B0B,oBAAoB,EAAE;OACvB,CAAC;IAAC;EACL;EAEA;EACMwE,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAjG,iBAAA;MACpB,IAAI;QACF,MAAMkG,mBAAmB,GAAwB;UAC/CC,aAAa,EAAE,CAAC;UAAE;UAClB9J,SAAS,EAAE4J,MAAI,CAACvS,mBAAmB;UACnC4I,QAAQ,EAAE2J,MAAI,CAAC7N,gBAAgB;UAC/BgO,aAAa,EAAEH,MAAI,CAAChT,qBAAqB,IAAI;SAC9C;QAED,MAAMgN,QAAQ,SAASgG,MAAI,CAACpP,eAAe,CAACwP,mCAAmC,CAAC;UAC9E1K,IAAI,EAAEuK;SACP,CAAC,CAAC/F,SAAS,EAAE;QAEd,IAAIF,QAAQ,EAAExF,UAAU,KAAK,CAAC,IAAIwF,QAAQ,CAACzF,OAAO,EAAE;UAClDyL,MAAI,CAACpQ,YAAY,GAAGoK,QAAQ,CAACzF,OAAO,CAACwB,GAAG,CAAC8B,IAAI,KAAK;YAChDhL,UAAU,EAAEgL,IAAI,CAACwI,WAAW;YAC5BvT,YAAY,EAAE+K,IAAI,CAACsI,aAAa,IAAI,EAAE;YACtCpT,WAAW,EAAE,SAAS8K,IAAI,CAACyI,SAAS,GAAG,IAAI/I,IAAI,CAACM,IAAI,CAACyI,SAAS,CAAC,CAACzB,kBAAkB,EAAE,GAAG,IAAI;WAC5F,CAAC,CAAC;UACH;UACA;UACAmB,MAAI,CAACxS,kBAAkB,GAAGwM,QAAQ,CAACnD,UAAU,IAAI,CAAC;UAClD;UACAmJ,MAAI,CAACO,2BAA2B,EAAE;QACpC,CAAC,MAAM;UACLP,MAAI,CAACpQ,YAAY,GAAG,EAAE;UACtBoQ,MAAI,CAACxS,kBAAkB,GAAG,CAAC;UAC3BwS,MAAI,CAAC9P,OAAO,CAACwE,YAAY,CAAC,UAAU,CAAC;QACvC;MACF,CAAC,CAAC,OAAO0G,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC4E,MAAI,CAAC9P,OAAO,CAACwE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACMzF,eAAeA,CAAA;IAAA,IAAAuR,MAAA;IAAA,OAAAzG,iBAAA;MACnB;MACAyG,MAAI,CAAC/S,mBAAmB,GAAG,CAAC;MAC5B,MAAM+S,MAAI,CAACT,gBAAgB,EAAE;IAAC;EAChC;EAEA;EACAzT,mBAAmBA,CAAA;IACjB,IAAI,CAACU,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACiC,eAAe,EAAE;EACxB;EAEA;EACMvC,uBAAuBA,CAACiS,QAAa;IAAA,IAAA8B,OAAA;IAAA,OAAA1G,iBAAA;MACzC0G,OAAI,CAAC7T,yBAAyB,GAAG+R,QAAQ;MACzC,MAAM8B,OAAI,CAACC,mBAAmB,CAAC/B,QAAQ,CAAC9R,UAAU,CAAC;IAAC;EACtD;EAEA;EACM6T,mBAAmBA,CAACC,UAAkB;IAAA,IAAAC,OAAA;IAAA,OAAA7G,iBAAA;MAC1C,IAAI;QACF,MAAM8G,IAAI,GAA8B;UACtCF,UAAU,EAAEA;SACb;QAED,MAAM3G,QAAQ,SAAS4G,OAAI,CAAChQ,eAAe,CAACkQ,yCAAyC,CAAC;UACpFpL,IAAI,EAAEmL;SACP,CAAC,CAAC3G,SAAS,EAAE;QAEd,IAAIF,QAAQ,EAAExF,UAAU,KAAK,CAAC,IAAIwF,QAAQ,CAACzF,OAAO,EAAE;UAClDqM,OAAI,CAAClS,kBAAkB,GAAGsL,QAAQ,CAACzF,OAAO,CAACwB,GAAG,CAAC8B,IAAI,KAAK;YACtDkJ,iBAAiB,EAAElJ,IAAI,CAACkJ,iBAAiB,IAAI,CAAC;YAC9CV,WAAW,EAAExI,IAAI,CAACwI,WAAW,IAAIM,UAAU;YAC3CxS,UAAU,EAAE0J,IAAI,CAAC1J,UAAU,IAAI,CAAC;YAChCD,YAAY,EAAE2J,IAAI,CAAC3J,YAAY,IAAI,EAAE;YACrCN,UAAU,EAAEiK,IAAI,CAACjK,UAAU,IAAI,EAAE;YACjCG,QAAQ,EAAE,KAAK,CAAC;WACjB,CAAC,CAAC;QACL,CAAC,MAAM;UACL6S,OAAI,CAAClS,kBAAkB,GAAG,EAAE;UAC5BkS,OAAI,CAAC1Q,OAAO,CAACwE,YAAY,CAAC,UAAU,CAAC;QACvC;MACF,CAAC,CAAC,OAAO0G,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCwF,OAAI,CAAC1Q,OAAO,CAACwE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACA1G,mBAAmBA,CAAA;IACjB;EAAA;EAGF;EACAM,sBAAsBA,CAAA;IACpB,IAAI,CAACI,kBAAkB,CAACsQ,OAAO,CAACnH,IAAI,IAAG;MACrCA,IAAI,CAAC9J,QAAQ,GAAG,IAAI;IACtB,CAAC,CAAC;EACJ;EAEA;EACAS,wBAAwBA,CAAA;IACtB,IAAI,CAACE,kBAAkB,CAACsQ,OAAO,CAACnH,IAAI,IAAG;MACrCA,IAAI,CAAC9J,QAAQ,GAAG,KAAK;IACvB,CAAC,CAAC;EACJ;EAEA;EACAU,6BAA6BA,CAAA;IAC3B,OAAO,IAAI,CAACC,kBAAkB,CAACuO,MAAM,CAACpF,IAAI,IAAIA,IAAI,CAAC9J,QAAQ,CAAC,CAAC9C,MAAM;EACrE;EAEA;EACM0E,2BAA2BA,CAACyH,GAAQ;IAAA,IAAA4J,OAAA;IAAA,OAAAjH,iBAAA;MACxC,MAAMkH,aAAa,GAAGD,OAAI,CAACtS,kBAAkB,CAACuO,MAAM,CAACpF,IAAI,IAAIA,IAAI,CAAC9J,QAAQ,CAAC;MAE3E,IAAIkT,aAAa,CAAChW,MAAM,KAAK,CAAC,EAAE;QAC9B+V,OAAI,CAAC9Q,OAAO,CAACwE,YAAY,CAAC,WAAW,CAAC;QACtC;MACF;MAEA,IAAI;QACF;QACA,MAAMwM,aAAa,GAAGD,aAAa,CAAClL,GAAG,CAAC8B,IAAI,KAAK;UAC/C2C,QAAQ,EAAEwG,OAAI,CAAChV,YAAY,EAAExJ,GAAG,IAAI,CAAC;UACrCmH,SAAS,EAAEkO,IAAI,CAAC3J,YAAY;UAC5BlE,KAAK,EAAE,EAAE;UAAE;UACXH,UAAU,EAAE,CAAC;UAAE;UACfK,MAAM,EAAE,CAAC;UAAE;UACX6Q,OAAO,EAAE,CAAC;UACVjb,kBAAkB,EAAEA,kBAAkB,CAACkb,GAAG;UAAE;UAC5CC,OAAO,EAAEpD,IAAI,CAACjK,UAAU,GAAG,OAAOiK,IAAI,CAACjK,UAAU,EAAE,GAAG,EAAE,CAAC;SAC1D,CAAC,CAAC;QAEH;QACAoT,OAAI,CAAChW,cAAc,CAACyQ,IAAI,CAAC,GAAGyF,aAAa,CAAC;QAE1C;QACAF,OAAI,CAAClX,cAAc,EAAE;QAErB;QACAkX,OAAI,CAAC9Q,OAAO,CAAC0F,aAAa,CAAC,QAAQqL,aAAa,CAAChW,MAAM,MAAM,CAAC;QAE9D;QACAmM,GAAG,CAACmB,KAAK,EAAE;MACb,CAAC,CAAC,OAAO6C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC4F,OAAI,CAAC9Q,OAAO,CAACwE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACA6L,2BAA2BA,CAAA;IACzB;IACA,IAAI,CAACnO,qBAAqB,GAAG,CAAC,GAAG,IAAI,CAACxC,YAAY,CAAC;EACrD;EAEAlC,qBAAqBA,CAAA;IACnB,MAAMyT,UAAU,GAAG5E,IAAI,CAAC6E,IAAI,CAAC,IAAI,CAAC5T,kBAAkB,GAAG,IAAI,CAAC2E,gBAAgB,CAAC;IAC7E,OAAOoK,IAAI,CAAC8E,GAAG,CAAC,CAAC,EAAEF,UAAU,CAAC,CAAC,CAAC;EAClC;EAEA7T,qBAAqBA,CAAA;IACnB,OAAO,CAAC,IAAI,CAACG,mBAAmB,GAAG,CAAC,IAAI,IAAI,CAAC0E,gBAAgB;EAC/D;EAEA5E,mBAAmBA,CAAA;IACjB,MAAM+T,QAAQ,GAAG,IAAI,CAAC7T,mBAAmB,GAAG,IAAI,CAAC0E,gBAAgB;IACjE,OAAOoK,IAAI,CAACgF,GAAG,CAACD,QAAQ,EAAE,IAAI,CAAC9T,kBAAkB,CAAC;EACpD;EAEML,oBAAoBA,CAAA;IAAA,IAAAqU,OAAA;IAAA,OAAAzH,iBAAA;MACxB,IAAIyH,OAAI,CAAC/T,mBAAmB,GAAG,CAAC,EAAE;QAChC+T,OAAI,CAAC/T,mBAAmB,EAAE;QAC1B,MAAM+T,OAAI,CAACzB,gBAAgB,EAAE;MAC/B;IAAC;EACH;EAEM1S,gBAAgBA,CAAA;IAAA,IAAAoU,OAAA;IAAA,OAAA1H,iBAAA;MACpB,IAAI0H,OAAI,CAAChU,mBAAmB,GAAGgU,OAAI,CAAC/T,qBAAqB,EAAE,EAAE;QAC3D+T,OAAI,CAAChU,mBAAmB,EAAE;QAC1B,MAAMgU,OAAI,CAAC1B,gBAAgB,EAAE;MAC/B;IAAC;EACH;;;uCA5uCWlQ,4BAA4B,EAAA9P,EAAA,CAAA2hB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7hB,EAAA,CAAA2hB,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA/hB,EAAA,CAAA2hB,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAjiB,EAAA,CAAA2hB,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAniB,EAAA,CAAA2hB,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAriB,EAAA,CAAA2hB,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAAviB,EAAA,CAAA2hB,iBAAA,CAAAW,EAAA,CAAAE,oBAAA,GAAAxiB,EAAA,CAAA2hB,iBAAA,CAAAW,EAAA,CAAAG,gBAAA,GAAAziB,EAAA,CAAA2hB,iBAAA,CAAAe,EAAA,CAAAC,aAAA,GAAA3iB,EAAA,CAAA2hB,iBAAA,CAAAiB,EAAA,CAAAC,MAAA,GAAA7iB,EAAA,CAAA2hB,iBAAA,CAAAmB,EAAA,CAAAC,YAAA,GAAA/iB,EAAA,CAAA2hB,iBAAA,CAAAqB,GAAA,CAAAC,cAAA,GAAAjjB,EAAA,CAAA2hB,iBAAA,CAAAuB,GAAA,CAAAC,gBAAA,GAAAnjB,EAAA,CAAA2hB,iBAAA,CAAAW,EAAA,CAAAc,eAAA;IAAA;EAAA;;;YAA5BtT,4BAA4B;MAAAuT,SAAA;MAAAC,SAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC9DvCxjB,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAyG,SAAA,qBAAiC;UACnCzG,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,aAA8B,cACN,cACqC,gBACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,qBACkD;UADtBD,EAAA,CAAAkE,gBAAA,2BAAAwf,0EAAAtf,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA3jB,EAAA,CAAAsE,kBAAA,CAAAmf,GAAA,CAAAnhB,WAAA,CAAAC,kBAAA,EAAA6B,MAAA,MAAAqf,GAAA,CAAAnhB,WAAA,CAAAC,kBAAA,GAAA6B,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA4C;UACtEpE,EAAA,CAAAiB,UAAA,4BAAA2iB,2EAAA;YAAA5jB,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA,OAAA3jB,EAAA,CAAAyB,WAAA,CAAkBgiB,GAAA,CAAA1M,0BAAA,EAA4B;UAAA,EAAC;UAC/C/W,EAAA,CAAAiC,UAAA,KAAA4hB,kDAAA,wBAAoE;UAK1E7jB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACX;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,qBAAsE;UAA3DD,EAAA,CAAAkE,gBAAA,2BAAA4f,0EAAA1f,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA3jB,EAAA,CAAAsE,kBAAA,CAAAmf,GAAA,CAAAnhB,WAAA,CAAA6D,kBAAA,EAAA/B,MAAA,MAAAqf,GAAA,CAAAnhB,WAAA,CAAA6D,kBAAA,GAAA/B,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA4C;UACrDpE,EAAA,CAAAiC,UAAA,KAAA8hB,kDAAA,wBAAgE;UAKtE/jB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAsB,eAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,eAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACiE;UAAhCD,EAAA,CAAAkE,gBAAA,2BAAA8f,sEAAA5f,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA3jB,EAAA,CAAAsE,kBAAA,CAAAmf,GAAA,CAAAnhB,WAAA,CAAAqR,KAAA,EAAAvP,MAAA,MAAAqf,GAAA,CAAAnhB,WAAA,CAAAqR,KAAA,GAAAvP,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA+B;UAC5FpE,EADE,CAAAG,YAAA,EAA2F,EAC7E;UAChBH,EAAA,CAAAC,cAAA,iBAA0C;UAAAD,EAAA,CAAAE,MAAA,UAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACuD;UAA9BD,EAAA,CAAAkE,gBAAA,2BAAA+f,sEAAA7f,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA3jB,EAAA,CAAAsE,kBAAA,CAAAmf,GAAA,CAAAnhB,WAAA,CAAAsR,GAAA,EAAAxP,MAAA,MAAAqf,GAAA,CAAAnhB,WAAA,CAAAsR,GAAA,GAAAxP,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA6B;UAGtFpE,EAHM,CAAAG,YAAA,EAAiF,EACnE,EACZ,EACF;UAENH,EAAA,CAAAyG,SAAA,eAWM;UAIFzG,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAuF;UAA3DD,EAAA,CAAAkE,gBAAA,2BAAAggB,0EAAA9f,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA3jB,EAAA,CAAAsE,kBAAA,CAAAmf,GAAA,CAAAnhB,WAAA,CAAA+Q,kBAAA,EAAAjP,MAAA,MAAAqf,GAAA,CAAAnhB,WAAA,CAAA+Q,kBAAA,GAAAjP,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA4C;UACtEpE,EAAA,CAAAiC,UAAA,KAAAkiB,kDAAA,wBAAgE;UAKtEnkB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAyF;UAA3DD,EAAA,CAAAkE,gBAAA,2BAAAkgB,0EAAAhgB,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA3jB,EAAA,CAAAsE,kBAAA,CAAAmf,GAAA,CAAAnhB,WAAA,CAAAkC,kBAAA,EAAAJ,MAAA,MAAAqf,GAAA,CAAAnhB,WAAA,CAAAkC,kBAAA,GAAAJ,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA4C;UACxEpE,EAAA,CAAAiC,UAAA,KAAAoiB,kDAAA,wBAAgE;UAKtErkB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACZ;UACzCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAsF;UAA1DD,EAAA,CAAAkE,gBAAA,2BAAAogB,0EAAAlgB,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA3jB,EAAA,CAAAsE,kBAAA,CAAAmf,GAAA,CAAAnhB,WAAA,CAAA0C,iBAAA,EAAAZ,MAAA,MAAAqf,GAAA,CAAAnhB,WAAA,CAAA0C,iBAAA,GAAAZ,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA2C;UACrEpE,EAAA,CAAAiC,UAAA,KAAAsiB,kDAAA,wBAA+D;UAKrEvkB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACd;UACvCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAqF;UAAzDD,EAAA,CAAAkE,gBAAA,2BAAAsgB,0EAAApgB,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA3jB,EAAA,CAAAsE,kBAAA,CAAAmf,GAAA,CAAAnhB,WAAA,CAAAoR,gBAAA,EAAAtP,MAAA,MAAAqf,GAAA,CAAAnhB,WAAA,CAAAoR,gBAAA,GAAAtP,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA0C;UACpEpE,EAAA,CAAAiC,UAAA,KAAAwiB,kDAAA,wBAAgE;UAKtEzkB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACV;UAC3CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAA0F;UAA5DD,EAAA,CAAAkE,gBAAA,2BAAAwgB,0EAAAtgB,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA3jB,EAAA,CAAAsE,kBAAA,CAAAmf,GAAA,CAAAnhB,WAAA,CAAAkR,mBAAA,EAAApP,MAAA,MAAAqf,GAAA,CAAAnhB,WAAA,CAAAkR,mBAAA,GAAApP,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA6C;UACzEpE,EAAA,CAAAiC,UAAA,KAAA0iB,kDAAA,wBAAiE;UAKvE3kB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACL;UAChDD,EAAA,CAAAE,MAAA,wCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAgG;UAAjED,EAAA,CAAAkE,gBAAA,2BAAA0gB,0EAAAxgB,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA3jB,EAAA,CAAAsE,kBAAA,CAAAmf,GAAA,CAAAnhB,WAAA,CAAAmR,wBAAA,EAAArP,MAAA,MAAAqf,GAAA,CAAAnhB,WAAA,CAAAmR,wBAAA,GAAArP,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAAkD;UAC/EpE,EAAA,CAAAiC,UAAA,KAAA4iB,kDAAA,wBAAsE;UAK5E7kB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAENH,EAAA,CAAAyG,SAAA,eAEM;UAKFzG,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACO;UAArBD,EAAA,CAAAiB,UAAA,mBAAA6jB,+DAAA;YAAA9kB,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA,OAAA3jB,EAAA,CAAAyB,WAAA,CAASgiB,GAAA,CAAA3P,QAAA,EAAU;UAAA,EAAC;UAC3D9T,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAyG,SAAA,aAA6B;UAGtCzG,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGJH,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAAiC,UAAA,KAAA8iB,+CAAA,qBAAmG;UAGnG/kB,EAAA,CAAAC,cAAA,kBAAqF;UAA5CD,EAAA,CAAAiB,UAAA,mBAAA+jB,+DAAA;YAAAhlB,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA,OAAA3jB,EAAA,CAAAyB,WAAA,CAASgiB,GAAA,CAAA9K,YAAA,CAAa,mBAAmB,CAAC;UAAA,EAAC;UAClF3Y,EAAA,CAAAE,MAAA,8CACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,kBAAiE;UAAxBD,EAAA,CAAAiB,UAAA,mBAAAgkB,+DAAA;YAAAjlB,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA,OAAA3jB,EAAA,CAAAyB,WAAA,CAASgiB,GAAA,CAAApP,WAAA,EAAa;UAAA,EAAC;UAC9DrU,EAAA,CAAAE,MAAA,oDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA4G;UAAzDD,EAAA,CAAAiB,UAAA,oBAAAikB,+DAAA9gB,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA,OAAA3jB,EAAA,CAAAyB,WAAA,CAAUgiB,GAAA,CAAAxO,cAAA,CAAA7Q,MAAA,CAAsB;UAAA,EAAC;UAApFpE,EAAA,CAAAG,YAAA,EAA4G;UAC5GH,EAAA,CAAAC,cAAA,kBAAiE;UAA7BD,EAAA,CAAAiB,UAAA,mBAAAkkB,+DAAA;YAAAnlB,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA,OAAA3jB,EAAA,CAAAyB,WAAA,CAASgiB,GAAA,CAAA5O,gBAAA,EAAkB;UAAA,EAAC;UAC9D7U,EAAA,CAAAE,MAAA,gEACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAOEH,EALR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cAErB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,uCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;UACRH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAiC,UAAA,MAAAmjB,4CAAA,mBAAmD;UA8C3DplB,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,2BAAsD,2BAES;UAD7CD,EAAA,CAAAkE,gBAAA,wBAAAmhB,6EAAAjhB,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA3jB,EAAA,CAAAsE,kBAAA,CAAAmf,GAAA,CAAAxS,SAAA,EAAA7M,MAAA,MAAAqf,GAAA,CAAAxS,SAAA,GAAA7M,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAAoB;UAClCpE,EAAA,CAAAiB,UAAA,wBAAAokB,6EAAAjhB,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAwiB,GAAA;YAAA,OAAA3jB,EAAA,CAAAyB,WAAA,CAAcgiB,GAAA,CAAAtP,WAAA,CAAA/P,MAAA,CAAmB;UAAA,EAAC;UAGxCpE,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UA6UVH,EA3UA,CAAAiC,UAAA,MAAAqjB,qDAAA,gCAAAtlB,EAAA,CAAAulB,sBAAA,CAAmE,MAAAC,qDAAA,iCAAAxlB,EAAA,CAAAulB,sBAAA,CAsIF,MAAAE,qDAAA,kCAAAzlB,EAAA,CAAAulB,sBAAA,CA+BJ,MAAAG,qDAAA,iCAAA1lB,EAAA,CAAAulB,sBAAA,CAsKK;;;UAljB5BvlB,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAA0E,gBAAA,YAAA+e,GAAA,CAAAnhB,WAAA,CAAAC,kBAAA,CAA4C;UAE1CvC,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAqjB,GAAA,CAAA3c,oBAAA,CAAuB;UAS1C9G,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAA0E,gBAAA,YAAA+e,GAAA,CAAAnhB,WAAA,CAAA6D,kBAAA,CAA4C;UACzBnG,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAqjB,GAAA,CAAA1c,gBAAA,CAAmB;UAYY/G,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAA0E,gBAAA,YAAA+e,GAAA,CAAAnhB,WAAA,CAAAqR,KAAA,CAA+B;UAKvC3T,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAA0E,gBAAA,YAAA+e,GAAA,CAAAnhB,WAAA,CAAAsR,GAAA,CAA6B;UAuBtD5T,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAA0E,gBAAA,YAAA+e,GAAA,CAAAnhB,WAAA,CAAA+Q,kBAAA,CAA4C;UAC1CrT,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAqjB,GAAA,CAAAjS,gBAAA,CAAmB;UAYnBxR,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAA0E,gBAAA,YAAA+e,GAAA,CAAAnhB,WAAA,CAAAkC,kBAAA,CAA4C;UAC5CxE,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAqjB,GAAA,CAAA7e,gBAAA,CAAmB;UAYrB5E,EAAA,CAAAM,SAAA,GAA2C;UAA3CN,EAAA,CAAA0E,gBAAA,YAAA+e,GAAA,CAAAnhB,WAAA,CAAA0C,iBAAA,CAA2C;UACzChF,EAAA,CAAAM,SAAA,EAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAAqjB,GAAA,CAAAve,eAAA,CAAkB;UAWpBlF,EAAA,CAAAM,SAAA,GAA0C;UAA1CN,EAAA,CAAA0E,gBAAA,YAAA+e,GAAA,CAAAnhB,WAAA,CAAAoR,gBAAA,CAA0C;UACxC1T,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAqjB,GAAA,CAAAnS,gBAAA,CAAmB;UAYnBtR,EAAA,CAAAM,SAAA,GAA6C;UAA7CN,EAAA,CAAA0E,gBAAA,YAAA+e,GAAA,CAAAnhB,WAAA,CAAAkR,mBAAA,CAA6C;UAC7CxT,EAAA,CAAAM,SAAA,EAAoB;UAApBN,EAAA,CAAAI,UAAA,YAAAqjB,GAAA,CAAAhS,iBAAA,CAAoB;UAYnBzR,EAAA,CAAAM,SAAA,GAAkD;UAAlDN,EAAA,CAAA0E,gBAAA,YAAA+e,GAAA,CAAAnhB,WAAA,CAAAmR,wBAAA,CAAkD;UACnDzT,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAAqjB,GAAA,CAAA/R,sBAAA,CAAyB;UAsBb1R,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,SAAAqjB,GAAA,CAAA3b,QAAA,CAAc;UAsCnC9H,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAI,UAAA,YAAAqjB,GAAA,CAAA5M,SAAA,CAAe;UAgD1B7W,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAA0E,gBAAA,SAAA+e,GAAA,CAAAxS,SAAA,CAAoB;UAAuBjR,EAAtB,CAAAI,UAAA,aAAAqjB,GAAA,CAAAzS,QAAA,CAAqB,mBAAAyS,GAAA,CAAAvS,YAAA,CAAgC;;;qBD/KlFnS,YAAY,EAAA4mB,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,IAAA,EAAE/mB,YAAY,EAAAgnB,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,kBAAA,EAAAJ,GAAA,CAAAK,YAAA,EAAAL,GAAA,CAAAM,YAAA,EAAAN,GAAA,CAAAO,OAAA,EAAArE,EAAA,CAAAsE,eAAA,EAAAtE,EAAA,CAAAuE,mBAAA,EAAAvE,EAAA,CAAAwE,qBAAA,EAAAxE,EAAA,CAAAyE,qBAAA,EAAAzE,EAAA,CAAA0E,mBAAA,EAAA1E,EAAA,CAAA2E,gBAAA,EAAA3E,EAAA,CAAA4E,iBAAA,EAAA5E,EAAA,CAAA6E,iBAAA,EAAA7E,EAAA,CAAA8E,oBAAA,EAAA9E,EAAA,CAAA+E,iBAAA,EAAA/E,EAAA,CAAAgF,eAAA,EAAAhF,EAAA,CAAAiF,qBAAA,EAAAjF,EAAA,CAAAkF,qBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAEvoB,kBAAkB,EAAEI,mBAAmB;MAAAooB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}