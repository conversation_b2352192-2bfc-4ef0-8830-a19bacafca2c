{"ast": null, "code": "import { FooterComponent } from '../../components/footer/footer.component';\nimport { HeaderComponent } from '../../components/header/header.component';\nimport { NbLayoutModule, NbSidebarModule } from '@nebular/theme';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nconst _c0 = [[[\"nb-menu\"]], [[\"router-outlet\"]]];\nconst _c1 = [\"nb-menu\", \"router-outlet\"];\nexport let OneColumnLayoutComponent = /*#__PURE__*/(() => {\n  class OneColumnLayoutComponent {\n    static {\n      this.ɵfac = function OneColumnLayoutComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || OneColumnLayoutComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: OneColumnLayoutComponent,\n        selectors: [[\"ngx-one-column-layout\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c1,\n        decls: 9,\n        vars: 0,\n        consts: [[\"windowMode\", \"\"], [\"fixed\", \"\"], [\"tag\", \"menu-sidebar\", \"responsive\", \"\", 1, \"menu-sidebar\"]],\n        template: function OneColumnLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c0);\n            i0.ɵɵelementStart(0, \"nb-layout\", 0)(1, \"nb-layout-header\", 1);\n            i0.ɵɵelement(2, \"ngx-header\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-sidebar\", 2);\n            i0.ɵɵprojection(4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"nb-layout-column\");\n            i0.ɵɵprojection(6, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"nb-layout-footer\", 1);\n            i0.ɵɵelement(8, \"ngx-footer\");\n            i0.ɵɵelementEnd()();\n          }\n        },\n        dependencies: [NbLayoutModule, i1.NbLayoutComponent, i1.NbLayoutColumnComponent, i1.NbLayoutFooterComponent, i1.NbLayoutHeaderComponent, HeaderComponent, NbSidebarModule, i1.NbSidebarComponent, FooterComponent, RouterModule],\n        styles: [\"\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n[_nghost-%COMP%]   .menu-sidebar[_ngcontent-%COMP%]     .scrollable{padding-top:var(--layout-padding-top)}\"]\n      });\n    }\n  }\n  return OneColumnLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}