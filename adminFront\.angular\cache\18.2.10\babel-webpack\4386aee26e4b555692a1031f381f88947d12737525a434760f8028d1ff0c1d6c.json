{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nfunction BuildingMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction BuildingMaterialComponent_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.exportExelMaterialList());\n    });\n    i0.ɵɵtext(1, \"\\u532F\\u51FA \");\n    i0.ɵɵelement(2, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.search());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(61);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u55AE\\u7B46\\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext();\n      const inputFile_r9 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(inputFile_r9.click());\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_th_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CSelectPictureId.length);\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CPrice);\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_57_tr_1_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r7 = i0.ɵɵreference(61);\n      return i0.ɵɵresetView(ctx_r3.onSelectedMaterial(item_r10, dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_57_tr_1_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imageBinder_r13 = i0.ɵɵreference(63);\n      return i0.ɵɵresetView(ctx_r3.bindImageForMaterial(item_r10, imageBinder_r13));\n    });\n    i0.ɵɵelement(1, \"i\", 55);\n    i0.ɵɵtext(2, \" \\u7D81\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"title\", \"\\u70BA \" + item_r10.CName + \" \\u7D81\\u5B9A\\u5716\\u7247\");\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"div\", 44);\n    i0.ɵɵtemplate(13, BuildingMaterialComponent_tbody_57_tr_1_span_13_Template, 2, 1, \"span\", 45)(14, BuildingMaterialComponent_tbody_57_tr_1_span_14_Template, 2, 0, \"span\", 46);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(17, BuildingMaterialComponent_tbody_57_tr_1_td_17_Template, 2, 1, \"td\", 33);\n    i0.ɵɵelementStart(18, \"td\")(19, \"span\", 47);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"td\", 48);\n    i0.ɵɵtemplate(22, BuildingMaterialComponent_tbody_57_tr_1_button_22_Template, 2, 0, \"button\", 49)(23, BuildingMaterialComponent_tbody_57_tr_1_button_23_Template, 3, 1, \"button\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CImageCode || \"\\u5F85\\u8A2D\\u5B9A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", item_r10.CName, \" - \", item_r10.CPart, \" - \", item_r10.CLocation, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(!item_r10.CIsMapping ? \"color: red\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CDescription);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r10.CSelectPictureId && item_r10.CSelectPictureId.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r10.CSelectPictureId || item_r10.CSelectPictureId.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CSelectPictureId && item_r10.CSelectPictureId.length > 0 ? \"\\u5DF2\\u7D81\\u5B9A\" : \"\\u672A\\u7D81\\u5B9A\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.CShowPrice == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(item_r10.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getStatusLabel(item_r10.CStatus || 0), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\");\n    i0.ɵɵtemplate(1, BuildingMaterialComponent_tbody_57_tr_1_Template, 24, 18, \"tr\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.materialList);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_60_nb_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r15.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r15.label, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_60_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"i\", 78);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u8A2D\\u5B9A\\u5EFA\\u6750\\u4EE3\\u865F: \", ctx_r3.selectedMaterial.CImageCode, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 56)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u5EFA\\u6750\\u7BA1\\u7406 > \\u65B0\\u589E\\u5EFA\\u6750 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 57)(4, \"h5\", 58);\n    i0.ɵɵtext(5, \"\\u8ACB\\u8F38\\u5165\\u4E0B\\u65B9\\u5167\\u5BB9\\u65B0\\u589E\\u5EFA\\u6750\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 59)(7, \"div\", 60)(8, \"label\", 61);\n    i0.ɵɵtext(9, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 62);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CImageCode, $event) || (ctx_r3.selectedMaterial.CImageCode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 63)(12, \"label\", 61);\n    i0.ɵɵtext(13, \"\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CName, $event) || (ctx_r3.selectedMaterial.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 63)(16, \"label\", 61);\n    i0.ɵɵtext(17, \"\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPart, $event) || (ctx_r3.selectedMaterial.CPart = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 63)(20, \"label\", 61);\n    i0.ɵɵtext(21, \"\\u4F4D\\u7F6E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CLocation, $event) || (ctx_r3.selectedMaterial.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 63)(24, \"label\", 61);\n    i0.ɵɵtext(25, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CSelectName, $event) || (ctx_r3.selectedMaterial.CSelectName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 63)(28, \"label\", 66);\n    i0.ɵɵtext(29, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"textarea\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_textarea_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CDescription, $event) || (ctx_r3.selectedMaterial.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 63)(32, \"label\", 66);\n    i0.ɵɵtext(33, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"input\", 68);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPrice, $event) || (ctx_r3.selectedMaterial.CPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 63)(36, \"label\", 61);\n    i0.ɵɵtext(37, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"nb-select\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_nb_select_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CStatus, $event) || (ctx_r3.selectedMaterial.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(39, BuildingMaterialComponent_ng_template_60_nb_option_39_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 70)(41, \"label\", 66);\n    i0.ɵɵtext(42, \"\\u5716\\u7247\\u7D81\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 71)(44, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_60_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const imageBinder_r13 = i0.ɵɵreference(63);\n      return i0.ɵɵresetView(ctx_r3.openImageBinder(imageBinder_r13));\n    });\n    i0.ɵɵelement(45, \"i\", 73);\n    i0.ɵɵtext(46, \"\\u9078\\u64C7\\u5716\\u7247 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, BuildingMaterialComponent_ng_template_60_div_47_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(48, \"nb-card-footer\", 34)(49, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_60_Template_button_click_49_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClose(ref_r16));\n    });\n    i0.ɵɵtext(50, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_60_Template_button_click_51_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSubmit(ref_r16));\n    });\n    i0.ɵɵtext(52, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CImageCode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPart);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CSelectName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CDescription);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPrice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.statusOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"title\", \"\\u70BA\\u5EFA\\u6750\\u7D81\\u5B9A\\u5716\\u7247\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedMaterial.CImageCode);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_50_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 141);\n  }\n  if (rf & 2) {\n    const image_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", image_r19.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"alt\", image_r19.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_50_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142);\n    i0.ɵɵelement(1, \"i\", 143);\n    i0.ɵɵelementStart(2, \"div\", 144);\n    i0.ɵɵtext(3, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 130);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_50_Template_div_click_0_listener() {\n      const image_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.moveToSelected(image_r19));\n    });\n    i0.ɵɵelementStart(1, \"div\", 131);\n    i0.ɵɵtemplate(2, BuildingMaterialComponent_ng_template_62_div_50_img_2_Template, 1, 2, \"img\", 132)(3, BuildingMaterialComponent_ng_template_62_div_50_div_3_Template, 4, 0, \"div\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 134)(5, \"div\", 135);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 136)(8, \"button\", 137);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_50_Template_button_click_8_listener($event) {\n      const image_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imagePreview_r20 = i0.ɵɵreference(65);\n      return i0.ɵɵresetView(ctx_r3.previewImage(image_r19, imagePreview_r20, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 139);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_50_Template_button_click_10_listener($event) {\n      const image_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.moveToSelected(image_r19, $event));\n    });\n    i0.ɵɵelement(11, \"i\", 140);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r19 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", image_r19.thumbnailUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !image_r19.thumbnailUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r19.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r19.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 145);\n    i0.ɵɵelement(1, \"i\", 146);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u627E\\u4E0D\\u5230\\u53EF\\u9078\\u64C7\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_73_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 154);\n    i0.ɵɵtext(1, \" \\u5DF2\\u7D81\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_73_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 155);\n    i0.ɵɵtext(1, \" \\u65B0\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_73_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 141);\n  }\n  if (rf & 2) {\n    const image_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", image_r22.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"alt\", image_r22.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_73_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142);\n    i0.ɵɵelement(1, \"i\", 143);\n    i0.ɵɵelementStart(2, \"div\", 144);\n    i0.ɵɵtext(3, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"div\", 148);\n    i0.ɵɵtemplate(2, BuildingMaterialComponent_ng_template_62_div_73_div_2_Template, 2, 0, \"div\", 149)(3, BuildingMaterialComponent_ng_template_62_div_73_div_3_Template, 2, 0, \"div\", 150);\n    i0.ɵɵelementStart(4, \"small\", 151);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 131);\n    i0.ɵɵtemplate(7, BuildingMaterialComponent_ng_template_62_div_73_img_7_Template, 1, 2, \"img\", 132)(8, BuildingMaterialComponent_ng_template_62_div_73_div_8_Template, 4, 0, \"div\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 134)(10, \"div\", 135);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 136)(13, \"button\", 137);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_73_Template_button_click_13_listener($event) {\n      const image_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imagePreview_r20 = i0.ɵɵreference(65);\n      return i0.ɵɵresetView(ctx_r3.previewImage(image_r22, imagePreview_r20, $event));\n    });\n    i0.ɵɵelement(14, \"i\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 152);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_73_Template_button_click_15_listener($event) {\n      const image_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.moveToAvailable(image_r22, $event));\n    });\n    i0.ɵɵelement(16, \"i\", 153);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r22 = ctx.$implicit;\n    const i_r23 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"border-success\", ctx_r3.isImageBound(image_r22))(\"bg-green-50\", ctx_r3.isImageBound(image_r22));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isImageBound(image_r22));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isImageBound(image_r22));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"#\", i_r23 + 1, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", image_r22.thumbnailUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !image_r22.thumbnailUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r22.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r22.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 145);\n    i0.ɵɵelement(1, \"i\", 156);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u5C1A\\u672A\\u9078\\u64C7\\u4EFB\\u4F55\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 157);\n    i0.ɵɵtext(5, \"\\u5F9E\\u5DE6\\u5074\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 158);\n    i0.ɵɵelement(1, \"i\", 159);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getBoundImagesCount(), \" \\u5F35\\u5DF2\\u7D81\\u5B9A \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 160);\n    i0.ɵɵelement(1, \"i\", 161);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getNewSelectedCount(), \" \\u5F35\\u65B0\\u9078\\u64C7 \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 79)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 80)(4, \"div\", 81)(5, \"div\", 82);\n    i0.ɵɵelement(6, \"i\", 83);\n    i0.ɵɵelementStart(7, \"div\", 84)(8, \"div\", 85);\n    i0.ɵɵtext(9, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 86)(11, \"span\", 87);\n    i0.ɵɵtext(12, \"\\u7576\\u524D\\u5EFA\\u6750\\u4EE3\\u865F\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 88);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\");\n    i0.ɵɵtext(16, \"\\u9078\\u64C7\\u5716\\u7247\\u5F8C\\uFF0C\\u5EFA\\u6750\\u4EE3\\u865F\\u5C07\\u6703\\u81EA\\u52D5\\u8A2D\\u5B9A\\u70BA\\u6240\\u9078\\u5716\\u7247\\u7684\\u6A94\\u540D\\uFF0C\\u4E26\\u5EFA\\u7ACB\\u5716\\u7247\\u8207\\u5EFA\\u6750\\u7684\\u7D81\\u5B9A\\u95DC\\u4FC2\\u3002\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 89)(18, \"div\", 90)(19, \"label\", 91);\n    i0.ɵɵtext(20, \"\\u5DF2\\u9078\\u64C7\\u6578\\u91CF\\u7D71\\u8A08\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 92)(22, \"div\", 93)(23, \"span\", 94);\n    i0.ɵɵtext(24, \"\\u5EFA\\u6750\\u5716\\u7247:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 95);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 96)(28, \"span\", 94);\n    i0.ɵɵtext(29, \"\\u793A\\u610F\\u5716\\u7247:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 97);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"div\", 98)(33, \"label\", 91);\n    i0.ɵɵtext(34, \"\\u641C\\u5C0B\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"input\", 99);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_62_Template_input_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageSearchTerm, $event) || (ctx_r3.imageSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function BuildingMaterialComponent_ng_template_62_Template_input_input_35_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.filterAvailableImages());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 100)(37, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.loadAllImages());\n    });\n    i0.ɵɵtext(38, \" \\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelement(39, \"i\", 102);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 103)(41, \"div\", 104)(42, \"div\", 105)(43, \"h6\", 106);\n    i0.ɵɵelement(44, \"i\", 107);\n    i0.ɵɵtext(45, \"\\u5EFA\\u6750\\u5716\\u7247 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 108);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 109)(49, \"div\", 110);\n    i0.ɵɵtemplate(50, BuildingMaterialComponent_ng_template_62_div_50_Template, 12, 4, \"div\", 111);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(51, BuildingMaterialComponent_ng_template_62_div_51_Template, 4, 0, \"div\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 113)(53, \"ngx-pagination\", 35);\n    i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_CollectionSizeChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageTotalRecords, $event) || (ctx_r3.imageTotalRecords = $event);\n      return i0.ɵɵresetView($event);\n    })(\"PageSizeChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageSizeChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imagePageSize, $event) || (ctx_r3.imagePageSize = $event);\n      return i0.ɵɵresetView($event);\n    })(\"PageChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageCurrentPage, $event) || (ctx_r3.imageCurrentPage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.imagePageChanged($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"div\", 114)(55, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_55_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.moveAllToSelected());\n    });\n    i0.ɵɵelement(56, \"i\", 116);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"button\", 117);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_57_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.moveAllToAvailable());\n    });\n    i0.ɵɵelement(58, \"i\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"hr\", 119);\n    i0.ɵɵelementStart(60, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_60_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.clearAllSelection());\n    });\n    i0.ɵɵelement(61, \"i\", 121)(62, \"br\");\n    i0.ɵɵelementStart(63, \"small\");\n    i0.ɵɵtext(64, \"\\u6E05\\u9664\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(65, \"div\", 104)(66, \"div\", 105)(67, \"h6\", 122);\n    i0.ɵɵtext(68, \"\\u5DF2\\u9078\\u64C7\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 108);\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 109)(72, \"div\", 110);\n    i0.ɵɵtemplate(73, BuildingMaterialComponent_ng_template_62_div_73_Template, 17, 11, \"div\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(74, BuildingMaterialComponent_ng_template_62_div_74_Template, 6, 0, \"div\", 112);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(75, \"nb-card-footer\", 124)(76, \"div\", 108);\n    i0.ɵɵtemplate(77, BuildingMaterialComponent_ng_template_62_span_77_Template, 3, 1, \"span\", 125)(78, BuildingMaterialComponent_ng_template_62_span_78_Template, 3, 1, \"span\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(79, \"div\", 127)(80, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_80_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCloseImageBinder(ref_r24));\n    });\n    i0.ɵɵtext(81, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_82_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onConfirmImageSelection(ref_r24));\n    });\n    i0.ɵɵtext(83);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5716\\u7247\\u7D81\\u5B9A - \", ctx_r3.selectedMaterial.CName ? \"\\u70BA \" + ctx_r3.selectedMaterial.CName + \" \\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\" : \"\\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\", \" \");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r3.selectedMaterial.CImageCode || \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getSelectedCountByCategory(ctx_r3.PictureCategory.BUILDING_MATERIAL), \" \\u5F35\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getSelectedCountByCategory(ctx_r3.PictureCategory.SCHEMATIC), \" \\u5F35\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.imageSearchTerm);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078: \", ctx_r3.getSelectedCountByCategory(ctx_r3.PictureCategory.BUILDING_MATERIAL), \" \\u5F35 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.availableImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.availableImages.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx_r3.imageTotalRecords)(\"PageSize\", ctx_r3.imagePageSize)(\"Page\", ctx_r3.imageCurrentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.availableImages.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u53D6: \", ctx_r3.selectedImages.length, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getBoundImagesCount() > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getNewSelectedCount() > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r3.selectedImages.length, \") \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_64_img_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 169);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.previewingImage.fullUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r3.previewingImage.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_64_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142);\n    i0.ɵɵelement(1, \"i\", 156);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u5716\\u7247\\u8F09\\u5165\\u5931\\u6557\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 162)(1, \"nb-card-header\", 124)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 127)(5, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.previousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 164);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.nextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 165);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 166);\n    i0.ɵɵtemplate(12, BuildingMaterialComponent_ng_template_64_img_12_Template, 1, 2, \"img\", 167)(13, BuildingMaterialComponent_ng_template_64_div_13_Template, 4, 0, \"div\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 124)(15, \"div\", 108);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 127)(18, \"button\", 168);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleImageSelectionInPreview());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_20_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r25).dialogRef;\n      return i0.ɵɵresetView(ref_r26.close());\n    });\n    i0.ɵɵtext(21, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r3.previewingImage == null ? null : ctx_r3.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex >= ctx_r3.allImages.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.previewingImage && ctx_r3.previewingImage.fullUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.previewingImage || !ctx_r3.previewingImage.fullUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.currentPreviewIndex + 1, \" / \", ctx_r3.allImages.length, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.previewingImage && ctx_r3.isImageSelected(ctx_r3.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 170)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3, \" \\u6AA2\\u8996 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 171)(5, \"div\", 172);\n    i0.ɵɵelement(6, \"img\", 173);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-footer\")(8, \"div\", 174)(9, \"button\", 175);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_66_Template_button_click_9_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r27).dialogRef;\n      return i0.ɵɵresetView(ref_r28.close());\n    });\n    i0.ɵɵtext(10, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r3.currentImageShowing, i0.ɵɵsanitizeUrl);\n  }\n}\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nexport class BuildingMaterialComponent extends BaseComponent {\n  // 根據狀態值獲取狀態標籤\n  getStatusLabel(status) {\n    const option = this.statusOptions.find(opt => opt.value === status);\n    return option ? option.label : '未設定';\n  }\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService, _pictureService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._materialService = _materialService;\n    this._utilityService = _utilityService;\n    this._pictureService = _pictureService;\n    this.isNew = true;\n    this.listBuildCases = [];\n    this.materialOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: false,\n      label: '方案'\n    }, {\n      value: true,\n      label: '選樣'\n    }];\n    this.materialOptionsId = null;\n    this.CSelectName = \"\";\n    // 移除圖片檔名相關欄位\n    // CImageCode: string = \"\"\n    // CInfoImageCode: string = \"\"\n    // 啟用建材代號欄位\n    this.CImageCode = \"\";\n    this.ShowPrice = false;\n    this.currentImageShowing = \"\";\n    this.filterMapping = false;\n    this.CIsMapping = true;\n    // 圖片綁定相關屬性 - 重構為雙區塊模式\n    this.buildingMaterialImages = []; // 建材圖片\n    this.schematicImages = []; // 示意圖片\n    this.allSelectedImages = []; // 所有已選擇的圖片（跨類別）\n    this.boundImageIds = []; // 已綁定的圖片ID\n    this.imageSearchTerm = \"\";\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n    // 分頁相關\n    this.buildingMaterialCurrentPage = 1;\n    this.buildingMaterialPageSize = 50;\n    this.buildingMaterialTotalRecords = 0;\n    this.schematicCurrentPage = 1;\n    this.schematicPageSize = 50;\n    this.schematicTotalRecords = 0;\n    // 圖片綁定分頁屬性\n    this.imageCurrentPage = 1;\n    this.imagePageSize = 50;\n    this.imageTotalRecords = 0;\n    // 類別選項\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = PictureCategory.BUILDING_MATERIAL;\n    this.isCategorySelected = true; // 預設選擇建材圖片\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory;\n    // 狀態選項\n    this.statusOptions = [{\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries?.length ? res.Entries : [];\n        this.selectedBuildCaseId = this.listBuildCases[0].cID;\n      }\n    }), mergeMap(() => this.getMaterialList())).subscribe();\n  }\n  getMaterialList(pageIndex = 1) {\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPlanUse: this.materialOptionsId,\n        CSelectName: this.CSelectName,\n        // 啟用建材代號查詢條件\n        CImageCode: this.CImageCode,\n        // CInfoImageCode: this.CInfoImageCode,\n        PageSize: this.pageSize,\n        PageIndex: pageIndex,\n        CIsMapping: this.CIsMapping\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.materialList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n        if (this.materialList.length > 0) {\n          this.ShowPrice = this.materialList[0].CShowPrice;\n        }\n      }\n    }));\n  }\n  search() {\n    this.getMaterialList().subscribe();\n  }\n  pageChanged(pageIndex) {\n    this.getMaterialList(pageIndex).subscribe();\n  }\n  exportExelMaterialList() {\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  exportExelMaterialTemplate() {\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedMaterial = {\n      CStatus: 1 // 預設為啟用狀態\n    };\n    this.dialogService.open(ref);\n  }\n  onSelectedMaterial(data, ref) {\n    this.isNew = false;\n    this.selectedMaterial = {\n      ...data\n    };\n    this.dialogService.open(ref);\n  }\n  bindImageForMaterial(data, ref) {\n    this.selectedMaterial = {\n      ...data\n    };\n    // 設定已綁定的圖片ID\n    this.boundImageIds = this.selectedMaterial.CSelectPictureId ? this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id) : [];\n    // 重置選擇狀態和分頁\n    this.allSelectedImages = []; // 重置全域已選擇圖片列表\n    this.buildingMaterialCurrentPage = 1;\n    this.schematicCurrentPage = 1;\n    this.imageSearchTerm = \"\";\n    this.loadAllImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[名稱]', this.selectedMaterial.CName);\n    this.valid.required('[項目]', this.selectedMaterial.CPart);\n    this.valid.required('[位置]', this.selectedMaterial.CLocation);\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n    // 啟用建材代號驗證\n    this.valid.required('[建材代號]', this.selectedMaterial.CImageCode);\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus);\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30);\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30);\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30);\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n    // 啟用建材代號長度驗證\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CImageCode, 30);\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        // 暫時保留 CImageCode 給圖片綁定功能使用\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  detectFileExcel(event) {\n    const target = event.target;\n    const reader = new FileReader();\n    reader.readAsBinaryString(target.files[0]);\n    reader.onload = e => {\n      const binarystr = e.target.result;\n      const wb = XLSX.read(binarystr, {\n        type: 'binary'\n      });\n      const wsname = wb.SheetNames[0];\n      const ws = wb.Sheets[wsname];\n      let isValidFile = true;\n      const data = XLSX.utils.sheet_to_json(ws);\n      if (data && data.length > 0) {\n        data.forEach(x => {\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'])) {\n            isValidFile = false;\n          }\n        });\n        if (!isValidFile) {\n          this.message.showErrorMSG(\"导入文件时出现错误\");\n        } else {\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n            body: {\n              CBuildCaseId: this.selectedBuildCaseId,\n              CFile: target.files[0]\n            }\n          }).pipe(tap(res => {\n            if (res.StatusCode == 0) {\n              this.message.showSucessMSG(\"執行成功\");\n            } else {\n              this.message.showErrorMSG(res.Message);\n            }\n          }), mergeMap(() => this.getMaterialList(1))).subscribe();\n        }\n      } else {\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n      }\n      event.target.value = null;\n    };\n  }\n  showImage(imageUrl, dialog) {\n    this.currentImageShowing = imageUrl;\n    this.dialogService.open(dialog);\n  }\n  changeFilter() {\n    if (this.filterMapping) {\n      this.CIsMapping = false;\n      this.getMaterialList().subscribe();\n    } else {\n      this.CIsMapping = true;\n      this.getMaterialList().subscribe();\n    }\n  }\n  // 圖片綁定功能方法\n  openImageBinder(ref) {\n    // 重置選擇狀態和分頁\n    this.allSelectedImages = [];\n    this.buildingMaterialCurrentPage = 1;\n    this.schematicCurrentPage = 1;\n    this.imageSearchTerm = \"\";\n    this.loadAllImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  loadImages() {\n    // 使用 PictureService API 載入圖片列表\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\n      this._pictureService.apiPictureGetPictureListPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: this.selectedCategory,\n          PageIndex: this.imageCurrentPage,\n          PageSize: this.imagePageSize\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          // 將 API 回應轉換為 ImageItem 格式\n          this.allImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            category: this.selectedCategory // 加入當前選擇的類別\n          })) || [];\n          this.imageTotalRecords = res.TotalItems || 0;\n          // 只在第一次開啟對話框時初始化已選擇的圖片（已綁定的圖片）\n          // 分頁變更時不重置已選擇的圖片\n          if (this.selectedImages.length === 0 && this.boundImageIds.length > 0) {\n            this.loadAllImagesForInitialSelection();\n          } else {\n            // 更新可選擇的圖片（排除已選擇的）\n            this.updateAvailableImages();\n          }\n        } else {\n          this.message.showErrorMSG(res.Message || '載入圖片失敗');\n          this.allImages = [];\n          this.availableImages = [];\n          // 只在第一次載入錯誤時清空已選圖片\n          if (this.selectedImages.length === 0) {\n            this.selectedImages = [];\n          }\n        }\n      });\n    } else {\n      // 如果沒有選擇類別或建案，清空圖片列表\n      this.allImages = [];\n      this.availableImages = [];\n      this.selectedImages = [];\n    }\n  }\n  // 載入所有圖片以進行初始選擇（僅用於已綁定圖片的初始化）\n  loadAllImagesForInitialSelection() {\n    // 為了初始化已綁定的圖片，我們需要載入所有圖片\n    // 這裡使用一個較大的 PageSize 來獲取所有圖片\n    this._pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        cPictureType: this.selectedCategory,\n        PageIndex: 1,\n        PageSize: 9999 // 使用大數字獲取所有圖片\n      }\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        const allAvailableImages = res.Entries?.map(picture => ({\n          id: picture.CId || 0,\n          name: picture.CPictureCode || picture.CName || '',\n          size: 0,\n          thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n          fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n          lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n          category: this.selectedCategory // 加入當前選擇的類別\n        })) || [];\n        // 從所有圖片中找出已綁定的圖片\n        const boundImages = allAvailableImages.filter(image => this.boundImageIds.includes(image.id));\n        this.selectedImages = [...boundImages];\n        // 將已綁定的圖片加入全域已選擇圖片列表\n        boundImages.forEach(image => {\n          const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n          if (allSelectedIndex === -1) {\n            this.allSelectedImages.push(image);\n          }\n        });\n        // 更新可選擇的圖片\n        this.updateAvailableImages();\n      }\n    });\n  }\n  // 新增 picklist 相關方法\n  updateAvailableImages() {\n    // 使用當前 API 回傳的圖片作為可選圖片基礎\n    let currentPageImages = [...this.allImages];\n    // 根據搜尋條件篩選當前分頁圖片\n    if (this.imageSearchTerm.trim()) {\n      const searchTerm = this.imageSearchTerm.toLowerCase();\n      currentPageImages = currentPageImages.filter(image => image.name.toLowerCase().includes(searchTerm));\n    }\n    // 排除已選擇的圖片\n    const selectedIds = this.selectedImages.map(img => img.id);\n    this.availableImages = currentPageImages.filter(image => !selectedIds.includes(image.id));\n  }\n  filterAvailableImages() {\n    this.updateAvailableImages();\n  }\n  moveToSelected(image, event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    // 將圖片從可選移到已選\n    const index = this.availableImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.selectedImages.push(image);\n      // 同時更新全域已選擇圖片列表\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n      if (allSelectedIndex === -1) {\n        this.allSelectedImages.push(image);\n      }\n      this.updateAvailableImages();\n    }\n  }\n  moveToAvailable(image, event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    // 將圖片從已選移到可選（包括已綁定的圖片也可以取消）\n    const index = this.selectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.selectedImages.splice(index, 1);\n      // 同時從全域已選擇圖片列表中移除\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n      if (allSelectedIndex > -1) {\n        this.allSelectedImages.splice(allSelectedIndex, 1);\n      }\n      this.updateAvailableImages();\n    }\n  }\n  moveAllToSelected() {\n    // 將所有可選圖片移到已選\n    this.selectedImages.push(...this.availableImages);\n    // 同時更新全域已選擇圖片列表\n    this.availableImages.forEach(image => {\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n      if (allSelectedIndex === -1) {\n        this.allSelectedImages.push(image);\n      }\n    });\n    this.updateAvailableImages();\n  }\n  moveAllToAvailable() {\n    // 將所有已選圖片移到可選（包括已綁定的圖片）\n    // 從全域已選擇圖片列表中移除當前類別的圖片\n    this.selectedImages.forEach(image => {\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n      if (allSelectedIndex > -1) {\n        this.allSelectedImages.splice(allSelectedIndex, 1);\n      }\n    });\n    this.selectedImages = [];\n    this.updateAvailableImages();\n  }\n  isImageBound(image) {\n    return this.boundImageIds.includes(image.id);\n  }\n  getBoundImagesCount() {\n    return this.selectedImages.filter(image => this.isImageBound(image)).length;\n  }\n  getNewSelectedCount() {\n    return this.selectedImages.filter(image => !this.isImageBound(image)).length;\n  }\n  // 清除所有選擇（包括已綁定的圖片）\n  clearAllSelection() {\n    // 從全域已選擇圖片列表中移除當前類別的圖片\n    this.selectedImages.forEach(image => {\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n      if (allSelectedIndex > -1) {\n        this.allSelectedImages.splice(allSelectedIndex, 1);\n      }\n    });\n    this.selectedImages = [];\n    this.updateAvailableImages();\n  }\n  previewImage(image, imagePreviewRef, event) {\n    event.stopPropagation();\n    this.previewingImage = image;\n    // 在所有圖片中找到當前預覽圖片的索引\n    this.currentPreviewIndex = this.allImages.findIndex(img => img.id === image.id);\n    this.dialogService.open(imagePreviewRef);\n  }\n  previousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.allImages[this.currentPreviewIndex];\n    }\n  }\n  nextImage() {\n    if (this.currentPreviewIndex < this.allImages.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.allImages[this.currentPreviewIndex];\n    }\n  }\n  toggleImageSelectionInPreview() {\n    if (this.previewingImage) {\n      const isSelected = this.selectedImages.some(img => img.id === this.previewingImage.id);\n      if (isSelected) {\n        this.moveToAvailable(this.previewingImage);\n      } else {\n        this.moveToSelected(this.previewingImage);\n      }\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  onConfirmImageSelection(ref) {\n    if (this.selectedImages.length > 0) {\n      // 收集選中圖片的 ID\n      const selectedImageIds = this.selectedImages.map(img => img.id); // 如果只選取一張圖片，直接設定圖片 ID\n      if (this.selectedImages.length === 1) {\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\n      } else {\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\n      }\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\n      this.selectedMaterial.selectedImageIds = selectedImageIds;\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\n      if (this.selectedMaterial.CId) {\n        this.saveImageBinding();\n      }\n    }\n    this.clearAllSelection();\n    ref.close();\n  } // 新增方法：保存圖片綁定\n  saveImageBinding() {\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(`圖片綁定成功`);\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => {\n      // 清空選取的建材\n      this.selectedMaterial = {};\n    })).subscribe();\n  }\n  onCloseImageBinder(ref) {\n    this.clearAllSelection();\n    this.imageSearchTerm = \"\";\n    this.imageCurrentPage = 1; // 重設圖片頁碼\n    ref.close();\n  } // 類別變更處理方法\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true;\n    // 當類別變更時，從全域已選擇圖片中篩選出當前類別的圖片\n    this.selectedImages = this.allSelectedImages.filter(image => image.category === category);\n    // 當類別變更時重設頁碼並重新載入圖片\n    this.imageCurrentPage = 1;\n    if (this.selectedBuildCaseId) {\n      this.loadImages();\n    }\n  }\n  // 獲取類別標籤的方法\n  getCategoryLabel(category) {\n    const option = this.categoryOptions.find(opt => opt.value === category);\n    return option ? option.label : '未知類別';\n  }\n  // 獲取指定類別的已選擇圖片數量\n  getSelectedCountByCategory(category) {\n    return this.allSelectedImages.filter(image => image.category === category).length;\n  }\n  // 載入所有圖片（建材圖片和示意圖片）\n  loadAllImages() {\n    this.loadBuildingMaterialImages();\n    this.loadSchematicImages();\n  }\n  // 載入建材圖片\n  loadBuildingMaterialImages() {\n    if (this.selectedBuildCaseId) {\n      this._pictureService.apiPictureGetPictureListPost$Json({\n        body: {\n          PageIndex: this.buildingMaterialCurrentPage,\n          PageSize: this.buildingMaterialPageSize,\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: PictureCategory.BUILDING_MATERIAL\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.buildingMaterialImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            category: PictureCategory.BUILDING_MATERIAL\n          })) || [];\n          this.buildingMaterialTotalRecords = res.TotalItems || 0;\n        }\n      });\n    }\n  }\n  // 載入示意圖片\n  loadSchematicImages() {\n    if (this.selectedBuildCaseId) {\n      this._pictureService.apiPictureGetPictureListPost$Json({\n        body: {\n          PageIndex: this.schematicCurrentPage,\n          PageSize: this.schematicPageSize,\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: PictureCategory.SCHEMATIC\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.schematicImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            category: PictureCategory.SCHEMATIC\n          })) || [];\n          this.schematicTotalRecords = res.TotalItems || 0;\n        }\n      });\n    }\n  }\n  // 選擇圖片\n  selectImage(image) {\n    const index = this.allSelectedImages.findIndex(img => img.id === image.id);\n    if (index === -1) {\n      this.allSelectedImages.push(image);\n    }\n  }\n  // 取消選擇圖片\n  unselectImage(image) {\n    const index = this.allSelectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.allSelectedImages.splice(index, 1);\n    }\n  }\n  // 檢查圖片是否已選擇\n  isImageSelected(image) {\n    return this.allSelectedImages.some(img => img.id === image.id);\n  }\n  // 切換圖片選擇狀態\n  toggleImageSelection(image) {\n    if (this.isImageSelected(image)) {\n      this.unselectImage(image);\n    } else {\n      this.selectImage(image);\n    }\n  }\n  // 獲取已選擇的建材圖片\n  getSelectedBuildingMaterialImages() {\n    return this.allSelectedImages.filter(image => image.category === PictureCategory.BUILDING_MATERIAL);\n  }\n  // 獲取已選擇的示意圖片\n  getSelectedSchematicImages() {\n    return this.allSelectedImages.filter(image => image.category === PictureCategory.SCHEMATIC);\n  }\n  // 建材圖片分頁變更\n  buildingMaterialPageChanged(page) {\n    this.buildingMaterialCurrentPage = page;\n    this.loadBuildingMaterialImages();\n  }\n  // 示意圖片分頁變更\n  schematicPageChanged(page) {\n    this.schematicCurrentPage = page;\n    this.loadSchematicImages();\n  }\n  // 圖片分頁變更處理方法\n  imagePageChanged(page) {\n    this.imageCurrentPage = page;\n    this.loadImages();\n  }\n  static {\n    this.ɵfac = function BuildingMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildingMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.MaterialService), i0.ɵɵdirectiveInject(i6.UtilityService), i0.ɵɵdirectiveInject(i5.PictureService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BuildingMaterialComponent,\n      selectors: [[\"ngx-building-material\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 68,\n      vars: 14,\n      consts: [[\"inputFile\", \"\"], [\"dialog\", \"\"], [\"imageBinder\", \"\"], [\"imagePreview\", \"\"], [\"dialogImage\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\", \"w-[22%]\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\", \"maxlength\", \"50\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u4EE3\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"status\", \"basic\", 1, \"flex\", 2, \"flex\", \"auto\", 3, \"checkedChange\", \"change\", \"checked\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mr-2 text-white ml-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 ml-2 mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".xls, .xlsx\", 1, \"hidden\", 3, \"change\"], [1, \"btn\", \"btn-success\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-file-download\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1200px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", \"class\", \"col-1\", 4, \"ngIf\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", \"mr-2\", \"text-white\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"ml-2\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"btn\", \"btn-info\", \"mx-1\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\"], [\"class\", \"badge badge-success mr-2\", 4, \"ngIf\"], [\"class\", \"badge badge-danger mr-2\", 4, \"ngIf\"], [1, \"badge\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-info btn-sm m-1\", 3, \"title\", \"click\", 4, \"ngIf\"], [1, \"badge\", \"badge-success\", \"mr-2\"], [1, \"badge\", \"badge-danger\", \"mr-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"m-1\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\"], [1, \"w-[700px]\"], [1, \"px-4\"], [1, \"text-base\"], [1, \"w-full\", \"mt-3\"], [1, \"flex\", \"items-center\"], [1, \"required-field\", \"w-[150px]\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"20\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"mt-3\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"50\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-[150px]\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [\"type\", \"number\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"mt-4\", \"pt-3\", \"border-t\", \"border-gray-200\"], [1, \"flex\", \"gap-2\", \"w-full\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\", \"mr-2\"], [\"class\", \"text-sm text-gray-600 flex items-center\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"text-sm\", \"text-gray-600\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-check-circle\", \"text-green-500\", \"mr-2\"], [1, \"w-[90vw]\", \"max-w-[1200px]\", \"h-[85vh]\"], [1, \"px-4\", \"d-flex\", \"flex-column\", 2, \"height\", \"calc(100% - 120px)\", \"overflow\", \"hidden\", \"padding-bottom\", \"0\"], [1, \"bg-blue-50\", \"border\", \"border-blue-200\", \"rounded-lg\", \"p-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"flex\", \"items-start\", \"gap-2\"], [1, \"fas\", \"fa-info-circle\", \"text-blue-500\", \"mt-1\"], [1, \"text-sm\", \"text-blue-700\"], [1, \"font-medium\", \"mb-1\"], [1, \"mb-2\"], [1, \"font-medium\"], [1, \"bg-white\", \"px-2\", \"py-1\", \"rounded\", \"border\"], [1, \"flex\", \"gap-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"w-64\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\", \"block\"], [1, \"bg-gray-50\", \"border\", \"rounded\", \"p-2\", \"text-sm\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-1\"], [1, \"text-gray-600\"], [1, \"font-medium\", \"text-blue-600\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"font-medium\", \"text-green-600\"], [1, \"flex-1\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"w-full\", \"search-input\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"flex\", \"flex-col\", \"justify-end\"], [1, \"btn\", \"btn-info\", \"btn-image-action\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"], [1, \"flex\", \"flex-col\", \"gap-4\", \"flex-1\", 2, \"min-height\", \"0\"], [1, \"flex-1\", \"d-flex\", \"flex-column\", \"border\", \"rounded\", \"p-3\", 2, \"min-height\", \"0\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\", \"font-medium\", \"text-blue-600\"], [1, \"fas\", \"fa-hammer\", \"mr-2\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"image-preview-container\", \"flex-1\", 2, \"overflow-y\", \"auto\"], [1, \"grid\", \"grid-cols-3\", \"gap-2\"], [\"class\", \"image-grid-item border rounded p-2 cursor-pointer hover:bg-gray-50\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-gray-500 py-20\", 4, \"ngIf\"], [1, \"mt-3\", \"d-flex\", \"justify-content-center\"], [1, \"d-flex\", \"flex-column\", \"justify-content-center\", \"gap-2\", 2, \"width\", \"80px\"], [\"title\", \"\\u5168\\u90E8\\u79FB\\u81F3\\u5DF2\\u9078\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [\"title\", \"\\u5168\\u90E8\\u79FB\\u81F3\\u53EF\\u9078\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [1, \"my-2\"], [\"title\", \"\\u6E05\\u9664\\u6240\\u6709\\u9078\\u64C7\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-times\"], [1, \"mb-0\", \"font-medium\"], [\"class\", \"image-grid-item border rounded p-2 cursor-pointer\", 3, \"border-success\", \"bg-green-50\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"class\", \"text-success\", 4, \"ngIf\"], [\"class\", \"text-info ml-3\", 4, \"ngIf\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"image-grid-item\", \"border\", \"rounded\", \"p-2\", \"cursor-pointer\", \"hover:bg-gray-50\", 3, \"click\"], [1, \"w-full\", \"h-24\", \"bg-gray-100\", \"rounded\", \"mb-2\", \"flex\", \"items-center\", \"justify-center\", \"overflow-hidden\"], [\"class\", \"image-thumbnail max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-600\"], [1, \"font-medium\", \"truncate\", 3, \"title\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mt-2\"], [1, \"btn\", \"btn-outline-info\", \"btn-xs\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"btn\", \"btn-outline-primary\", \"btn-xs\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"image-thumbnail\", \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-xl\", \"mb-1\"], [1, \"text-xs\"], [1, \"text-center\", \"text-gray-500\", \"py-20\"], [1, \"fas\", \"fa-images\", \"text-4xl\", \"mb-3\"], [1, \"image-grid-item\", \"border\", \"rounded\", \"p-2\", \"cursor-pointer\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [\"class\", \"badge badge-success text-xs px-2 py-1\", \"title\", \"\\u6B64\\u5716\\u7247\\u5DF2\\u7D93\\u7D81\\u5B9A\\u5230\\u6B64\\u5EFA\\u6750\", 4, \"ngIf\"], [\"class\", \"badge badge-info text-xs px-2 py-1\", 4, \"ngIf\"], [1, \"text-gray-500\"], [1, \"btn\", \"btn-outline-danger\", \"btn-xs\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [\"title\", \"\\u6B64\\u5716\\u7247\\u5DF2\\u7D93\\u7D81\\u5B9A\\u5230\\u6B64\\u5EFA\\u6750\", 1, \"badge\", \"badge-success\", \"text-xs\", \"px-2\", \"py-1\"], [1, \"badge\", \"badge-info\", \"text-xs\", \"px-2\", \"py-1\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"text-sm\", \"mt-2\"], [1, \"text-success\"], [1, \"fas\", \"fa-check-circle\"], [1, \"text-info\", \"ml-3\"], [1, \"fas\", \"fa-plus-circle\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [2, \"padding\", \"1rem 2rem\"], [1, \"w-full\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"]],\n      template: function BuildingMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 5)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 6);\n          i0.ɵɵtext(5, \" \\u53EF\\u8A2D\\u5B9A\\u55AE\\u7B46\\u6216\\u6279\\u6B21\\u532F\\u5165\\u8A2D\\u5B9A\\u5404\\u5340\\u57DF\\u53CA\\u65B9\\u6848\\u5C0D\\u61C9\\u4E4B\\u5EFA\\u6750\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9)(9, \"label\", 10);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.search());\n          });\n          i0.ɵɵtemplate(12, BuildingMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"label\", 10);\n          i0.ɵɵtext(16, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CSelectName, $event) || (ctx.CSelectName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 8)(19, \"div\", 9)(20, \"label\", 10);\n          i0.ɵɵtext(21, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CImageCode, $event) || (ctx.CImageCode = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\", 16)(25, \"nb-checkbox\", 17);\n          i0.ɵɵtwoWayListener(\"checkedChange\", function BuildingMaterialComponent_Template_nb_checkbox_checkedChange_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.filterMapping, $event) || (ctx.filterMapping = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_nb_checkbox_change_25_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.changeFilter());\n          });\n          i0.ɵɵtext(26, \" \\u53EA\\u986F\\u793A\\u7F3A\\u5C11\\u5EFA\\u6750\\u5716\\u7247\\u6216\\u793A\\u610F\\u5716\\u7247\\u7684\\u5EFA\\u6750 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, BuildingMaterialComponent_button_27_Template, 3, 0, \"button\", 18)(28, BuildingMaterialComponent_button_28_Template, 3, 0, \"button\", 19)(29, BuildingMaterialComponent_button_29_Template, 3, 0, \"button\", 20)(30, BuildingMaterialComponent_button_30_Template, 2, 0, \"button\", 21);\n          i0.ɵɵelementStart(31, \"input\", 22, 0);\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_input_change_31_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.detectFileExcel($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_Template_button_click_33_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportExelMaterialTemplate());\n          });\n          i0.ɵɵtext(34, \"\\u4E0B\\u8F09\\u7BC4\\u4F8B\\u6A94\\u6848 \");\n          i0.ɵɵelement(35, \"i\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(36, \"div\", 25)(37, \"table\", 26)(38, \"thead\")(39, \"tr\", 27)(40, \"th\", 28);\n          i0.ɵɵtext(41, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"th\", 28);\n          i0.ɵɵtext(43, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"th\", 29);\n          i0.ɵɵtext(45, \"\\u9078\\u9805\\u984C\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"th\", 28);\n          i0.ɵɵtext(47, \"\\u9078\\u9805\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"th\", 30);\n          i0.ɵɵtext(49, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"th\", 28);\n          i0.ɵɵtext(51, \"\\u5DF2\\u7D81\\u5B9A\\u5716\\u7247\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, BuildingMaterialComponent_th_52_Template, 2, 0, \"th\", 31);\n          i0.ɵɵelementStart(53, \"th\", 28);\n          i0.ɵɵtext(54, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"th\", 32);\n          i0.ɵɵtext(56, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(57, BuildingMaterialComponent_tbody_57_Template, 2, 1, \"tbody\", 33);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"nb-card-footer\", 34)(59, \"ngx-pagination\", 35);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(60, BuildingMaterialComponent_ng_template_60_Template, 53, 12, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(62, BuildingMaterialComponent_ng_template_62_Template, 84, 21, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(64, BuildingMaterialComponent_ng_template_64_Template, 22, 8, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(66, BuildingMaterialComponent_ng_template_66_Template, 11, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCases);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CSelectName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CImageCode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"checked\", ctx.filterMapping);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelExport);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelImport);\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngIf\", ctx.ShowPrice == true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.materialList != null && ctx.materialList.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.MaxLengthValidator, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.image-table[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  cursor: pointer;\\n}\\n\\n.empty-image[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n\\n.fit-size[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  max-width: 100%;\\n  max-height: 500px;\\n  object-fit: contain;\\n}\\n\\n.image-grid-item[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.image-grid-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.image-grid-item.selected[_ngcontent-%COMP%] {\\n  border-color: #3366ff;\\n  background-color: #f0f7ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.2);\\n}\\n\\n.image-checkbox[_ngcontent-%COMP%] {\\n  border-color: #ccc;\\n  background-color: white;\\n  transition: all 0.2s ease;\\n}\\n.image-checkbox.checked[_ngcontent-%COMP%] {\\n  background-color: #3366ff;\\n  border-color: #3366ff;\\n}\\n\\n.image-thumbnail[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.image-thumbnail[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.image-preview-container[_ngcontent-%COMP%] {\\n  max-height: 480px;\\n  overflow-y: auto !important;\\n  overflow-x: hidden;\\n  \\n\\n  \\n\\n  -webkit-overflow-scrolling: touch;\\n  scrollbar-width: thin;\\n  scrollbar-color: #c1c1c1 #f1f1f1;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease-in-out;\\n}\\n.search-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3366ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.1);\\n}\\n\\n.btn-image-action[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  font-size: 12px;\\n  border-radius: 4px;\\n  transition: all 0.2s ease-in-out;\\n}\\n.btn-image-action[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n.btn-image-action.btn-xs[_ngcontent-%COMP%] {\\n  padding: 2px 6px;\\n  font-size: 10px;\\n}\\n\\n\\n\\n.d-flex.flex-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.flex-1[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n}\\n\\n\\n\\nnb-card.w-\\\\__ph-0__[_ngcontent-%COMP%]   .nb-card-body[_ngcontent-%COMP%] {\\n  height: 580px !important;\\n  overflow: hidden !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n\\n\\n\\n.flex-shrink-0[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.image-preview-container.flex-1[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  min-height: 0;\\n  height: auto;\\n  overflow-y: scroll !important;\\n  overflow-x: hidden !important;\\n}\\n\\n\\n\\n.grid.grid-cols-4[_ngcontent-%COMP%] {\\n  min-height: min-content;\\n}\\n\\n\\n\\n  nb-card-body .image-preview-container {\\n  max-height: none !important;\\n  height: 100% !important;\\n}\\n\\n\\n\\n.picklist-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  height: 100%;\\n  min-height: 400px;\\n}\\n\\n.picklist-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 1rem;\\n  background-color: #fafafa;\\n}\\n.picklist-panel[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n  color: #333;\\n  font-weight: 600;\\n}\\n\\n.picklist-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  width: 80px;\\n  gap: 0.5rem;\\n}\\n.picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.picklist-controls[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 0.5rem 0;\\n  border-color: #ddd;\\n}\\n\\n.image-grid-item.hover\\\\:bg-gray-50[_ngcontent-%COMP%]:hover {\\n  background-color: #f9f9f9;\\n}\\n.image-grid-item.border-success[_ngcontent-%COMP%] {\\n  border-color: #28a745 !important;\\n  background-color: #f8fff9;\\n}\\n.image-grid-item.bg-green-50[_ngcontent-%COMP%] {\\n  background-color: #f0fff4;\\n}\\n\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .picklist-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .picklist-controls[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    width: 100%;\\n    height: auto;\\n  }\\n  .picklist-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: auto;\\n    min-width: 60px;\\n  }\\n  .grid.grid-cols-3[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "finalize", "mergeMap", "tap", "XLSX", "SharedModule", "BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "buildCase_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵlistener", "BuildingMaterialComponent_button_27_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "exportExelMaterialList", "ɵɵelement", "BuildingMaterialComponent_button_28_Template_button_click_0_listener", "_r5", "search", "BuildingMaterialComponent_button_29_Template_button_click_0_listener", "_r6", "dialog_r7", "ɵɵreference", "addNew", "BuildingMaterialComponent_button_30_Template_button_click_0_listener", "_r8", "inputFile_r9", "click", "ɵɵtextInterpolate", "item_r10", "CSelectPictureId", "length", "CPrice", "BuildingMaterialComponent_tbody_57_tr_1_button_22_Template_button_click_0_listener", "_r11", "$implicit", "onSelectedMaterial", "BuildingMaterialComponent_tbody_57_tr_1_button_23_Template_button_click_0_listener", "_r12", "imageBinder_r13", "bindImageForMaterial", "CName", "ɵɵtemplate", "BuildingMaterialComponent_tbody_57_tr_1_span_13_Template", "BuildingMaterialComponent_tbody_57_tr_1_span_14_Template", "BuildingMaterialComponent_tbody_57_tr_1_td_17_Template", "BuildingMaterialComponent_tbody_57_tr_1_button_22_Template", "BuildingMaterialComponent_tbody_57_tr_1_button_23_Template", "CId", "CImageCode", "ɵɵtextInterpolate3", "<PERSON>art", "CLocation", "ɵɵstyleMap", "CIsMapping", "CSelectName", "CDescription", "CShowPrice", "ɵɵclassMap", "CStatus", "getStatusLabel", "isRead", "BuildingMaterialComponent_tbody_57_tr_1_Template", "materialList", "option_r15", "value", "label", "selectedMaterial", "ɵɵtwoWayListener", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_10_listener", "$event", "_r14", "ɵɵtwoWayBindingSet", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_14_listener", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_18_listener", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_26_listener", "BuildingMaterialComponent_ng_template_60_Template_textarea_ngModelChange_30_listener", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_34_listener", "BuildingMaterialComponent_ng_template_60_Template_nb_select_ngModelChange_38_listener", "BuildingMaterialComponent_ng_template_60_nb_option_39_Template", "BuildingMaterialComponent_ng_template_60_Template_button_click_44_listener", "openImageBinder", "BuildingMaterialComponent_ng_template_60_div_47_Template", "BuildingMaterialComponent_ng_template_60_Template_button_click_49_listener", "ref_r16", "dialogRef", "onClose", "BuildingMaterialComponent_ng_template_60_Template_button_click_51_listener", "onSubmit", "ɵɵtwoWayProperty", "statusOptions", "image_r19", "thumbnailUrl", "ɵɵsanitizeUrl", "name", "BuildingMaterialComponent_ng_template_62_div_50_Template_div_click_0_listener", "_r18", "moveToSelected", "BuildingMaterialComponent_ng_template_62_div_50_img_2_Template", "BuildingMaterialComponent_ng_template_62_div_50_div_3_Template", "BuildingMaterialComponent_ng_template_62_div_50_Template_button_click_8_listener", "imagePreview_r20", "previewImage", "BuildingMaterialComponent_ng_template_62_div_50_Template_button_click_10_listener", "image_r22", "BuildingMaterialComponent_ng_template_62_div_73_div_2_Template", "BuildingMaterialComponent_ng_template_62_div_73_div_3_Template", "BuildingMaterialComponent_ng_template_62_div_73_img_7_Template", "BuildingMaterialComponent_ng_template_62_div_73_div_8_Template", "BuildingMaterialComponent_ng_template_62_div_73_Template_button_click_13_listener", "_r21", "BuildingMaterialComponent_ng_template_62_div_73_Template_button_click_15_listener", "moveToAvailable", "ɵɵclassProp", "isImageBound", "i_r23", "getBoundImagesCount", "getNewSelectedCount", "BuildingMaterialComponent_ng_template_62_Template_input_ngModelChange_35_listener", "_r17", "imageSearchTerm", "BuildingMaterialComponent_ng_template_62_Template_input_input_35_listener", "filterAvailableImages", "BuildingMaterialComponent_ng_template_62_Template_button_click_37_listener", "loadAllImages", "BuildingMaterialComponent_ng_template_62_div_50_Template", "BuildingMaterialComponent_ng_template_62_div_51_Template", "BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_CollectionSizeChange_53_listener", "imageTotalRecords", "BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageSizeChange_53_listener", "imagePageSize", "BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageChange_53_listener", "imageCurrentPage", "imagePageChanged", "BuildingMaterialComponent_ng_template_62_Template_button_click_55_listener", "moveAllToSelected", "BuildingMaterialComponent_ng_template_62_Template_button_click_57_listener", "moveAllToAvailable", "BuildingMaterialComponent_ng_template_62_Template_button_click_60_listener", "clearAllSelection", "BuildingMaterialComponent_ng_template_62_div_73_Template", "BuildingMaterialComponent_ng_template_62_div_74_Template", "BuildingMaterialComponent_ng_template_62_span_77_Template", "BuildingMaterialComponent_ng_template_62_span_78_Template", "BuildingMaterialComponent_ng_template_62_Template_button_click_80_listener", "ref_r24", "onCloseImageBinder", "BuildingMaterialComponent_ng_template_62_Template_button_click_82_listener", "onConfirmImageSelection", "getSelectedCountByCategory", "PictureCategory", "BUILDING_MATERIAL", "SCHEMATIC", "availableImages", "selectedImages", "previewingImage", "fullUrl", "BuildingMaterialComponent_ng_template_64_Template_button_click_5_listener", "_r25", "previousImage", "BuildingMaterialComponent_ng_template_64_Template_button_click_8_listener", "nextImage", "BuildingMaterialComponent_ng_template_64_img_12_Template", "BuildingMaterialComponent_ng_template_64_div_13_Template", "BuildingMaterialComponent_ng_template_64_Template_button_click_18_listener", "toggleImageSelectionInPreview", "BuildingMaterialComponent_ng_template_64_Template_button_click_20_listener", "ref_r26", "close", "currentPreviewIndex", "allImages", "ɵɵtextInterpolate2", "isImageSelected", "BuildingMaterialComponent_ng_template_66_Template_button_click_9_listener", "ref_r28", "_r27", "currentImageShowing", "BuildingMaterialComponent", "status", "option", "find", "opt", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_materialService", "_utilityService", "_pictureService", "isNew", "listBuildCases", "materialOptions", "materialOptionsId", "ShowPrice", "filterMapping", "buildingMaterialImages", "schematicImages", "allSelectedImages", "boundImageIds", "buildingMaterialCurrentPage", "buildingMaterialPageSize", "buildingMaterialTotalRecords", "schematicCurrentPage", "schematicPageSize", "schematicTotalRecords", "categoryOptions", "selectedCate<PERSON><PERSON>", "isCategorySelected", "ngOnInit", "getListBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "body", "CIsPagi", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "getMaterialList", "subscribe", "pageIndex", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPlanUse", "PageSize", "pageSize", "PageIndex", "totalRecords", "TotalItems", "pageChanged", "apiMaterialExportExcelMaterialListPost$Json", "FileByte", "downloadExcelFile", "exportExelMaterialTemplate", "apiMaterialExportExcelMaterialTemplatePost$Json", "ref", "open", "data", "map", "id", "parseInt", "closeOnBackdropClick", "validation", "clear", "required", "isStringMaxLength", "errorMessages", "showErrorMSGs", "apiMaterialSaveMaterialAdminPost$Json", "CMaterialId", "CPictureId", "selectedImageIds", "showSucessMSG", "showErrorMSG", "Message", "detectFileExcel", "event", "target", "reader", "FileReader", "readAsBinaryString", "files", "onload", "e", "binarystr", "result", "wb", "read", "type", "wsname", "SheetNames", "ws", "Sheets", "isValidFile", "utils", "sheet_to_json", "for<PERSON>ach", "x", "apiMaterialImportExcelMaterialListPost$Json", "CFile", "showImage", "imageUrl", "dialog", "changeFilter", "loadImages", "apiPictureGetPictureListPost$Json", "cPictureType", "picture", "CPictureCode", "size", "CBase64", "lastModified", "CUpdateDT", "Date", "category", "loadAllImagesForInitialSelection", "updateAvailableImages", "allAvailableImages", "boundImages", "filter", "image", "includes", "allSelectedIndex", "findIndex", "img", "push", "currentPageImages", "trim", "searchTerm", "toLowerCase", "selectedIds", "stopPropagation", "index", "splice", "imagePreviewRef", "isSelected", "some", "selected", "imageNames", "join", "saveImageBinding", "categoryChanged", "getCategoryLabel", "loadBuildingMaterialImages", "loadSchematicImages", "selectImage", "unselectImage", "toggleImageSelection", "getSelectedBuildingMaterialImages", "getSelectedSchematicImages", "buildingMaterialPageChanged", "page", "schematicPageChanged", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "BuildCaseService", "MaterialService", "i6", "UtilityService", "PictureService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BuildingMaterialComponent_Template", "rf", "ctx", "BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "BuildingMaterialComponent_nb_option_12_Template", "BuildingMaterialComponent_Template_input_ngModelChange_17_listener", "BuildingMaterialComponent_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_Template_nb_checkbox_checkedChange_25_listener", "BuildingMaterialComponent_Template_nb_checkbox_change_25_listener", "BuildingMaterialComponent_button_27_Template", "BuildingMaterialComponent_button_28_Template", "BuildingMaterialComponent_button_29_Template", "BuildingMaterialComponent_button_30_Template", "BuildingMaterialComponent_Template_input_change_31_listener", "BuildingMaterialComponent_Template_button_click_33_listener", "BuildingMaterialComponent_th_52_Template", "BuildingMaterialComponent_tbody_57_Template", "BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_59_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_59_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageChange_59_listener", "BuildingMaterialComponent_ng_template_60_Template", "ɵɵtemplateRefExtractor", "BuildingMaterialComponent_ng_template_62_Template", "BuildingMaterialComponent_ng_template_64_Template", "BuildingMaterialComponent_ng_template_66_Template", "isExcelExport", "isCreate", "isExcelImport", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i8", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.html"], "sourcesContent": ["import { Component, OnInit, TemplateRef, } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse, GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';\r\nimport { finalize, mergeMap, tap } from 'rxjs';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport * as XLSX from 'xlsx';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n// 圖片項目介面\r\ninterface ImageItem {\r\n  id: number;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n  category?: PictureCategory; // 新增類別屬性\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-building-material',\r\n  templateUrl: './building-material.component.html',\r\n  styleUrls: ['./building-material.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule],\r\n})\r\n\r\nexport class BuildingMaterialComponent extends BaseComponent implements OnInit {\r\n  isNew = true\r\n\r\n  materialList: GetMaterialListResponse[]\r\n  selectedMaterial: GetMaterialListResponse\r\n\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n\r\n  materialOptions = [\r\n    {\r\n      value: null,\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: false,\r\n      label: '方案',\r\n    },\r\n    {\r\n      value: true,\r\n      label: '選樣',\r\n    }]; materialOptionsId = null;\r\n  CSelectName: string = \"\"\r\n  // 移除圖片檔名相關欄位\r\n  // CImageCode: string = \"\"\r\n  // CInfoImageCode: string = \"\"\r\n  // 啟用建材代號欄位\r\n  CImageCode: string = \"\"\r\n  ShowPrice: boolean = false\r\n  currentImageShowing: string = \"\"\r\n  filterMapping: boolean = false\r\n  CIsMapping: boolean = true\r\n  // 圖片綁定相關屬性 - 重構為雙區塊模式\r\n  buildingMaterialImages: ImageItem[] = [] // 建材圖片\r\n  schematicImages: ImageItem[] = [] // 示意圖片\r\n  allSelectedImages: ImageItem[] = [] // 所有已選擇的圖片（跨類別）\r\n  boundImageIds: number[] = [] // 已綁定的圖片ID\r\n  imageSearchTerm: string = \"\"\r\n  previewingImage: ImageItem | null = null\r\n  currentPreviewIndex: number = 0\r\n\r\n  // 分頁相關\r\n  buildingMaterialCurrentPage: number = 1\r\n  buildingMaterialPageSize: number = 50\r\n  buildingMaterialTotalRecords: number = 0\r\n\r\n  schematicCurrentPage: number = 1\r\n  schematicPageSize: number = 50\r\n  schematicTotalRecords: number = 0\r\n\r\n  // 圖片綁定分頁屬性\r\n  imageCurrentPage: number = 1\r\n  imagePageSize: number = 50\r\n  imageTotalRecords: number = 0\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n  selectedCategory: PictureCategory = PictureCategory.BUILDING_MATERIAL\r\n  isCategorySelected: boolean = true // 預設選擇建材圖片\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;\r\n\r\n  // 狀態選項\r\n  statusOptions = [{\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  }];\r\n  // 根據狀態值獲取狀態標籤\r\n  getStatusLabel(status: number): string {\r\n    const option = this.statusOptions.find(opt => opt.value === status);\r\n    return option ? option.label : '未設定';\r\n  }\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _materialService: MaterialService,\r\n    private _utilityService: UtilityService,\r\n    private _pictureService: PictureService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.listBuildCases = res.Entries?.length ? res.Entries : []\r\n            this.selectedBuildCaseId = this.listBuildCases[0].cID!\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList())\r\n      ).subscribe()\r\n  } getMaterialList(pageIndex: number = 1) {\r\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPlanUse: this.materialOptionsId,\r\n        CSelectName: this.CSelectName,\r\n        // 啟用建材代號查詢條件\r\n        CImageCode: this.CImageCode,\r\n        // CInfoImageCode: this.CInfoImageCode,\r\n        PageSize: this.pageSize,\r\n        PageIndex: pageIndex,\r\n        CIsMapping: this.CIsMapping\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.materialList = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n\r\n          if (this.materialList.length > 0) {\r\n            this.ShowPrice = this.materialList[0].CShowPrice!;\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  search() {\r\n    this.getMaterialList().subscribe()\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    this.getMaterialList(pageIndex).subscribe()\r\n  }\r\n\r\n  exportExelMaterialList() {\r\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  exportExelMaterialTemplate() {\r\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n  addNew(ref: any) {\r\n    this.isNew = true\r\n    this.selectedMaterial = {\r\n      CStatus: 1 // 預設為啟用狀態\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {\r\n    this.isNew = false\r\n    this.selectedMaterial = { ...data }\r\n    this.dialogService.open(ref)\r\n  }\r\n  bindImageForMaterial(data: GetMaterialListResponse, ref: TemplateRef<any>) {\r\n    this.selectedMaterial = { ...data }\r\n    // 設定已綁定的圖片ID\r\n    this.boundImageIds = this.selectedMaterial.CSelectPictureId ?\r\n      this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id) : []\r\n\r\n    // 重置選擇狀態和分頁\r\n    this.allSelectedImages = [] // 重置全域已選擇圖片列表\r\n    this.buildingMaterialCurrentPage = 1\r\n    this.schematicCurrentPage = 1\r\n    this.imageSearchTerm = \"\"\r\n\r\n    this.loadAllImages()\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false })\r\n  } validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[名稱]', this.selectedMaterial.CName)\r\n    this.valid.required('[項目]', this.selectedMaterial.CPart)\r\n    this.valid.required('[位置]', this.selectedMaterial.CLocation)\r\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)\r\n    // 啟用建材代號驗證\r\n    this.valid.required('[建材代號]', this.selectedMaterial.CImageCode)\r\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus)\r\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30)\r\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30)\r\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30)\r\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)\r\n    // 啟用建材代號長度驗證\r\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CImageCode, 30)\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    } this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        // 暫時保留 CImageCode 給圖片綁定功能使用\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  detectFileExcel(event: any) {\r\n    const target: DataTransfer = <DataTransfer>(event.target);\r\n    const reader: FileReader = new FileReader();\r\n    reader.readAsBinaryString(target.files[0]);\r\n    reader.onload = (e: any) => {\r\n      const binarystr: string = e.target.result;\r\n      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });\r\n\r\n      const wsname: string = wb.SheetNames[0];\r\n      const ws: XLSX.WorkSheet = wb.Sheets[wsname];\r\n\r\n      let isValidFile: boolean = true;\r\n      const data = XLSX.utils.sheet_to_json(ws);\r\n      if (data && data.length > 0) {\r\n        data.forEach((x: any) => {\r\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'])) {\r\n            isValidFile = false;\r\n          }\r\n        })\r\n\r\n        if (!isValidFile) {\r\n          this.message.showErrorMSG(\"导入文件时出现错误\")\r\n        } else {\r\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\r\n            body: {\r\n              CBuildCaseId: this.selectedBuildCaseId,\r\n              CFile: target.files[0]\r\n            }\r\n          }).pipe(\r\n            tap(res => {\r\n              if (res.StatusCode == 0) {\r\n                this.message.showSucessMSG(\"執行成功\")\r\n              } else {\r\n                this.message.showErrorMSG(res.Message!)\r\n              }\r\n            }),\r\n            mergeMap(() => this.getMaterialList(1))\r\n          ).subscribe();\r\n        }\r\n      } else {\r\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\")\r\n      }\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  showImage(imageUrl: string, dialog: TemplateRef<any>) {\r\n    this.currentImageShowing = imageUrl;\r\n    this.dialogService.open(dialog);\r\n  }\r\n  changeFilter() {\r\n    if (this.filterMapping) {\r\n      this.CIsMapping = false;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n    else {\r\n      this.CIsMapping = true;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n  }\r\n  // 圖片綁定功能方法\r\n  openImageBinder(ref: TemplateRef<any>) {\r\n    // 重置選擇狀態和分頁\r\n    this.allSelectedImages = []\r\n    this.buildingMaterialCurrentPage = 1\r\n    this.schematicCurrentPage = 1\r\n    this.imageSearchTerm = \"\"\r\n\r\n    this.loadAllImages();\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false });\r\n  }\r\n\r\n  loadImages() {\r\n    // 使用 PictureService API 載入圖片列表\r\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\r\n      this._pictureService.apiPictureGetPictureListPost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: this.selectedCategory,\r\n          PageIndex: this.imageCurrentPage,\r\n          PageSize: this.imagePageSize\r\n        }\r\n      }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          // 將 API 回應轉換為 ImageItem 格式\r\n          this.allImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || picture.CName || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            category: this.selectedCategory // 加入當前選擇的類別\r\n          })) || [];\r\n\r\n          this.imageTotalRecords = res.TotalItems || 0;\r\n\r\n          // 只在第一次開啟對話框時初始化已選擇的圖片（已綁定的圖片）\r\n          // 分頁變更時不重置已選擇的圖片\r\n          if (this.selectedImages.length === 0 && this.boundImageIds.length > 0) {\r\n            this.loadAllImagesForInitialSelection();\r\n          } else {\r\n            // 更新可選擇的圖片（排除已選擇的）\r\n            this.updateAvailableImages();\r\n          }\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入圖片失敗');\r\n          this.allImages = [];\r\n          this.availableImages = [];\r\n          // 只在第一次載入錯誤時清空已選圖片\r\n          if (this.selectedImages.length === 0) {\r\n            this.selectedImages = [];\r\n          }\r\n        }\r\n      });\r\n    } else {\r\n      // 如果沒有選擇類別或建案，清空圖片列表\r\n      this.allImages = [];\r\n      this.availableImages = [];\r\n      this.selectedImages = [];\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // 載入所有圖片以進行初始選擇（僅用於已綁定圖片的初始化）\r\n  loadAllImagesForInitialSelection() {\r\n    // 為了初始化已綁定的圖片，我們需要載入所有圖片\r\n    // 這裡使用一個較大的 PageSize 來獲取所有圖片\r\n    this._pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        cPictureType: this.selectedCategory,\r\n        PageIndex: 1,\r\n        PageSize: 9999 // 使用大數字獲取所有圖片\r\n      }\r\n    }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n      if (res.StatusCode === 0) {\r\n        const allAvailableImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n          id: picture.CId || 0,\r\n          name: picture.CPictureCode || picture.CName || '',\r\n          size: 0,\r\n          thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n          fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n          lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n          category: this.selectedCategory // 加入當前選擇的類別\r\n        })) || [];\r\n\r\n        // 從所有圖片中找出已綁定的圖片\r\n        const boundImages = allAvailableImages.filter(image => this.boundImageIds.includes(image.id));\r\n        this.selectedImages = [...boundImages];\r\n\r\n        // 將已綁定的圖片加入全域已選擇圖片列表\r\n        boundImages.forEach(image => {\r\n          const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n          if (allSelectedIndex === -1) {\r\n            this.allSelectedImages.push(image);\r\n          }\r\n        });\r\n\r\n        // 更新可選擇的圖片\r\n        this.updateAvailableImages();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增 picklist 相關方法\r\n  updateAvailableImages() {\r\n    // 使用當前 API 回傳的圖片作為可選圖片基礎\r\n    let currentPageImages = [...this.allImages];\r\n\r\n    // 根據搜尋條件篩選當前分頁圖片\r\n    if (this.imageSearchTerm.trim()) {\r\n      const searchTerm = this.imageSearchTerm.toLowerCase();\r\n      currentPageImages = currentPageImages.filter(image =>\r\n        image.name.toLowerCase().includes(searchTerm)\r\n      );\r\n    }\r\n\r\n    // 排除已選擇的圖片\r\n    const selectedIds = this.selectedImages.map(img => img.id);\r\n    this.availableImages = currentPageImages.filter(image => !selectedIds.includes(image.id));\r\n  }\r\n\r\n  filterAvailableImages() {\r\n    this.updateAvailableImages();\r\n  }\r\n\r\n  moveToSelected(image: ImageItem, event?: Event) {\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n\r\n    // 將圖片從可選移到已選\r\n    const index = this.availableImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.push(image);\r\n\r\n      // 同時更新全域已選擇圖片列表\r\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n      if (allSelectedIndex === -1) {\r\n        this.allSelectedImages.push(image);\r\n      }\r\n\r\n      this.updateAvailableImages();\r\n    }\r\n  }\r\n\r\n  moveToAvailable(image: ImageItem, event?: Event) {\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n\r\n    // 將圖片從已選移到可選（包括已綁定的圖片也可以取消）\r\n    const index = this.selectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.splice(index, 1);\r\n\r\n      // 同時從全域已選擇圖片列表中移除\r\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n      if (allSelectedIndex > -1) {\r\n        this.allSelectedImages.splice(allSelectedIndex, 1);\r\n      }\r\n\r\n      this.updateAvailableImages();\r\n    }\r\n  }\r\n\r\n  moveAllToSelected() {\r\n    // 將所有可選圖片移到已選\r\n    this.selectedImages.push(...this.availableImages);\r\n\r\n    // 同時更新全域已選擇圖片列表\r\n    this.availableImages.forEach(image => {\r\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n      if (allSelectedIndex === -1) {\r\n        this.allSelectedImages.push(image);\r\n      }\r\n    });\r\n\r\n    this.updateAvailableImages();\r\n  }\r\n\r\n  moveAllToAvailable() {\r\n    // 將所有已選圖片移到可選（包括已綁定的圖片）\r\n    // 從全域已選擇圖片列表中移除當前類別的圖片\r\n    this.selectedImages.forEach(image => {\r\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n      if (allSelectedIndex > -1) {\r\n        this.allSelectedImages.splice(allSelectedIndex, 1);\r\n      }\r\n    });\r\n\r\n    this.selectedImages = [];\r\n    this.updateAvailableImages();\r\n  }\r\n\r\n  isImageBound(image: ImageItem): boolean {\r\n    return this.boundImageIds.includes(image.id);\r\n  }\r\n\r\n  getBoundImagesCount(): number {\r\n    return this.selectedImages.filter(image => this.isImageBound(image)).length;\r\n  }\r\n\r\n  getNewSelectedCount(): number {\r\n    return this.selectedImages.filter(image => !this.isImageBound(image)).length;\r\n  }\r\n\r\n  // 清除所有選擇（包括已綁定的圖片）\r\n  clearAllSelection() {\r\n    // 從全域已選擇圖片列表中移除當前類別的圖片\r\n    this.selectedImages.forEach(image => {\r\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n      if (allSelectedIndex > -1) {\r\n        this.allSelectedImages.splice(allSelectedIndex, 1);\r\n      }\r\n    });\r\n\r\n    this.selectedImages = [];\r\n    this.updateAvailableImages();\r\n  }\r\n\r\n  previewImage(image: ImageItem, imagePreviewRef: TemplateRef<any>, event: Event) {\r\n    event.stopPropagation();\r\n    this.previewingImage = image;\r\n    // 在所有圖片中找到當前預覽圖片的索引\r\n    this.currentPreviewIndex = this.allImages.findIndex((img: ImageItem) => img.id === image.id);\r\n    this.dialogService.open(imagePreviewRef);\r\n  }\r\n\r\n  previousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.allImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  nextImage() {\r\n    if (this.currentPreviewIndex < this.allImages.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.allImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  toggleImageSelectionInPreview() {\r\n    if (this.previewingImage) {\r\n      const isSelected = this.selectedImages.some(img => img.id === this.previewingImage!.id);\r\n      if (isSelected) {\r\n        this.moveToAvailable(this.previewingImage);\r\n      } else {\r\n        this.moveToSelected(this.previewingImage);\r\n      }\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n  onConfirmImageSelection(ref: any) {\r\n    if (this.selectedImages.length > 0) {\r\n      // 收集選中圖片的 ID\r\n      const selectedImageIds = this.selectedImages.map(img => img.id);      // 如果只選取一張圖片，直接設定圖片 ID\r\n      if (this.selectedImages.length === 1) {\r\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\r\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\r\n      } else {\r\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\r\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\r\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\r\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\r\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\r\n      }\r\n\r\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\r\n      (this.selectedMaterial as any).selectedImageIds = selectedImageIds;\r\n\r\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\r\n      if (this.selectedMaterial.CId) {\r\n        this.saveImageBinding();\r\n      }\r\n    }\r\n\r\n    this.clearAllSelection();\r\n    ref.close();\r\n  }  // 新增方法：保存圖片綁定\r\n  saveImageBinding() {\r\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(`圖片綁定成功`);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      }),\r\n      mergeMap(() => this.getMaterialList()),\r\n      finalize(() => {\r\n        // 清空選取的建材\r\n        this.selectedMaterial = {};\r\n      })\r\n    ).subscribe()\r\n  }\r\n  onCloseImageBinder(ref: any) {\r\n    this.clearAllSelection();\r\n    this.imageSearchTerm = \"\";\r\n    this.imageCurrentPage = 1; // 重設圖片頁碼\r\n    ref.close();\r\n  }// 類別變更處理方法\r\n  categoryChanged(category: PictureCategory) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true;\r\n\r\n    // 當類別變更時，從全域已選擇圖片中篩選出當前類別的圖片\r\n    this.selectedImages = this.allSelectedImages.filter(image => image.category === category);\r\n\r\n    // 當類別變更時重設頁碼並重新載入圖片\r\n    this.imageCurrentPage = 1;\r\n    if (this.selectedBuildCaseId) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 獲取類別標籤的方法\r\n  getCategoryLabel(category: number): string {\r\n    const option = this.categoryOptions.find(opt => opt.value === category);\r\n    return option ? option.label : '未知類別';\r\n  }\r\n\r\n  // 獲取指定類別的已選擇圖片數量\r\n  getSelectedCountByCategory(category: PictureCategory): number {\r\n    return this.allSelectedImages.filter(image => image.category === category).length;\r\n  }\r\n\r\n  // 載入所有圖片（建材圖片和示意圖片）\r\n  loadAllImages() {\r\n    this.loadBuildingMaterialImages();\r\n    this.loadSchematicImages();\r\n  }\r\n\r\n  // 載入建材圖片\r\n  loadBuildingMaterialImages() {\r\n    if (this.selectedBuildCaseId) {\r\n      this._pictureService.apiPictureGetPictureListPost$Json({\r\n        body: {\r\n          PageIndex: this.buildingMaterialCurrentPage,\r\n          PageSize: this.buildingMaterialPageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: PictureCategory.BUILDING_MATERIAL\r\n        }\r\n      }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          this.buildingMaterialImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || picture.CName || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            category: PictureCategory.BUILDING_MATERIAL\r\n          })) || [];\r\n          this.buildingMaterialTotalRecords = res.TotalItems || 0;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // 載入示意圖片\r\n  loadSchematicImages() {\r\n    if (this.selectedBuildCaseId) {\r\n      this._pictureService.apiPictureGetPictureListPost$Json({\r\n        body: {\r\n          PageIndex: this.schematicCurrentPage,\r\n          PageSize: this.schematicPageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: PictureCategory.SCHEMATIC\r\n        }\r\n      }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          this.schematicImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || picture.CName || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            category: PictureCategory.SCHEMATIC\r\n          })) || [];\r\n          this.schematicTotalRecords = res.TotalItems || 0;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // 選擇圖片\r\n  selectImage(image: ImageItem) {\r\n    const index = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n    if (index === -1) {\r\n      this.allSelectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  // 取消選擇圖片\r\n  unselectImage(image: ImageItem) {\r\n    const index = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.allSelectedImages.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  // 檢查圖片是否已選擇\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.allSelectedImages.some(img => img.id === image.id);\r\n  }\r\n\r\n  // 切換圖片選擇狀態\r\n  toggleImageSelection(image: ImageItem) {\r\n    if (this.isImageSelected(image)) {\r\n      this.unselectImage(image);\r\n    } else {\r\n      this.selectImage(image);\r\n    }\r\n  }\r\n\r\n  // 獲取已選擇的建材圖片\r\n  getSelectedBuildingMaterialImages(): ImageItem[] {\r\n    return this.allSelectedImages.filter(image => image.category === PictureCategory.BUILDING_MATERIAL);\r\n  }\r\n\r\n  // 獲取已選擇的示意圖片\r\n  getSelectedSchematicImages(): ImageItem[] {\r\n    return this.allSelectedImages.filter(image => image.category === PictureCategory.SCHEMATIC);\r\n  }\r\n\r\n  // 建材圖片分頁變更\r\n  buildingMaterialPageChanged(page: number) {\r\n    this.buildingMaterialCurrentPage = page;\r\n    this.loadBuildingMaterialImages();\r\n  }\r\n\r\n  // 示意圖片分頁變更\r\n  schematicPageChanged(page: number) {\r\n    this.schematicCurrentPage = page;\r\n    this.loadSchematicImages();\r\n  }\r\n\r\n  // 圖片分頁變更處理方法\r\n  imagePageChanged(page: number) {\r\n    this.imageCurrentPage = page;\r\n    this.loadImages();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"> 可設定單筆或批次匯入設定各區域及方案對應之建材。\r\n    </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建案</label> <nb-select placeholder=\"建案\"\r\n            [(ngModel)]=\"selectedBuildCaseId\" (ngModelChange)=\"search()\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let buildCase of listBuildCases\" [value]=\"buildCase.cID\">\r\n              {{ buildCase.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2  w-[22%]\">建材類別</label>\r\n          <nb-select placeholder=\"建材類別\" [(ngModel)]=\"materialOptionsId\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of materialOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div> -->\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材選項名稱 </label>\r\n          <input type=\"text\" nbInput placeholder=\"建材選項名稱\" [(ngModel)]=\"CSelectName\" class=\"w-full\" maxlength=\"50\">\r\n        </div>\r\n      </div>\r\n      <!-- 啟用建材代號欄位 -->\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材代號</label>\r\n          <input type=\"text\" nbInput placeholder=\"建材代號\" [(ngModel)]=\"CImageCode\" class=\"w-full\" maxlength=\"20\">\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <nb-checkbox status=\"basic\" class=\"flex\" style=\"flex:auto\" [(checked)]=\"filterMapping\"\r\n            (change)=\"changeFilter()\">\r\n            只顯示缺少建材圖片或示意圖片的建材\r\n          </nb-checkbox>\r\n          <button *ngIf=\"isExcelExport\" class=\"btn btn-success mr-2\" (click)=\"exportExelMaterialList()\">匯出 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n          <button *ngIf=\"isRead\" class=\"btn btn-info mr-2 text-white ml-2\" (click)=\"search()\">\r\n            查詢 <i class=\"fas fa-search\"></i></button>\r\n          <button *ngIf=\"isCreate\" class=\"btn btn-info mx-1 ml-2 mr-2\" (click)=\"addNew(dialog)\">單筆新增 <i\r\n              class=\"fas fa-plus\"></i></button>\r\n          <button class=\"btn btn-info mx-1\" *ngIf=\"isExcelImport\" (click)=\"inputFile.click()\"> 批次匯入 </button>\r\n          <input class=\"hidden\" type=\"file\" accept=\".xls, .xlsx\" #inputFile (change)=\"detectFileExcel($event)\">\r\n          <button class=\"btn btn-success ml-2\" (click)=\"exportExelMaterialTemplate()\">下載範例檔案 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1200px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <th scope=\"col\" class=\"col-1\">建材代號</th>\r\n            <th scope=\"col\" class=\"col-2\">選項題目</th>\r\n            <th scope=\"col\" class=\"col-1\">選項名稱</th>\r\n            <th scope=\"col\" class=\"col-3\">建材說明</th>\r\n            <th scope=\"col\" class=\"col-1\">已綁定圖片</th>\r\n            <th scope=\"col\" class=\"col-1\" *ngIf=\"ShowPrice == true\">價格</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-1 text-center\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody *ngIf=\"materialList != null && materialList.length > 0\">\r\n          <tr *ngFor=\"let item of materialList ; let i = index\">\r\n            <td>{{ item.CId}}</td>\r\n            <td>{{ item.CImageCode || '待設定' }}</td>\r\n            <td>{{ item.CName }} - {{ item.CPart }} - {{ item.CLocation }}</td>\r\n            <td [style]=\"!item.CIsMapping ? 'color: red' : ''\">{{ item.CSelectName}}</td>\r\n            <td>{{ item.CDescription}}</td>\r\n            <td>\r\n              <div class=\"d-flex align-items-center\">\r\n                <span *ngIf=\"item.CSelectPictureId && item.CSelectPictureId.length > 0\"\r\n                  class=\"badge badge-success mr-2\">{{ item.CSelectPictureId.length }}</span>\r\n                <span *ngIf=\"!item.CSelectPictureId || item.CSelectPictureId.length === 0\"\r\n                  class=\"badge badge-danger mr-2\">0</span>\r\n                <span>{{ (item.CSelectPictureId && item.CSelectPictureId.length > 0) ? '已綁定' : '未綁定' }}</span>\r\n              </div>\r\n            </td>\r\n            <td *ngIf=\"item.CShowPrice == true\">{{ item.CPrice}}</td>\r\n            <td>\r\n              <span class=\"badge\" [class]=\"item.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                {{ getStatusLabel(item.CStatus || 0) }}\r\n              </span>\r\n            </td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"onSelectedMaterial(item, dialog)\"\r\n                *ngIf=\"isRead\">編輯</button> <button class=\"btn btn-outline-info btn-sm m-1\"\r\n                (click)=\"bindImageForMaterial(item, imageBinder)\" *ngIf=\"isRead\" [title]=\"'為 ' + item.CName + ' 綁定圖片'\">\r\n                <i class=\"fas fa-images\"></i> 綁定\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(PageSize)]=\"pageSize\" [(Page)]=\"pageIndex\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[700px]\">\r\n    <nb-card-header>\r\n      建材管理 > 新增建材\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <h5 class=\"text-base\">請輸入下方內容新增建材。</h5>\r\n      <div class=\"w-full mt-3\">\r\n        <!-- 啟用建材代號欄位 -->\r\n        <div class=\"flex items-center\">\r\n          <label class=\"required-field w-[150px]\">建材代號</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"20\"\r\n            [(ngModel)]=\"selectedMaterial.CImageCode\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">名稱</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CName\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">項目</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CPart\" />\r\n        </div>\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">位置</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CLocation\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">建材選項名稱</label>\r\n          <input type=\"text\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"50\"\r\n            [(ngModel)]=\"selectedMaterial.CSelectName\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">建材說明</label>\r\n          <textarea nbInput [(ngModel)]=\"selectedMaterial.CDescription\" [rows]=\"4\"\r\n            class=\"resize-none w-full !max-w-full p-2 rounded text-[13px]\"></textarea>\r\n        </div>\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">價格</label>\r\n          <input type=\"number\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CPrice\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">狀態</label>\r\n          <nb-select [(ngModel)]=\"selectedMaterial.CStatus\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of statusOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n        <!-- 圖片綁定按鈕 -->\r\n        <div class=\"flex items-center mt-4 pt-3 border-t border-gray-200\">\r\n          <label class=\"w-[150px]\">圖片綁定</label>\r\n          <div class=\"flex gap-2 w-full\">\r\n            <button type=\"button\" class=\"btn btn-outline-info btn-sm\" (click)=\"openImageBinder(imageBinder)\"\r\n              [title]=\"'為建材綁定圖片'\">\r\n              <i class=\"fas fa-images mr-2\"></i>選擇圖片\r\n            </button>\r\n            <div class=\"text-sm text-gray-600 flex items-center\" *ngIf=\"selectedMaterial.CImageCode\">\r\n              <i class=\"fas fa-check-circle text-green-500 mr-2\"></i>\r\n              已設定建材代號: {{ selectedMaterial.CImageCode }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">關閉</button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #imageBinder let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[90vw] max-w-[1200px] h-[85vh]\">\r\n    <nb-card-header>\r\n      圖片綁定 - {{ selectedMaterial.CName ? '為 ' + selectedMaterial.CName + ' 選擇建材圖片' : '選擇建材圖片' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4 d-flex flex-column\"\r\n      style=\"height: calc(100% - 120px); overflow: hidden; padding-bottom: 0;\">\r\n\r\n      <!-- 自動綁定說明文案 -->\r\n      <div class=\"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 flex-shrink-0\">\r\n        <div class=\"flex items-start gap-2\">\r\n          <i class=\"fas fa-info-circle text-blue-500 mt-1\"></i>\r\n          <div class=\"text-sm text-blue-700\">\r\n            <div class=\"font-medium mb-1\">建材代號</div>\r\n            <div class=\"mb-2\">\r\n              <span class=\"font-medium\">當前建材代號：</span>\r\n              <span class=\"bg-white px-2 py-1 rounded border\">{{ selectedMaterial.CImageCode || '未設定' }}</span>\r\n            </div>\r\n            <div>選擇圖片後，建材代號將會自動設定為所選圖片的檔名，並建立圖片與建材的綁定關係。</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 搜尋和統計 -->\r\n      <div class=\"flex gap-3 mb-4 flex-shrink-0\">\r\n        <!-- 已選擇數量統計 -->\r\n        <div class=\"w-64\">\r\n          <label class=\"text-sm font-medium text-gray-700 mb-2 block\">已選擇數量統計</label>\r\n          <div class=\"bg-gray-50 border rounded p-2 text-sm\">\r\n            <div class=\"flex justify-between items-center mb-1\">\r\n              <span class=\"text-gray-600\">建材圖片:</span>\r\n              <span class=\"font-medium text-blue-600\">{{ getSelectedCountByCategory(PictureCategory.BUILDING_MATERIAL)\r\n                }} 張</span>\r\n            </div>\r\n            <div class=\"flex justify-between items-center\">\r\n              <span class=\"text-gray-600\">示意圖片:</span>\r\n              <span class=\"font-medium text-green-600\">{{ getSelectedCountByCategory(PictureCategory.SCHEMATIC) }}\r\n                張</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <label class=\"text-sm font-medium text-gray-700 mb-2 block\">搜尋圖片</label>\r\n          <input type=\"text\" class=\"w-full search-input\" placeholder=\"搜尋圖片名稱...\" [(ngModel)]=\"imageSearchTerm\"\r\n            (input)=\"filterAvailableImages()\" />\r\n        </div>\r\n        <div class=\"flex flex-col justify-end\">\r\n          <button class=\"btn btn-info btn-image-action\" (click)=\"loadAllImages()\">\r\n            重新載入 <i class=\"fas fa-refresh\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 雙類別圖片區塊布局 -->\r\n      <div class=\"flex flex-col gap-4 flex-1\" style=\"min-height: 0;\">\r\n\r\n        <!-- 建材圖片區塊 -->\r\n        <div class=\"flex-1 d-flex flex-column border rounded p-3\" style=\"min-height: 0;\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n            <h6 class=\"mb-0 font-medium text-blue-600\">\r\n              <i class=\"fas fa-hammer mr-2\"></i>建材圖片\r\n            </h6>\r\n            <div class=\"text-sm text-gray-600\">\r\n              已選: {{ getSelectedCountByCategory(PictureCategory.BUILDING_MATERIAL) }} 張\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"image-preview-container flex-1\" style=\"overflow-y: auto;\">\r\n            <div class=\"grid grid-cols-3 gap-2\">\r\n              <div *ngFor=\"let image of availableImages\"\r\n                class=\"image-grid-item border rounded p-2 cursor-pointer hover:bg-gray-50\"\r\n                (click)=\"moveToSelected(image)\">\r\n\r\n                <!-- 圖片預覽 -->\r\n                <div class=\"w-full h-24 bg-gray-100 rounded mb-2 flex items-center justify-center overflow-hidden\">\r\n                  <img *ngIf=\"image.thumbnailUrl\" [src]=\"image.thumbnailUrl\" [alt]=\"image.name\"\r\n                    class=\"image-thumbnail max-w-full max-h-full object-contain\" />\r\n                  <div *ngIf=\"!image.thumbnailUrl\" class=\"text-gray-400 text-center\">\r\n                    <i class=\"fas fa-image text-xl mb-1\"></i>\r\n                    <div class=\"text-xs\">無預覽</div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 圖片資訊 -->\r\n                <div class=\"text-xs text-gray-600\">\r\n                  <div class=\"font-medium truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n                </div>\r\n\r\n                <!-- 操作按鈕 -->\r\n                <div class=\"flex justify-between items-center mt-2\">\r\n                  <button class=\"btn btn-outline-info btn-xs\" (click)=\"previewImage(image, imagePreview, $event)\">\r\n                    <i class=\"fas fa-eye\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-primary btn-xs\" (click)=\"moveToSelected(image, $event)\">\r\n                    <i class=\"fas fa-arrow-right\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空狀態 -->\r\n            <div *ngIf=\"availableImages.length === 0\" class=\"text-center text-gray-500 py-20\">\r\n              <i class=\"fas fa-images text-4xl mb-3\"></i>\r\n              <div>找不到可選擇的圖片</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 分頁控制 -->\r\n          <div class=\"mt-3 d-flex justify-content-center\">\r\n            <ngx-pagination [(CollectionSize)]=\"imageTotalRecords\" [(PageSize)]=\"imagePageSize\"\r\n              [(Page)]=\"imageCurrentPage\" (PageChange)=\"imagePageChanged($event)\">\r\n            </ngx-pagination>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 中間：操作按鈕 -->\r\n        <div class=\"d-flex flex-column justify-content-center gap-2\" style=\"width: 80px;\">\r\n          <button class=\"btn btn-outline-primary btn-sm\" (click)=\"moveAllToSelected()\"\r\n            [disabled]=\"availableImages.length === 0\" title=\"全部移至已選\">\r\n            <i class=\"fas fa-angle-double-right\"></i>\r\n          </button>\r\n          <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"moveAllToAvailable()\"\r\n            [disabled]=\"selectedImages.length === 0\" title=\"全部移至可選\">\r\n            <i class=\"fas fa-angle-double-left\"></i>\r\n          </button>\r\n          <hr class=\"my-2\">\r\n          <button class=\"btn btn-outline-danger btn-sm\" (click)=\"clearAllSelection()\"\r\n            [disabled]=\"selectedImages.length === 0\" title=\"清除所有選擇\">\r\n            <i class=\"fas fa-times\"></i><br>\r\n            <small>清除</small>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 右側：已選擇的圖片 -->\r\n        <div class=\"flex-1 d-flex flex-column border rounded p-3\" style=\"min-height: 0;\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n            <h6 class=\"mb-0 font-medium\">已選擇圖片</h6>\r\n            <div class=\"text-sm text-gray-600\">\r\n              已選取: {{ selectedImages.length }} 張圖片\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"image-preview-container flex-1\" style=\"overflow-y: auto;\">\r\n            <div class=\"grid grid-cols-3 gap-2\">\r\n              <div *ngFor=\"let image of selectedImages; let i = index\"\r\n                class=\"image-grid-item border rounded p-2 cursor-pointer\" [class.border-success]=\"isImageBound(image)\"\r\n                [class.bg-green-50]=\"isImageBound(image)\">\r\n\r\n                <!-- 已綁定標示 -->\r\n                <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n                  <div *ngIf=\"isImageBound(image)\" class=\"badge badge-success text-xs px-2 py-1\" title=\"此圖片已經綁定到此建材\">\r\n                    已綁定\r\n                  </div>\r\n                  <div *ngIf=\"!isImageBound(image)\" class=\"badge badge-info text-xs px-2 py-1\">\r\n                    新選擇\r\n                  </div>\r\n                  <small class=\"text-gray-500\">#{{ i + 1 }}</small>\r\n                </div>\r\n\r\n                <!-- 圖片預覽 -->\r\n                <div class=\"w-full h-24 bg-gray-100 rounded mb-2 flex items-center justify-center overflow-hidden\">\r\n                  <img *ngIf=\"image.thumbnailUrl\" [src]=\"image.thumbnailUrl\" [alt]=\"image.name\"\r\n                    class=\"image-thumbnail max-w-full max-h-full object-contain\" />\r\n                  <div *ngIf=\"!image.thumbnailUrl\" class=\"text-gray-400 text-center\">\r\n                    <i class=\"fas fa-image text-xl mb-1\"></i>\r\n                    <div class=\"text-xs\">無預覽</div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 圖片資訊 -->\r\n                <div class=\"text-xs text-gray-600\">\r\n                  <div class=\"font-medium truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n                </div>\r\n\r\n                <!-- 操作按鈕 -->\r\n                <div class=\"flex justify-between items-center mt-2\">\r\n                  <button class=\"btn btn-outline-info btn-xs\" (click)=\"previewImage(image, imagePreview, $event)\">\r\n                    <i class=\"fas fa-eye\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-danger btn-xs\" (click)=\"moveToAvailable(image, $event)\">\r\n                    <i class=\"fas fa-arrow-left\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空狀態 -->\r\n            <div *ngIf=\"selectedImages.length === 0\" class=\"text-center text-gray-500 py-20\">\r\n              <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n              <div>尚未選擇任何圖片</div>\r\n              <div class=\"text-sm mt-2\">從左側選擇圖片</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        <span *ngIf=\"getBoundImagesCount() > 0\" class=\"text-success\">\r\n          <i class=\"fas fa-check-circle\"></i> {{ getBoundImagesCount() }} 張已綁定\r\n        </span>\r\n        <span *ngIf=\"getNewSelectedCount() > 0\" class=\"text-info ml-3\">\r\n          <i class=\"fas fa-plus-circle\"></i> {{ getNewSelectedCount() }} 張新選擇\r\n        </span>\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onCloseImageBinder(ref)\">取消</button>\r\n        <button class=\"btn btn-success btn-sm\" [disabled]=\"selectedImages.length === 0\"\r\n          (click)=\"onConfirmImageSelection(ref)\">\r\n          確定選擇 ({{ selectedImages.length }})\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 圖片預覽對話框 -->\r\n<ng-template #imagePreview let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[800px] h-[600px]\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n      <span>圖片預覽 - {{ previewingImage?.name }}</span>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex <= 0\" (click)=\"previousImage()\">\r\n          <i class=\"fas fa-chevron-left\"></i> 上一張\r\n        </button>\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex >= allImages.length - 1\"\r\n          (click)=\"nextImage()\">\r\n          下一張 <i class=\"fas fa-chevron-right\"></i>\r\n        </button>\r\n      </div>\r\n    </nb-card-header> <nb-card-body class=\"p-0 d-flex justify-content-center align-items-center\" style=\"height: 500px;\">\r\n      <img *ngIf=\"previewingImage && previewingImage.fullUrl\" [src]=\"previewingImage.fullUrl\"\r\n        [alt]=\"previewingImage.name\" class=\"max-w-full max-h-full object-contain\" />\r\n      <div *ngIf=\"!previewingImage || !previewingImage.fullUrl\" class=\"text-gray-400 text-center\">\r\n        <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n        <div>圖片載入失敗</div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        {{ currentPreviewIndex + 1 }} / {{ allImages.length }}\r\n      </div>\r\n      <div class=\"d-flex gap-2\"> <button class=\"btn btn-outline-info btn-sm\" (click)=\"toggleImageSelectionInPreview()\">\r\n          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}\r\n        </button>\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 原有的圖片檢視對話框 -->\r\n<ng-template #dialogImage let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; width: 700px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span>\r\n        檢視\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"w-full h-auto\">\r\n        <img class=\"fit-size\" [src]=\"currentImageShowing\">\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"flex justify-center items-center\">\r\n        <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAG9C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;;;;;;;;;;;;;;ICAvDC,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAuB;IACzEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,YAAA,CAAAI,cAAA,MACF;;;;;;IAiCFT,EAAA,CAAAC,cAAA,iBAA8F;IAAnCD,EAAA,CAAAU,UAAA,mBAAAC,qEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,sBAAA,EAAwB;IAAA,EAAC;IAACjB,EAAA,CAAAE,MAAA,oBAAG;IAAAF,EAAA,CAAAkB,SAAA,YAC5D;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC9CH,EAAA,CAAAC,cAAA,iBAAoF;IAAnBD,EAAA,CAAAU,UAAA,mBAAAS,qEAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAO,MAAA,EAAQ;IAAA,EAAC;IACjFrB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAkB,SAAA,YAA6B;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3CH,EAAA,CAAAC,cAAA,iBAAsF;IAAzBD,EAAA,CAAAU,UAAA,mBAAAY,qEAAA;MAAAtB,EAAA,CAAAY,aAAA,CAAAW,GAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAS,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAY,MAAA,CAAAF,SAAA,CAAc;IAAA,EAAC;IAACxB,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAkB,SAAA,YAC/D;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IACrCH,EAAA,CAAAC,cAAA,iBAAoF;IAA5BD,EAAA,CAAAU,UAAA,mBAAAiB,qEAAA;MAAA3B,EAAA,CAAAY,aAAA,CAAAgB,GAAA;MAAA5B,EAAA,CAAAe,aAAA;MAAA,MAAAc,YAAA,GAAA7B,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASa,YAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAE9B,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAiBjGH,EAAA,CAAAC,cAAA,aAAwD;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAc3DH,EAAA,CAAAC,cAAA,eACmC;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzCH,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAC,gBAAA,CAAAC,MAAA,CAAkC;;;;;IACrElC,EAAA,CAAAC,cAAA,eACkC;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI9CH,EAAA,CAAAC,cAAA,SAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAArBH,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAG,MAAA,CAAgB;;;;;;IAOlDnC,EAAA,CAAAC,cAAA,iBACiB;IADkCD,EAAA,CAAAU,UAAA,mBAAA0B,mFAAA;MAAApC,EAAA,CAAAY,aAAA,CAAAyB,IAAA;MAAA,MAAAL,QAAA,GAAAhC,EAAA,CAAAe,aAAA,GAAAuB,SAAA;MAAA,MAAAxB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAS,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAyB,kBAAA,CAAAP,QAAA,EAAAR,SAAA,CAAgC;IAAA,EAAC;IAC5ExB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAACH,EAAA,CAAAC,cAAA,iBAC4E;IAAvGD,EAAA,CAAAU,UAAA,mBAAA8B,mFAAA;MAAAxC,EAAA,CAAAY,aAAA,CAAA6B,IAAA;MAAA,MAAAT,QAAA,GAAAhC,EAAA,CAAAe,aAAA,GAAAuB,SAAA;MAAA,MAAAxB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAA2B,eAAA,GAAA1C,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA6B,oBAAA,CAAAX,QAAA,EAAAU,eAAA,CAAuC;IAAA,EAAC;IACjD1C,EAAA,CAAAkB,SAAA,YAA6B;IAAClB,EAAA,CAAAE,MAAA,qBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF0DH,EAAA,CAAAI,UAAA,sBAAA4B,QAAA,CAAAY,KAAA,+BAAqC;;;;;IAvB1G5C,EADF,CAAAC,cAAA,SAAsD,SAChD;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnEH,EAAA,CAAAC,cAAA,SAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7EH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE7BH,EADF,CAAAC,cAAA,UAAI,eACqC;IAGrCD,EAFA,CAAA6C,UAAA,KAAAC,wDAAA,mBACmC,KAAAC,wDAAA,mBAED;IAClC/C,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAiF;IAE3FF,EAF2F,CAAAG,YAAA,EAAO,EAC1F,EACH;IACLH,EAAA,CAAA6C,UAAA,KAAAG,sDAAA,iBAAoC;IAElChD,EADF,CAAAC,cAAA,UAAI,gBACqF;IACrFD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IACLH,EAAA,CAAAC,cAAA,cAA6B;IAEED,EAD7B,CAAA6C,UAAA,KAAAI,0DAAA,qBACiB,KAAAC,0DAAA,qBACwF;IAI7GlD,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IA3BCH,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAmB,GAAA,CAAa;IACbnD,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAoB,UAAA,yBAA8B;IAC9BpD,EAAA,CAAAO,SAAA,GAA0D;IAA1DP,EAAA,CAAAqD,kBAAA,KAAArB,QAAA,CAAAY,KAAA,SAAAZ,QAAA,CAAAsB,KAAA,SAAAtB,QAAA,CAAAuB,SAAA,KAA0D;IAC1DvD,EAAA,CAAAO,SAAA,EAA8C;IAA9CP,EAAA,CAAAwD,UAAA,EAAAxB,QAAA,CAAAyB,UAAA,qBAA8C;IAACzD,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAA0B,WAAA,CAAqB;IACpE1D,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAA2B,YAAA,CAAsB;IAGf3D,EAAA,CAAAO,SAAA,GAA+D;IAA/DP,EAAA,CAAAI,UAAA,SAAA4B,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAAC,gBAAA,CAAAC,MAAA,KAA+D;IAE/DlC,EAAA,CAAAO,SAAA,EAAkE;IAAlEP,EAAA,CAAAI,UAAA,UAAA4B,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAAC,gBAAA,CAAAC,MAAA,OAAkE;IAEnElC,EAAA,CAAAO,SAAA,GAAiF;IAAjFP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAAC,gBAAA,CAAAC,MAAA,mDAAiF;IAGtFlC,EAAA,CAAAO,SAAA,EAA6B;IAA7BP,EAAA,CAAAI,UAAA,SAAA4B,QAAA,CAAA4B,UAAA,SAA6B;IAEZ5D,EAAA,CAAAO,SAAA,GAAkE;IAAlEP,EAAA,CAAA6D,UAAA,CAAA7B,QAAA,CAAA8B,OAAA,6CAAkE;IACpF9D,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAAiD,cAAA,CAAA/B,QAAA,CAAA8B,OAAA,YACF;IAIG9D,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAkD,MAAA,CAAY;IACsChE,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAkD,MAAA,CAAY;;;;;IAzBvEhE,EAAA,CAAAC,cAAA,YAA+D;IAC7DD,EAAA,CAAA6C,UAAA,IAAAoB,gDAAA,mBAAsD;IA6BxDjE,EAAA,CAAAG,YAAA,EAAQ;;;;IA7BeH,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAoD,YAAA,CAAkB;;;;;IA4FrClE,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAA+D,UAAA,CAAAC,KAAA,CAAsB;IACpEpE,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA2D,UAAA,CAAAE,KAAA,MACF;;;;;IAYArE,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAkB,SAAA,YAAuD;IACvDlB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,kDAAAM,MAAA,CAAAwD,gBAAA,CAAAlB,UAAA,MACF;;;;;;IAnERpD,EADF,CAAAC,cAAA,kBAA2B,qBACT;IACdD,EAAA,CAAAE,MAAA,4DACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEfH,EADF,CAAAC,cAAA,uBAA2B,aACH;IAAAD,EAAA,CAAAE,MAAA,+EAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAInCH,EAHJ,CAAAC,cAAA,cAAyB,cAEQ,gBACW;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpDH,EAAA,CAAAC,cAAA,iBAC8C;IAA5CD,EAAA,CAAAuE,gBAAA,2BAAAC,kFAAAC,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAAwD,gBAAA,CAAAlB,UAAA,EAAAqB,MAAA,MAAA3D,MAAA,CAAAwD,gBAAA,CAAAlB,UAAA,GAAAqB,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EAAyC;IAC7CzE,EAFE,CAAAG,YAAA,EAC8C,EAC1C;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAAuE,gBAAA,2BAAAK,kFAAAH,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAAwD,gBAAA,CAAA1B,KAAA,EAAA6B,MAAA,MAAA3D,MAAA,CAAAwD,gBAAA,CAAA1B,KAAA,GAAA6B,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EAAoC;IACxCzE,EAFE,CAAAG,YAAA,EACyC,EACrC;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAAuE,gBAAA,2BAAAM,kFAAAJ,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAAwD,gBAAA,CAAAhB,KAAA,EAAAmB,MAAA,MAAA3D,MAAA,CAAAwD,gBAAA,CAAAhB,KAAA,GAAAmB,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EAAoC;IACxCzE,EAFE,CAAAG,YAAA,EACyC,EACrC;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBAC6C;IAA3CD,EAAA,CAAAuE,gBAAA,2BAAAO,kFAAAL,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAAwD,gBAAA,CAAAf,SAAA,EAAAkB,MAAA,MAAA3D,MAAA,CAAAwD,gBAAA,CAAAf,SAAA,GAAAkB,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EAAwC;IAC5CzE,EAFE,CAAAG,YAAA,EAC6C,EACzC;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,4CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,iBAC+C;IAA7CD,EAAA,CAAAuE,gBAAA,2BAAAQ,kFAAAN,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAAwD,gBAAA,CAAAZ,WAAA,EAAAe,MAAA,MAAA3D,MAAA,CAAAwD,gBAAA,CAAAZ,WAAA,GAAAe,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EAA0C;IAC9CzE,EAFE,CAAAG,YAAA,EAC+C,EAC3C;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrCH,EAAA,CAAAC,cAAA,oBACiE;IAD/CD,EAAA,CAAAuE,gBAAA,2BAAAS,qFAAAP,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAAwD,gBAAA,CAAAX,YAAA,EAAAc,MAAA,MAAA3D,MAAA,CAAAwD,gBAAA,CAAAX,YAAA,GAAAc,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EAA2C;IAE/DzE,EADmE,CAAAG,YAAA,EAAW,EACxE;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnCH,EAAA,CAAAC,cAAA,iBAC0C;IAAxCD,EAAA,CAAAuE,gBAAA,2BAAAU,kFAAAR,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAAwD,gBAAA,CAAAnC,MAAA,EAAAsC,MAAA,MAAA3D,MAAA,CAAAwD,gBAAA,CAAAnC,MAAA,GAAAsC,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EAAqC;IACzCzE,EAFE,CAAAG,YAAA,EAC0C,EACtC;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,qBAAiE;IAAtDD,EAAA,CAAAuE,gBAAA,2BAAAW,sFAAAT,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAAwD,gBAAA,CAAAR,OAAA,EAAAW,MAAA,MAAA3D,MAAA,CAAAwD,gBAAA,CAAAR,OAAA,GAAAW,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EAAsC;IAC/CzE,EAAA,CAAA6C,UAAA,KAAAsC,8DAAA,wBAAuE;IAI3EnF,EADE,CAAAG,YAAA,EAAY,EACR;IAIJH,EADF,CAAAC,cAAA,eAAkE,iBACvC;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEnCH,EADF,CAAAC,cAAA,eAA+B,kBAEP;IADoCD,EAAA,CAAAU,UAAA,mBAAA0E,2EAAA;MAAApF,EAAA,CAAAY,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAA2B,eAAA,GAAA1C,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAuE,eAAA,CAAA3C,eAAA,CAA4B;IAAA,EAAC;IAE9F1C,EAAA,CAAAkB,SAAA,aAAkC;IAAAlB,EAAA,CAAAE,MAAA,iCACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAA6C,UAAA,KAAAyC,wDAAA,kBAAyF;IAOjGtF,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACc;IAAvBD,EAAA,CAAAU,UAAA,mBAAA6E,2EAAA;MAAA,MAAAC,OAAA,GAAAxF,EAAA,CAAAY,aAAA,CAAA8D,IAAA,EAAAe,SAAA;MAAA,MAAA3E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA4E,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAACxF,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7EH,EAAA,CAAAC,cAAA,kBAA+D;IAAxBD,EAAA,CAAAU,UAAA,mBAAAiF,2EAAA;MAAA,MAAAH,OAAA,GAAAxF,EAAA,CAAAY,aAAA,CAAA8D,IAAA,EAAAe,SAAA;MAAA,MAAA3E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA8E,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAACxF,EAAA,CAAAE,MAAA,oBAAE;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EAC3D,EACT;;;;IAlEAH,EAAA,CAAAO,SAAA,IAAyC;IAAzCP,EAAA,CAAA6F,gBAAA,YAAA/E,MAAA,CAAAwD,gBAAA,CAAAlB,UAAA,CAAyC;IAMzCpD,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAA6F,gBAAA,YAAA/E,MAAA,CAAAwD,gBAAA,CAAA1B,KAAA,CAAoC;IAMpC5C,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAA6F,gBAAA,YAAA/E,MAAA,CAAAwD,gBAAA,CAAAhB,KAAA,CAAoC;IAKpCtD,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAA6F,gBAAA,YAAA/E,MAAA,CAAAwD,gBAAA,CAAAf,SAAA,CAAwC;IAMxCvD,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAA6F,gBAAA,YAAA/E,MAAA,CAAAwD,gBAAA,CAAAZ,WAAA,CAA0C;IAK1B1D,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAA6F,gBAAA,YAAA/E,MAAA,CAAAwD,gBAAA,CAAAX,YAAA,CAA2C;IAAC3D,EAAA,CAAAI,UAAA,WAAU;IAMtEJ,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAA6F,gBAAA,YAAA/E,MAAA,CAAAwD,gBAAA,CAAAnC,MAAA,CAAqC;IAK5BnC,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAA6F,gBAAA,YAAA/E,MAAA,CAAAwD,gBAAA,CAAAR,OAAA,CAAsC;IACjB9D,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAgF,aAAA,CAAgB;IAW5C9F,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAI,UAAA,uDAAmB;IAGiCJ,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAwD,gBAAA,CAAAlB,UAAA,CAAiC;;;;;IA0FjFpD,EAAA,CAAAkB,SAAA,eACiE;;;;IADNlB,EAA3B,CAAAI,UAAA,QAAA2F,SAAA,CAAAC,YAAA,EAAAhG,EAAA,CAAAiG,aAAA,CAA0B,QAAAF,SAAA,CAAAG,IAAA,CAAmB;;;;;IAE7ElG,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAkB,SAAA,aAAyC;IACzClB,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAC1BF,EAD0B,CAAAG,YAAA,EAAM,EAC1B;;;;;;IAXVH,EAAA,CAAAC,cAAA,eAEkC;IAAhCD,EAAA,CAAAU,UAAA,mBAAAyF,8EAAA;MAAA,MAAAJ,SAAA,GAAA/F,EAAA,CAAAY,aAAA,CAAAwF,IAAA,EAAA9D,SAAA;MAAA,MAAAxB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAuF,cAAA,CAAAN,SAAA,CAAqB;IAAA,EAAC;IAG/B/F,EAAA,CAAAC,cAAA,eAAmG;IAGjGD,EAFA,CAAA6C,UAAA,IAAAyD,8DAAA,mBACiE,IAAAC,8DAAA,mBACE;IAIrEvG,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAmC,eACsB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACzEF,EADyE,CAAAG,YAAA,EAAM,EACzE;IAIJH,EADF,CAAAC,cAAA,eAAoD,kBAC8C;IAApDD,EAAA,CAAAU,UAAA,mBAAA8F,iFAAA/B,MAAA;MAAA,MAAAsB,SAAA,GAAA/F,EAAA,CAAAY,aAAA,CAAAwF,IAAA,EAAA9D,SAAA;MAAA,MAAAxB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAA0F,gBAAA,GAAAzG,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA4F,YAAA,CAAAX,SAAA,EAAAU,gBAAA,EAAAhC,MAAA,CAAyC;IAAA,EAAC;IAC7FzE,EAAA,CAAAkB,SAAA,aAA0B;IAC5BlB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAuF;IAAxCD,EAAA,CAAAU,UAAA,mBAAAiG,kFAAAlC,MAAA;MAAA,MAAAsB,SAAA,GAAA/F,EAAA,CAAAY,aAAA,CAAAwF,IAAA,EAAA9D,SAAA;MAAA,MAAAxB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAuF,cAAA,CAAAN,SAAA,EAAAtB,MAAA,CAA6B;IAAA,EAAC;IACpFzE,EAAA,CAAAkB,SAAA,cAAkC;IAGxClB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAtBIH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAA2F,SAAA,CAAAC,YAAA,CAAwB;IAExBhG,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAA2F,SAAA,CAAAC,YAAA,CAAyB;IAQGhG,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAA2F,SAAA,CAAAG,IAAA,CAAoB;IAAClG,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA+B,iBAAA,CAAAgE,SAAA,CAAAG,IAAA,CAAgB;;;;;IAgB7ElG,EAAA,CAAAC,cAAA,eAAkF;IAChFD,EAAA,CAAAkB,SAAA,aAA2C;IAC3ClB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAChBF,EADgB,CAAAG,YAAA,EAAM,EAChB;;;;;IA8CAH,EAAA,CAAAC,cAAA,eAAmG;IACjGD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMNH,EAAA,CAAAkB,SAAA,eACiE;;;;IADNlB,EAA3B,CAAAI,UAAA,QAAAwG,SAAA,CAAAZ,YAAA,EAAAhG,EAAA,CAAAiG,aAAA,CAA0B,QAAAW,SAAA,CAAAV,IAAA,CAAmB;;;;;IAE7ElG,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAkB,SAAA,aAAyC;IACzClB,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAC1BF,EAD0B,CAAAG,YAAA,EAAM,EAC1B;;;;;;IAjBRH,EALF,CAAAC,cAAA,eAE4C,eAG0B;IAIlED,EAHA,CAAA6C,UAAA,IAAAgE,8DAAA,mBAAmG,IAAAC,8DAAA,mBAGtB;IAG7E9G,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAC3CF,EAD2C,CAAAG,YAAA,EAAQ,EAC7C;IAGNH,EAAA,CAAAC,cAAA,eAAmG;IAGjGD,EAFA,CAAA6C,UAAA,IAAAkE,8DAAA,mBACiE,IAAAC,8DAAA,mBACE;IAIrEhH,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAmC,gBACsB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IACzEF,EADyE,CAAAG,YAAA,EAAM,EACzE;IAIJH,EADF,CAAAC,cAAA,gBAAoD,mBAC8C;IAApDD,EAAA,CAAAU,UAAA,mBAAAuG,kFAAAxC,MAAA;MAAA,MAAAmC,SAAA,GAAA5G,EAAA,CAAAY,aAAA,CAAAsG,IAAA,EAAA5E,SAAA;MAAA,MAAAxB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAA0F,gBAAA,GAAAzG,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA4F,YAAA,CAAAE,SAAA,EAAAH,gBAAA,EAAAhC,MAAA,CAAyC;IAAA,EAAC;IAC7FzE,EAAA,CAAAkB,SAAA,cAA0B;IAC5BlB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAuF;IAAzCD,EAAA,CAAAU,UAAA,mBAAAyG,kFAAA1C,MAAA;MAAA,MAAAmC,SAAA,GAAA5G,EAAA,CAAAY,aAAA,CAAAsG,IAAA,EAAA5E,SAAA;MAAA,MAAAxB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAsG,eAAA,CAAAR,SAAA,EAAAnC,MAAA,CAA8B;IAAA,EAAC;IACpFzE,EAAA,CAAAkB,SAAA,cAAiC;IAGvClB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;;IArCJH,EAD0D,CAAAqH,WAAA,mBAAAvG,MAAA,CAAAwG,YAAA,CAAAV,SAAA,EAA4C,gBAAA9F,MAAA,CAAAwG,YAAA,CAAAV,SAAA,EAC7D;IAIjC5G,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAwG,YAAA,CAAAV,SAAA,EAAyB;IAGzB5G,EAAA,CAAAO,SAAA,EAA0B;IAA1BP,EAAA,CAAAI,UAAA,UAAAU,MAAA,CAAAwG,YAAA,CAAAV,SAAA,EAA0B;IAGH5G,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAQ,kBAAA,MAAA+G,KAAA,SAAY;IAKnCvH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAwG,SAAA,CAAAZ,YAAA,CAAwB;IAExBhG,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAwG,SAAA,CAAAZ,YAAA,CAAyB;IAQGhG,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAwG,SAAA,CAAAV,IAAA,CAAoB;IAAClG,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA+B,iBAAA,CAAA6E,SAAA,CAAAV,IAAA,CAAgB;;;;;IAgB7ElG,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAkB,SAAA,aAA0C;IAC1ClB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnBH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IACnCF,EADmC,CAAAG,YAAA,EAAM,EACnC;;;;;IAQVH,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAkB,SAAA,aAAmC;IAAClB,EAAA,CAAAE,MAAA,GACtC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAD+BH,EAAA,CAAAO,SAAA,GACtC;IADsCP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAA0G,mBAAA,iCACtC;;;;;IACAxH,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAkB,SAAA,aAAkC;IAAClB,EAAA,CAAAE,MAAA,GACrC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAD8BH,EAAA,CAAAO,SAAA,GACrC;IADqCP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAA2G,mBAAA,iCACrC;;;;;;IA1MJzH,EADF,CAAAC,cAAA,kBAAkD,qBAChC;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAMbH,EALJ,CAAAC,cAAA,uBAC2E,cAGQ,cAC3C;IAClCD,EAAA,CAAAkB,SAAA,YAAqD;IAEnDlB,EADF,CAAAC,cAAA,cAAmC,cACH;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEtCH,EADF,CAAAC,cAAA,eAAkB,gBACU;IAAAD,EAAA,CAAAE,MAAA,kDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,gBAAgD;IAAAD,EAAA,CAAAE,MAAA,IAA0C;IAC5FF,EAD4F,CAAAG,YAAA,EAAO,EAC7F;IACNH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,kPAAuC;IAGlDF,EAHkD,CAAAG,YAAA,EAAM,EAC9C,EACF,EACF;IAMFH,EAHJ,CAAAC,cAAA,eAA2C,eAEvB,iBAC4C;IAAAD,EAAA,CAAAE,MAAA,kDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGvEH,EAFJ,CAAAC,cAAA,eAAmD,eACG,gBACtB;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,gBAAwC;IAAAD,EAAA,CAAAE,MAAA,IAClC;IACRF,EADQ,CAAAG,YAAA,EAAO,EACT;IAEJH,EADF,CAAAC,cAAA,eAA+C,gBACjB;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,gBAAyC;IAAAD,EAAA,CAAAE,MAAA,IACtC;IAGTF,EAHS,CAAAG,YAAA,EAAO,EACN,EACF,EACF;IAEJH,EADF,CAAAC,cAAA,eAAoB,iBAC0C;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxEH,EAAA,CAAAC,cAAA,iBACsC;IADiCD,EAAA,CAAAuE,gBAAA,2BAAAmD,kFAAAjD,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAA8G,eAAA,EAAAnD,MAAA,MAAA3D,MAAA,CAAA8G,eAAA,GAAAnD,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EAA6B;IAClGzE,EAAA,CAAAU,UAAA,mBAAAmH,0EAAA;MAAA7H,EAAA,CAAAY,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAgH,qBAAA,EAAuB;IAAA,EAAC;IACrC9H,EAFE,CAAAG,YAAA,EACsC,EAClC;IAEJH,EADF,CAAAC,cAAA,gBAAuC,mBACmC;IAA1BD,EAAA,CAAAU,UAAA,mBAAAqH,2EAAA;MAAA/H,EAAA,CAAAY,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAkH,aAAA,EAAe;IAAA,EAAC;IACrEhI,EAAA,CAAAE,MAAA,kCAAK;IAAAF,EAAA,CAAAkB,SAAA,cAA8B;IAGzClB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAQAH,EALN,CAAAC,cAAA,gBAA+D,gBAGoB,gBACX,eACvB;IACzCD,EAAA,CAAAkB,SAAA,cAAkC;IAAAlB,EAAA,CAAAE,MAAA,iCACpC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,gBAAmC;IACjCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,gBAAsE,gBAChC;IAClCD,EAAA,CAAA6C,UAAA,KAAAoF,wDAAA,oBAEkC;IA2BpCjI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA6C,UAAA,KAAAqF,wDAAA,mBAAkF;IAIpFlI,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,gBAAgD,0BAEwB;IAApED,EADc,CAAAuE,gBAAA,kCAAA4D,kGAAA1D,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAAsH,iBAAA,EAAA3D,MAAA,MAAA3D,MAAA,CAAAsH,iBAAA,GAAA3D,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EAAsC,4BAAA4D,4FAAA5D,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAAwH,aAAA,EAAA7D,MAAA,MAAA3D,MAAA,CAAAwH,aAAA,GAAA7D,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EAA6B,wBAAA8D,wFAAA9D,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA2E,kBAAA,CAAA7D,MAAA,CAAA0H,gBAAA,EAAA/D,MAAA,MAAA3D,MAAA,CAAA0H,gBAAA,GAAA/D,MAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;IAAA,EACtD;IAACzE,EAAA,CAAAU,UAAA,wBAAA6H,wFAAA9D,MAAA;MAAAzE,EAAA,CAAAY,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAcF,MAAA,CAAA2H,gBAAA,CAAAhE,MAAA,CAAwB;IAAA,EAAC;IAGzEzE,EAFI,CAAAG,YAAA,EAAiB,EACb,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAkF,mBAErB;IADZD,EAAA,CAAAU,UAAA,mBAAAgI,2EAAA;MAAA1I,EAAA,CAAAY,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA6H,iBAAA,EAAmB;IAAA,EAAC;IAE1E3I,EAAA,CAAAkB,SAAA,cAAyC;IAC3ClB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAC0D;IADTD,EAAA,CAAAU,UAAA,mBAAAkI,2EAAA;MAAA5I,EAAA,CAAAY,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA+H,kBAAA,EAAoB;IAAA,EAAC;IAE7E7I,EAAA,CAAAkB,SAAA,cAAwC;IAC1ClB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAkB,SAAA,eAAiB;IACjBlB,EAAA,CAAAC,cAAA,mBAC0D;IADZD,EAAA,CAAAU,UAAA,mBAAAoI,2EAAA;MAAA9I,EAAA,CAAAY,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAiI,iBAAA,EAAmB;IAAA,EAAC;IAE7C/I,EAA5B,CAAAkB,SAAA,cAA4B,UAAI;IAChClB,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEbF,EAFa,CAAAG,YAAA,EAAQ,EACV,EACL;IAKFH,EAFJ,CAAAC,cAAA,gBAAiF,gBACX,eACrC;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,gBAAmC;IACjCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,gBAAsE,gBAChC;IAClCD,EAAA,CAAA6C,UAAA,KAAAmG,wDAAA,qBAE4C;IAsC9ChJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA6C,UAAA,KAAAoG,wDAAA,mBAAiF;IAQzFjJ,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;IAGbH,EADF,CAAAC,cAAA,2BAA0E,gBACrC;IAIjCD,EAHA,CAAA6C,UAAA,KAAAqG,yDAAA,oBAA6D,KAAAC,yDAAA,oBAGE;IAGjEnJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,gBAA0B,mBACgD;IAAlCD,EAAA,CAAAU,UAAA,mBAAA0I,2EAAA;MAAA,MAAAC,OAAA,GAAArJ,EAAA,CAAAY,aAAA,CAAA+G,IAAA,EAAAlC,SAAA;MAAA,MAAA3E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAwI,kBAAA,CAAAD,OAAA,CAAuB;IAAA,EAAC;IAACrJ,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnFH,EAAA,CAAAC,cAAA,mBACyC;IAAvCD,EAAA,CAAAU,UAAA,mBAAA6I,2EAAA;MAAA,MAAAF,OAAA,GAAArJ,EAAA,CAAAY,aAAA,CAAA+G,IAAA,EAAAlC,SAAA;MAAA,MAAA3E,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA0I,uBAAA,CAAAH,OAAA,CAA4B;IAAA,EAAC;IACtCrJ,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;;;;IAnNNH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,iCAAAM,MAAA,CAAAwD,gBAAA,CAAA1B,KAAA,eAAA9B,MAAA,CAAAwD,gBAAA,CAAA1B,KAAA,yFACF;IAY0D5C,EAAA,CAAAO,SAAA,IAA0C;IAA1CP,EAAA,CAAA+B,iBAAA,CAAAjB,MAAA,CAAAwD,gBAAA,CAAAlB,UAAA,yBAA0C;IAelDpD,EAAA,CAAAO,SAAA,IAClC;IADkCP,EAAA,CAAAQ,kBAAA,KAAAM,MAAA,CAAA2I,0BAAA,CAAA3I,MAAA,CAAA4I,eAAA,CAAAC,iBAAA,aAClC;IAImC3J,EAAA,CAAAO,SAAA,GACtC;IADsCP,EAAA,CAAAQ,kBAAA,KAAAM,MAAA,CAAA2I,0BAAA,CAAA3I,MAAA,CAAA4I,eAAA,CAAAE,SAAA,aACtC;IAMgE5J,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAA6F,gBAAA,YAAA/E,MAAA,CAAA8G,eAAA,CAA6B;IAoBhG5H,EAAA,CAAAO,SAAA,IACF;IADEP,EAAA,CAAAQ,kBAAA,oBAAAM,MAAA,CAAA2I,0BAAA,CAAA3I,MAAA,CAAA4I,eAAA,CAAAC,iBAAA,cACF;IAKyB3J,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAA+I,eAAA,CAAkB;IAgCrC7J,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA+I,eAAA,CAAA3H,MAAA,OAAkC;IAQxBlC,EAAA,CAAAO,SAAA,GAAsC;IACpDP,EADc,CAAA6F,gBAAA,mBAAA/E,MAAA,CAAAsH,iBAAA,CAAsC,aAAAtH,MAAA,CAAAwH,aAAA,CAA6B,SAAAxH,MAAA,CAAA0H,gBAAA,CACtD;IAQ7BxI,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAA+I,eAAA,CAAA3H,MAAA,OAAyC;IAIzClC,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAAgJ,cAAA,CAAA5H,MAAA,OAAwC;IAKxClC,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAAgJ,cAAA,CAAA5H,MAAA,OAAwC;IAWtClC,EAAA,CAAAO,SAAA,IACF;IADEP,EAAA,CAAAQ,kBAAA,0BAAAM,MAAA,CAAAgJ,cAAA,CAAA5H,MAAA,yBACF;IAKyBlC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAgJ,cAAA,CAAmB;IA2CtC9J,EAAA,CAAAO,SAAA,EAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAgJ,cAAA,CAAA5H,MAAA,OAAiC;IAYpClC,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA0G,mBAAA,OAA+B;IAG/BxH,EAAA,CAAAO,SAAA,EAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA2G,mBAAA,OAA+B;IAMCzH,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAAgJ,cAAA,CAAA5H,MAAA,OAAwC;IAE7ElC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,gCAAAM,MAAA,CAAAgJ,cAAA,CAAA5H,MAAA,OACF;;;;;IAqBFlC,EAAA,CAAAkB,SAAA,eAC8E;;;;IAA5ElB,EADsD,CAAAI,UAAA,QAAAU,MAAA,CAAAiJ,eAAA,CAAAC,OAAA,EAAAhK,EAAA,CAAAiG,aAAA,CAA+B,QAAAnF,MAAA,CAAAiJ,eAAA,CAAA7D,IAAA,CACzD;;;;;IAC9BlG,EAAA,CAAAC,cAAA,eAA4F;IAC1FD,EAAA,CAAAkB,SAAA,aAA0C;IAC1ClB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACbF,EADa,CAAAG,YAAA,EAAM,EACb;;;;;;IAhBNH,EAFJ,CAAAC,cAAA,mBAAqC,0BACuC,WAClE;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7CH,EADF,CAAAC,cAAA,eAA0B,kBACuF;IAA1BD,EAAA,CAAAU,UAAA,mBAAAuJ,0EAAA;MAAAjK,EAAA,CAAAY,aAAA,CAAAsJ,IAAA;MAAA,MAAApJ,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAqJ,aAAA,EAAe;IAAA,EAAC;IAC5GnK,EAAA,CAAAkB,SAAA,aAAmC;IAAClB,EAAA,CAAAE,MAAA,2BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACwB;IAAtBD,EAAA,CAAAU,UAAA,mBAAA0J,0EAAA;MAAApK,EAAA,CAAAY,aAAA,CAAAsJ,IAAA;MAAA,MAAApJ,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAuJ,SAAA,EAAW;IAAA,EAAC;IACrBrK,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAkB,SAAA,cAAoC;IAG9ClB,EAFI,CAAAG,YAAA,EAAS,EACL,EACS;IAACH,EAAA,CAAAC,cAAA,yBAAkG;IAGlHD,EAFA,CAAA6C,UAAA,KAAAyH,wDAAA,mBAC8E,KAAAC,wDAAA,mBACc;IAI9FvK,EAAA,CAAAG,YAAA,EAAe;IAEbH,EADF,CAAAC,cAAA,2BAA0E,gBACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACqBH,EAA3B,CAAAC,cAAA,gBAA0B,mBAAuF;IAA1CD,EAAA,CAAAU,UAAA,mBAAA8J,2EAAA;MAAAxK,EAAA,CAAAY,aAAA,CAAAsJ,IAAA;MAAA,MAAApJ,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA2J,6BAAA,EAA+B;IAAA,EAAC;IAC5GzK,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAA4D;IAAtBD,EAAA,CAAAU,UAAA,mBAAAgK,2EAAA;MAAA,MAAAC,OAAA,GAAA3K,EAAA,CAAAY,aAAA,CAAAsJ,IAAA,EAAAzE,SAAA;MAAA,OAAAzF,EAAA,CAAAgB,WAAA,CAAS2J,OAAA,CAAAC,KAAA,EAAW;IAAA,EAAC;IAAC5K,EAAA,CAAAE,MAAA,oBAAE;IAGpEF,EAHoE,CAAAG,YAAA,EAAS,EACnE,EACS,EACT;;;;IA5BAH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,gCAAAM,MAAA,CAAAiJ,eAAA,kBAAAjJ,MAAA,CAAAiJ,eAAA,CAAA7D,IAAA,KAAkC;IAESlG,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAA+J,mBAAA,MAAqC;IAGrC7K,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAA+J,mBAAA,IAAA/J,MAAA,CAAAgK,SAAA,CAAA5I,MAAA,KAAwD;IAMnGlC,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAiJ,eAAA,IAAAjJ,MAAA,CAAAiJ,eAAA,CAAAC,OAAA,CAAgD;IAEhDhK,EAAA,CAAAO,SAAA,EAAkD;IAAlDP,EAAA,CAAAI,UAAA,UAAAU,MAAA,CAAAiJ,eAAA,KAAAjJ,MAAA,CAAAiJ,eAAA,CAAAC,OAAA,CAAkD;IAOtDhK,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA+K,kBAAA,MAAAjK,MAAA,CAAA+J,mBAAA,aAAA/J,MAAA,CAAAgK,SAAA,CAAA5I,MAAA,MACF;IAEIlC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAAiJ,eAAA,IAAAjJ,MAAA,CAAAkK,eAAA,CAAAlK,MAAA,CAAAiJ,eAAA,uEACF;;;;;;IAWF/J,EAFJ,CAAAC,cAAA,mBAAqF,qBACnE,WACR;IACJD,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACQ;IAEfH,EADF,CAAAC,cAAA,wBAAwC,eACX;IACzBD,EAAA,CAAAkB,SAAA,eAAkD;IAEtDlB,EADE,CAAAG,YAAA,EAAM,EACO;IAGXH,EAFJ,CAAAC,cAAA,qBAAgB,eACgC,kBACc;IAAtBD,EAAA,CAAAU,UAAA,mBAAAuK,0EAAA;MAAA,MAAAC,OAAA,GAAAlL,EAAA,CAAAY,aAAA,CAAAuK,IAAA,EAAA1F,SAAA;MAAA,OAAAzF,EAAA,CAAAgB,WAAA,CAASkK,OAAA,CAAAN,KAAA,EAAW;IAAA,EAAC;IAAC5K,EAAA,CAAAE,MAAA,oBAAE;IAGlEF,EAHkE,CAAAG,YAAA,EAAS,EACjE,EACS,EACT;;;;IARkBH,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,QAAAU,MAAA,CAAAsK,mBAAA,EAAApL,EAAA,CAAAiG,aAAA,CAA2B;;;AD7bzD;AACA,IAAKyD,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAyBpB,OAAM,MAAO2B,yBAA0B,SAAQtL,aAAa;EAyE1D;EACAgE,cAAcA,CAACuH,MAAc;IAC3B,MAAMC,MAAM,GAAG,IAAI,CAACzF,aAAa,CAAC0F,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACrH,KAAK,KAAKkH,MAAM,CAAC;IACnE,OAAOC,MAAM,GAAGA,MAAM,CAAClH,KAAK,GAAG,KAAK;EACtC;EAEAqH,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B,EAC/BC,eAA+B;IAEvC,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAtFzB,KAAAC,KAAK,GAAG,IAAI;IAKZ,KAAAC,cAAc,GAA8B,EAAE;IAG9C,KAAAC,eAAe,GAAG,CAChB;MACEjI,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,CAAC;IAAE,KAAAiI,iBAAiB,GAAG,IAAI;IAC9B,KAAA5I,WAAW,GAAW,EAAE;IACxB;IACA;IACA;IACA;IACA,KAAAN,UAAU,GAAW,EAAE;IACvB,KAAAmJ,SAAS,GAAY,KAAK;IAC1B,KAAAnB,mBAAmB,GAAW,EAAE;IAChC,KAAAoB,aAAa,GAAY,KAAK;IAC9B,KAAA/I,UAAU,GAAY,IAAI;IAC1B;IACA,KAAAgJ,sBAAsB,GAAgB,EAAE,EAAC;IACzC,KAAAC,eAAe,GAAgB,EAAE,EAAC;IAClC,KAAAC,iBAAiB,GAAgB,EAAE,EAAC;IACpC,KAAAC,aAAa,GAAa,EAAE,EAAC;IAC7B,KAAAhF,eAAe,GAAW,EAAE;IAC5B,KAAAmC,eAAe,GAAqB,IAAI;IACxC,KAAAc,mBAAmB,GAAW,CAAC;IAE/B;IACA,KAAAgC,2BAA2B,GAAW,CAAC;IACvC,KAAAC,wBAAwB,GAAW,EAAE;IACrC,KAAAC,4BAA4B,GAAW,CAAC;IAExC,KAAAC,oBAAoB,GAAW,CAAC;IAChC,KAAAC,iBAAiB,GAAW,EAAE;IAC9B,KAAAC,qBAAqB,GAAW,CAAC;IAEjC;IACA,KAAA1E,gBAAgB,GAAW,CAAC;IAC5B,KAAAF,aAAa,GAAW,EAAE;IAC1B,KAAAF,iBAAiB,GAAW,CAAC;IAE7B;IACA,KAAA+E,eAAe,GAAG,CAChB;MAAE/I,KAAK,EAAEsF,eAAe,CAACC,iBAAiB;MAAEtF,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAED,KAAK,EAAEsF,eAAe,CAACE,SAAS;MAAEvF,KAAK,EAAE;IAAM,CAAE,CACpD;IACD,KAAA+I,gBAAgB,GAAoB1D,eAAe,CAACC,iBAAiB;IACrE,KAAA0D,kBAAkB,GAAY,IAAI,EAAC;IACnC;IACA,KAAA3D,eAAe,GAAGA,eAAe;IAEjC;IACA,KAAA5D,aAAa,GAAG,CAAC;MACf1B,KAAK,EAAE,CAAC;MAAE;MACVC,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC;KACb,CAAC;EAkBF;EAESiJ,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACxB,iBAAiB,CAACyB,6CAA6C,CAAC;MACnEC,IAAI,EAAE;QACJC,OAAO,EAAE,KAAK;QACd5J,OAAO,EAAE;;KAEZ,CAAC,CACC6J,IAAI,CACH/N,GAAG,CAACgO,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACzB,cAAc,GAAGwB,GAAG,CAACE,OAAO,EAAE5L,MAAM,GAAG0L,GAAG,CAACE,OAAO,GAAG,EAAE;QAC5D,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAAC3B,cAAc,CAAC,CAAC,CAAC,CAAC9L,GAAI;MACxD;IACF,CAAC,CAAC,EACFX,QAAQ,CAAC,MAAM,IAAI,CAACqO,eAAe,EAAE,CAAC,CACvC,CAACC,SAAS,EAAE;EACjB;EAAED,eAAeA,CAACE,SAAA,GAAoB,CAAC;IACrC,OAAO,IAAI,CAAClC,gBAAgB,CAACmC,mCAAmC,CAAC;MAC/DV,IAAI,EAAE;QACJW,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtCM,QAAQ,EAAE,IAAI,CAAC/B,iBAAiB;QAChC5I,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7B;QACAN,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B;QACAkL,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,SAAS,EAAEN,SAAS;QACpBzK,UAAU,EAAE,IAAI,CAACA;;KAEpB,CAAC,CAACkK,IAAI,CACL/N,GAAG,CAACgO,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC3J,YAAY,GAAG0J,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACW,YAAY,GAAGb,GAAG,CAACc,UAAW;QAEnC,IAAI,IAAI,CAACxK,YAAY,CAAChC,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACqK,SAAS,GAAG,IAAI,CAACrI,YAAY,CAAC,CAAC,CAAC,CAACN,UAAW;QACnD;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEAvC,MAAMA,CAAA;IACJ,IAAI,CAAC2M,eAAe,EAAE,CAACC,SAAS,EAAE;EACpC;EAEAU,WAAWA,CAACT,SAAiB;IAC3B,IAAI,CAACF,eAAe,CAACE,SAAS,CAAC,CAACD,SAAS,EAAE;EAC7C;EAEAhN,sBAAsBA,CAAA;IACpB,IAAI,CAAC+K,gBAAgB,CAAC4C,2CAA2C,CAAC;MAChEnB,IAAI,EAAE,IAAI,CAACM;KACZ,CAAC,CAACE,SAAS,CAACL,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE;UACzB,IAAI,CAAC5C,eAAe,CAAC6C,iBAAiB,CAAClB,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CAAA;IACxB,IAAI,CAAC/C,gBAAgB,CAACgD,+CAA+C,CAAC;MACpEvB,IAAI,EAAE,IAAI,CAACM;KACZ,CAAC,CAACE,SAAS,CAACL,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE;UACzB,IAAI,CAAC5C,eAAe,CAAC6C,iBAAiB,CAAClB,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EACAnN,MAAMA,CAACuN,GAAQ;IACb,IAAI,CAAC9C,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC7H,gBAAgB,GAAG;MACtBR,OAAO,EAAE,CAAC,CAAC;KACZ;IACD,IAAI,CAAC8H,aAAa,CAACsD,IAAI,CAACD,GAAG,CAAC;EAC9B;EACA1M,kBAAkBA,CAAC4M,IAA6B,EAAEF,GAAQ;IACxD,IAAI,CAAC9C,KAAK,GAAG,KAAK;IAClB,IAAI,CAAC7H,gBAAgB,GAAG;MAAE,GAAG6K;IAAI,CAAE;IACnC,IAAI,CAACvD,aAAa,CAACsD,IAAI,CAACD,GAAG,CAAC;EAC9B;EACAtM,oBAAoBA,CAACwM,IAA6B,EAAEF,GAAqB;IACvE,IAAI,CAAC3K,gBAAgB,GAAG;MAAE,GAAG6K;IAAI,CAAE;IACnC;IACA,IAAI,CAACvC,aAAa,GAAG,IAAI,CAACtI,gBAAgB,CAACrC,gBAAgB,GACzD,IAAI,CAACqC,gBAAgB,CAACrC,gBAAgB,CAACmN,GAAG,CAACC,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,GAAGC,QAAQ,CAACD,EAAE,CAAC,GAAGA,EAAE,CAAC,GAAG,EAAE;IAEnG;IACA,IAAI,CAAC1C,iBAAiB,GAAG,EAAE,EAAC;IAC5B,IAAI,CAACE,2BAA2B,GAAG,CAAC;IACpC,IAAI,CAACG,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACpF,eAAe,GAAG,EAAE;IAEzB,IAAI,CAACI,aAAa,EAAE;IACpB,IAAI,CAAC4D,aAAa,CAACsD,IAAI,CAACD,GAAG,EAAE;MAAEM,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAC1D,KAAK,CAAC2D,KAAK,EAAE;IAClB,IAAI,CAAC3D,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpL,gBAAgB,CAAC1B,KAAK,CAAC;IACxD,IAAI,CAACkJ,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpL,gBAAgB,CAAChB,KAAK,CAAC;IACxD,IAAI,CAACwI,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpL,gBAAgB,CAACf,SAAS,CAAC;IAC5D,IAAI,CAACuI,KAAK,CAAC4D,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACpL,gBAAgB,CAACZ,WAAW,CAAC;IAClE;IACA,IAAI,CAACoI,KAAK,CAAC4D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACpL,gBAAgB,CAAClB,UAAU,CAAC;IAC/D,IAAI,CAAC0I,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpL,gBAAgB,CAACR,OAAO,CAAC;IAC1D,IAAI,CAACgI,KAAK,CAAC6D,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACrL,gBAAgB,CAAC1B,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAACkJ,KAAK,CAAC6D,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACrL,gBAAgB,CAAChB,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAACwI,KAAK,CAAC6D,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACrL,gBAAgB,CAACf,SAAS,EAAE,EAAE,CAAC;IACzE,IAAI,CAACuI,KAAK,CAAC6D,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAACrL,gBAAgB,CAACZ,WAAW,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,CAACoI,KAAK,CAAC6D,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACrL,gBAAgB,CAAClB,UAAU,EAAE,EAAE,CAAC;EAC9E;EAEAwC,QAAQA,CAACqJ,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC1D,KAAK,CAAC8D,aAAa,CAAC1N,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC2J,OAAO,CAACgE,aAAa,CAAC,IAAI,CAAC/D,KAAK,CAAC8D,aAAa,CAAC;MACpD;IACF;IAAE,IAAI,CAAC5D,gBAAgB,CAAC8D,qCAAqC,CAAC;MAC5DrC,IAAI,EAAE;QACJW,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtC;QACA3K,UAAU,EAAE,IAAI,CAACkB,gBAAgB,CAAClB,UAAU;QAC5CR,KAAK,EAAE,IAAI,CAAC0B,gBAAgB,CAAC1B,KAAK;QAClCU,KAAK,EAAE,IAAI,CAACgB,gBAAgB,CAAChB,KAAK;QAClCC,SAAS,EAAE,IAAI,CAACe,gBAAgB,CAACf,SAAS;QAC1CG,WAAW,EAAE,IAAI,CAACY,gBAAgB,CAACZ,WAAW;QAC9CC,YAAY,EAAE,IAAI,CAACW,gBAAgB,CAACX,YAAY;QAChDoM,WAAW,EAAE,IAAI,CAAC5D,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC7H,gBAAgB,CAACnB,GAAI;QAC3DhB,MAAM,EAAE,IAAI,CAACmC,gBAAgB,CAACnC,MAAM;QACpC2B,OAAO,EAAE,IAAI,CAACQ,gBAAgB,CAACR,OAAO;QAAE;QACxCkM,UAAU,EAAG,IAAI,CAAC1L,gBAAwB,CAAC2L,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CACCtC,IAAI,CACH/N,GAAG,CAACgO,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChC,OAAO,CAACqE,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACrE,OAAO,CAACsE,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFzQ,QAAQ,CAAC,MAAM,IAAI,CAACqO,eAAe,EAAE,CAAC,EACtCtO,QAAQ,CAAC,MAAMuP,GAAG,CAACrE,KAAK,EAAE,CAAC,CAC5B,CAACqD,SAAS,EAAE;EACjB;EAEAvI,OAAOA,CAACuJ,GAAQ;IACdA,GAAG,CAACrE,KAAK,EAAE;EACb;EAEAyF,eAAeA,CAACC,KAAU;IACxB,MAAMC,MAAM,GAAgCD,KAAK,CAACC,MAAO;IACzD,MAAMC,MAAM,GAAe,IAAIC,UAAU,EAAE;IAC3CD,MAAM,CAACE,kBAAkB,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACI,MAAM,GAAIC,CAAM,IAAI;MACzB,MAAMC,SAAS,GAAWD,CAAC,CAACN,MAAM,CAACQ,MAAM;MACzC,MAAMC,EAAE,GAAkBnR,IAAI,CAACoR,IAAI,CAACH,SAAS,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAE,CAAC;MAElE,MAAMC,MAAM,GAAWH,EAAE,CAACI,UAAU,CAAC,CAAC,CAAC;MACvC,MAAMC,EAAE,GAAmBL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC;MAE5C,IAAII,WAAW,GAAY,IAAI;MAC/B,MAAMpC,IAAI,GAAGtP,IAAI,CAAC2R,KAAK,CAACC,aAAa,CAACJ,EAAE,CAAC;MACzC,IAAIlC,IAAI,IAAIA,IAAI,CAACjN,MAAM,GAAG,CAAC,EAAE;QAC3BiN,IAAI,CAACuC,OAAO,CAAEC,CAAM,IAAI;UACtB,IAAI,EAAEA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE;YACnDJ,WAAW,GAAG,KAAK;UACrB;QACF,CAAC,CAAC;QAEF,IAAI,CAACA,WAAW,EAAE;UAChB,IAAI,CAAC1F,OAAO,CAACsE,YAAY,CAAC,WAAW,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAACnE,gBAAgB,CAAC4F,2CAA2C,CAAC;YAChEnE,IAAI,EAAE;cACJW,YAAY,EAAE,IAAI,CAACL,mBAAmB;cACtC8D,KAAK,EAAEtB,MAAM,CAACI,KAAK,CAAC,CAAC;;WAExB,CAAC,CAAChD,IAAI,CACL/N,GAAG,CAACgO,GAAG,IAAG;YACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;cACvB,IAAI,CAAChC,OAAO,CAACqE,aAAa,CAAC,MAAM,CAAC;YACpC,CAAC,MAAM;cACL,IAAI,CAACrE,OAAO,CAACsE,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;YACzC;UACF,CAAC,CAAC,EACFzQ,QAAQ,CAAC,MAAM,IAAI,CAACqO,eAAe,CAAC,CAAC,CAAC,CAAC,CACxC,CAACC,SAAS,EAAE;QACf;MACF,CAAC,MAAM;QACL,IAAI,CAACpC,OAAO,CAACsE,YAAY,CAAC,uBAAuB,CAAC;MACpD;MACAG,KAAK,CAACC,MAAM,CAACnM,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEA0N,SAASA,CAACC,QAAgB,EAAEC,MAAwB;IAClD,IAAI,CAAC5G,mBAAmB,GAAG2G,QAAQ;IACnC,IAAI,CAACnG,aAAa,CAACsD,IAAI,CAAC8C,MAAM,CAAC;EACjC;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACzF,aAAa,EAAE;MACtB,IAAI,CAAC/I,UAAU,GAAG,KAAK;MACvB,IAAI,CAACuK,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC,CAAC,MACI;MACH,IAAI,CAACxK,UAAU,GAAG,IAAI;MACtB,IAAI,CAACuK,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC;EACF;EACA;EACA5I,eAAeA,CAAC4J,GAAqB;IACnC;IACA,IAAI,CAACtC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACE,2BAA2B,GAAG,CAAC;IACpC,IAAI,CAACG,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACpF,eAAe,GAAG,EAAE;IAEzB,IAAI,CAACI,aAAa,EAAE;IACpB,IAAI,CAAC4D,aAAa,CAACsD,IAAI,CAACD,GAAG,EAAE;MAAEM,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAEA2C,UAAUA,CAAA;IACR;IACA,IAAI,IAAI,CAAC7E,kBAAkB,IAAI,IAAI,CAACU,mBAAmB,EAAE;MACvD,IAAI,CAAC7B,eAAe,CAACiG,iCAAiC,CAAC;QACrD1E,IAAI,EAAE;UACJW,YAAY,EAAE,IAAI,CAACL,mBAAmB;UACtCqE,YAAY,EAAE,IAAI,CAAChF,gBAAgB;UACnCoB,SAAS,EAAE,IAAI,CAAChG,gBAAgB;UAChC8F,QAAQ,EAAE,IAAI,CAAChG;;OAElB,CAAC,CAAC2F,SAAS,CAAEL,GAA2C,IAAI;QAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,IAAI,CAAC/C,SAAS,GAAG8C,GAAG,CAACE,OAAO,EAAEsB,GAAG,CAAEiD,OAA+B,KAAM;YACtEhD,EAAE,EAAEgD,OAAO,CAAClP,GAAG,IAAI,CAAC;YACpB+C,IAAI,EAAEmM,OAAO,CAACC,YAAY,IAAID,OAAO,CAACzP,KAAK,IAAI,EAAE;YACjD2P,IAAI,EAAE,CAAC;YACPvM,YAAY,EAAEqM,OAAO,CAACG,OAAO,GAAG,0BAA0BH,OAAO,CAACG,OAAO,EAAE,GAAG,EAAE;YAChFxI,OAAO,EAAEqI,OAAO,CAACG,OAAO,GAAG,0BAA0BH,OAAO,CAACG,OAAO,EAAE,GAAG,EAAE;YAC3EC,YAAY,EAAEJ,OAAO,CAACK,SAAS,GAAG,IAAIC,IAAI,CAACN,OAAO,CAACK,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,QAAQ,EAAE,IAAI,CAACxF,gBAAgB,CAAC;WACjC,CAAC,CAAC,IAAI,EAAE;UAET,IAAI,CAAChF,iBAAiB,GAAGwF,GAAG,CAACc,UAAU,IAAI,CAAC;UAE5C;UACA;UACA,IAAI,IAAI,CAAC5E,cAAc,CAAC5H,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC0K,aAAa,CAAC1K,MAAM,GAAG,CAAC,EAAE;YACrE,IAAI,CAAC2Q,gCAAgC,EAAE;UACzC,CAAC,MAAM;YACL;YACA,IAAI,CAACC,qBAAqB,EAAE;UAC9B;QACF,CAAC,MAAM;UACL,IAAI,CAACjH,OAAO,CAACsE,YAAY,CAACvC,GAAG,CAACwC,OAAO,IAAI,QAAQ,CAAC;UAClD,IAAI,CAACtF,SAAS,GAAG,EAAE;UACnB,IAAI,CAACjB,eAAe,GAAG,EAAE;UACzB;UACA,IAAI,IAAI,CAACC,cAAc,CAAC5H,MAAM,KAAK,CAAC,EAAE;YACpC,IAAI,CAAC4H,cAAc,GAAG,EAAE;UAC1B;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACgB,SAAS,GAAG,EAAE;MACnB,IAAI,CAACjB,eAAe,GAAG,EAAE;MACzB,IAAI,CAACC,cAAc,GAAG,EAAE;IAC1B;EACF;EAIA;EACA+I,gCAAgCA,CAAA;IAC9B;IACA;IACA,IAAI,CAAC3G,eAAe,CAACiG,iCAAiC,CAAC;MACrD1E,IAAI,EAAE;QACJW,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtCqE,YAAY,EAAE,IAAI,CAAChF,gBAAgB;QACnCoB,SAAS,EAAE,CAAC;QACZF,QAAQ,EAAE,IAAI,CAAC;;KAElB,CAAC,CAACL,SAAS,CAAEL,GAA2C,IAAI;MAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,MAAMkF,kBAAkB,GAAGnF,GAAG,CAACE,OAAO,EAAEsB,GAAG,CAAEiD,OAA+B,KAAM;UAChFhD,EAAE,EAAEgD,OAAO,CAAClP,GAAG,IAAI,CAAC;UACpB+C,IAAI,EAAEmM,OAAO,CAACC,YAAY,IAAID,OAAO,CAACzP,KAAK,IAAI,EAAE;UACjD2P,IAAI,EAAE,CAAC;UACPvM,YAAY,EAAEqM,OAAO,CAACG,OAAO,GAAG,0BAA0BH,OAAO,CAACG,OAAO,EAAE,GAAG,EAAE;UAChFxI,OAAO,EAAEqI,OAAO,CAACG,OAAO,GAAG,0BAA0BH,OAAO,CAACG,OAAO,EAAE,GAAG,EAAE;UAC3EC,YAAY,EAAEJ,OAAO,CAACK,SAAS,GAAG,IAAIC,IAAI,CAACN,OAAO,CAACK,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;UAC1EC,QAAQ,EAAE,IAAI,CAACxF,gBAAgB,CAAC;SACjC,CAAC,CAAC,IAAI,EAAE;QAET;QACA,MAAM4F,WAAW,GAAGD,kBAAkB,CAACE,MAAM,CAACC,KAAK,IAAI,IAAI,CAACtG,aAAa,CAACuG,QAAQ,CAACD,KAAK,CAAC7D,EAAE,CAAC,CAAC;QAC7F,IAAI,CAACvF,cAAc,GAAG,CAAC,GAAGkJ,WAAW,CAAC;QAEtC;QACAA,WAAW,CAACtB,OAAO,CAACwB,KAAK,IAAG;UAC1B,MAAME,gBAAgB,GAAG,IAAI,CAACzG,iBAAiB,CAAC0G,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;UACrF,IAAI+D,gBAAgB,KAAK,CAAC,CAAC,EAAE;YAC3B,IAAI,CAACzG,iBAAiB,CAAC4G,IAAI,CAACL,KAAK,CAAC;UACpC;QACF,CAAC,CAAC;QAEF;QACA,IAAI,CAACJ,qBAAqB,EAAE;MAC9B;IACF,CAAC,CAAC;EACJ;EAEA;EACAA,qBAAqBA,CAAA;IACnB;IACA,IAAIU,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAAC1I,SAAS,CAAC;IAE3C;IACA,IAAI,IAAI,CAAClD,eAAe,CAAC6L,IAAI,EAAE,EAAE;MAC/B,MAAMC,UAAU,GAAG,IAAI,CAAC9L,eAAe,CAAC+L,WAAW,EAAE;MACrDH,iBAAiB,GAAGA,iBAAiB,CAACP,MAAM,CAACC,KAAK,IAChDA,KAAK,CAAChN,IAAI,CAACyN,WAAW,EAAE,CAACR,QAAQ,CAACO,UAAU,CAAC,CAC9C;IACH;IAEA;IACA,MAAME,WAAW,GAAG,IAAI,CAAC9J,cAAc,CAACsF,GAAG,CAACkE,GAAG,IAAIA,GAAG,CAACjE,EAAE,CAAC;IAC1D,IAAI,CAACxF,eAAe,GAAG2J,iBAAiB,CAACP,MAAM,CAACC,KAAK,IAAI,CAACU,WAAW,CAACT,QAAQ,CAACD,KAAK,CAAC7D,EAAE,CAAC,CAAC;EAC3F;EAEAvH,qBAAqBA,CAAA;IACnB,IAAI,CAACgL,qBAAqB,EAAE;EAC9B;EAEAzM,cAAcA,CAAC6M,KAAgB,EAAE5C,KAAa;IAC5C,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACuD,eAAe,EAAE;IACzB;IAEA;IACA,MAAMC,KAAK,GAAG,IAAI,CAACjK,eAAe,CAACwJ,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;IACxE,IAAIyE,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAChK,cAAc,CAACyJ,IAAI,CAACL,KAAK,CAAC;MAE/B;MACA,MAAME,gBAAgB,GAAG,IAAI,CAACzG,iBAAiB,CAAC0G,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;MACrF,IAAI+D,gBAAgB,KAAK,CAAC,CAAC,EAAE;QAC3B,IAAI,CAACzG,iBAAiB,CAAC4G,IAAI,CAACL,KAAK,CAAC;MACpC;MAEA,IAAI,CAACJ,qBAAqB,EAAE;IAC9B;EACF;EAEA1L,eAAeA,CAAC8L,KAAgB,EAAE5C,KAAa;IAC7C,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACuD,eAAe,EAAE;IACzB;IAEA;IACA,MAAMC,KAAK,GAAG,IAAI,CAAChK,cAAc,CAACuJ,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;IACvE,IAAIyE,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAChK,cAAc,CAACiK,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAEpC;MACA,MAAMV,gBAAgB,GAAG,IAAI,CAACzG,iBAAiB,CAAC0G,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;MACrF,IAAI+D,gBAAgB,GAAG,CAAC,CAAC,EAAE;QACzB,IAAI,CAACzG,iBAAiB,CAACoH,MAAM,CAACX,gBAAgB,EAAE,CAAC,CAAC;MACpD;MAEA,IAAI,CAACN,qBAAqB,EAAE;IAC9B;EACF;EAEAnK,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACmB,cAAc,CAACyJ,IAAI,CAAC,GAAG,IAAI,CAAC1J,eAAe,CAAC;IAEjD;IACA,IAAI,CAACA,eAAe,CAAC6H,OAAO,CAACwB,KAAK,IAAG;MACnC,MAAME,gBAAgB,GAAG,IAAI,CAACzG,iBAAiB,CAAC0G,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;MACrF,IAAI+D,gBAAgB,KAAK,CAAC,CAAC,EAAE;QAC3B,IAAI,CAACzG,iBAAiB,CAAC4G,IAAI,CAACL,KAAK,CAAC;MACpC;IACF,CAAC,CAAC;IAEF,IAAI,CAACJ,qBAAqB,EAAE;EAC9B;EAEAjK,kBAAkBA,CAAA;IAChB;IACA;IACA,IAAI,CAACiB,cAAc,CAAC4H,OAAO,CAACwB,KAAK,IAAG;MAClC,MAAME,gBAAgB,GAAG,IAAI,CAACzG,iBAAiB,CAAC0G,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;MACrF,IAAI+D,gBAAgB,GAAG,CAAC,CAAC,EAAE;QACzB,IAAI,CAACzG,iBAAiB,CAACoH,MAAM,CAACX,gBAAgB,EAAE,CAAC,CAAC;MACpD;IACF,CAAC,CAAC;IAEF,IAAI,CAACtJ,cAAc,GAAG,EAAE;IACxB,IAAI,CAACgJ,qBAAqB,EAAE;EAC9B;EAEAxL,YAAYA,CAAC4L,KAAgB;IAC3B,OAAO,IAAI,CAACtG,aAAa,CAACuG,QAAQ,CAACD,KAAK,CAAC7D,EAAE,CAAC;EAC9C;EAEA7H,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACsC,cAAc,CAACmJ,MAAM,CAACC,KAAK,IAAI,IAAI,CAAC5L,YAAY,CAAC4L,KAAK,CAAC,CAAC,CAAChR,MAAM;EAC7E;EAEAuF,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACqC,cAAc,CAACmJ,MAAM,CAACC,KAAK,IAAI,CAAC,IAAI,CAAC5L,YAAY,CAAC4L,KAAK,CAAC,CAAC,CAAChR,MAAM;EAC9E;EAEA;EACA6G,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACe,cAAc,CAAC4H,OAAO,CAACwB,KAAK,IAAG;MAClC,MAAME,gBAAgB,GAAG,IAAI,CAACzG,iBAAiB,CAAC0G,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;MACrF,IAAI+D,gBAAgB,GAAG,CAAC,CAAC,EAAE;QACzB,IAAI,CAACzG,iBAAiB,CAACoH,MAAM,CAACX,gBAAgB,EAAE,CAAC,CAAC;MACpD;IACF,CAAC,CAAC;IAEF,IAAI,CAACtJ,cAAc,GAAG,EAAE;IACxB,IAAI,CAACgJ,qBAAqB,EAAE;EAC9B;EAEApM,YAAYA,CAACwM,KAAgB,EAAEc,eAAiC,EAAE1D,KAAY;IAC5EA,KAAK,CAACuD,eAAe,EAAE;IACvB,IAAI,CAAC9J,eAAe,GAAGmJ,KAAK;IAC5B;IACA,IAAI,CAACrI,mBAAmB,GAAG,IAAI,CAACC,SAAS,CAACuI,SAAS,CAAEC,GAAc,IAAKA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;IAC5F,IAAI,CAACzD,aAAa,CAACsD,IAAI,CAAC8E,eAAe,CAAC;EAC1C;EAEA7J,aAAaA,CAAA;IACX,IAAI,IAAI,CAACU,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAACd,eAAe,GAAG,IAAI,CAACe,SAAS,CAAC,IAAI,CAACD,mBAAmB,CAAC;IACjE;EACF;EAEAR,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,mBAAmB,GAAG,IAAI,CAACC,SAAS,CAAC5I,MAAM,GAAG,CAAC,EAAE;MACxD,IAAI,CAAC2I,mBAAmB,EAAE;MAC1B,IAAI,CAACd,eAAe,GAAG,IAAI,CAACe,SAAS,CAAC,IAAI,CAACD,mBAAmB,CAAC;IACjE;EACF;EAEAJ,6BAA6BA,CAAA;IAC3B,IAAI,IAAI,CAACV,eAAe,EAAE;MACxB,MAAMkK,UAAU,GAAG,IAAI,CAACnK,cAAc,CAACoK,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK,IAAI,CAACtF,eAAgB,CAACsF,EAAE,CAAC;MACvF,IAAI4E,UAAU,EAAE;QACd,IAAI,CAAC7M,eAAe,CAAC,IAAI,CAAC2C,eAAe,CAAC;MAC5C,CAAC,MAAM;QACL,IAAI,CAAC1D,cAAc,CAAC,IAAI,CAAC0D,eAAe,CAAC;MAC3C;IACF;EACF;EAEAiB,eAAeA,CAACkI,KAAgB;IAC9B,OAAO,IAAI,CAACpJ,cAAc,CAACoK,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAAC9E,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;EACvE;EACA7F,uBAAuBA,CAACyF,GAAQ;IAC9B,IAAI,IAAI,CAACnF,cAAc,CAAC5H,MAAM,GAAG,CAAC,EAAE;MAClC;MACA,MAAM+N,gBAAgB,GAAG,IAAI,CAACnG,cAAc,CAACsF,GAAG,CAACkE,GAAG,IAAIA,GAAG,CAACjE,EAAE,CAAC,CAAC,CAAM;MACtE,IAAI,IAAI,CAACvF,cAAc,CAAC5H,MAAM,KAAK,CAAC,EAAE;QACpC;QACA,IAAI,CAACoC,gBAAgB,CAAC0L,UAAU,GAAG,IAAI,CAAClG,cAAc,CAAC,CAAC,CAAC,CAACuF,EAAE;MAC9D,CAAC,MAAM;QACL;QACA,MAAM+E,UAAU,GAAG,IAAI,CAACtK,cAAc,CAACsF,GAAG,CAACkE,GAAG,IAAIA,GAAG,CAACpN,IAAI,CAAC,CAACmO,IAAI,CAAC,IAAI,CAAC;QACtE,IAAI,CAACxI,OAAO,CAACqE,aAAa,CAAC,OAAO,IAAI,CAACpG,cAAc,CAAC5H,MAAM,SAASkS,UAAU,EAAE,CAAC;QAClF;QACA,IAAI,CAAC9P,gBAAgB,CAAC0L,UAAU,GAAG,IAAI,CAAClG,cAAc,CAAC,CAAC,CAAC,CAACuF,EAAE;MAC9D;MAEA;MACC,IAAI,CAAC/K,gBAAwB,CAAC2L,gBAAgB,GAAGA,gBAAgB;MAElE;MACA,IAAI,IAAI,CAAC3L,gBAAgB,CAACnB,GAAG,EAAE;QAC7B,IAAI,CAACmR,gBAAgB,EAAE;MACzB;IACF;IAEA,IAAI,CAACvL,iBAAiB,EAAE;IACxBkG,GAAG,CAACrE,KAAK,EAAE;EACb,CAAC,CAAE;EACH0J,gBAAgBA,CAAA;IACd,IAAI,CAACtI,gBAAgB,CAAC8D,qCAAqC,CAAC;MAC1DrC,IAAI,EAAE;QACJW,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtC3K,UAAU,EAAE,IAAI,CAACkB,gBAAgB,CAAClB,UAAU;QAC5CR,KAAK,EAAE,IAAI,CAAC0B,gBAAgB,CAAC1B,KAAK;QAClCU,KAAK,EAAE,IAAI,CAACgB,gBAAgB,CAAChB,KAAK;QAClCC,SAAS,EAAE,IAAI,CAACe,gBAAgB,CAACf,SAAS;QAC1CG,WAAW,EAAE,IAAI,CAACY,gBAAgB,CAACZ,WAAW;QAC9CC,YAAY,EAAE,IAAI,CAACW,gBAAgB,CAACX,YAAY;QAChDoM,WAAW,EAAE,IAAI,CAACzL,gBAAgB,CAACnB,GAAI;QACvChB,MAAM,EAAE,IAAI,CAACmC,gBAAgB,CAACnC,MAAM;QACpC2B,OAAO,EAAE,IAAI,CAACQ,gBAAgB,CAACR,OAAO;QAAE;QACxCkM,UAAU,EAAG,IAAI,CAAC1L,gBAAwB,CAAC2L,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CAACtC,IAAI,CACL/N,GAAG,CAACgO,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChC,OAAO,CAACqE,aAAa,CAAC,QAAQ,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,CAACrE,OAAO,CAACsE,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFzQ,QAAQ,CAAC,MAAM,IAAI,CAACqO,eAAe,EAAE,CAAC,EACtCtO,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAAC4E,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC,CACH,CAAC2J,SAAS,EAAE;EACf;EACA3E,kBAAkBA,CAAC2F,GAAQ;IACzB,IAAI,CAAClG,iBAAiB,EAAE;IACxB,IAAI,CAACnB,eAAe,GAAG,EAAE;IACzB,IAAI,CAACY,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAC3ByG,GAAG,CAACrE,KAAK,EAAE;EACb,CAAC;EACD2J,eAAeA,CAAC3B,QAAyB;IACvC,IAAI,CAACxF,gBAAgB,GAAGwF,QAAQ;IAChC,IAAI,CAACvF,kBAAkB,GAAG,IAAI;IAE9B;IACA,IAAI,CAACvD,cAAc,GAAG,IAAI,CAAC6C,iBAAiB,CAACsG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACN,QAAQ,KAAKA,QAAQ,CAAC;IAEzF;IACA,IAAI,CAACpK,gBAAgB,GAAG,CAAC;IACzB,IAAI,IAAI,CAACuF,mBAAmB,EAAE;MAC5B,IAAI,CAACmE,UAAU,EAAE;IACnB;EACF;EAEA;EACAsC,gBAAgBA,CAAC5B,QAAgB;IAC/B,MAAMrH,MAAM,GAAG,IAAI,CAAC4B,eAAe,CAAC3B,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACrH,KAAK,KAAKwO,QAAQ,CAAC;IACvE,OAAOrH,MAAM,GAAGA,MAAM,CAAClH,KAAK,GAAG,MAAM;EACvC;EAEA;EACAoF,0BAA0BA,CAACmJ,QAAyB;IAClD,OAAO,IAAI,CAACjG,iBAAiB,CAACsG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACN,QAAQ,KAAKA,QAAQ,CAAC,CAAC1Q,MAAM;EACnF;EAEA;EACA8F,aAAaA,CAAA;IACX,IAAI,CAACyM,0BAA0B,EAAE;IACjC,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAD,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAAC1G,mBAAmB,EAAE;MAC5B,IAAI,CAAC7B,eAAe,CAACiG,iCAAiC,CAAC;QACrD1E,IAAI,EAAE;UACJe,SAAS,EAAE,IAAI,CAAC3B,2BAA2B;UAC3CyB,QAAQ,EAAE,IAAI,CAACxB,wBAAwB;UACvCsB,YAAY,EAAE,IAAI,CAACL,mBAAmB;UACtCqE,YAAY,EAAE1I,eAAe,CAACC;;OAEjC,CAAC,CAACsE,SAAS,CAAEL,GAA2C,IAAI;QAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACpB,sBAAsB,GAAGmB,GAAG,CAACE,OAAO,EAAEsB,GAAG,CAAEiD,OAA+B,KAAM;YACnFhD,EAAE,EAAEgD,OAAO,CAAClP,GAAG,IAAI,CAAC;YACpB+C,IAAI,EAAEmM,OAAO,CAACC,YAAY,IAAID,OAAO,CAACzP,KAAK,IAAI,EAAE;YACjD2P,IAAI,EAAE,CAAC;YACPvM,YAAY,EAAEqM,OAAO,CAACG,OAAO,GAAG,0BAA0BH,OAAO,CAACG,OAAO,EAAE,GAAG,EAAE;YAChFxI,OAAO,EAAEqI,OAAO,CAACG,OAAO,GAAG,0BAA0BH,OAAO,CAACG,OAAO,EAAE,GAAG,EAAE;YAC3EC,YAAY,EAAEJ,OAAO,CAACK,SAAS,GAAG,IAAIC,IAAI,CAACN,OAAO,CAACK,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,QAAQ,EAAElJ,eAAe,CAACC;WAC3B,CAAC,CAAC,IAAI,EAAE;UACT,IAAI,CAACoD,4BAA4B,GAAGa,GAAG,CAACc,UAAU,IAAI,CAAC;QACzD;MACF,CAAC,CAAC;IACJ;EACF;EAEA;EACAgG,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAAC3G,mBAAmB,EAAE;MAC5B,IAAI,CAAC7B,eAAe,CAACiG,iCAAiC,CAAC;QACrD1E,IAAI,EAAE;UACJe,SAAS,EAAE,IAAI,CAACxB,oBAAoB;UACpCsB,QAAQ,EAAE,IAAI,CAACrB,iBAAiB;UAChCmB,YAAY,EAAE,IAAI,CAACL,mBAAmB;UACtCqE,YAAY,EAAE1I,eAAe,CAACE;;OAEjC,CAAC,CAACqE,SAAS,CAAEL,GAA2C,IAAI;QAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACnB,eAAe,GAAGkB,GAAG,CAACE,OAAO,EAAEsB,GAAG,CAAEiD,OAA+B,KAAM;YAC5EhD,EAAE,EAAEgD,OAAO,CAAClP,GAAG,IAAI,CAAC;YACpB+C,IAAI,EAAEmM,OAAO,CAACC,YAAY,IAAID,OAAO,CAACzP,KAAK,IAAI,EAAE;YACjD2P,IAAI,EAAE,CAAC;YACPvM,YAAY,EAAEqM,OAAO,CAACG,OAAO,GAAG,0BAA0BH,OAAO,CAACG,OAAO,EAAE,GAAG,EAAE;YAChFxI,OAAO,EAAEqI,OAAO,CAACG,OAAO,GAAG,0BAA0BH,OAAO,CAACG,OAAO,EAAE,GAAG,EAAE;YAC3EC,YAAY,EAAEJ,OAAO,CAACK,SAAS,GAAG,IAAIC,IAAI,CAACN,OAAO,CAACK,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,QAAQ,EAAElJ,eAAe,CAACE;WAC3B,CAAC,CAAC,IAAI,EAAE;UACT,IAAI,CAACsD,qBAAqB,GAAGU,GAAG,CAACc,UAAU,IAAI,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EACF;EAEA;EACAiG,WAAWA,CAACzB,KAAgB;IAC1B,MAAMY,KAAK,GAAG,IAAI,CAACnH,iBAAiB,CAAC0G,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;IAC1E,IAAIyE,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAACnH,iBAAiB,CAAC4G,IAAI,CAACL,KAAK,CAAC;IACpC;EACF;EAEA;EACA0B,aAAaA,CAAC1B,KAAgB;IAC5B,MAAMY,KAAK,GAAG,IAAI,CAACnH,iBAAiB,CAAC0G,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;IAC1E,IAAIyE,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACnH,iBAAiB,CAACoH,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IACzC;EACF;EAEA;EACA9I,eAAeA,CAACkI,KAAgB;IAC9B,OAAO,IAAI,CAACvG,iBAAiB,CAACuH,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAACjE,EAAE,KAAK6D,KAAK,CAAC7D,EAAE,CAAC;EAChE;EAEA;EACAwF,oBAAoBA,CAAC3B,KAAgB;IACnC,IAAI,IAAI,CAAClI,eAAe,CAACkI,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC0B,aAAa,CAAC1B,KAAK,CAAC;IAC3B,CAAC,MAAM;MACL,IAAI,CAACyB,WAAW,CAACzB,KAAK,CAAC;IACzB;EACF;EAEA;EACA4B,iCAAiCA,CAAA;IAC/B,OAAO,IAAI,CAACnI,iBAAiB,CAACsG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACN,QAAQ,KAAKlJ,eAAe,CAACC,iBAAiB,CAAC;EACrG;EAEA;EACAoL,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAACpI,iBAAiB,CAACsG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACN,QAAQ,KAAKlJ,eAAe,CAACE,SAAS,CAAC;EAC7F;EAEA;EACAoL,2BAA2BA,CAACC,IAAY;IACtC,IAAI,CAACpI,2BAA2B,GAAGoI,IAAI;IACvC,IAAI,CAACR,0BAA0B,EAAE;EACnC;EAEA;EACAS,oBAAoBA,CAACD,IAAY;IAC/B,IAAI,CAACjI,oBAAoB,GAAGiI,IAAI;IAChC,IAAI,CAACP,mBAAmB,EAAE;EAC5B;EAEA;EACAjM,gBAAgBA,CAACwM,IAAY;IAC3B,IAAI,CAACzM,gBAAgB,GAAGyM,IAAI;IAC5B,IAAI,CAAC/C,UAAU,EAAE;EACnB;;;uCAtwBW7G,yBAAyB,EAAArL,EAAA,CAAAmV,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArV,EAAA,CAAAmV,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAvV,EAAA,CAAAmV,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAzV,EAAA,CAAAmV,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA3V,EAAA,CAAAmV,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA7V,EAAA,CAAAmV,iBAAA,CAAAS,EAAA,CAAAE,eAAA,GAAA9V,EAAA,CAAAmV,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAAhW,EAAA,CAAAmV,iBAAA,CAAAS,EAAA,CAAAK,cAAA;IAAA;EAAA;;;YAAzB5K,yBAAyB;MAAA6K,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApW,EAAA,CAAAqW,0BAAA,EAAArW,EAAA,CAAAsW,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCvCpC5W,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAkB,SAAA,qBAAiC;UACnClB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAACD,EAAA,CAAAE,MAAA,yJACtC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAICH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,gBACF;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAACH,EAAA,CAAAC,cAAA,qBACc;UAA5ED,EAAA,CAAAuE,gBAAA,2BAAAuS,uEAAArS,MAAA;YAAAzE,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA/W,EAAA,CAAA2E,kBAAA,CAAAkS,GAAA,CAAA9I,mBAAA,EAAAtJ,MAAA,MAAAoS,GAAA,CAAA9I,mBAAA,GAAAtJ,MAAA;YAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;UAAA,EAAiC;UAACzE,EAAA,CAAAU,UAAA,2BAAAoW,uEAAA;YAAA9W,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA,OAAA/W,EAAA,CAAAgB,WAAA,CAAiB6V,GAAA,CAAAxV,MAAA,EAAQ;UAAA,EAAC;UAC5DrB,EAAA,CAAA6C,UAAA,KAAAmU,+CAAA,wBAA4E;UAKlFhX,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAaFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACF;UAAAD,EAAA,CAAAE,MAAA,6CAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAC,cAAA,iBAAwG;UAAxDD,EAAA,CAAAuE,gBAAA,2BAAA0S,mEAAAxS,MAAA;YAAAzE,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA/W,EAAA,CAAA2E,kBAAA,CAAAkS,GAAA,CAAAnT,WAAA,EAAAe,MAAA,MAAAoS,GAAA,CAAAnT,WAAA,GAAAe,MAAA;YAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;UAAA,EAAyB;UAE7EzE,EAFI,CAAAG,YAAA,EAAwG,EACpG,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACF;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjEH,EAAA,CAAAC,cAAA,iBAAqG;UAAvDD,EAAA,CAAAuE,gBAAA,2BAAA2S,mEAAAzS,MAAA;YAAAzE,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA/W,EAAA,CAAA2E,kBAAA,CAAAkS,GAAA,CAAAzT,UAAA,EAAAqB,MAAA,MAAAoS,GAAA,CAAAzT,UAAA,GAAAqB,MAAA;YAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;UAAA,EAAwB;UAE1EzE,EAFI,CAAAG,YAAA,EAAqG,EACjG,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAuB,eAC0B,uBAEjB;UAD+BD,EAAA,CAAAuE,gBAAA,2BAAA4S,yEAAA1S,MAAA;YAAAzE,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA/W,EAAA,CAAA2E,kBAAA,CAAAkS,GAAA,CAAArK,aAAA,EAAA/H,MAAA,MAAAoS,GAAA,CAAArK,aAAA,GAAA/H,MAAA;YAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;UAAA,EAA2B;UACpFzE,EAAA,CAAAU,UAAA,oBAAA0W,kEAAA;YAAApX,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA,OAAA/W,EAAA,CAAAgB,WAAA,CAAU6V,GAAA,CAAA5E,YAAA,EAAc;UAAA,EAAC;UACzBjS,EAAA,CAAAE,MAAA,gHACF;UAAAF,EAAA,CAAAG,YAAA,EAAc;UAOdH,EANA,CAAA6C,UAAA,KAAAwU,4CAAA,qBAA8F,KAAAC,4CAAA,qBAEV,KAAAC,4CAAA,qBAEE,KAAAC,4CAAA,qBAEF;UACpFxX,EAAA,CAAAC,cAAA,oBAAqG;UAAnCD,EAAA,CAAAU,UAAA,oBAAA+W,4DAAAhT,MAAA;YAAAzE,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA,OAAA/W,EAAA,CAAAgB,WAAA,CAAU6V,GAAA,CAAAxG,eAAA,CAAA5L,MAAA,CAAuB;UAAA,EAAC;UAApGzE,EAAA,CAAAG,YAAA,EAAqG;UACrGH,EAAA,CAAAC,cAAA,kBAA4E;UAAvCD,EAAA,CAAAU,UAAA,mBAAAgX,4DAAA;YAAA1X,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA,OAAA/W,EAAA,CAAAgB,WAAA,CAAS6V,GAAA,CAAA9H,0BAAA,EAA4B;UAAA,EAAC;UAAC/O,EAAA,CAAAE,MAAA,6CAAO;UAAAF,EAAA,CAAAkB,SAAA,aAC9C;UAG3ClB,EAH2C,CAAAG,YAAA,EAAS,EAC1C,EACF,EACF;UAKEH,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAA6C,UAAA,KAAA8U,wCAAA,iBAAwD;UACxD3X,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEhDF,EAFgD,CAAAG,YAAA,EAAK,EAC9C,EACC;UACRH,EAAA,CAAA6C,UAAA,KAAA+U,2CAAA,oBAA+D;UAiCrE5X,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADqCD,EAA1D,CAAAuE,gBAAA,kCAAAsT,mFAAApT,MAAA;YAAAzE,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA/W,EAAA,CAAA2E,kBAAA,CAAAkS,GAAA,CAAApI,YAAA,EAAAhK,MAAA,MAAAoS,GAAA,CAAApI,YAAA,GAAAhK,MAAA;YAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;UAAA,EAAiC,4BAAAqT,6EAAArT,MAAA;YAAAzE,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA/W,EAAA,CAAA2E,kBAAA,CAAAkS,GAAA,CAAAtI,QAAA,EAAA9J,MAAA,MAAAoS,GAAA,CAAAtI,QAAA,GAAA9J,MAAA;YAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;UAAA,EAAwB,wBAAAsT,yEAAAtT,MAAA;YAAAzE,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA/W,EAAA,CAAA2E,kBAAA,CAAAkS,GAAA,CAAA3I,SAAA,EAAAzJ,MAAA,MAAAoS,GAAA,CAAA3I,SAAA,GAAAzJ,MAAA;YAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAAyD,MAAA;UAAA,EAAqB;UAC5FzE,EAAA,CAAAU,UAAA,wBAAAqX,yEAAAtT,MAAA;YAAAzE,EAAA,CAAAY,aAAA,CAAAmW,GAAA;YAAA,OAAA/W,EAAA,CAAAgB,WAAA,CAAc6V,GAAA,CAAAlI,WAAA,CAAAlK,MAAA,CAAmB;UAAA,EAAC;UAGxCzE,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAgVVH,EA9UA,CAAA6C,UAAA,KAAAmV,iDAAA,kCAAAhY,EAAA,CAAAiY,sBAAA,CAAoD,KAAAC,iDAAA,kCAAAlY,EAAA,CAAAiY,sBAAA,CAiFK,KAAAE,iDAAA,iCAAAnY,EAAA,CAAAiY,sBAAA,CA0NC,KAAAG,iDAAA,iCAAApY,EAAA,CAAAiY,sBAAA,CAmCH;;;UAvb3CjY,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAA6F,gBAAA,YAAAgR,GAAA,CAAA9I,mBAAA,CAAiC;UACA/N,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAyW,GAAA,CAAAzK,cAAA,CAAiB;UAmBJpM,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAA6F,gBAAA,YAAAgR,GAAA,CAAAnT,WAAA,CAAyB;UAO3B1D,EAAA,CAAAO,SAAA,GAAwB;UAAxBP,EAAA,CAAA6F,gBAAA,YAAAgR,GAAA,CAAAzT,UAAA,CAAwB;UAKXpD,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAA6F,gBAAA,YAAAgR,GAAA,CAAArK,aAAA,CAA2B;UAI7ExM,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,SAAAyW,GAAA,CAAAwB,aAAA,CAAmB;UAEnBrY,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,SAAAyW,GAAA,CAAA7S,MAAA,CAAY;UAEZhE,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAyW,GAAA,CAAAyB,QAAA,CAAc;UAEYtY,EAAA,CAAAO,SAAA,EAAmB;UAAnBP,EAAA,CAAAI,UAAA,SAAAyW,GAAA,CAAA0B,aAAA,CAAmB;UAiBrBvY,EAAA,CAAAO,SAAA,IAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAAyW,GAAA,CAAAtK,SAAA,SAAuB;UAKlDvM,EAAA,CAAAO,SAAA,GAAqD;UAArDP,EAAA,CAAAI,UAAA,SAAAyW,GAAA,CAAA3S,YAAA,YAAA2S,GAAA,CAAA3S,YAAA,CAAAhC,MAAA,KAAqD;UAmCjDlC,EAAA,CAAAO,SAAA,GAAiC;UAAyBP,EAA1D,CAAA6F,gBAAA,mBAAAgR,GAAA,CAAApI,YAAA,CAAiC,aAAAoI,GAAA,CAAAtI,QAAA,CAAwB,SAAAsI,GAAA,CAAA3I,SAAA,CAAqB;;;qBDzEtFzO,YAAY,EAAA+Y,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE5Y,YAAY,EAAA6Y,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAA1D,EAAA,CAAA2D,eAAA,EAAA3D,EAAA,CAAA4D,mBAAA,EAAA5D,EAAA,CAAA6D,qBAAA,EAAA7D,EAAA,CAAA8D,qBAAA,EAAA9D,EAAA,CAAA+D,mBAAA,EAAA/D,EAAA,CAAAgE,gBAAA,EAAAhE,EAAA,CAAAiE,iBAAA,EAAAjE,EAAA,CAAAkE,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}