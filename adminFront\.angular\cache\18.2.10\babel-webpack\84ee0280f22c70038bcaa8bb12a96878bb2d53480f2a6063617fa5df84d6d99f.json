{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction TemplateCreatorComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.validationErrors[\"api\"], \" \");\n  }\n}\nfunction TemplateCreatorComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.validationErrors[\"name\"], \" \");\n  }\n}\nfunction TemplateCreatorComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.validationErrors[\"description\"], \" \");\n  }\n}\nfunction TemplateCreatorComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"label\", 40)(2, \"input\", 41);\n    i0.ɵɵlistener(\"change\", function TemplateCreatorComponent_div_38_Template_input_change_2_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleSelectAll());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 42);\n    i0.ɵɵelementStart(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r0.isAllSelected())(\"indeterminate\", ctx_r0.isIndeterminate())(\"disabled\", ctx_r0.isSubmitting);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isAllSelected() ? \"\\u53D6\\u6D88\\u5168\\u9078\" : \"\\u5168\\u9078\", \" \");\n  }\n}\nfunction TemplateCreatorComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u66AB\\u7121\\u53EF\\u9078\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateCreatorComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"label\", 47);\n    i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_div_41_Template_label_click_1_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleItemSelection(item_r4));\n    });\n    i0.ɵɵelementStart(2, \"input\", 48);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateCreatorComponent_div_41_Template_input_ngModelChange_2_listener($event) {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r4.selected, $event) || (item_r4.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_div_41_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49)(4, \"div\", 50);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"item_\", i_r5, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r4.selected);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSubmitting);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r4.CRequirement || item_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.CGroupName || item_r4.description);\n  }\n}\nfunction TemplateCreatorComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.validationErrors[\"items\"], \" \");\n  }\n}\nfunction TemplateCreatorComponent_i_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 54);\n  }\n}\nfunction TemplateCreatorComponent_i_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 55);\n  }\n}\nexport class TemplateCreatorComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.availableData = []; // 父元件傳入的可選資料\n    this.templateType = 1; // 模板類型，1=客變需求\n    this.close = new EventEmitter(); // 關閉事件\n    this.templateCreated = new EventEmitter(); // 模板創建成功事件\n    // 新增模板表單\n    this.newTemplate = {\n      name: ''\n    };\n    // 表單驗證狀態\n    this.isSubmitting = false;\n    this.validationErrors = {};\n  }\n  ngOnInit() {\n    // 初始化時重置所有選擇狀態\n    this.resetForm();\n  }\n  // 重置表單\n  resetForm() {\n    this.newTemplate = {\n      name: ''\n    };\n    this.validationErrors = {};\n    this.isSubmitting = false;\n    // 重置選擇狀態\n    if (this.availableData) {\n      this.availableData.forEach(item => item.selected = false);\n    }\n  }\n  // 驗證表單\n  validateForm() {\n    this.validationErrors = {};\n    let isValid = true;\n    // 驗證模板名稱\n    if (!this.newTemplate.name.trim()) {\n      this.validationErrors['name'] = '請輸入模板名稱';\n      isValid = false;\n    } else if (this.newTemplate.name.trim().length > 50) {\n      this.validationErrors['name'] = '模板名稱不能超過50個字符';\n      isValid = false;\n    }\n    // 驗證是否選擇了項目\n    const selectedItems = this.getSelectedItems();\n    if (selectedItems.length === 0) {\n      this.validationErrors['items'] = '請至少選擇一個項目';\n      isValid = false;\n    }\n    return isValid;\n  }\n  // 獲取選中的項目\n  getSelectedItems() {\n    if (!this.availableData) return [];\n    return this.availableData.filter(item => item.selected).map(item => ({\n      CGroupName: item.CGroupName || null,\n      CReleateName: item.CReleateName || item.CRequirement || item.name || null,\n      CReleateId: item.CReleateId || item.CRequirementID || item.ID || item.id || 0\n    }));\n  }\n  // 儲存新模板\n  saveTemplate() {\n    if (!this.validateForm()) {\n      return;\n    }\n    this.isSubmitting = true;\n    const selectedItems = this.getSelectedItems();\n    // 準備 API 請求資料\n    const saveTemplateArgs = {\n      CTemplateId: null,\n      // 新增時為 null\n      CTemplateName: this.newTemplate.name.trim(),\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      CStatus: 1,\n      // 啟用狀態\n      Details: selectedItems.map(item => ({\n        CTemplateDetailId: null,\n        // 新增時為 null\n        CReleateId: item.CReleateId,\n        // 關聯主檔ID\n        CReleateName: item.CReleateName,\n        // 關聯名稱\n        CGroupName: item.CGroupName // 群組名稱\n      }))\n    };\n    // 調用 SaveTemplate API\n    this.templateService.apiTemplateSaveTemplatePost$Json({\n      body: saveTemplateArgs\n    }).subscribe({\n      next: response => {\n        this.isSubmitting = false;\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          this.templateCreated.emit(); // 通知父組件模板創建成功\n          this.close.emit(); // 關閉對話框\n        } else {\n          // API 返回錯誤\n          this.validationErrors['api'] = response.Message || '保存失敗，請稍後重試';\n        }\n      },\n      error: error => {\n        this.isSubmitting = false;\n        this.validationErrors['api'] = '網絡錯誤，請檢查網絡連接後重試';\n        console.error('保存模板失敗:', error);\n      }\n    });\n  }\n  // 取消操作\n  cancel() {\n    this.close.emit();\n  }\n  // 獲取選中項目數量\n  getSelectedCount() {\n    return this.availableData ? this.availableData.filter(item => item.selected).length : 0;\n  }\n  // 切換項目選擇狀態\n  toggleItemSelection(item) {\n    item.selected = !item.selected;\n    // 清除項目選擇相關的驗證錯誤\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\n      delete this.validationErrors['items'];\n    }\n  }\n  // 全選/取消全選\n  toggleSelectAll() {\n    const hasUnselected = this.availableData.some(item => !item.selected);\n    this.availableData.forEach(item => item.selected = hasUnselected);\n    // 清除項目選擇相關的驗證錯誤\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\n      delete this.validationErrors['items'];\n    }\n  }\n  // 檢查是否全選\n  isAllSelected() {\n    return this.availableData && this.availableData.length > 0 && this.availableData.every(item => item.selected);\n  }\n  // 檢查是否部分選中\n  isIndeterminate() {\n    const selectedCount = this.getSelectedCount();\n    return selectedCount > 0 && selectedCount < this.availableData.length;\n  }\n  static {\n    this.ɵfac = function TemplateCreatorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateCreatorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateCreatorComponent,\n      selectors: [[\"app-template-creator\"]],\n      inputs: {\n        availableData: \"availableData\",\n        templateType: \"templateType\"\n      },\n      outputs: {\n        close: \"close\",\n        templateCreated: \"templateCreated\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 52,\n      vars: 23,\n      consts: [[2, \"width\", \"90vw\", \"max-width\", \"800px\", \"height\", \"80vh\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"fas\", \"fa-plus\", \"mr-2\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [2, \"overflow\", \"auto\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [1, \"template-form\", 3, \"ngSubmit\"], [1, \"form-section\", \"mb-4\"], [1, \"section-title\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [1, \"input-row\"], [1, \"input-group\"], [1, \"input-label\"], [1, \"required\"], [\"type\", \"text\", \"name\", \"templateName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"maxlength\", \"50\", 1, \"input-field\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"type\", \"text\", \"name\", \"templateDescription\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u63CF\\u8FF0\\uFF08\\u53EF\\u9078\\uFF09\", \"maxlength\", \"100\", 1, \"input-field\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"form-section\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"section-title\", \"mb-0\"], [1, \"fas\", \"fa-list\", \"mr-2\"], [1, \"selection-summary\"], [1, \"badge\", \"badge-primary\"], [\"class\", \"select-all-control mb-3\", 4, \"ngIf\"], [1, \"items-selector\"], [\"class\", \"empty-items\", 4, \"ngIf\"], [\"class\", \"item-option\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"invalid-feedback d-block\", 4, \"ngIf\"], [1, \"form-actions\", \"d-flex\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"mr-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", 3, \"click\", \"disabled\"], [\"class\", \"fas fa-spinner fa-spin mr-1\", 4, \"ngIf\"], [\"class\", \"fas fa-save mr-1\", 4, \"ngIf\"], [1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-2\"], [1, \"invalid-feedback\"], [1, \"select-all-control\", \"mb-3\"], [1, \"select-all-label\"], [\"type\", \"checkbox\", 1, \"select-all-checkbox\", 3, \"change\", \"checked\", \"indeterminate\", \"disabled\"], [1, \"checkmark\"], [1, \"select-all-text\"], [1, \"empty-items\"], [1, \"fas\", \"fa-info-circle\"], [1, \"item-option\"], [1, \"item-label\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"item-checkbox\", 3, \"ngModelChange\", \"click\", \"ngModel\", \"name\", \"disabled\"], [1, \"item-content\"], [1, \"item-title\"], [1, \"item-desc\"], [1, \"invalid-feedback\", \"d-block\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-1\"], [1, \"fas\", \"fa-save\", \"mr-1\"]],\n      template: function TemplateCreatorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\")(2, \"div\", 1)(3, \"h5\", 2);\n          i0.ɵɵelement(4, \"i\", 3);\n          i0.ɵɵtext(5, \"\\u65B0\\u589E\\u6A21\\u677F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_Template_button_click_6_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelement(7, \"i\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"nb-card-body\", 6);\n          i0.ɵɵtemplate(9, TemplateCreatorComponent_div_9_Template, 3, 1, \"div\", 7);\n          i0.ɵɵelementStart(10, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function TemplateCreatorComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.saveTemplate();\n          });\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"h6\", 10);\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵtext(14, \"\\u6A21\\u677F\\u8CC7\\u8A0A \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13)(17, \"label\", 14);\n          i0.ɵɵtext(18, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n          i0.ɵɵelementStart(19, \"span\", 15);\n          i0.ɵɵtext(20, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"input\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateCreatorComponent_Template_input_ngModelChange_21_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.newTemplate.name, $event) || (ctx.newTemplate.name = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, TemplateCreatorComponent_div_22_Template, 2, 1, \"div\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 13)(24, \"label\", 14);\n          i0.ɵɵtext(25, \"\\u6A21\\u677F\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"input\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateCreatorComponent_Template_input_ngModelChange_26_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.newTemplate.description, $event) || (ctx.newTemplate.description = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, TemplateCreatorComponent_div_27_Template, 2, 1, \"div\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 19)(29, \"div\", 20)(30, \"h6\", 21);\n          i0.ɵɵelement(31, \"i\", 22);\n          i0.ɵɵtext(32, \"\\u9078\\u64C7\\u8981\\u52A0\\u5165\\u6A21\\u677F\\u7684\\u9805\\u76EE \");\n          i0.ɵɵelementStart(33, \"span\", 15);\n          i0.ɵɵtext(34, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 23)(36, \"span\", 24);\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(38, TemplateCreatorComponent_div_38_Template, 6, 4, \"div\", 25);\n          i0.ɵɵelementStart(39, \"div\", 26);\n          i0.ɵɵtemplate(40, TemplateCreatorComponent_div_40_Template, 4, 0, \"div\", 27)(41, TemplateCreatorComponent_div_41_Template, 8, 6, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, TemplateCreatorComponent_div_42_Template, 3, 1, \"div\", 29);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"nb-card-footer\")(44, \"div\", 30)(45, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_Template_button_click_45_listener() {\n            return ctx.cancel();\n          });\n          i0.ɵɵelement(46, \"i\", 32);\n          i0.ɵɵtext(47, \"\\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_Template_button_click_48_listener() {\n            return ctx.saveTemplate();\n          });\n          i0.ɵɵtemplate(49, TemplateCreatorComponent_i_49_Template, 1, 0, \"i\", 34)(50, TemplateCreatorComponent_i_50_Template, 1, 0, \"i\", 35);\n          i0.ɵɵtext(51);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.validationErrors[\"api\"]);\n          i0.ɵɵadvance(12);\n          i0.ɵɵclassProp(\"is-invalid\", ctx.validationErrors[\"name\"]);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newTemplate.name);\n          i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.validationErrors[\"name\"]);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ctx.validationErrors[\"description\"]);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newTemplate.description);\n          i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.validationErrors[\"description\"]);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7 \", ctx.getSelectedCount(), \" \\u9805\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.availableData.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"has-error\", ctx.validationErrors[\"items\"]);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.availableData.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.validationErrors[\"items\"]);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"\\u4FDD\\u5B58\\u4E2D...\" : \"\\u5132\\u5B58\\u6A21\\u677F\", \" \");\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, NbCardModule, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardFooterComponent, i4.NbCardHeaderComponent, NbButtonModule],\n      styles: [\".template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%] {\\n  border: 1px solid #e4e9f0;\\n  border-radius: 8px;\\n  padding: 1.5rem;\\n  background-color: #fafbfc;\\n}\\n.template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n  margin-bottom: 1rem;\\n}\\n.template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3498db;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n@media (max-width: 768px) {\\n  .template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 250px;\\n  margin-bottom: 1rem;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 500;\\n  color: #2c3e50;\\n  margin-bottom: 0.5rem;\\n  font-size: 0.9rem;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n  margin-left: 2px;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 0.9rem;\\n  transition: border-color 0.3s ease;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3498db;\\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #e74c3c;\\n  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:disabled {\\n  background-color: #f8f9fa;\\n  cursor: not-allowed;\\n}\\n.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n  font-size: 0.8rem;\\n  margin-top: 0.25rem;\\n  display: block;\\n}\\n\\n.selection-summary[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.4rem 0.8rem;\\n}\\n\\n.select-all-control[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 4px;\\n}\\n.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 0;\\n  cursor: pointer;\\n  font-weight: 500;\\n}\\n.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  transform: scale(1.1);\\n}\\n.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%]   .select-all-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%]:hover   .select-all-text[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.items-selector[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n  border: 1px solid #dee2e6;\\n  border-radius: 4px;\\n  background-color: white;\\n}\\n.items-selector.has-error[_ngcontent-%COMP%] {\\n  border-color: #e74c3c;\\n}\\n.items-selector[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #6c757d;\\n}\\n.items-selector[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  display: block;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f1f3f4;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem;\\n  margin: 0;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  transform: scale(1.1);\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2c3e50;\\n  margin-bottom: 0.25rem;\\n}\\n.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-desc[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #6c757d;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  gap: 0.5rem;\\n}\\n.form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n  font-weight: 500;\\n}\\n.form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n}\\n\\n@media (max-width: 768px) {\\n  .template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n    min-width: 100%;\\n  }\\n  .items-selector[_ngcontent-%COMP%] {\\n    max-height: 250px;\\n  }\\n  .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 0.5rem;\\n  }\\n}\\n.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n.item-option[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  transition: box-shadow 0.3s ease;\\n}\\n.form-section[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "validationErrors", "ɵɵlistener", "TemplateCreatorComponent_div_38_Template_input_change_2_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "toggleSelectAll", "ɵɵproperty", "isAllSelected", "isIndeterminate", "isSubmitting", "TemplateCreatorComponent_div_41_Template_label_click_1_listener", "item_r4", "_r3", "$implicit", "toggleItemSelection", "ɵɵtwoWayListener", "TemplateCreatorComponent_div_41_Template_input_ngModelChange_2_listener", "$event", "ɵɵtwoWayBindingSet", "selected", "TemplateCreatorComponent_div_41_Template_input_click_2_listener", "stopPropagation", "ɵɵpropertyInterpolate1", "i_r5", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CRequirement", "name", "CGroupName", "description", "TemplateCreatorComponent", "constructor", "templateService", "availableData", "templateType", "close", "templateCreated", "newTemplate", "ngOnInit", "resetForm", "for<PERSON>ach", "item", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "trim", "length", "selectedItems", "getSelectedItems", "filter", "map", "CReleateName", "CReleateId", "CRequirementID", "ID", "id", "saveTemplate", "saveTemplateArgs", "CTemplateId", "CTemplateName", "CTemplateType", "CStatus", "Details", "CTemplateDetailId", "apiTemplateSaveTemplatePost$Json", "body", "subscribe", "next", "response", "StatusCode", "emit", "Message", "error", "console", "cancel", "getSelectedCount", "has<PERSON><PERSON><PERSON>", "some", "every", "selectedCount", "ɵɵdirectiveInject", "i1", "TemplateService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TemplateCreatorComponent_Template", "rf", "ctx", "TemplateCreatorComponent_Template_button_click_6_listener", "ɵɵtemplate", "TemplateCreatorComponent_div_9_Template", "TemplateCreatorComponent_Template_form_ngSubmit_10_listener", "TemplateCreatorComponent_Template_input_ngModelChange_21_listener", "TemplateCreatorComponent_div_22_Template", "TemplateCreatorComponent_Template_input_ngModelChange_26_listener", "TemplateCreatorComponent_div_27_Template", "TemplateCreatorComponent_div_38_Template", "TemplateCreatorComponent_div_40_Template", "TemplateCreatorComponent_div_41_Template", "TemplateCreatorComponent_div_42_Template", "TemplateCreatorComponent_Template_button_click_45_listener", "TemplateCreatorComponent_Template_button_click_48_listener", "TemplateCreatorComponent_i_49_Template", "TemplateCreatorComponent_i_50_Template", "ɵɵclassProp", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "CheckboxControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "MaxLengthValidator", "NgModel", "NgForm", "i4", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-creator\\template-creator.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-creator\\template-creator.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport { TemplateService } from 'src/services/api/services/template.service';\nimport { SaveTemplateArgs, SaveTemplateDetailArgs } from 'src/services/api/models';\n\n// 選中項目的介面定義\ninterface SelectedItem {\n  CGroupName: string | null;\n  CReleateName: string | null;\n  CReleateId: number;\n}\n\n@Component({\n  selector: 'app-template-creator',\n  templateUrl: './template-creator.component.html',\n  styleUrls: ['./template-creator.component.scss'],\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\n})\nexport class TemplateCreatorComponent implements OnInit {\n  @Input() availableData: any[] = []; // 父元件傳入的可選資料\n  @Input() templateType: number = 1; // 模板類型，1=客變需求\n  @Output() close = new EventEmitter<void>(); // 關閉事件\n  @Output() templateCreated = new EventEmitter<void>(); // 模板創建成功事件\n\n  // 新增模板表單\n  newTemplate = {\n    name: ''\n  };\n\n  // 表單驗證狀態\n  isSubmitting = false;\n  validationErrors: { [key: string]: string } = {};\n\n  constructor(private templateService: TemplateService) { }\n\n  ngOnInit() {\n    // 初始化時重置所有選擇狀態\n    this.resetForm();\n  }\n\n  // 重置表單\n  resetForm() {\n    this.newTemplate = {\n      name: ''\n    };\n    this.validationErrors = {};\n    this.isSubmitting = false;\n\n    // 重置選擇狀態\n    if (this.availableData) {\n      this.availableData.forEach(item => item.selected = false);\n    }\n  }\n\n  // 驗證表單\n  validateForm(): boolean {\n    this.validationErrors = {};\n    let isValid = true;\n\n    // 驗證模板名稱\n    if (!this.newTemplate.name.trim()) {\n      this.validationErrors['name'] = '請輸入模板名稱';\n      isValid = false;\n    } else if (this.newTemplate.name.trim().length > 50) {\n      this.validationErrors['name'] = '模板名稱不能超過50個字符';\n      isValid = false;\n    }\n\n\n\n    // 驗證是否選擇了項目\n    const selectedItems = this.getSelectedItems();\n    if (selectedItems.length === 0) {\n      this.validationErrors['items'] = '請至少選擇一個項目';\n      isValid = false;\n    }\n\n    return isValid;\n  }\n\n  // 獲取選中的項目\n  getSelectedItems(): SelectedItem[] {\n    if (!this.availableData) return [];\n\n    return this.availableData\n      .filter(item => item.selected)\n      .map(item => ({\n        CGroupName: item.CGroupName || null,\n        CReleateName: item.CReleateName || item.CRequirement || item.name || null,\n        CReleateId: item.CReleateId || item.CRequirementID || item.ID || item.id || 0\n      }));\n  }\n\n  // 儲存新模板\n  saveTemplate() {\n    if (!this.validateForm()) {\n      return;\n    }\n\n    this.isSubmitting = true;\n    const selectedItems = this.getSelectedItems();\n\n    // 準備 API 請求資料\n    const saveTemplateArgs: SaveTemplateArgs = {\n      CTemplateId: null, // 新增時為 null\n      CTemplateName: this.newTemplate.name.trim(),\n      CTemplateType: this.templateType, // 1=客變需求\n      CStatus: 1, // 啟用狀態\n      Details: selectedItems.map(item => ({\n        CTemplateDetailId: null, // 新增時為 null\n        CReleateId: item.CReleateId, // 關聯主檔ID\n        CReleateName: item.CReleateName, // 關聯名稱\n        CGroupName: item.CGroupName // 群組名稱\n      } as SaveTemplateDetailArgs))\n    };\n\n    // 調用 SaveTemplate API\n    this.templateService.apiTemplateSaveTemplatePost$Json({\n      body: saveTemplateArgs\n    }).subscribe({\n      next: (response) => {\n        this.isSubmitting = false;\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          this.templateCreated.emit(); // 通知父組件模板創建成功\n          this.close.emit(); // 關閉對話框\n        } else {\n          // API 返回錯誤\n          this.validationErrors['api'] = response.Message || '保存失敗，請稍後重試';\n        }\n      },\n      error: (error) => {\n        this.isSubmitting = false;\n        this.validationErrors['api'] = '網絡錯誤，請檢查網絡連接後重試';\n        console.error('保存模板失敗:', error);\n      }\n    });\n  }\n\n  // 取消操作\n  cancel() {\n    this.close.emit();\n  }\n\n  // 獲取選中項目數量\n  getSelectedCount(): number {\n    return this.availableData ? this.availableData.filter(item => item.selected).length : 0;\n  }\n\n  // 切換項目選擇狀態\n  toggleItemSelection(item: any) {\n    item.selected = !item.selected;\n    // 清除項目選擇相關的驗證錯誤\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\n      delete this.validationErrors['items'];\n    }\n  }\n\n  // 全選/取消全選\n  toggleSelectAll() {\n    const hasUnselected = this.availableData.some(item => !item.selected);\n    this.availableData.forEach(item => item.selected = hasUnselected);\n\n    // 清除項目選擇相關的驗證錯誤\n    if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\n      delete this.validationErrors['items'];\n    }\n  }\n\n  // 檢查是否全選\n  isAllSelected(): boolean {\n    return this.availableData && this.availableData.length > 0 &&\n      this.availableData.every(item => item.selected);\n  }\n\n  // 檢查是否部分選中\n  isIndeterminate(): boolean {\n    const selectedCount = this.getSelectedCount();\n    return selectedCount > 0 && selectedCount < this.availableData.length;\n  }\n}\n", "<nb-card style=\"width: 90vw; max-width: 800px; height: 80vh;\">\n  <nb-card-header>\n    <div class=\"d-flex justify-content-between align-items-center\">\n      <h5 class=\"mb-0\">\n        <i class=\"fas fa-plus mr-2\"></i>新增模板\n      </h5>\n      <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"cancel()\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n  </nb-card-header>\n  \n  <nb-card-body style=\"overflow: auto;\">\n    <!-- API 錯誤提示 -->\n    <div class=\"alert alert-danger\" *ngIf=\"validationErrors['api']\">\n      <i class=\"fas fa-exclamation-triangle mr-2\"></i>\n      {{ validationErrors['api'] }}\n    </div>\n\n    <form (ngSubmit)=\"saveTemplate()\" class=\"template-form\">\n      <!-- 模板基本資訊 -->\n      <div class=\"form-section mb-4\">\n        <h6 class=\"section-title\">\n          <i class=\"fas fa-info-circle mr-2\"></i>模板資訊\n        </h6>\n        \n        <div class=\"input-row\">\n          <div class=\"input-group\">\n            <label class=\"input-label\">\n              模板名稱 <span class=\"required\">*</span>\n            </label>\n            <input \n              type=\"text\" \n              class=\"input-field\"\n              [class.is-invalid]=\"validationErrors['name']\"\n              [(ngModel)]=\"newTemplate.name\" \n              name=\"templateName\"\n              placeholder=\"請輸入模板名稱\" \n              maxlength=\"50\"\n              [disabled]=\"isSubmitting\">\n            <div class=\"invalid-feedback\" *ngIf=\"validationErrors['name']\">\n              {{ validationErrors['name'] }}\n            </div>\n          </div>\n          \n          <div class=\"input-group\">\n            <label class=\"input-label\">模板描述</label>\n            <input \n              type=\"text\" \n              class=\"input-field\"\n              [class.is-invalid]=\"validationErrors['description']\"\n              [(ngModel)]=\"newTemplate.description\" \n              name=\"templateDescription\"\n              placeholder=\"請輸入模板描述（可選）\" \n              maxlength=\"100\"\n              [disabled]=\"isSubmitting\">\n            <div class=\"invalid-feedback\" *ngIf=\"validationErrors['description']\">\n              {{ validationErrors['description'] }}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 項目選擇 -->\n      <div class=\"form-section\">\n        <div class=\"d-flex justify-content-between align-items-center mb-3\">\n          <h6 class=\"section-title mb-0\">\n            <i class=\"fas fa-list mr-2\"></i>選擇要加入模板的項目 <span class=\"required\">*</span>\n          </h6>\n          <div class=\"selection-summary\">\n            <span class=\"badge badge-primary\">已選擇 {{ getSelectedCount() }} 項</span>\n          </div>\n        </div>\n\n        <!-- 全選控制 -->\n        <div class=\"select-all-control mb-3\" *ngIf=\"availableData.length > 0\">\n          <label class=\"select-all-label\">\n            <input \n              type=\"checkbox\" \n              class=\"select-all-checkbox\"\n              [checked]=\"isAllSelected()\"\n              [indeterminate]=\"isIndeterminate()\"\n              (change)=\"toggleSelectAll()\"\n              [disabled]=\"isSubmitting\">\n            <span class=\"checkmark\"></span>\n            <span class=\"select-all-text\">\n              {{ isAllSelected() ? '取消全選' : '全選' }}\n            </span>\n          </label>\n        </div>\n\n        <!-- 項目列表 -->\n        <div class=\"items-selector\" [class.has-error]=\"validationErrors['items']\">\n          <div *ngIf=\"availableData.length === 0\" class=\"empty-items\">\n            <i class=\"fas fa-info-circle\"></i>\n            <span>暫無可選項目</span>\n          </div>\n          \n          <div *ngFor=\"let item of availableData; let i = index\" class=\"item-option\">\n            <label class=\"item-label\" (click)=\"toggleItemSelection(item)\">\n              <input \n                type=\"checkbox\" \n                class=\"item-checkbox\" \n                [(ngModel)]=\"item.selected\" \n                name=\"item_{{i}}\"\n                [disabled]=\"isSubmitting\"\n                (click)=\"$event.stopPropagation()\">\n              <div class=\"item-content\">\n                <div class=\"item-title\">{{ item.CRequirement || item.name }}</div>\n                <div class=\"item-desc\">{{ item.CGroupName || item.description }}</div>\n              </div>\n            </label>\n          </div>\n        </div>\n        \n        <div class=\"invalid-feedback d-block\" *ngIf=\"validationErrors['items']\">\n          <i class=\"fas fa-exclamation-triangle mr-1\"></i>\n          {{ validationErrors['items'] }}\n        </div>\n      </div>\n    </form>\n  </nb-card-body>\n  \n  <nb-card-footer>\n    <div class=\"form-actions d-flex justify-content-end\">\n      <button \n        type=\"button\" \n        class=\"btn btn-outline-secondary mr-2\" \n        (click)=\"cancel()\"\n        [disabled]=\"isSubmitting\">\n        <i class=\"fas fa-times mr-1\"></i>取消\n      </button>\n      <button \n        type=\"button\" \n        class=\"btn btn-success\" \n        (click)=\"saveTemplate()\"\n        [disabled]=\"isSubmitting\">\n        <i class=\"fas fa-spinner fa-spin mr-1\" *ngIf=\"isSubmitting\"></i>\n        <i class=\"fas fa-save mr-1\" *ngIf=\"!isSubmitting\"></i>\n        {{ isSubmitting ? '保存中...' : '儲存模板' }}\n      </button>\n    </div>\n  </nb-card-footer>\n</nb-card>\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;;;;;;;;ICWzDC,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,aACF;;;;;IAuBQR,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,cACF;;;;;IAcAR,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,qBACF;;;;;;IAmBAR,EAFJ,CAAAC,cAAA,cAAsE,gBACpC,gBAOF;IAD1BD,EAAA,CAAAS,UAAA,oBAAAC,iEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAUP,MAAA,CAAAQ,eAAA,EAAiB;IAAA,EAAC;IAL9Bf,EAAA,CAAAI,YAAA,EAM4B;IAC5BJ,EAAA,CAAAE,SAAA,eAA+B;IAC/BF,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAG,MAAA,GACF;IAEJH,EAFI,CAAAI,YAAA,EAAO,EACD,EACJ;;;;IATAJ,EAAA,CAAAK,SAAA,GAA2B;IAG3BL,EAHA,CAAAgB,UAAA,YAAAT,MAAA,CAAAU,aAAA,GAA2B,kBAAAV,MAAA,CAAAW,eAAA,GACQ,aAAAX,MAAA,CAAAY,YAAA,CAEV;IAGzBnB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAU,aAAA,sDACF;;;;;IAMFjB,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAE,SAAA,YAAkC;IAClCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,2CAAM;IACdH,EADc,CAAAI,YAAA,EAAO,EACf;;;;;;IAGJJ,EADF,CAAAC,cAAA,cAA2E,gBACX;IAApCD,EAAA,CAAAS,UAAA,mBAAAW,gEAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAW,aAAA,CAAAW,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAiB,mBAAA,CAAAH,OAAA,CAAyB;IAAA,EAAC;IAC3DrB,EAAA,CAAAC,cAAA,gBAMqC;IAHnCD,EAAA,CAAAyB,gBAAA,2BAAAC,wEAAAC,MAAA;MAAA,MAAAN,OAAA,GAAArB,EAAA,CAAAW,aAAA,CAAAW,GAAA,EAAAC,SAAA;MAAAvB,EAAA,CAAA4B,kBAAA,CAAAP,OAAA,CAAAQ,QAAA,EAAAF,MAAA,MAAAN,OAAA,CAAAQ,QAAA,GAAAF,MAAA;MAAA,OAAA3B,EAAA,CAAAc,WAAA,CAAAa,MAAA;IAAA,EAA2B;IAG3B3B,EAAA,CAAAS,UAAA,mBAAAqB,gEAAAH,MAAA;MAAA3B,EAAA,CAAAW,aAAA,CAAAW,GAAA;MAAA,OAAAtB,EAAA,CAAAc,WAAA,CAASa,MAAA,CAAAI,eAAA,EAAwB;IAAA,EAAC;IANpC/B,EAAA,CAAAI,YAAA,EAMqC;IAEnCJ,EADF,CAAAC,cAAA,cAA0B,cACA;IAAAD,EAAA,CAAAG,MAAA,GAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAClEJ,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAG,MAAA,GAAyC;IAGtEH,EAHsE,CAAAI,YAAA,EAAM,EAClE,EACA,EACJ;;;;;;IARAJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAgC,sBAAA,kBAAAC,IAAA,KAAiB;IADjBjC,EAAA,CAAAkC,gBAAA,YAAAb,OAAA,CAAAQ,QAAA,CAA2B;IAE3B7B,EAAA,CAAAgB,UAAA,aAAAT,MAAA,CAAAY,YAAA,CAAyB;IAGDnB,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAmC,iBAAA,CAAAd,OAAA,CAAAe,YAAA,IAAAf,OAAA,CAAAgB,IAAA,CAAoC;IACrCrC,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAmC,iBAAA,CAAAd,OAAA,CAAAiB,UAAA,IAAAjB,OAAA,CAAAkB,WAAA,CAAyC;;;;;IAMxEvC,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,eACF;;;;;IAmBAR,EAAA,CAAAE,SAAA,YAAgE;;;;;IAChEF,EAAA,CAAAE,SAAA,YAAsD;;;ADrH9D,OAAM,MAAOsC,wBAAwB;EAenCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAd1B,KAAAC,aAAa,GAAU,EAAE,CAAC,CAAC;IAC3B,KAAAC,YAAY,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAC,KAAK,GAAG,IAAIlD,YAAY,EAAQ,CAAC,CAAC;IAClC,KAAAmD,eAAe,GAAG,IAAInD,YAAY,EAAQ,CAAC,CAAC;IAEtD;IACA,KAAAoD,WAAW,GAAG;MACZV,IAAI,EAAE;KACP;IAED;IACA,KAAAlB,YAAY,GAAG,KAAK;IACpB,KAAAX,gBAAgB,GAA8B,EAAE;EAEQ;EAExDwC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;EACAA,SAASA,CAAA;IACP,IAAI,CAACF,WAAW,GAAG;MACjBV,IAAI,EAAE;KACP;IACD,IAAI,CAAC7B,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACW,YAAY,GAAG,KAAK;IAEzB;IACA,IAAI,IAAI,CAACwB,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACO,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACtB,QAAQ,GAAG,KAAK,CAAC;IAC3D;EACF;EAEA;EACAuB,YAAYA,CAAA;IACV,IAAI,CAAC5C,gBAAgB,GAAG,EAAE;IAC1B,IAAI6C,OAAO,GAAG,IAAI;IAElB;IACA,IAAI,CAAC,IAAI,CAACN,WAAW,CAACV,IAAI,CAACiB,IAAI,EAAE,EAAE;MACjC,IAAI,CAAC9C,gBAAgB,CAAC,MAAM,CAAC,GAAG,SAAS;MACzC6C,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAI,IAAI,CAACN,WAAW,CAACV,IAAI,CAACiB,IAAI,EAAE,CAACC,MAAM,GAAG,EAAE,EAAE;MACnD,IAAI,CAAC/C,gBAAgB,CAAC,MAAM,CAAC,GAAG,eAAe;MAC/C6C,OAAO,GAAG,KAAK;IACjB;IAIA;IACA,MAAMG,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAC7C,IAAID,aAAa,CAACD,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAAC/C,gBAAgB,CAAC,OAAO,CAAC,GAAG,WAAW;MAC5C6C,OAAO,GAAG,KAAK;IACjB;IAEA,OAAOA,OAAO;EAChB;EAEA;EACAI,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACd,aAAa,EAAE,OAAO,EAAE;IAElC,OAAO,IAAI,CAACA,aAAa,CACtBe,MAAM,CAACP,IAAI,IAAIA,IAAI,CAACtB,QAAQ,CAAC,CAC7B8B,GAAG,CAACR,IAAI,KAAK;MACZb,UAAU,EAAEa,IAAI,CAACb,UAAU,IAAI,IAAI;MACnCsB,YAAY,EAAET,IAAI,CAACS,YAAY,IAAIT,IAAI,CAACf,YAAY,IAAIe,IAAI,CAACd,IAAI,IAAI,IAAI;MACzEwB,UAAU,EAAEV,IAAI,CAACU,UAAU,IAAIV,IAAI,CAACW,cAAc,IAAIX,IAAI,CAACY,EAAE,IAAIZ,IAAI,CAACa,EAAE,IAAI;KAC7E,CAAC,CAAC;EACP;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACb,YAAY,EAAE,EAAE;MACxB;IACF;IAEA,IAAI,CAACjC,YAAY,GAAG,IAAI;IACxB,MAAMqC,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAE7C;IACA,MAAMS,gBAAgB,GAAqB;MACzCC,WAAW,EAAE,IAAI;MAAE;MACnBC,aAAa,EAAE,IAAI,CAACrB,WAAW,CAACV,IAAI,CAACiB,IAAI,EAAE;MAC3Ce,aAAa,EAAE,IAAI,CAACzB,YAAY;MAAE;MAClC0B,OAAO,EAAE,CAAC;MAAE;MACZC,OAAO,EAAEf,aAAa,CAACG,GAAG,CAACR,IAAI,KAAK;QAClCqB,iBAAiB,EAAE,IAAI;QAAE;QACzBX,UAAU,EAAEV,IAAI,CAACU,UAAU;QAAE;QAC7BD,YAAY,EAAET,IAAI,CAACS,YAAY;QAAE;QACjCtB,UAAU,EAAEa,IAAI,CAACb,UAAU,CAAC;OACF;KAC7B;IAED;IACA,IAAI,CAACI,eAAe,CAAC+B,gCAAgC,CAAC;MACpDC,IAAI,EAAER;KACP,CAAC,CAACS,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC1D,YAAY,GAAG,KAAK;QACzB,IAAI0D,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACA,IAAI,CAAChC,eAAe,CAACiC,IAAI,EAAE,CAAC,CAAC;UAC7B,IAAI,CAAClC,KAAK,CAACkC,IAAI,EAAE,CAAC,CAAC;QACrB,CAAC,MAAM;UACL;UACA,IAAI,CAACvE,gBAAgB,CAAC,KAAK,CAAC,GAAGqE,QAAQ,CAACG,OAAO,IAAI,YAAY;QACjE;MACF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9D,YAAY,GAAG,KAAK;QACzB,IAAI,CAACX,gBAAgB,CAAC,KAAK,CAAC,GAAG,iBAAiB;QAChD0E,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MACjC;KACD,CAAC;EACJ;EAEA;EACAE,MAAMA,CAAA;IACJ,IAAI,CAACtC,KAAK,CAACkC,IAAI,EAAE;EACnB;EAEA;EACAK,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACzC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACe,MAAM,CAACP,IAAI,IAAIA,IAAI,CAACtB,QAAQ,CAAC,CAAC0B,MAAM,GAAG,CAAC;EACzF;EAEA;EACA/B,mBAAmBA,CAAC2B,IAAS;IAC3BA,IAAI,CAACtB,QAAQ,GAAG,CAACsB,IAAI,CAACtB,QAAQ;IAC9B;IACA,IAAI,IAAI,CAACrB,gBAAgB,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC4E,gBAAgB,EAAE,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAAC5E,gBAAgB,CAAC,OAAO,CAAC;IACvC;EACF;EAEA;EACAO,eAAeA,CAAA;IACb,MAAMsE,aAAa,GAAG,IAAI,CAAC1C,aAAa,CAAC2C,IAAI,CAACnC,IAAI,IAAI,CAACA,IAAI,CAACtB,QAAQ,CAAC;IACrE,IAAI,CAACc,aAAa,CAACO,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACtB,QAAQ,GAAGwD,aAAa,CAAC;IAEjE;IACA,IAAI,IAAI,CAAC7E,gBAAgB,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC4E,gBAAgB,EAAE,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAAC5E,gBAAgB,CAAC,OAAO,CAAC;IACvC;EACF;EAEA;EACAS,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC0B,aAAa,IAAI,IAAI,CAACA,aAAa,CAACY,MAAM,GAAG,CAAC,IACxD,IAAI,CAACZ,aAAa,CAAC4C,KAAK,CAACpC,IAAI,IAAIA,IAAI,CAACtB,QAAQ,CAAC;EACnD;EAEA;EACAX,eAAeA,CAAA;IACb,MAAMsE,aAAa,GAAG,IAAI,CAACJ,gBAAgB,EAAE;IAC7C,OAAOI,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,IAAI,CAAC7C,aAAa,CAACY,MAAM;EACvE;;;uCAjKWf,wBAAwB,EAAAxC,EAAA,CAAAyF,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAxBnD,wBAAwB;MAAAoD,SAAA;MAAAC,MAAA;QAAAlD,aAAA;QAAAC,YAAA;MAAA;MAAAkD,OAAA;QAAAjD,KAAA;QAAAC,eAAA;MAAA;MAAAiD,UAAA;MAAAC,QAAA,GAAAhG,EAAA,CAAAiG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClB/BvG,EAHN,CAAAC,cAAA,iBAA8D,qBAC5C,aACiD,YAC5C;UACfD,EAAA,CAAAE,SAAA,WAAgC;UAAAF,EAAA,CAAAG,MAAA,gCAClC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,gBAAoE;UAAnBD,EAAA,CAAAS,UAAA,mBAAAgG,0DAAA;YAAA,OAASD,GAAA,CAAArB,MAAA,EAAQ;UAAA,EAAC;UACjEnF,EAAA,CAAAE,SAAA,WAA4B;UAGlCF,EAFI,CAAAI,YAAA,EAAS,EACL,EACS;UAEjBJ,EAAA,CAAAC,cAAA,sBAAsC;UAEpCD,EAAA,CAAA0G,UAAA,IAAAC,uCAAA,iBAAgE;UAKhE3G,EAAA,CAAAC,cAAA,eAAwD;UAAlDD,EAAA,CAAAS,UAAA,sBAAAmG,4DAAA;YAAA,OAAYJ,GAAA,CAAAvC,YAAA,EAAc;UAAA,EAAC;UAG7BjE,EADF,CAAAC,cAAA,cAA+B,cACH;UACxBD,EAAA,CAAAE,SAAA,aAAuC;UAAAF,EAAA,CAAAG,MAAA,iCACzC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAIDJ,EAFJ,CAAAC,cAAA,eAAuB,eACI,iBACI;UACzBD,EAAA,CAAAG,MAAA,kCAAK;UAAAH,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAG,MAAA,SAAC;UAC/BH,EAD+B,CAAAI,YAAA,EAAO,EAC9B;UACRJ,EAAA,CAAAC,cAAA,iBAQ4B;UAJ1BD,EAAA,CAAAyB,gBAAA,2BAAAoF,kEAAAlF,MAAA;YAAA3B,EAAA,CAAA4B,kBAAA,CAAA4E,GAAA,CAAAzD,WAAA,CAAAV,IAAA,EAAAV,MAAA,MAAA6E,GAAA,CAAAzD,WAAA,CAAAV,IAAA,GAAAV,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAJhC3B,EAAA,CAAAI,YAAA,EAQ4B;UAC5BJ,EAAA,CAAA0G,UAAA,KAAAI,wCAAA,kBAA+D;UAGjE9G,EAAA,CAAAI,YAAA,EAAM;UAGJJ,EADF,CAAAC,cAAA,eAAyB,iBACI;UAAAD,EAAA,CAAAG,MAAA,gCAAI;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACvCJ,EAAA,CAAAC,cAAA,iBAQ4B;UAJ1BD,EAAA,CAAAyB,gBAAA,2BAAAsF,kEAAApF,MAAA;YAAA3B,EAAA,CAAA4B,kBAAA,CAAA4E,GAAA,CAAAzD,WAAA,CAAAR,WAAA,EAAAZ,MAAA,MAAA6E,GAAA,CAAAzD,WAAA,CAAAR,WAAA,GAAAZ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqC;UAJvC3B,EAAA,CAAAI,YAAA,EAQ4B;UAC5BJ,EAAA,CAAA0G,UAAA,KAAAM,wCAAA,kBAAsE;UAK5EhH,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;UAKFJ,EAFJ,CAAAC,cAAA,eAA0B,eAC4C,cACnC;UAC7BD,EAAA,CAAAE,SAAA,aAAgC;UAAAF,EAAA,CAAAG,MAAA,qEAAW;UAAAH,EAAA,CAAAC,cAAA,gBAAuB;UAAAD,EAAA,CAAAG,MAAA,SAAC;UACrEH,EADqE,CAAAI,YAAA,EAAO,EACvE;UAEHJ,EADF,CAAAC,cAAA,eAA+B,gBACK;UAAAD,EAAA,CAAAG,MAAA,IAA8B;UAEpEH,EAFoE,CAAAI,YAAA,EAAO,EACnE,EACF;UAGNJ,EAAA,CAAA0G,UAAA,KAAAO,wCAAA,kBAAsE;UAiBtEjH,EAAA,CAAAC,cAAA,eAA0E;UAMxED,EALA,CAAA0G,UAAA,KAAAQ,wCAAA,kBAA4D,KAAAC,wCAAA,kBAKe;UAe7EnH,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAA0G,UAAA,KAAAU,wCAAA,kBAAwE;UAM9EpH,EAFI,CAAAI,YAAA,EAAM,EACD,EACM;UAIXJ,EAFJ,CAAAC,cAAA,sBAAgB,eACuC,kBAKvB;UAD1BD,EAAA,CAAAS,UAAA,mBAAA4G,2DAAA;YAAA,OAASb,GAAA,CAAArB,MAAA,EAAQ;UAAA,EAAC;UAElBnF,EAAA,CAAAE,SAAA,aAAiC;UAAAF,EAAA,CAAAG,MAAA,qBACnC;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,kBAI4B;UAD1BD,EAAA,CAAAS,UAAA,mBAAA6G,2DAAA;YAAA,OAASd,GAAA,CAAAvC,YAAA,EAAc;UAAA,EAAC;UAGxBjE,EADA,CAAA0G,UAAA,KAAAa,sCAAA,gBAA4D,KAAAC,sCAAA,gBACV;UAClDxH,EAAA,CAAAG,MAAA,IACF;UAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACS,EACT;;;UAjI2BJ,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAAhG,gBAAA,QAA6B;UAoBpDR,EAAA,CAAAK,SAAA,IAA6C;UAA7CL,EAAA,CAAAyH,WAAA,eAAAjB,GAAA,CAAAhG,gBAAA,SAA6C;UAC7CR,EAAA,CAAAkC,gBAAA,YAAAsE,GAAA,CAAAzD,WAAA,CAAAV,IAAA,CAA8B;UAI9BrC,EAAA,CAAAgB,UAAA,aAAAwF,GAAA,CAAArF,YAAA,CAAyB;UACInB,EAAA,CAAAK,SAAA,EAA8B;UAA9BL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAAhG,gBAAA,SAA8B;UAU3DR,EAAA,CAAAK,SAAA,GAAoD;UAApDL,EAAA,CAAAyH,WAAA,eAAAjB,GAAA,CAAAhG,gBAAA,gBAAoD;UACpDR,EAAA,CAAAkC,gBAAA,YAAAsE,GAAA,CAAAzD,WAAA,CAAAR,WAAA,CAAqC;UAIrCvC,EAAA,CAAAgB,UAAA,aAAAwF,GAAA,CAAArF,YAAA,CAAyB;UACInB,EAAA,CAAAK,SAAA,EAAqC;UAArCL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAAhG,gBAAA,gBAAqC;UAclCR,EAAA,CAAAK,SAAA,IAA8B;UAA9BL,EAAA,CAAAM,kBAAA,wBAAAkG,GAAA,CAAApB,gBAAA,cAA8B;UAK9BpF,EAAA,CAAAK,SAAA,EAA8B;UAA9BL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAA7D,aAAA,CAAAY,MAAA,KAA8B;UAiBxCvD,EAAA,CAAAK,SAAA,EAA6C;UAA7CL,EAAA,CAAAyH,WAAA,cAAAjB,GAAA,CAAAhG,gBAAA,UAA6C;UACjER,EAAA,CAAAK,SAAA,EAAgC;UAAhCL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAA7D,aAAA,CAAAY,MAAA,OAAgC;UAKhBvD,EAAA,CAAAK,SAAA,EAAkB;UAAlBL,EAAA,CAAAgB,UAAA,YAAAwF,GAAA,CAAA7D,aAAA,CAAkB;UAiBH3C,EAAA,CAAAK,SAAA,EAA+B;UAA/BL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAAhG,gBAAA,UAA+B;UActER,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAgB,UAAA,aAAAwF,GAAA,CAAArF,YAAA,CAAyB;UAOzBnB,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAgB,UAAA,aAAAwF,GAAA,CAAArF,YAAA,CAAyB;UACenB,EAAA,CAAAK,SAAA,EAAkB;UAAlBL,EAAA,CAAAgB,UAAA,SAAAwF,GAAA,CAAArF,YAAA,CAAkB;UAC7BnB,EAAA,CAAAK,SAAA,EAAmB;UAAnBL,EAAA,CAAAgB,UAAA,UAAAwF,GAAA,CAAArF,YAAA,CAAmB;UAChDnB,EAAA,CAAAK,SAAA,EACF;UADEL,EAAA,CAAAM,kBAAA,MAAAkG,GAAA,CAAArF,YAAA,6DACF;;;qBDzHMvB,YAAY,EAAA8H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE/H,WAAW,EAAAgI,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,4BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,kBAAA,EAAAN,EAAA,CAAAO,OAAA,EAAAP,EAAA,CAAAQ,MAAA,EAAEvI,YAAY,EAAAwI,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAE3I,cAAc;MAAA4I,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}