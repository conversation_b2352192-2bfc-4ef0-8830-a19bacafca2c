{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSunday\n * @category Weekday Helpers\n * @summary Is the given date Sunday?\n *\n * @description\n * Is the given date Sunday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Sunday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 21 September 2014 Sunday?\n * const result = isSunday(new Date(2014, 8, 21))\n * //=> true\n */\nexport default function isSunday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 0;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}