{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let FilterListItemsPipe = /*#__PURE__*/(() => {\n  class FilterListItemsPipe {\n    transform(value, searchText) {\n      if (!searchText) {\n        return value;\n      }\n      return value.filter(data => this.matchValue(data, searchText));\n    }\n    matchValue(data, value) {\n      return Object.keys(data).map(key => {\n        return new RegExp(value, 'gi').test(data[key]);\n      }).some(result => result);\n    }\n    static {\n      this.ɵfac = function FilterListItemsPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FilterListItemsPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"filterListItems\",\n        type: FilterListItemsPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return FilterListItemsPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}