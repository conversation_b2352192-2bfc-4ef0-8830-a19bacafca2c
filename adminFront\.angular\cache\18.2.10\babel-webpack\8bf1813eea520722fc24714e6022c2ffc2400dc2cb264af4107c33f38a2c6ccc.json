{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nlet BuildingMaterialComponent = class BuildingMaterialComponent extends BaseComponent {\n  // 根據狀態值獲取狀態標籤\n  getStatusLabel(status) {\n    const option = this.statusOptions.find(opt => opt.value === status);\n    return option ? option.label : '未設定';\n  }\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService, _pictureService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._materialService = _materialService;\n    this._utilityService = _utilityService;\n    this._pictureService = _pictureService;\n    this.isNew = true;\n    this.listBuildCases = [];\n    this.materialOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: false,\n      label: '方案'\n    }, {\n      value: true,\n      label: '選樣'\n    }];\n    this.materialOptionsId = null;\n    this.CSelectName = \"\";\n    // 移除圖片檔名相關欄位\n    // CImageCode: string = \"\"\n    // CInfoImageCode: string = \"\"\n    // 啟用建材代號欄位\n    this.CImageCode = \"\";\n    this.ShowPrice = false;\n    this.currentImageShowing = \"\";\n    this.filterMapping = false;\n    this.CIsMapping = true;\n    // 圖片綁定相關屬性 - 重構為雙區塊模式\n    this.buildingMaterialImages = []; // 建材圖片\n    this.schematicImages = []; // 示意圖片\n    this.allSelectedImages = []; // 所有已選擇的圖片（跨類別）\n    this.boundImageIds = []; // 已綁定的圖片ID\n    this.imageSearchTerm = \"\";\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n    // 分頁相關\n    this.buildingMaterialCurrentPage = 1;\n    this.buildingMaterialPageSize = 50;\n    this.buildingMaterialTotalRecords = 0;\n    this.schematicCurrentPage = 1;\n    this.schematicPageSize = 50;\n    this.schematicTotalRecords = 0;\n    // 圖片綁定分頁屬性\n    this.imageCurrentPage = 1;\n    this.imagePageSize = 50;\n    this.imageTotalRecords = 0;\n    // 類別選項\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = PictureCategory.BUILDING_MATERIAL;\n    this.isCategorySelected = true; // 預設選擇建材圖片\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory;\n    // 狀態選項\n    this.statusOptions = [{\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries?.length ? res.Entries : [];\n        this.selectedBuildCaseId = this.listBuildCases[0].cID;\n      }\n    }), mergeMap(() => this.getMaterialList())).subscribe();\n  }\n  getMaterialList(pageIndex = 1) {\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPlanUse: this.materialOptionsId,\n        CSelectName: this.CSelectName,\n        // 啟用建材代號查詢條件\n        CImageCode: this.CImageCode,\n        // CInfoImageCode: this.CInfoImageCode,\n        PageSize: this.pageSize,\n        PageIndex: pageIndex,\n        CIsMapping: this.CIsMapping\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.materialList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n        if (this.materialList.length > 0) {\n          this.ShowPrice = this.materialList[0].CShowPrice;\n        }\n      }\n    }));\n  }\n  search() {\n    this.getMaterialList().subscribe();\n  }\n  pageChanged(pageIndex) {\n    this.getMaterialList(pageIndex).subscribe();\n  }\n  exportExelMaterialList() {\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  exportExelMaterialTemplate() {\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedMaterial = {\n      CStatus: 1 // 預設為啟用狀態\n    };\n    this.dialogService.open(ref);\n  }\n  onSelectedMaterial(data, ref) {\n    this.isNew = false;\n    this.selectedMaterial = {\n      ...data\n    };\n    this.dialogService.open(ref);\n  }\n  bindImageForMaterial(data, ref) {\n    this.selectedMaterial = {\n      ...data\n    };\n    // 設定已綁定的圖片ID\n    this.boundImageIds = this.selectedMaterial.CSelectPictureId ? this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id) : [];\n    // 重置選擇狀態和分頁\n    this.allSelectedImages = []; // 重置全域已選擇圖片列表\n    this.buildingMaterialCurrentPage = 1;\n    this.schematicCurrentPage = 1;\n    this.imageSearchTerm = \"\";\n    this.loadAllImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[名稱]', this.selectedMaterial.CName);\n    this.valid.required('[項目]', this.selectedMaterial.CPart);\n    this.valid.required('[位置]', this.selectedMaterial.CLocation);\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n    // 啟用建材代號驗證\n    this.valid.required('[建材代號]', this.selectedMaterial.CImageCode);\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus);\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30);\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30);\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30);\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n    // 啟用建材代號長度驗證\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CImageCode, 30);\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        // 暫時保留 CImageCode 給圖片綁定功能使用\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  detectFileExcel(event) {\n    const target = event.target;\n    const reader = new FileReader();\n    reader.readAsBinaryString(target.files[0]);\n    reader.onload = e => {\n      const binarystr = e.target.result;\n      const wb = XLSX.read(binarystr, {\n        type: 'binary'\n      });\n      const wsname = wb.SheetNames[0];\n      const ws = wb.Sheets[wsname];\n      let isValidFile = true;\n      const data = XLSX.utils.sheet_to_json(ws);\n      if (data && data.length > 0) {\n        data.forEach(x => {\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'])) {\n            isValidFile = false;\n          }\n        });\n        if (!isValidFile) {\n          this.message.showErrorMSG(\"导入文件时出现错误\");\n        } else {\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n            body: {\n              CBuildCaseId: this.selectedBuildCaseId,\n              CFile: target.files[0]\n            }\n          }).pipe(tap(res => {\n            if (res.StatusCode == 0) {\n              this.message.showSucessMSG(\"執行成功\");\n            } else {\n              this.message.showErrorMSG(res.Message);\n            }\n          }), mergeMap(() => this.getMaterialList(1))).subscribe();\n        }\n      } else {\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n      }\n      event.target.value = null;\n    };\n  }\n  showImage(imageUrl, dialog) {\n    this.currentImageShowing = imageUrl;\n    this.dialogService.open(dialog);\n  }\n  changeFilter() {\n    if (this.filterMapping) {\n      this.CIsMapping = false;\n      this.getMaterialList().subscribe();\n    } else {\n      this.CIsMapping = true;\n      this.getMaterialList().subscribe();\n    }\n  }\n  // 圖片綁定功能方法\n  openImageBinder(ref) {\n    // 重置選擇狀態和分頁\n    this.allSelectedImages = [];\n    this.buildingMaterialCurrentPage = 1;\n    this.schematicCurrentPage = 1;\n    this.imageSearchTerm = \"\";\n    this.loadAllImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  loadImages() {\n    // 使用 PictureService API 載入圖片列表\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\n      this._pictureService.apiPictureGetPictureListPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: this.selectedCategory,\n          PageIndex: this.imageCurrentPage,\n          PageSize: this.imagePageSize\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          // 將 API 回應轉換為 ImageItem 格式\n          this.allImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            category: this.selectedCategory // 加入當前選擇的類別\n          })) || [];\n          this.imageTotalRecords = res.TotalItems || 0;\n          // 只在第一次開啟對話框時初始化已選擇的圖片（已綁定的圖片）\n          // 分頁變更時不重置已選擇的圖片\n          if (this.selectedImages.length === 0 && this.boundImageIds.length > 0) {\n            this.loadAllImagesForInitialSelection();\n          } else {\n            // 更新可選擇的圖片（排除已選擇的）\n            this.updateAvailableImages();\n          }\n        } else {\n          this.message.showErrorMSG(res.Message || '載入圖片失敗');\n          this.allImages = [];\n          this.availableImages = [];\n          // 只在第一次載入錯誤時清空已選圖片\n          if (this.selectedImages.length === 0) {\n            this.selectedImages = [];\n          }\n        }\n      });\n    } else {\n      // 如果沒有選擇類別或建案，清空圖片列表\n      this.allImages = [];\n      this.availableImages = [];\n      this.selectedImages = [];\n    }\n  }\n  // 載入所有圖片以進行初始選擇（僅用於已綁定圖片的初始化）\n  loadAllImagesForInitialSelection() {\n    // 為了初始化已綁定的圖片，我們需要載入所有圖片\n    // 這裡使用一個較大的 PageSize 來獲取所有圖片\n    this._pictureService.apiPictureGetPictureListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        cPictureType: this.selectedCategory,\n        PageIndex: 1,\n        PageSize: 9999 // 使用大數字獲取所有圖片\n      }\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        const allAvailableImages = res.Entries?.map(picture => ({\n          id: picture.CId || 0,\n          name: picture.CPictureCode || picture.CName || '',\n          size: 0,\n          thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n          fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n          lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n          category: this.selectedCategory // 加入當前選擇的類別\n        })) || [];\n        // 從所有圖片中找出已綁定的圖片\n        const boundImages = allAvailableImages.filter(image => this.boundImageIds.includes(image.id));\n        this.selectedImages = [...boundImages];\n        // 將已綁定的圖片加入全域已選擇圖片列表\n        boundImages.forEach(image => {\n          const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n          if (allSelectedIndex === -1) {\n            this.allSelectedImages.push(image);\n          }\n        });\n        // 更新可選擇的圖片\n        this.updateAvailableImages();\n      }\n    });\n  }\n  // 新增 picklist 相關方法\n  updateAvailableImages() {\n    // 使用當前 API 回傳的圖片作為可選圖片基礎\n    let currentPageImages = [...this.allImages];\n    // 根據搜尋條件篩選當前分頁圖片\n    if (this.imageSearchTerm.trim()) {\n      const searchTerm = this.imageSearchTerm.toLowerCase();\n      currentPageImages = currentPageImages.filter(image => image.name.toLowerCase().includes(searchTerm));\n    }\n    // 排除已選擇的圖片\n    const selectedIds = this.selectedImages.map(img => img.id);\n    this.availableImages = currentPageImages.filter(image => !selectedIds.includes(image.id));\n  }\n  filterAvailableImages() {\n    this.updateAvailableImages();\n  }\n  moveToSelected(image, event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    // 將圖片從可選移到已選\n    const index = this.availableImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.selectedImages.push(image);\n      // 同時更新全域已選擇圖片列表\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n      if (allSelectedIndex === -1) {\n        this.allSelectedImages.push(image);\n      }\n      this.updateAvailableImages();\n    }\n  }\n  moveToAvailable(image, event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    // 將圖片從已選移到可選（包括已綁定的圖片也可以取消）\n    const index = this.selectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.selectedImages.splice(index, 1);\n      // 同時從全域已選擇圖片列表中移除\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n      if (allSelectedIndex > -1) {\n        this.allSelectedImages.splice(allSelectedIndex, 1);\n      }\n      this.updateAvailableImages();\n    }\n  }\n  moveAllToSelected() {\n    // 將所有可選圖片移到已選\n    this.selectedImages.push(...this.availableImages);\n    // 同時更新全域已選擇圖片列表\n    this.availableImages.forEach(image => {\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n      if (allSelectedIndex === -1) {\n        this.allSelectedImages.push(image);\n      }\n    });\n    this.updateAvailableImages();\n  }\n  moveAllToAvailable() {\n    // 將所有已選圖片移到可選（包括已綁定的圖片）\n    // 從全域已選擇圖片列表中移除當前類別的圖片\n    this.selectedImages.forEach(image => {\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n      if (allSelectedIndex > -1) {\n        this.allSelectedImages.splice(allSelectedIndex, 1);\n      }\n    });\n    this.selectedImages = [];\n    this.updateAvailableImages();\n  }\n  isImageBound(image) {\n    return this.boundImageIds.includes(image.id);\n  }\n  getBoundImagesCount() {\n    return this.selectedImages.filter(image => this.isImageBound(image)).length;\n  }\n  getNewSelectedCount() {\n    return this.selectedImages.filter(image => !this.isImageBound(image)).length;\n  }\n  // 清除所有選擇（包括已綁定的圖片）\n  clearAllSelection() {\n    // 從全域已選擇圖片列表中移除當前類別的圖片\n    this.selectedImages.forEach(image => {\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\n      if (allSelectedIndex > -1) {\n        this.allSelectedImages.splice(allSelectedIndex, 1);\n      }\n    });\n    this.selectedImages = [];\n    this.updateAvailableImages();\n  }\n  previewImage(image, imagePreviewRef, event) {\n    event.stopPropagation();\n    this.previewingImage = image;\n    // 在所有圖片中找到當前預覽圖片的索引\n    this.currentPreviewIndex = this.allImages.findIndex(img => img.id === image.id);\n    this.dialogService.open(imagePreviewRef);\n  }\n  previousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.allImages[this.currentPreviewIndex];\n    }\n  }\n  nextImage() {\n    if (this.currentPreviewIndex < this.allImages.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.allImages[this.currentPreviewIndex];\n    }\n  }\n  toggleImageSelectionInPreview() {\n    if (this.previewingImage) {\n      const isSelected = this.selectedImages.some(img => img.id === this.previewingImage.id);\n      if (isSelected) {\n        this.moveToAvailable(this.previewingImage);\n      } else {\n        this.moveToSelected(this.previewingImage);\n      }\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  onConfirmImageSelection(ref) {\n    if (this.selectedImages.length > 0) {\n      // 收集選中圖片的 ID\n      const selectedImageIds = this.selectedImages.map(img => img.id); // 如果只選取一張圖片，直接設定圖片 ID\n      if (this.selectedImages.length === 1) {\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\n      } else {\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\n      }\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\n      this.selectedMaterial.selectedImageIds = selectedImageIds;\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\n      if (this.selectedMaterial.CId) {\n        this.saveImageBinding();\n      }\n    }\n    this.clearAllSelection();\n    ref.close();\n  } // 新增方法：保存圖片綁定\n  saveImageBinding() {\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus,\n        // 加入狀態欄位\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(`圖片綁定成功`);\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => {\n      // 清空選取的建材\n      this.selectedMaterial = {};\n    })).subscribe();\n  }\n  onCloseImageBinder(ref) {\n    this.clearAllSelection();\n    this.imageSearchTerm = \"\";\n    this.imageCurrentPage = 1; // 重設圖片頁碼\n    ref.close();\n  } // 類別變更處理方法\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true;\n    // 當類別變更時，從全域已選擇圖片中篩選出當前類別的圖片\n    this.selectedImages = this.allSelectedImages.filter(image => image.category === category);\n    // 當類別變更時重設頁碼並重新載入圖片\n    this.imageCurrentPage = 1;\n    if (this.selectedBuildCaseId) {\n      this.loadImages();\n    }\n  }\n  // 獲取類別標籤的方法\n  getCategoryLabel(category) {\n    const option = this.categoryOptions.find(opt => opt.value === category);\n    return option ? option.label : '未知類別';\n  }\n  // 獲取指定類別的已選擇圖片數量\n  getSelectedCountByCategory(category) {\n    return this.allSelectedImages.filter(image => image.category === category).length;\n  }\n  // 載入所有圖片（建材圖片和示意圖片）\n  loadAllImages() {\n    this.loadBuildingMaterialImages();\n    this.loadSchematicImages();\n  }\n  // 載入建材圖片\n  loadBuildingMaterialImages() {\n    if (this.selectedBuildCaseId) {\n      this._pictureService.apiPictureGetPictureListPost$Json({\n        body: {\n          PageIndex: this.buildingMaterialCurrentPage,\n          PageSize: this.buildingMaterialPageSize,\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: PictureCategory.BUILDING_MATERIAL\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.buildingMaterialImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            category: PictureCategory.BUILDING_MATERIAL\n          })) || [];\n          this.buildingMaterialTotalRecords = res.TotalItems || 0;\n        }\n      });\n    }\n  }\n  // 載入示意圖片\n  loadSchematicImages() {\n    if (this.selectedBuildCaseId) {\n      this._pictureService.apiPictureGetPictureListPost$Json({\n        body: {\n          PageIndex: this.schematicCurrentPage,\n          PageSize: this.schematicPageSize,\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: PictureCategory.SCHEMATIC\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.schematicImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\n            category: PictureCategory.SCHEMATIC\n          })) || [];\n          this.schematicTotalRecords = res.TotalItems || 0;\n        }\n      });\n    }\n  }\n  // 選擇圖片\n  selectImage(image) {\n    const index = this.allSelectedImages.findIndex(img => img.id === image.id);\n    if (index === -1) {\n      this.allSelectedImages.push(image);\n    }\n  }\n  // 取消選擇圖片\n  unselectImage(image) {\n    const index = this.allSelectedImages.findIndex(img => img.id === image.id);\n    if (index > -1) {\n      this.allSelectedImages.splice(index, 1);\n    }\n  }\n  // 檢查圖片是否已選擇\n  isImageSelected(image) {\n    return this.allSelectedImages.some(img => img.id === image.id);\n  }\n  // 切換圖片選擇狀態\n  toggleImageSelection(image) {\n    if (this.isImageSelected(image)) {\n      this.unselectImage(image);\n    } else {\n      this.selectImage(image);\n    }\n  }\n  // 獲取已選擇的建材圖片\n  getSelectedBuildingMaterialImages() {\n    return this.allSelectedImages.filter(image => image.category === PictureCategory.BUILDING_MATERIAL);\n  }\n  // 獲取已選擇的示意圖片\n  getSelectedSchematicImages() {\n    return this.allSelectedImages.filter(image => image.category === PictureCategory.SCHEMATIC);\n  }\n  // 建材圖片分頁變更\n  buildingMaterialPageChanged(page) {\n    this.buildingMaterialCurrentPage = page;\n    this.loadBuildingMaterialImages();\n  }\n  // 示意圖片分頁變更\n  schematicPageChanged(page) {\n    this.schematicCurrentPage = page;\n    this.loadSchematicImages();\n  }\n  // 圖片分頁變更處理方法\n  imagePageChanged(page) {\n    this.imageCurrentPage = page;\n    this.loadImages();\n  }\n};\nBuildingMaterialComponent = __decorate([Component({\n  selector: 'ngx-building-material',\n  templateUrl: './building-material.component.html',\n  styleUrls: ['./building-material.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule]\n})], BuildingMaterialComponent);\nexport { BuildingMaterialComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "finalize", "mergeMap", "tap", "XLSX", "SharedModule", "BaseComponent", "PictureCategory", "BuildingMaterialComponent", "getStatusLabel", "status", "option", "statusOptions", "find", "opt", "value", "label", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_materialService", "_utilityService", "_pictureService", "isNew", "listBuildCases", "materialOptions", "materialOptionsId", "CSelectName", "CImageCode", "ShowPrice", "currentImageShowing", "filterMapping", "CIsMapping", "buildingMaterialImages", "schematicImages", "allSelectedImages", "boundImageIds", "imageSearchTerm", "previewingImage", "currentPreviewIndex", "buildingMaterialCurrentPage", "buildingMaterialPageSize", "buildingMaterialTotalRecords", "schematicCurrentPage", "schematicPageSize", "schematicTotalRecords", "imageCurrentPage", "imagePageSize", "imageTotalRecords", "categoryOptions", "BUILDING_MATERIAL", "SCHEMATIC", "selectedCate<PERSON><PERSON>", "isCategorySelected", "ngOnInit", "getListBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "body", "CIsPagi", "CStatus", "pipe", "res", "StatusCode", "Entries", "length", "selectedBuildCaseId", "cID", "getMaterialList", "subscribe", "pageIndex", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPlanUse", "PageSize", "pageSize", "PageIndex", "materialList", "totalRecords", "TotalItems", "CShowPrice", "search", "pageChanged", "exportExelMaterialList", "apiMaterialExportExcelMaterialListPost$Json", "FileByte", "downloadExcelFile", "exportExelMaterialTemplate", "apiMaterialExportExcelMaterialTemplatePost$Json", "addNew", "ref", "selectedMaterial", "open", "onSelectedMaterial", "data", "bindImageForMaterial", "CSelectPictureId", "map", "id", "parseInt", "loadAllImages", "closeOnBackdropClick", "validation", "clear", "required", "CName", "<PERSON>art", "CLocation", "isStringMaxLength", "onSubmit", "errorMessages", "showErrorMSGs", "apiMaterialSaveMaterialAdminPost$Json", "CDescription", "CMaterialId", "CId", "CPrice", "CPictureId", "selectedImageIds", "showSucessMSG", "showErrorMSG", "Message", "close", "onClose", "detectFileExcel", "event", "target", "reader", "FileReader", "readAsBinaryString", "files", "onload", "e", "binarystr", "result", "wb", "read", "type", "wsname", "SheetNames", "ws", "Sheets", "isValidFile", "utils", "sheet_to_json", "for<PERSON>ach", "x", "apiMaterialImportExcelMaterialListPost$Json", "CFile", "showImage", "imageUrl", "dialog", "changeFilter", "openImageBinder", "loadImages", "apiPictureGetPictureListPost$Json", "cPictureType", "allImages", "picture", "name", "CPictureCode", "size", "thumbnailUrl", "CBase64", "fullUrl", "lastModified", "CUpdateDT", "Date", "category", "selectedImages", "loadAllImagesForInitialSelection", "updateAvailableImages", "availableImages", "allAvailableImages", "boundImages", "filter", "image", "includes", "allSelectedIndex", "findIndex", "img", "push", "currentPageImages", "trim", "searchTerm", "toLowerCase", "selectedIds", "filterAvailableImages", "moveToSelected", "stopPropagation", "index", "moveToAvailable", "splice", "moveAllToSelected", "moveAllToAvailable", "isImageBound", "getBoundImagesCount", "getNewSelectedCount", "clearAllSelection", "previewImage", "imagePreviewRef", "previousImage", "nextImage", "toggleImageSelectionInPreview", "isSelected", "some", "isImageSelected", "selected", "onConfirmImageSelection", "imageNames", "join", "saveImageBinding", "onCloseImageBinder", "categoryChanged", "getCategoryLabel", "getSelectedCountByCategory", "loadBuildingMaterialImages", "loadSchematicImages", "selectImage", "unselectImage", "toggleImageSelection", "getSelectedBuildingMaterialImages", "getSelectedSchematicImages", "buildingMaterialPageChanged", "page", "schematicPageChanged", "imagePageChanged", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.ts"], "sourcesContent": ["import { Component, OnInit, TemplateRef, } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse, GetPictureListResponseListResponseBase, GetPictureListResponse } from 'src/services/api/models';\r\nimport { finalize, mergeMap, tap } from 'rxjs';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport * as XLSX from 'xlsx';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n// 圖片項目介面\r\ninterface ImageItem {\r\n  id: number;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n  category?: PictureCategory; // 新增類別屬性\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-building-material',\r\n  templateUrl: './building-material.component.html',\r\n  styleUrls: ['./building-material.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule],\r\n})\r\n\r\nexport class BuildingMaterialComponent extends BaseComponent implements OnInit {\r\n  isNew = true\r\n\r\n  materialList: GetMaterialListResponse[]\r\n  selectedMaterial: GetMaterialListResponse\r\n\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n\r\n  materialOptions = [\r\n    {\r\n      value: null,\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: false,\r\n      label: '方案',\r\n    },\r\n    {\r\n      value: true,\r\n      label: '選樣',\r\n    }]; materialOptionsId = null;\r\n  CSelectName: string = \"\"\r\n  // 移除圖片檔名相關欄位\r\n  // CImageCode: string = \"\"\r\n  // CInfoImageCode: string = \"\"\r\n  // 啟用建材代號欄位\r\n  CImageCode: string = \"\"\r\n  ShowPrice: boolean = false\r\n  currentImageShowing: string = \"\"\r\n  filterMapping: boolean = false\r\n  CIsMapping: boolean = true\r\n  // 圖片綁定相關屬性 - 重構為雙區塊模式\r\n  buildingMaterialImages: ImageItem[] = [] // 建材圖片\r\n  schematicImages: ImageItem[] = [] // 示意圖片\r\n  allSelectedImages: ImageItem[] = [] // 所有已選擇的圖片（跨類別）\r\n  boundImageIds: number[] = [] // 已綁定的圖片ID\r\n  imageSearchTerm: string = \"\"\r\n  previewingImage: ImageItem | null = null\r\n  currentPreviewIndex: number = 0\r\n\r\n  // 分頁相關\r\n  buildingMaterialCurrentPage: number = 1\r\n  buildingMaterialPageSize: number = 50\r\n  buildingMaterialTotalRecords: number = 0\r\n\r\n  schematicCurrentPage: number = 1\r\n  schematicPageSize: number = 50\r\n  schematicTotalRecords: number = 0\r\n\r\n  // 圖片綁定分頁屬性\r\n  imageCurrentPage: number = 1\r\n  imagePageSize: number = 50\r\n  imageTotalRecords: number = 0\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n  selectedCategory: PictureCategory = PictureCategory.BUILDING_MATERIAL\r\n  isCategorySelected: boolean = true // 預設選擇建材圖片\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;\r\n\r\n  // 狀態選項\r\n  statusOptions = [{\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  }];\r\n  // 根據狀態值獲取狀態標籤\r\n  getStatusLabel(status: number): string {\r\n    const option = this.statusOptions.find(opt => opt.value === status);\r\n    return option ? option.label : '未設定';\r\n  }\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _materialService: MaterialService,\r\n    private _utilityService: UtilityService,\r\n    private _pictureService: PictureService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.listBuildCases = res.Entries?.length ? res.Entries : []\r\n            this.selectedBuildCaseId = this.listBuildCases[0].cID!\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList())\r\n      ).subscribe()\r\n  } getMaterialList(pageIndex: number = 1) {\r\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPlanUse: this.materialOptionsId,\r\n        CSelectName: this.CSelectName,\r\n        // 啟用建材代號查詢條件\r\n        CImageCode: this.CImageCode,\r\n        // CInfoImageCode: this.CInfoImageCode,\r\n        PageSize: this.pageSize,\r\n        PageIndex: pageIndex,\r\n        CIsMapping: this.CIsMapping\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.materialList = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n\r\n          if (this.materialList.length > 0) {\r\n            this.ShowPrice = this.materialList[0].CShowPrice!;\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  search() {\r\n    this.getMaterialList().subscribe()\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    this.getMaterialList(pageIndex).subscribe()\r\n  }\r\n\r\n  exportExelMaterialList() {\r\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  exportExelMaterialTemplate() {\r\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n  addNew(ref: any) {\r\n    this.isNew = true\r\n    this.selectedMaterial = {\r\n      CStatus: 1 // 預設為啟用狀態\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {\r\n    this.isNew = false\r\n    this.selectedMaterial = { ...data }\r\n    this.dialogService.open(ref)\r\n  }\r\n  bindImageForMaterial(data: GetMaterialListResponse, ref: TemplateRef<any>) {\r\n    this.selectedMaterial = { ...data }\r\n    // 設定已綁定的圖片ID\r\n    this.boundImageIds = this.selectedMaterial.CSelectPictureId ?\r\n      this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id) : []\r\n\r\n    // 重置選擇狀態和分頁\r\n    this.allSelectedImages = [] // 重置全域已選擇圖片列表\r\n    this.buildingMaterialCurrentPage = 1\r\n    this.schematicCurrentPage = 1\r\n    this.imageSearchTerm = \"\"\r\n\r\n    this.loadAllImages()\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false })\r\n  } validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[名稱]', this.selectedMaterial.CName)\r\n    this.valid.required('[項目]', this.selectedMaterial.CPart)\r\n    this.valid.required('[位置]', this.selectedMaterial.CLocation)\r\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)\r\n    // 啟用建材代號驗證\r\n    this.valid.required('[建材代號]', this.selectedMaterial.CImageCode)\r\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus)\r\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30)\r\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30)\r\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30)\r\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)\r\n    // 啟用建材代號長度驗證\r\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CImageCode, 30)\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    } this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        // 暫時保留 CImageCode 給圖片綁定功能使用\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  detectFileExcel(event: any) {\r\n    const target: DataTransfer = <DataTransfer>(event.target);\r\n    const reader: FileReader = new FileReader();\r\n    reader.readAsBinaryString(target.files[0]);\r\n    reader.onload = (e: any) => {\r\n      const binarystr: string = e.target.result;\r\n      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });\r\n\r\n      const wsname: string = wb.SheetNames[0];\r\n      const ws: XLSX.WorkSheet = wb.Sheets[wsname];\r\n\r\n      let isValidFile: boolean = true;\r\n      const data = XLSX.utils.sheet_to_json(ws);\r\n      if (data && data.length > 0) {\r\n        data.forEach((x: any) => {\r\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'])) {\r\n            isValidFile = false;\r\n          }\r\n        })\r\n\r\n        if (!isValidFile) {\r\n          this.message.showErrorMSG(\"导入文件时出现错误\")\r\n        } else {\r\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\r\n            body: {\r\n              CBuildCaseId: this.selectedBuildCaseId,\r\n              CFile: target.files[0]\r\n            }\r\n          }).pipe(\r\n            tap(res => {\r\n              if (res.StatusCode == 0) {\r\n                this.message.showSucessMSG(\"執行成功\")\r\n              } else {\r\n                this.message.showErrorMSG(res.Message!)\r\n              }\r\n            }),\r\n            mergeMap(() => this.getMaterialList(1))\r\n          ).subscribe();\r\n        }\r\n      } else {\r\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\")\r\n      }\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  showImage(imageUrl: string, dialog: TemplateRef<any>) {\r\n    this.currentImageShowing = imageUrl;\r\n    this.dialogService.open(dialog);\r\n  }\r\n  changeFilter() {\r\n    if (this.filterMapping) {\r\n      this.CIsMapping = false;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n    else {\r\n      this.CIsMapping = true;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n  }\r\n  // 圖片綁定功能方法\r\n  openImageBinder(ref: TemplateRef<any>) {\r\n    // 重置選擇狀態和分頁\r\n    this.allSelectedImages = []\r\n    this.buildingMaterialCurrentPage = 1\r\n    this.schematicCurrentPage = 1\r\n    this.imageSearchTerm = \"\"\r\n\r\n    this.loadAllImages();\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false });\r\n  }\r\n\r\n  loadImages() {\r\n    // 使用 PictureService API 載入圖片列表\r\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\r\n      this._pictureService.apiPictureGetPictureListPost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: this.selectedCategory,\r\n          PageIndex: this.imageCurrentPage,\r\n          PageSize: this.imagePageSize\r\n        }\r\n      }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          // 將 API 回應轉換為 ImageItem 格式\r\n          this.allImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || picture.CName || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            category: this.selectedCategory // 加入當前選擇的類別\r\n          })) || [];\r\n\r\n          this.imageTotalRecords = res.TotalItems || 0;\r\n\r\n          // 只在第一次開啟對話框時初始化已選擇的圖片（已綁定的圖片）\r\n          // 分頁變更時不重置已選擇的圖片\r\n          if (this.selectedImages.length === 0 && this.boundImageIds.length > 0) {\r\n            this.loadAllImagesForInitialSelection();\r\n          } else {\r\n            // 更新可選擇的圖片（排除已選擇的）\r\n            this.updateAvailableImages();\r\n          }\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入圖片失敗');\r\n          this.allImages = [];\r\n          this.availableImages = [];\r\n          // 只在第一次載入錯誤時清空已選圖片\r\n          if (this.selectedImages.length === 0) {\r\n            this.selectedImages = [];\r\n          }\r\n        }\r\n      });\r\n    } else {\r\n      // 如果沒有選擇類別或建案，清空圖片列表\r\n      this.allImages = [];\r\n      this.availableImages = [];\r\n      this.selectedImages = [];\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // 載入所有圖片以進行初始選擇（僅用於已綁定圖片的初始化）\r\n  loadAllImagesForInitialSelection() {\r\n    // 為了初始化已綁定的圖片，我們需要載入所有圖片\r\n    // 這裡使用一個較大的 PageSize 來獲取所有圖片\r\n    this._pictureService.apiPictureGetPictureListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        cPictureType: this.selectedCategory,\r\n        PageIndex: 1,\r\n        PageSize: 9999 // 使用大數字獲取所有圖片\r\n      }\r\n    }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n      if (res.StatusCode === 0) {\r\n        const allAvailableImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n          id: picture.CId || 0,\r\n          name: picture.CPictureCode || picture.CName || '',\r\n          size: 0,\r\n          thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n          fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n          lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n          category: this.selectedCategory // 加入當前選擇的類別\r\n        })) || [];\r\n\r\n        // 從所有圖片中找出已綁定的圖片\r\n        const boundImages = allAvailableImages.filter(image => this.boundImageIds.includes(image.id));\r\n        this.selectedImages = [...boundImages];\r\n\r\n        // 將已綁定的圖片加入全域已選擇圖片列表\r\n        boundImages.forEach(image => {\r\n          const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n          if (allSelectedIndex === -1) {\r\n            this.allSelectedImages.push(image);\r\n          }\r\n        });\r\n\r\n        // 更新可選擇的圖片\r\n        this.updateAvailableImages();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增 picklist 相關方法\r\n  updateAvailableImages() {\r\n    // 使用當前 API 回傳的圖片作為可選圖片基礎\r\n    let currentPageImages = [...this.allImages];\r\n\r\n    // 根據搜尋條件篩選當前分頁圖片\r\n    if (this.imageSearchTerm.trim()) {\r\n      const searchTerm = this.imageSearchTerm.toLowerCase();\r\n      currentPageImages = currentPageImages.filter(image =>\r\n        image.name.toLowerCase().includes(searchTerm)\r\n      );\r\n    }\r\n\r\n    // 排除已選擇的圖片\r\n    const selectedIds = this.selectedImages.map(img => img.id);\r\n    this.availableImages = currentPageImages.filter(image => !selectedIds.includes(image.id));\r\n  }\r\n\r\n  filterAvailableImages() {\r\n    this.updateAvailableImages();\r\n  }\r\n\r\n  moveToSelected(image: ImageItem, event?: Event) {\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n\r\n    // 將圖片從可選移到已選\r\n    const index = this.availableImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.push(image);\r\n\r\n      // 同時更新全域已選擇圖片列表\r\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n      if (allSelectedIndex === -1) {\r\n        this.allSelectedImages.push(image);\r\n      }\r\n\r\n      this.updateAvailableImages();\r\n    }\r\n  }\r\n\r\n  moveToAvailable(image: ImageItem, event?: Event) {\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n\r\n    // 將圖片從已選移到可選（包括已綁定的圖片也可以取消）\r\n    const index = this.selectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.splice(index, 1);\r\n\r\n      // 同時從全域已選擇圖片列表中移除\r\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n      if (allSelectedIndex > -1) {\r\n        this.allSelectedImages.splice(allSelectedIndex, 1);\r\n      }\r\n\r\n      this.updateAvailableImages();\r\n    }\r\n  }\r\n\r\n  moveAllToSelected() {\r\n    // 將所有可選圖片移到已選\r\n    this.selectedImages.push(...this.availableImages);\r\n\r\n    // 同時更新全域已選擇圖片列表\r\n    this.availableImages.forEach(image => {\r\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n      if (allSelectedIndex === -1) {\r\n        this.allSelectedImages.push(image);\r\n      }\r\n    });\r\n\r\n    this.updateAvailableImages();\r\n  }\r\n\r\n  moveAllToAvailable() {\r\n    // 將所有已選圖片移到可選（包括已綁定的圖片）\r\n    // 從全域已選擇圖片列表中移除當前類別的圖片\r\n    this.selectedImages.forEach(image => {\r\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n      if (allSelectedIndex > -1) {\r\n        this.allSelectedImages.splice(allSelectedIndex, 1);\r\n      }\r\n    });\r\n\r\n    this.selectedImages = [];\r\n    this.updateAvailableImages();\r\n  }\r\n\r\n  isImageBound(image: ImageItem): boolean {\r\n    return this.boundImageIds.includes(image.id);\r\n  }\r\n\r\n  getBoundImagesCount(): number {\r\n    return this.selectedImages.filter(image => this.isImageBound(image)).length;\r\n  }\r\n\r\n  getNewSelectedCount(): number {\r\n    return this.selectedImages.filter(image => !this.isImageBound(image)).length;\r\n  }\r\n\r\n  // 清除所有選擇（包括已綁定的圖片）\r\n  clearAllSelection() {\r\n    // 從全域已選擇圖片列表中移除當前類別的圖片\r\n    this.selectedImages.forEach(image => {\r\n      const allSelectedIndex = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n      if (allSelectedIndex > -1) {\r\n        this.allSelectedImages.splice(allSelectedIndex, 1);\r\n      }\r\n    });\r\n\r\n    this.selectedImages = [];\r\n    this.updateAvailableImages();\r\n  }\r\n\r\n  previewImage(image: ImageItem, imagePreviewRef: TemplateRef<any>, event: Event) {\r\n    event.stopPropagation();\r\n    this.previewingImage = image;\r\n    // 在所有圖片中找到當前預覽圖片的索引\r\n    this.currentPreviewIndex = this.allImages.findIndex((img: ImageItem) => img.id === image.id);\r\n    this.dialogService.open(imagePreviewRef);\r\n  }\r\n\r\n  previousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.allImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  nextImage() {\r\n    if (this.currentPreviewIndex < this.allImages.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.allImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  toggleImageSelectionInPreview() {\r\n    if (this.previewingImage) {\r\n      const isSelected = this.selectedImages.some(img => img.id === this.previewingImage!.id);\r\n      if (isSelected) {\r\n        this.moveToAvailable(this.previewingImage);\r\n      } else {\r\n        this.moveToSelected(this.previewingImage);\r\n      }\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n  onConfirmImageSelection(ref: any) {\r\n    if (this.selectedImages.length > 0) {\r\n      // 收集選中圖片的 ID\r\n      const selectedImageIds = this.selectedImages.map(img => img.id);      // 如果只選取一張圖片，直接設定圖片 ID\r\n      if (this.selectedImages.length === 1) {\r\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\r\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\r\n      } else {\r\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\r\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\r\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\r\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\r\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\r\n      }\r\n\r\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\r\n      (this.selectedMaterial as any).selectedImageIds = selectedImageIds;\r\n\r\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\r\n      if (this.selectedMaterial.CId) {\r\n        this.saveImageBinding();\r\n      }\r\n    }\r\n\r\n    this.clearAllSelection();\r\n    ref.close();\r\n  }  // 新增方法：保存圖片綁定\r\n  saveImageBinding() {\r\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus, // 加入狀態欄位\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(`圖片綁定成功`);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      }),\r\n      mergeMap(() => this.getMaterialList()),\r\n      finalize(() => {\r\n        // 清空選取的建材\r\n        this.selectedMaterial = {};\r\n      })\r\n    ).subscribe()\r\n  }\r\n  onCloseImageBinder(ref: any) {\r\n    this.clearAllSelection();\r\n    this.imageSearchTerm = \"\";\r\n    this.imageCurrentPage = 1; // 重設圖片頁碼\r\n    ref.close();\r\n  }// 類別變更處理方法\r\n  categoryChanged(category: PictureCategory) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true;\r\n\r\n    // 當類別變更時，從全域已選擇圖片中篩選出當前類別的圖片\r\n    this.selectedImages = this.allSelectedImages.filter(image => image.category === category);\r\n\r\n    // 當類別變更時重設頁碼並重新載入圖片\r\n    this.imageCurrentPage = 1;\r\n    if (this.selectedBuildCaseId) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 獲取類別標籤的方法\r\n  getCategoryLabel(category: number): string {\r\n    const option = this.categoryOptions.find(opt => opt.value === category);\r\n    return option ? option.label : '未知類別';\r\n  }\r\n\r\n  // 獲取指定類別的已選擇圖片數量\r\n  getSelectedCountByCategory(category: PictureCategory): number {\r\n    return this.allSelectedImages.filter(image => image.category === category).length;\r\n  }\r\n\r\n  // 載入所有圖片（建材圖片和示意圖片）\r\n  loadAllImages() {\r\n    this.loadBuildingMaterialImages();\r\n    this.loadSchematicImages();\r\n  }\r\n\r\n  // 載入建材圖片\r\n  loadBuildingMaterialImages() {\r\n    if (this.selectedBuildCaseId) {\r\n      this._pictureService.apiPictureGetPictureListPost$Json({\r\n        body: {\r\n          PageIndex: this.buildingMaterialCurrentPage,\r\n          PageSize: this.buildingMaterialPageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: PictureCategory.BUILDING_MATERIAL\r\n        }\r\n      }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          this.buildingMaterialImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || picture.CName || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            category: PictureCategory.BUILDING_MATERIAL\r\n          })) || [];\r\n          this.buildingMaterialTotalRecords = res.TotalItems || 0;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // 載入示意圖片\r\n  loadSchematicImages() {\r\n    if (this.selectedBuildCaseId) {\r\n      this._pictureService.apiPictureGetPictureListPost$Json({\r\n        body: {\r\n          PageIndex: this.schematicCurrentPage,\r\n          PageSize: this.schematicPageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: PictureCategory.SCHEMATIC\r\n        }\r\n      }).subscribe((res: GetPictureListResponseListResponseBase) => {\r\n        if (res.StatusCode === 0) {\r\n          this.schematicImages = res.Entries?.map((picture: GetPictureListResponse) => ({\r\n            id: picture.CId || 0,\r\n            name: picture.CPictureCode || picture.CName || '',\r\n            size: 0,\r\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date(),\r\n            category: PictureCategory.SCHEMATIC\r\n          })) || [];\r\n          this.schematicTotalRecords = res.TotalItems || 0;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // 選擇圖片\r\n  selectImage(image: ImageItem) {\r\n    const index = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n    if (index === -1) {\r\n      this.allSelectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  // 取消選擇圖片\r\n  unselectImage(image: ImageItem) {\r\n    const index = this.allSelectedImages.findIndex(img => img.id === image.id);\r\n    if (index > -1) {\r\n      this.allSelectedImages.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  // 檢查圖片是否已選擇\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.allSelectedImages.some(img => img.id === image.id);\r\n  }\r\n\r\n  // 切換圖片選擇狀態\r\n  toggleImageSelection(image: ImageItem) {\r\n    if (this.isImageSelected(image)) {\r\n      this.unselectImage(image);\r\n    } else {\r\n      this.selectImage(image);\r\n    }\r\n  }\r\n\r\n  // 獲取已選擇的建材圖片\r\n  getSelectedBuildingMaterialImages(): ImageItem[] {\r\n    return this.allSelectedImages.filter(image => image.category === PictureCategory.BUILDING_MATERIAL);\r\n  }\r\n\r\n  // 獲取已選擇的示意圖片\r\n  getSelectedSchematicImages(): ImageItem[] {\r\n    return this.allSelectedImages.filter(image => image.category === PictureCategory.SCHEMATIC);\r\n  }\r\n\r\n  // 建材圖片分頁變更\r\n  buildingMaterialPageChanged(page: number) {\r\n    this.buildingMaterialCurrentPage = page;\r\n    this.loadBuildingMaterialImages();\r\n  }\r\n\r\n  // 示意圖片分頁變更\r\n  schematicPageChanged(page: number) {\r\n    this.schematicCurrentPage = page;\r\n    this.loadSchematicImages();\r\n  }\r\n\r\n  // 圖片分頁變更處理方法\r\n  imagePageChanged(page: number) {\r\n    this.imageCurrentPage = page;\r\n    this.loadImages();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAA8B,eAAe;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAG9C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AAEnE;AACA,IAAKC,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAyBb,IAAMC,yBAAyB,GAA/B,MAAMA,yBAA0B,SAAQF,aAAa;EAyE1D;EACAG,cAAcA,CAACC,MAAc;IAC3B,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKL,MAAM,CAAC;IACnE,OAAOC,MAAM,GAAGA,MAAM,CAACK,KAAK,GAAG,KAAK;EACtC;EAEAC,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B,EAC/BC,eAA+B;IAEvC,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAtFzB,KAAAC,KAAK,GAAG,IAAI;IAKZ,KAAAC,cAAc,GAA8B,EAAE;IAG9C,KAAAC,eAAe,GAAG,CAChB;MACEb,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,CAAC;IAAE,KAAAa,iBAAiB,GAAG,IAAI;IAC9B,KAAAC,WAAW,GAAW,EAAE;IACxB;IACA;IACA;IACA;IACA,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,mBAAmB,GAAW,EAAE;IAChC,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,UAAU,GAAY,IAAI;IAC1B;IACA,KAAAC,sBAAsB,GAAgB,EAAE,EAAC;IACzC,KAAAC,eAAe,GAAgB,EAAE,EAAC;IAClC,KAAAC,iBAAiB,GAAgB,EAAE,EAAC;IACpC,KAAAC,aAAa,GAAa,EAAE,EAAC;IAC7B,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,eAAe,GAAqB,IAAI;IACxC,KAAAC,mBAAmB,GAAW,CAAC;IAE/B;IACA,KAAAC,2BAA2B,GAAW,CAAC;IACvC,KAAAC,wBAAwB,GAAW,EAAE;IACrC,KAAAC,4BAA4B,GAAW,CAAC;IAExC,KAAAC,oBAAoB,GAAW,CAAC;IAChC,KAAAC,iBAAiB,GAAW,EAAE;IAC9B,KAAAC,qBAAqB,GAAW,CAAC;IAEjC;IACA,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,iBAAiB,GAAW,CAAC;IAE7B;IACA,KAAAC,eAAe,GAAG,CAChB;MAAErC,KAAK,EAAER,eAAe,CAAC8C,iBAAiB;MAAErC,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAED,KAAK,EAAER,eAAe,CAAC+C,SAAS;MAAEtC,KAAK,EAAE;IAAM,CAAE,CACpD;IACD,KAAAuC,gBAAgB,GAAoBhD,eAAe,CAAC8C,iBAAiB;IACrE,KAAAG,kBAAkB,GAAY,IAAI,EAAC;IACnC;IACA,KAAAjD,eAAe,GAAGA,eAAe;IAEjC;IACA,KAAAK,aAAa,GAAG,CAAC;MACfG,KAAK,EAAE,CAAC;MAAE;MACVC,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC;KACb,CAAC;EAkBF;EAESyC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACpC,iBAAiB,CAACqC,6CAA6C,CAAC;MACnEC,IAAI,EAAE;QACJC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CACCC,IAAI,CACH5D,GAAG,CAAC6D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACtC,cAAc,GAAGqC,GAAG,CAACE,OAAO,EAAEC,MAAM,GAAGH,GAAG,CAACE,OAAO,GAAG,EAAE;QAC5D,IAAI,CAACE,mBAAmB,GAAG,IAAI,CAACzC,cAAc,CAAC,CAAC,CAAC,CAAC0C,GAAI;MACxD;IACF,CAAC,CAAC,EACFnE,QAAQ,CAAC,MAAM,IAAI,CAACoE,eAAe,EAAE,CAAC,CACvC,CAACC,SAAS,EAAE;EACjB;EAAED,eAAeA,CAACE,SAAA,GAAoB,CAAC;IACrC,OAAO,IAAI,CAACjD,gBAAgB,CAACkD,mCAAmC,CAAC;MAC/Db,IAAI,EAAE;QACJc,YAAY,EAAE,IAAI,CAACN,mBAAmB;QACtCO,QAAQ,EAAE,IAAI,CAAC9C,iBAAiB;QAChCC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7B;QACAC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B;QACA6C,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,SAAS,EAAEN,SAAS;QACpBrC,UAAU,EAAE,IAAI,CAACA;;KAEpB,CAAC,CAAC4B,IAAI,CACL5D,GAAG,CAAC6D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACc,YAAY,GAAGf,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACc,YAAY,GAAGhB,GAAG,CAACiB,UAAW;QAEnC,IAAI,IAAI,CAACF,YAAY,CAACZ,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACnC,SAAS,GAAG,IAAI,CAAC+C,YAAY,CAAC,CAAC,CAAC,CAACG,UAAW;QACnD;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACb,eAAe,EAAE,CAACC,SAAS,EAAE;EACpC;EAEAa,WAAWA,CAACZ,SAAiB;IAC3B,IAAI,CAACF,eAAe,CAACE,SAAS,CAAC,CAACD,SAAS,EAAE;EAC7C;EAEAc,sBAAsBA,CAAA;IACpB,IAAI,CAAC9D,gBAAgB,CAAC+D,2CAA2C,CAAC;MAChE1B,IAAI,EAAE,IAAI,CAACQ;KACZ,CAAC,CAACG,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACqB,QAAQ,EAAE;UACzB,IAAI,CAAC/D,eAAe,CAACgE,iBAAiB,CAACxB,GAAG,CAACE,OAAQ,CAACqB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CAAA;IACxB,IAAI,CAAClE,gBAAgB,CAACmE,+CAA+C,CAAC;MACpE9B,IAAI,EAAE,IAAI,CAACQ;KACZ,CAAC,CAACG,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACqB,QAAQ,EAAE;UACzB,IAAI,CAAC/D,eAAe,CAACgE,iBAAiB,CAACxB,GAAG,CAACE,OAAQ,CAACqB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EACAI,MAAMA,CAACC,GAAQ;IACb,IAAI,CAAClE,KAAK,GAAG,IAAI;IACjB,IAAI,CAACmE,gBAAgB,GAAG;MACtB/B,OAAO,EAAE,CAAC,CAAC;KACZ;IACD,IAAI,CAAC3C,aAAa,CAAC2E,IAAI,CAACF,GAAG,CAAC;EAC9B;EACAG,kBAAkBA,CAACC,IAA6B,EAAEJ,GAAQ;IACxD,IAAI,CAAClE,KAAK,GAAG,KAAK;IAClB,IAAI,CAACmE,gBAAgB,GAAG;MAAE,GAAGG;IAAI,CAAE;IACnC,IAAI,CAAC7E,aAAa,CAAC2E,IAAI,CAACF,GAAG,CAAC;EAC9B;EACAK,oBAAoBA,CAACD,IAA6B,EAAEJ,GAAqB;IACvE,IAAI,CAACC,gBAAgB,GAAG;MAAE,GAAGG;IAAI,CAAE;IACnC;IACA,IAAI,CAACzD,aAAa,GAAG,IAAI,CAACsD,gBAAgB,CAACK,gBAAgB,GACzD,IAAI,CAACL,gBAAgB,CAACK,gBAAgB,CAACC,GAAG,CAACC,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,GAAGC,QAAQ,CAACD,EAAE,CAAC,GAAGA,EAAE,CAAC,GAAG,EAAE;IAEnG;IACA,IAAI,CAAC9D,iBAAiB,GAAG,EAAE,EAAC;IAC5B,IAAI,CAACK,2BAA2B,GAAG,CAAC;IACpC,IAAI,CAACG,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACN,eAAe,GAAG,EAAE;IAEzB,IAAI,CAAC8D,aAAa,EAAE;IACpB,IAAI,CAACnF,aAAa,CAAC2E,IAAI,CAACF,GAAG,EAAE;MAAEW,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAAEC,UAAUA,CAAA;IACV,IAAI,CAACnF,KAAK,CAACoF,KAAK,EAAE;IAClB,IAAI,CAACpF,KAAK,CAACqF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACb,gBAAgB,CAACc,KAAK,CAAC;IACxD,IAAI,CAACtF,KAAK,CAACqF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACb,gBAAgB,CAACe,KAAK,CAAC;IACxD,IAAI,CAACvF,KAAK,CAACqF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACb,gBAAgB,CAACgB,SAAS,CAAC;IAC5D,IAAI,CAACxF,KAAK,CAACqF,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACb,gBAAgB,CAAC/D,WAAW,CAAC;IAClE;IACA,IAAI,CAACT,KAAK,CAACqF,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACb,gBAAgB,CAAC9D,UAAU,CAAC;IAC/D,IAAI,CAACV,KAAK,CAACqF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACb,gBAAgB,CAAC/B,OAAO,CAAC;IAC1D,IAAI,CAACzC,KAAK,CAACyF,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACjB,gBAAgB,CAACc,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAACtF,KAAK,CAACyF,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACjB,gBAAgB,CAACe,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAACvF,KAAK,CAACyF,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACjB,gBAAgB,CAACgB,SAAS,EAAE,EAAE,CAAC;IACzE,IAAI,CAACxF,KAAK,CAACyF,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAACjB,gBAAgB,CAAC/D,WAAW,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,CAACT,KAAK,CAACyF,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACjB,gBAAgB,CAAC9D,UAAU,EAAE,EAAE,CAAC;EAC9E;EAEAgF,QAAQA,CAACnB,GAAQ;IACf,IAAI,CAACY,UAAU,EAAE;IACjB,IAAI,IAAI,CAACnF,KAAK,CAAC2F,aAAa,CAAC7C,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC/C,OAAO,CAAC6F,aAAa,CAAC,IAAI,CAAC5F,KAAK,CAAC2F,aAAa,CAAC;MACpD;IACF;IAAE,IAAI,CAACzF,gBAAgB,CAAC2F,qCAAqC,CAAC;MAC5DtD,IAAI,EAAE;QACJc,YAAY,EAAE,IAAI,CAACN,mBAAmB;QACtC;QACArC,UAAU,EAAE,IAAI,CAAC8D,gBAAgB,CAAC9D,UAAU;QAC5C4E,KAAK,EAAE,IAAI,CAACd,gBAAgB,CAACc,KAAK;QAClCC,KAAK,EAAE,IAAI,CAACf,gBAAgB,CAACe,KAAK;QAClCC,SAAS,EAAE,IAAI,CAAChB,gBAAgB,CAACgB,SAAS;QAC1C/E,WAAW,EAAE,IAAI,CAAC+D,gBAAgB,CAAC/D,WAAW;QAC9CqF,YAAY,EAAE,IAAI,CAACtB,gBAAgB,CAACsB,YAAY;QAChDC,WAAW,EAAE,IAAI,CAAC1F,KAAK,GAAG,IAAI,GAAG,IAAI,CAACmE,gBAAgB,CAACwB,GAAI;QAC3DC,MAAM,EAAE,IAAI,CAACzB,gBAAgB,CAACyB,MAAM;QACpCxD,OAAO,EAAE,IAAI,CAAC+B,gBAAgB,CAAC/B,OAAO;QAAE;QACxCyD,UAAU,EAAG,IAAI,CAAC1B,gBAAwB,CAAC2B,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CACCzD,IAAI,CACH5D,GAAG,CAAC6D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC7C,OAAO,CAACqG,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACrG,OAAO,CAACsG,YAAY,CAAC1D,GAAG,CAAC2D,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFzH,QAAQ,CAAC,MAAM,IAAI,CAACoE,eAAe,EAAE,CAAC,EACtCrE,QAAQ,CAAC,MAAM2F,GAAG,CAACgC,KAAK,EAAE,CAAC,CAC5B,CAACrD,SAAS,EAAE;EACjB;EAEAsD,OAAOA,CAACjC,GAAQ;IACdA,GAAG,CAACgC,KAAK,EAAE;EACb;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,MAAM,GAAgCD,KAAK,CAACC,MAAO;IACzD,MAAMC,MAAM,GAAe,IAAIC,UAAU,EAAE;IAC3CD,MAAM,CAACE,kBAAkB,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACI,MAAM,GAAIC,CAAM,IAAI;MACzB,MAAMC,SAAS,GAAWD,CAAC,CAACN,MAAM,CAACQ,MAAM;MACzC,MAAMC,EAAE,GAAkBrI,IAAI,CAACsI,IAAI,CAACH,SAAS,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAE,CAAC;MAElE,MAAMC,MAAM,GAAWH,EAAE,CAACI,UAAU,CAAC,CAAC,CAAC;MACvC,MAAMC,EAAE,GAAmBL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC;MAE5C,IAAII,WAAW,GAAY,IAAI;MAC/B,MAAMhD,IAAI,GAAG5F,IAAI,CAAC6I,KAAK,CAACC,aAAa,CAACJ,EAAE,CAAC;MACzC,IAAI9C,IAAI,IAAIA,IAAI,CAAC7B,MAAM,GAAG,CAAC,EAAE;QAC3B6B,IAAI,CAACmD,OAAO,CAAEC,CAAM,IAAI;UACtB,IAAI,EAAEA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE;YACnDJ,WAAW,GAAG,KAAK;UACrB;QACF,CAAC,CAAC;QAEF,IAAI,CAACA,WAAW,EAAE;UAChB,IAAI,CAAC5H,OAAO,CAACsG,YAAY,CAAC,WAAW,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAACnG,gBAAgB,CAAC8H,2CAA2C,CAAC;YAChEzF,IAAI,EAAE;cACJc,YAAY,EAAE,IAAI,CAACN,mBAAmB;cACtCkF,KAAK,EAAEtB,MAAM,CAACI,KAAK,CAAC,CAAC;;WAExB,CAAC,CAACrE,IAAI,CACL5D,GAAG,CAAC6D,GAAG,IAAG;YACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;cACvB,IAAI,CAAC7C,OAAO,CAACqG,aAAa,CAAC,MAAM,CAAC;YACpC,CAAC,MAAM;cACL,IAAI,CAACrG,OAAO,CAACsG,YAAY,CAAC1D,GAAG,CAAC2D,OAAQ,CAAC;YACzC;UACF,CAAC,CAAC,EACFzH,QAAQ,CAAC,MAAM,IAAI,CAACoE,eAAe,CAAC,CAAC,CAAC,CAAC,CACxC,CAACC,SAAS,EAAE;QACf;MACF,CAAC,MAAM;QACL,IAAI,CAACnD,OAAO,CAACsG,YAAY,CAAC,uBAAuB,CAAC;MACpD;MACAK,KAAK,CAACC,MAAM,CAACjH,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEAwI,SAASA,CAACC,QAAgB,EAAEC,MAAwB;IAClD,IAAI,CAACxH,mBAAmB,GAAGuH,QAAQ;IACnC,IAAI,CAACrI,aAAa,CAAC2E,IAAI,CAAC2D,MAAM,CAAC;EACjC;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACxH,aAAa,EAAE;MACtB,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACmC,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC,CAAC,MACI;MACH,IAAI,CAACpC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmC,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC;EACF;EACA;EACAoF,eAAeA,CAAC/D,GAAqB;IACnC;IACA,IAAI,CAACtD,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACK,2BAA2B,GAAG,CAAC;IACpC,IAAI,CAACG,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACN,eAAe,GAAG,EAAE;IAEzB,IAAI,CAAC8D,aAAa,EAAE;IACpB,IAAI,CAACnF,aAAa,CAAC2E,IAAI,CAACF,GAAG,EAAE;MAAEW,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAEAqD,UAAUA,CAAA;IACR;IACA,IAAI,IAAI,CAACpG,kBAAkB,IAAI,IAAI,CAACY,mBAAmB,EAAE;MACvD,IAAI,CAAC3C,eAAe,CAACoI,iCAAiC,CAAC;QACrDjG,IAAI,EAAE;UACJc,YAAY,EAAE,IAAI,CAACN,mBAAmB;UACtC0F,YAAY,EAAE,IAAI,CAACvG,gBAAgB;UACnCuB,SAAS,EAAE,IAAI,CAAC7B,gBAAgB;UAChC2B,QAAQ,EAAE,IAAI,CAAC1B;;OAElB,CAAC,CAACqB,SAAS,CAAEP,GAA2C,IAAI;QAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,IAAI,CAAC8F,SAAS,GAAG/F,GAAG,CAACE,OAAO,EAAEiC,GAAG,CAAE6D,OAA+B,KAAM;YACtE5D,EAAE,EAAE4D,OAAO,CAAC3C,GAAG,IAAI,CAAC;YACpB4C,IAAI,EAAED,OAAO,CAACE,YAAY,IAAIF,OAAO,CAACrD,KAAK,IAAI,EAAE;YACjDwD,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEJ,OAAO,CAACK,OAAO,GAAG,0BAA0BL,OAAO,CAACK,OAAO,EAAE,GAAG,EAAE;YAChFC,OAAO,EAAEN,OAAO,CAACK,OAAO,GAAG,0BAA0BL,OAAO,CAACK,OAAO,EAAE,GAAG,EAAE;YAC3EE,YAAY,EAAEP,OAAO,CAACQ,SAAS,GAAG,IAAIC,IAAI,CAACT,OAAO,CAACQ,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,QAAQ,EAAE,IAAI,CAACnH,gBAAgB,CAAC;WACjC,CAAC,CAAC,IAAI,EAAE;UAET,IAAI,CAACJ,iBAAiB,GAAGa,GAAG,CAACiB,UAAU,IAAI,CAAC;UAE5C;UACA;UACA,IAAI,IAAI,CAAC0F,cAAc,CAACxG,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC5B,aAAa,CAAC4B,MAAM,GAAG,CAAC,EAAE;YACrE,IAAI,CAACyG,gCAAgC,EAAE;UACzC,CAAC,MAAM;YACL;YACA,IAAI,CAACC,qBAAqB,EAAE;UAC9B;QACF,CAAC,MAAM;UACL,IAAI,CAACzJ,OAAO,CAACsG,YAAY,CAAC1D,GAAG,CAAC2D,OAAO,IAAI,QAAQ,CAAC;UAClD,IAAI,CAACoC,SAAS,GAAG,EAAE;UACnB,IAAI,CAACe,eAAe,GAAG,EAAE;UACzB;UACA,IAAI,IAAI,CAACH,cAAc,CAACxG,MAAM,KAAK,CAAC,EAAE;YACpC,IAAI,CAACwG,cAAc,GAAG,EAAE;UAC1B;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACZ,SAAS,GAAG,EAAE;MACnB,IAAI,CAACe,eAAe,GAAG,EAAE;MACzB,IAAI,CAACH,cAAc,GAAG,EAAE;IAC1B;EACF;EAIA;EACAC,gCAAgCA,CAAA;IAC9B;IACA;IACA,IAAI,CAACnJ,eAAe,CAACoI,iCAAiC,CAAC;MACrDjG,IAAI,EAAE;QACJc,YAAY,EAAE,IAAI,CAACN,mBAAmB;QACtC0F,YAAY,EAAE,IAAI,CAACvG,gBAAgB;QACnCuB,SAAS,EAAE,CAAC;QACZF,QAAQ,EAAE,IAAI,CAAC;;KAElB,CAAC,CAACL,SAAS,CAAEP,GAA2C,IAAI;MAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,MAAM8G,kBAAkB,GAAG/G,GAAG,CAACE,OAAO,EAAEiC,GAAG,CAAE6D,OAA+B,KAAM;UAChF5D,EAAE,EAAE4D,OAAO,CAAC3C,GAAG,IAAI,CAAC;UACpB4C,IAAI,EAAED,OAAO,CAACE,YAAY,IAAIF,OAAO,CAACrD,KAAK,IAAI,EAAE;UACjDwD,IAAI,EAAE,CAAC;UACPC,YAAY,EAAEJ,OAAO,CAACK,OAAO,GAAG,0BAA0BL,OAAO,CAACK,OAAO,EAAE,GAAG,EAAE;UAChFC,OAAO,EAAEN,OAAO,CAACK,OAAO,GAAG,0BAA0BL,OAAO,CAACK,OAAO,EAAE,GAAG,EAAE;UAC3EE,YAAY,EAAEP,OAAO,CAACQ,SAAS,GAAG,IAAIC,IAAI,CAACT,OAAO,CAACQ,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;UAC1EC,QAAQ,EAAE,IAAI,CAACnH,gBAAgB,CAAC;SACjC,CAAC,CAAC,IAAI,EAAE;QAET;QACA,MAAMyH,WAAW,GAAGD,kBAAkB,CAACE,MAAM,CAACC,KAAK,IAAI,IAAI,CAAC3I,aAAa,CAAC4I,QAAQ,CAACD,KAAK,CAAC9E,EAAE,CAAC,CAAC;QAC7F,IAAI,CAACuE,cAAc,GAAG,CAAC,GAAGK,WAAW,CAAC;QAEtC;QACAA,WAAW,CAAC7B,OAAO,CAAC+B,KAAK,IAAG;UAC1B,MAAME,gBAAgB,GAAG,IAAI,CAAC9I,iBAAiB,CAAC+I,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;UACrF,IAAIgF,gBAAgB,KAAK,CAAC,CAAC,EAAE;YAC3B,IAAI,CAAC9I,iBAAiB,CAACiJ,IAAI,CAACL,KAAK,CAAC;UACpC;QACF,CAAC,CAAC;QAEF;QACA,IAAI,CAACL,qBAAqB,EAAE;MAC9B;IACF,CAAC,CAAC;EACJ;EAEA;EACAA,qBAAqBA,CAAA;IACnB;IACA,IAAIW,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACzB,SAAS,CAAC;IAE3C;IACA,IAAI,IAAI,CAACvH,eAAe,CAACiJ,IAAI,EAAE,EAAE;MAC/B,MAAMC,UAAU,GAAG,IAAI,CAAClJ,eAAe,CAACmJ,WAAW,EAAE;MACrDH,iBAAiB,GAAGA,iBAAiB,CAACP,MAAM,CAACC,KAAK,IAChDA,KAAK,CAACjB,IAAI,CAAC0B,WAAW,EAAE,CAACR,QAAQ,CAACO,UAAU,CAAC,CAC9C;IACH;IAEA;IACA,MAAME,WAAW,GAAG,IAAI,CAACjB,cAAc,CAACxE,GAAG,CAACmF,GAAG,IAAIA,GAAG,CAAClF,EAAE,CAAC;IAC1D,IAAI,CAAC0E,eAAe,GAAGU,iBAAiB,CAACP,MAAM,CAACC,KAAK,IAAI,CAACU,WAAW,CAACT,QAAQ,CAACD,KAAK,CAAC9E,EAAE,CAAC,CAAC;EAC3F;EAEAyF,qBAAqBA,CAAA;IACnB,IAAI,CAAChB,qBAAqB,EAAE;EAC9B;EAEAiB,cAAcA,CAACZ,KAAgB,EAAEnD,KAAa;IAC5C,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACgE,eAAe,EAAE;IACzB;IAEA;IACA,MAAMC,KAAK,GAAG,IAAI,CAAClB,eAAe,CAACO,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;IACxE,IAAI4F,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACrB,cAAc,CAACY,IAAI,CAACL,KAAK,CAAC;MAE/B;MACA,MAAME,gBAAgB,GAAG,IAAI,CAAC9I,iBAAiB,CAAC+I,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;MACrF,IAAIgF,gBAAgB,KAAK,CAAC,CAAC,EAAE;QAC3B,IAAI,CAAC9I,iBAAiB,CAACiJ,IAAI,CAACL,KAAK,CAAC;MACpC;MAEA,IAAI,CAACL,qBAAqB,EAAE;IAC9B;EACF;EAEAoB,eAAeA,CAACf,KAAgB,EAAEnD,KAAa;IAC7C,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACgE,eAAe,EAAE;IACzB;IAEA;IACA,MAAMC,KAAK,GAAG,IAAI,CAACrB,cAAc,CAACU,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;IACvE,IAAI4F,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACrB,cAAc,CAACuB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAEpC;MACA,MAAMZ,gBAAgB,GAAG,IAAI,CAAC9I,iBAAiB,CAAC+I,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;MACrF,IAAIgF,gBAAgB,GAAG,CAAC,CAAC,EAAE;QACzB,IAAI,CAAC9I,iBAAiB,CAAC4J,MAAM,CAACd,gBAAgB,EAAE,CAAC,CAAC;MACpD;MAEA,IAAI,CAACP,qBAAqB,EAAE;IAC9B;EACF;EAEAsB,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACxB,cAAc,CAACY,IAAI,CAAC,GAAG,IAAI,CAACT,eAAe,CAAC;IAEjD;IACA,IAAI,CAACA,eAAe,CAAC3B,OAAO,CAAC+B,KAAK,IAAG;MACnC,MAAME,gBAAgB,GAAG,IAAI,CAAC9I,iBAAiB,CAAC+I,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;MACrF,IAAIgF,gBAAgB,KAAK,CAAC,CAAC,EAAE;QAC3B,IAAI,CAAC9I,iBAAiB,CAACiJ,IAAI,CAACL,KAAK,CAAC;MACpC;IACF,CAAC,CAAC;IAEF,IAAI,CAACL,qBAAqB,EAAE;EAC9B;EAEAuB,kBAAkBA,CAAA;IAChB;IACA;IACA,IAAI,CAACzB,cAAc,CAACxB,OAAO,CAAC+B,KAAK,IAAG;MAClC,MAAME,gBAAgB,GAAG,IAAI,CAAC9I,iBAAiB,CAAC+I,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;MACrF,IAAIgF,gBAAgB,GAAG,CAAC,CAAC,EAAE;QACzB,IAAI,CAAC9I,iBAAiB,CAAC4J,MAAM,CAACd,gBAAgB,EAAE,CAAC,CAAC;MACpD;IACF,CAAC,CAAC;IAEF,IAAI,CAACT,cAAc,GAAG,EAAE;IACxB,IAAI,CAACE,qBAAqB,EAAE;EAC9B;EAEAwB,YAAYA,CAACnB,KAAgB;IAC3B,OAAO,IAAI,CAAC3I,aAAa,CAAC4I,QAAQ,CAACD,KAAK,CAAC9E,EAAE,CAAC;EAC9C;EAEAkG,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC3B,cAAc,CAACM,MAAM,CAACC,KAAK,IAAI,IAAI,CAACmB,YAAY,CAACnB,KAAK,CAAC,CAAC,CAAC/G,MAAM;EAC7E;EAEAoI,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC5B,cAAc,CAACM,MAAM,CAACC,KAAK,IAAI,CAAC,IAAI,CAACmB,YAAY,CAACnB,KAAK,CAAC,CAAC,CAAC/G,MAAM;EAC9E;EAEA;EACAqI,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAC7B,cAAc,CAACxB,OAAO,CAAC+B,KAAK,IAAG;MAClC,MAAME,gBAAgB,GAAG,IAAI,CAAC9I,iBAAiB,CAAC+I,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;MACrF,IAAIgF,gBAAgB,GAAG,CAAC,CAAC,EAAE;QACzB,IAAI,CAAC9I,iBAAiB,CAAC4J,MAAM,CAACd,gBAAgB,EAAE,CAAC,CAAC;MACpD;IACF,CAAC,CAAC;IAEF,IAAI,CAACT,cAAc,GAAG,EAAE;IACxB,IAAI,CAACE,qBAAqB,EAAE;EAC9B;EAEA4B,YAAYA,CAACvB,KAAgB,EAAEwB,eAAiC,EAAE3E,KAAY;IAC5EA,KAAK,CAACgE,eAAe,EAAE;IACvB,IAAI,CAACtJ,eAAe,GAAGyI,KAAK;IAC5B;IACA,IAAI,CAACxI,mBAAmB,GAAG,IAAI,CAACqH,SAAS,CAACsB,SAAS,CAAEC,GAAc,IAAKA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;IAC5F,IAAI,CAACjF,aAAa,CAAC2E,IAAI,CAAC4G,eAAe,CAAC;EAC1C;EAEAC,aAAaA,CAAA;IACX,IAAI,IAAI,CAACjK,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAACD,eAAe,GAAG,IAAI,CAACsH,SAAS,CAAC,IAAI,CAACrH,mBAAmB,CAAC;IACjE;EACF;EAEAkK,SAASA,CAAA;IACP,IAAI,IAAI,CAAClK,mBAAmB,GAAG,IAAI,CAACqH,SAAS,CAAC5F,MAAM,GAAG,CAAC,EAAE;MACxD,IAAI,CAACzB,mBAAmB,EAAE;MAC1B,IAAI,CAACD,eAAe,GAAG,IAAI,CAACsH,SAAS,CAAC,IAAI,CAACrH,mBAAmB,CAAC;IACjE;EACF;EAEAmK,6BAA6BA,CAAA;IAC3B,IAAI,IAAI,CAACpK,eAAe,EAAE;MACxB,MAAMqK,UAAU,GAAG,IAAI,CAACnC,cAAc,CAACoC,IAAI,CAACzB,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK,IAAI,CAAC3D,eAAgB,CAAC2D,EAAE,CAAC;MACvF,IAAI0G,UAAU,EAAE;QACd,IAAI,CAACb,eAAe,CAAC,IAAI,CAACxJ,eAAe,CAAC;MAC5C,CAAC,MAAM;QACL,IAAI,CAACqJ,cAAc,CAAC,IAAI,CAACrJ,eAAe,CAAC;MAC3C;IACF;EACF;EAEAuK,eAAeA,CAAC9B,KAAgB;IAC9B,OAAO,IAAI,CAACP,cAAc,CAACoC,IAAI,CAACE,QAAQ,IAAIA,QAAQ,CAAC7G,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;EACvE;EACA8G,uBAAuBA,CAACtH,GAAQ;IAC9B,IAAI,IAAI,CAAC+E,cAAc,CAACxG,MAAM,GAAG,CAAC,EAAE;MAClC;MACA,MAAMqD,gBAAgB,GAAG,IAAI,CAACmD,cAAc,CAACxE,GAAG,CAACmF,GAAG,IAAIA,GAAG,CAAClF,EAAE,CAAC,CAAC,CAAM;MACtE,IAAI,IAAI,CAACuE,cAAc,CAACxG,MAAM,KAAK,CAAC,EAAE;QACpC;QACA,IAAI,CAAC0B,gBAAgB,CAAC0B,UAAU,GAAG,IAAI,CAACoD,cAAc,CAAC,CAAC,CAAC,CAACvE,EAAE;MAC9D,CAAC,MAAM;QACL;QACA,MAAM+G,UAAU,GAAG,IAAI,CAACxC,cAAc,CAACxE,GAAG,CAACmF,GAAG,IAAIA,GAAG,CAACrB,IAAI,CAAC,CAACmD,IAAI,CAAC,IAAI,CAAC;QACtE,IAAI,CAAChM,OAAO,CAACqG,aAAa,CAAC,OAAO,IAAI,CAACkD,cAAc,CAACxG,MAAM,SAASgJ,UAAU,EAAE,CAAC;QAClF;QACA,IAAI,CAACtH,gBAAgB,CAAC0B,UAAU,GAAG,IAAI,CAACoD,cAAc,CAAC,CAAC,CAAC,CAACvE,EAAE;MAC9D;MAEA;MACC,IAAI,CAACP,gBAAwB,CAAC2B,gBAAgB,GAAGA,gBAAgB;MAElE;MACA,IAAI,IAAI,CAAC3B,gBAAgB,CAACwB,GAAG,EAAE;QAC7B,IAAI,CAACgG,gBAAgB,EAAE;MACzB;IACF;IAEA,IAAI,CAACb,iBAAiB,EAAE;IACxB5G,GAAG,CAACgC,KAAK,EAAE;EACb,CAAC,CAAE;EACHyF,gBAAgBA,CAAA;IACd,IAAI,CAAC9L,gBAAgB,CAAC2F,qCAAqC,CAAC;MAC1DtD,IAAI,EAAE;QACJc,YAAY,EAAE,IAAI,CAACN,mBAAmB;QACtCrC,UAAU,EAAE,IAAI,CAAC8D,gBAAgB,CAAC9D,UAAU;QAC5C4E,KAAK,EAAE,IAAI,CAACd,gBAAgB,CAACc,KAAK;QAClCC,KAAK,EAAE,IAAI,CAACf,gBAAgB,CAACe,KAAK;QAClCC,SAAS,EAAE,IAAI,CAAChB,gBAAgB,CAACgB,SAAS;QAC1C/E,WAAW,EAAE,IAAI,CAAC+D,gBAAgB,CAAC/D,WAAW;QAC9CqF,YAAY,EAAE,IAAI,CAACtB,gBAAgB,CAACsB,YAAY;QAChDC,WAAW,EAAE,IAAI,CAACvB,gBAAgB,CAACwB,GAAI;QACvCC,MAAM,EAAE,IAAI,CAACzB,gBAAgB,CAACyB,MAAM;QACpCxD,OAAO,EAAE,IAAI,CAAC+B,gBAAgB,CAAC/B,OAAO;QAAE;QACxCyD,UAAU,EAAG,IAAI,CAAC1B,gBAAwB,CAAC2B,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CAACzD,IAAI,CACL5D,GAAG,CAAC6D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC7C,OAAO,CAACqG,aAAa,CAAC,QAAQ,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,CAACrG,OAAO,CAACsG,YAAY,CAAC1D,GAAG,CAAC2D,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFzH,QAAQ,CAAC,MAAM,IAAI,CAACoE,eAAe,EAAE,CAAC,EACtCrE,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAAC4F,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC,CACH,CAACtB,SAAS,EAAE;EACf;EACA+I,kBAAkBA,CAAC1H,GAAQ;IACzB,IAAI,CAAC4G,iBAAiB,EAAE;IACxB,IAAI,CAAChK,eAAe,GAAG,EAAE;IACzB,IAAI,CAACS,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAC3B2C,GAAG,CAACgC,KAAK,EAAE;EACb,CAAC;EACD2F,eAAeA,CAAC7C,QAAyB;IACvC,IAAI,CAACnH,gBAAgB,GAAGmH,QAAQ;IAChC,IAAI,CAAClH,kBAAkB,GAAG,IAAI;IAE9B;IACA,IAAI,CAACmH,cAAc,GAAG,IAAI,CAACrI,iBAAiB,CAAC2I,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACR,QAAQ,KAAKA,QAAQ,CAAC;IAEzF;IACA,IAAI,CAACzH,gBAAgB,GAAG,CAAC;IACzB,IAAI,IAAI,CAACmB,mBAAmB,EAAE;MAC5B,IAAI,CAACwF,UAAU,EAAE;IACnB;EACF;EAEA;EACA4D,gBAAgBA,CAAC9C,QAAgB;IAC/B,MAAM/J,MAAM,GAAG,IAAI,CAACyC,eAAe,CAACvC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAK2J,QAAQ,CAAC;IACvE,OAAO/J,MAAM,GAAGA,MAAM,CAACK,KAAK,GAAG,MAAM;EACvC;EAEA;EACAyM,0BAA0BA,CAAC/C,QAAyB;IAClD,OAAO,IAAI,CAACpI,iBAAiB,CAAC2I,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACR,QAAQ,KAAKA,QAAQ,CAAC,CAACvG,MAAM;EACnF;EAEA;EACAmC,aAAaA,CAAA;IACX,IAAI,CAACoH,0BAA0B,EAAE;IACjC,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAD,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAACtJ,mBAAmB,EAAE;MAC5B,IAAI,CAAC3C,eAAe,CAACoI,iCAAiC,CAAC;QACrDjG,IAAI,EAAE;UACJkB,SAAS,EAAE,IAAI,CAACnC,2BAA2B;UAC3CiC,QAAQ,EAAE,IAAI,CAAChC,wBAAwB;UACvC8B,YAAY,EAAE,IAAI,CAACN,mBAAmB;UACtC0F,YAAY,EAAEvJ,eAAe,CAAC8C;;OAEjC,CAAC,CAACkB,SAAS,CAAEP,GAA2C,IAAI;QAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC7B,sBAAsB,GAAG4B,GAAG,CAACE,OAAO,EAAEiC,GAAG,CAAE6D,OAA+B,KAAM;YACnF5D,EAAE,EAAE4D,OAAO,CAAC3C,GAAG,IAAI,CAAC;YACpB4C,IAAI,EAAED,OAAO,CAACE,YAAY,IAAIF,OAAO,CAACrD,KAAK,IAAI,EAAE;YACjDwD,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEJ,OAAO,CAACK,OAAO,GAAG,0BAA0BL,OAAO,CAACK,OAAO,EAAE,GAAG,EAAE;YAChFC,OAAO,EAAEN,OAAO,CAACK,OAAO,GAAG,0BAA0BL,OAAO,CAACK,OAAO,EAAE,GAAG,EAAE;YAC3EE,YAAY,EAAEP,OAAO,CAACQ,SAAS,GAAG,IAAIC,IAAI,CAACT,OAAO,CAACQ,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,QAAQ,EAAEnK,eAAe,CAAC8C;WAC3B,CAAC,CAAC,IAAI,EAAE;UACT,IAAI,CAACR,4BAA4B,GAAGmB,GAAG,CAACiB,UAAU,IAAI,CAAC;QACzD;MACF,CAAC,CAAC;IACJ;EACF;EAEA;EACA0I,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACvJ,mBAAmB,EAAE;MAC5B,IAAI,CAAC3C,eAAe,CAACoI,iCAAiC,CAAC;QACrDjG,IAAI,EAAE;UACJkB,SAAS,EAAE,IAAI,CAAChC,oBAAoB;UACpC8B,QAAQ,EAAE,IAAI,CAAC7B,iBAAiB;UAChC2B,YAAY,EAAE,IAAI,CAACN,mBAAmB;UACtC0F,YAAY,EAAEvJ,eAAe,CAAC+C;;OAEjC,CAAC,CAACiB,SAAS,CAAEP,GAA2C,IAAI;QAC3D,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC5B,eAAe,GAAG2B,GAAG,CAACE,OAAO,EAAEiC,GAAG,CAAE6D,OAA+B,KAAM;YAC5E5D,EAAE,EAAE4D,OAAO,CAAC3C,GAAG,IAAI,CAAC;YACpB4C,IAAI,EAAED,OAAO,CAACE,YAAY,IAAIF,OAAO,CAACrD,KAAK,IAAI,EAAE;YACjDwD,IAAI,EAAE,CAAC;YACPC,YAAY,EAAEJ,OAAO,CAACK,OAAO,GAAG,0BAA0BL,OAAO,CAACK,OAAO,EAAE,GAAG,EAAE;YAChFC,OAAO,EAAEN,OAAO,CAACK,OAAO,GAAG,0BAA0BL,OAAO,CAACK,OAAO,EAAE,GAAG,EAAE;YAC3EE,YAAY,EAAEP,OAAO,CAACQ,SAAS,GAAG,IAAIC,IAAI,CAACT,OAAO,CAACQ,SAAS,CAAC,GAAG,IAAIC,IAAI,EAAE;YAC1EC,QAAQ,EAAEnK,eAAe,CAAC+C;WAC3B,CAAC,CAAC,IAAI,EAAE;UACT,IAAI,CAACN,qBAAqB,GAAGgB,GAAG,CAACiB,UAAU,IAAI,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EACF;EAEA;EACA2I,WAAWA,CAAC1C,KAAgB;IAC1B,MAAMc,KAAK,GAAG,IAAI,CAAC1J,iBAAiB,CAAC+I,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;IAC1E,IAAI4F,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC1J,iBAAiB,CAACiJ,IAAI,CAACL,KAAK,CAAC;IACpC;EACF;EAEA;EACA2C,aAAaA,CAAC3C,KAAgB;IAC5B,MAAMc,KAAK,GAAG,IAAI,CAAC1J,iBAAiB,CAAC+I,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;IAC1E,IAAI4F,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC1J,iBAAiB,CAAC4J,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IACzC;EACF;EAEA;EACAgB,eAAeA,CAAC9B,KAAgB;IAC9B,OAAO,IAAI,CAAC5I,iBAAiB,CAACyK,IAAI,CAACzB,GAAG,IAAIA,GAAG,CAAClF,EAAE,KAAK8E,KAAK,CAAC9E,EAAE,CAAC;EAChE;EAEA;EACA0H,oBAAoBA,CAAC5C,KAAgB;IACnC,IAAI,IAAI,CAAC8B,eAAe,CAAC9B,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC2C,aAAa,CAAC3C,KAAK,CAAC;IAC3B,CAAC,MAAM;MACL,IAAI,CAAC0C,WAAW,CAAC1C,KAAK,CAAC;IACzB;EACF;EAEA;EACA6C,iCAAiCA,CAAA;IAC/B,OAAO,IAAI,CAACzL,iBAAiB,CAAC2I,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACR,QAAQ,KAAKnK,eAAe,CAAC8C,iBAAiB,CAAC;EACrG;EAEA;EACA2K,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAAC1L,iBAAiB,CAAC2I,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACR,QAAQ,KAAKnK,eAAe,CAAC+C,SAAS,CAAC;EAC7F;EAEA;EACA2K,2BAA2BA,CAACC,IAAY;IACtC,IAAI,CAACvL,2BAA2B,GAAGuL,IAAI;IACvC,IAAI,CAACR,0BAA0B,EAAE;EACnC;EAEA;EACAS,oBAAoBA,CAACD,IAAY;IAC/B,IAAI,CAACpL,oBAAoB,GAAGoL,IAAI;IAChC,IAAI,CAACP,mBAAmB,EAAE;EAC5B;EAEA;EACAS,gBAAgBA,CAACF,IAAY;IAC3B,IAAI,CAACjL,gBAAgB,GAAGiL,IAAI;IAC5B,IAAI,CAACtE,UAAU,EAAE;EACnB;CACD;AAvwBYpJ,yBAAyB,GAAA6N,UAAA,EARrCtO,SAAS,CAAC;EACTuO,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC1O,YAAY,EAAEK,YAAY;CACrC,CAAC,C,EAEWG,yBAAyB,CAuwBrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}