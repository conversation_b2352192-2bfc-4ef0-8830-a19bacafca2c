{"ast": null, "code": "// previous version:\n// https://github.com/angular-ui/bootstrap/blob/07c31d0731f7cb068a1932b8e01d2312b796b4ec/src/position/position.js\nvar Positioning = /** @class */function () {\n  function Positioning() {}\n  Positioning.prototype.getAllStyles = function (element) {\n    return window.getComputedStyle(element);\n  };\n  Positioning.prototype.getStyle = function (element, prop) {\n    return this.getAllStyles(element)[prop];\n  };\n  Positioning.prototype.isStaticPositioned = function (element) {\n    return (this.getStyle(element, 'position') || 'static') === 'static';\n  };\n  Positioning.prototype.offsetParent = function (element) {\n    var offsetParentEl = element.offsetParent || document.documentElement;\n    while (offsetParentEl && offsetParentEl !== document.documentElement && this.isStaticPositioned(offsetParentEl)) {\n      offsetParentEl = offsetParentEl.offsetParent;\n    }\n    return offsetParentEl || document.documentElement;\n  };\n  Positioning.prototype.position = function (element, round) {\n    if (round === void 0) {\n      round = true;\n    }\n    var elPosition;\n    var parentOffset = {\n      width: 0,\n      height: 0,\n      top: 0,\n      bottom: 0,\n      left: 0,\n      right: 0\n    };\n    if (this.getStyle(element, 'position') === 'fixed') {\n      elPosition = element.getBoundingClientRect();\n      elPosition = {\n        top: elPosition.top,\n        bottom: elPosition.bottom,\n        left: elPosition.left,\n        right: elPosition.right,\n        height: elPosition.height,\n        width: elPosition.width\n      };\n    } else {\n      var offsetParentEl = this.offsetParent(element);\n      elPosition = this.offset(element, false);\n      if (offsetParentEl !== document.documentElement) {\n        parentOffset = this.offset(offsetParentEl, false);\n      }\n      parentOffset.top += offsetParentEl.clientTop;\n      parentOffset.left += offsetParentEl.clientLeft;\n    }\n    elPosition.top -= parentOffset.top;\n    elPosition.bottom -= parentOffset.top;\n    elPosition.left -= parentOffset.left;\n    elPosition.right -= parentOffset.left;\n    if (round) {\n      elPosition.top = Math.round(elPosition.top);\n      elPosition.bottom = Math.round(elPosition.bottom);\n      elPosition.left = Math.round(elPosition.left);\n      elPosition.right = Math.round(elPosition.right);\n    }\n    return elPosition;\n  };\n  Positioning.prototype.offset = function (element, round) {\n    if (round === void 0) {\n      round = true;\n    }\n    var elBcr = element.getBoundingClientRect();\n    var viewportOffset = {\n      top: window.pageYOffset - document.documentElement.clientTop,\n      left: window.pageXOffset - document.documentElement.clientLeft\n    };\n    var elOffset = {\n      height: elBcr.height || element.offsetHeight,\n      width: elBcr.width || element.offsetWidth,\n      top: elBcr.top + viewportOffset.top,\n      bottom: elBcr.bottom + viewportOffset.top,\n      left: elBcr.left + viewportOffset.left,\n      right: elBcr.right + viewportOffset.left\n    };\n    if (round) {\n      elOffset.height = Math.round(elOffset.height);\n      elOffset.width = Math.round(elOffset.width);\n      elOffset.top = Math.round(elOffset.top);\n      elOffset.bottom = Math.round(elOffset.bottom);\n      elOffset.left = Math.round(elOffset.left);\n      elOffset.right = Math.round(elOffset.right);\n    }\n    return elOffset;\n  };\n  /*\n    Return false if the element to position is outside the viewport\n  */\n  Positioning.prototype.positionElements = function (hostElement, targetElement, placement, appendToBody) {\n    var _a = placement.split('-'),\n      _b = _a[0],\n      placementPrimary = _b === void 0 ? 'top' : _b,\n      _c = _a[1],\n      placementSecondary = _c === void 0 ? 'center' : _c;\n    var hostElPosition = appendToBody ? this.offset(hostElement, false) : this.position(hostElement, false);\n    var targetElStyles = this.getAllStyles(targetElement);\n    var marginTop = parseFloat(targetElStyles.marginTop);\n    var marginBottom = parseFloat(targetElStyles.marginBottom);\n    var marginLeft = parseFloat(targetElStyles.marginLeft);\n    var marginRight = parseFloat(targetElStyles.marginRight);\n    var topPosition = 0;\n    var leftPosition = 0;\n    switch (placementPrimary) {\n      case 'top':\n        topPosition = hostElPosition.top - (targetElement.offsetHeight + marginTop + marginBottom);\n        break;\n      case 'bottom':\n        topPosition = hostElPosition.top + hostElPosition.height;\n        break;\n      case 'left':\n        leftPosition = hostElPosition.left - (targetElement.offsetWidth + marginLeft + marginRight);\n        break;\n      case 'right':\n        leftPosition = hostElPosition.left + hostElPosition.width;\n        break;\n    }\n    switch (placementSecondary) {\n      case 'top':\n        topPosition = hostElPosition.top;\n        break;\n      case 'bottom':\n        topPosition = hostElPosition.top + hostElPosition.height - targetElement.offsetHeight;\n        break;\n      case 'left':\n        leftPosition = hostElPosition.left;\n        break;\n      case 'right':\n        leftPosition = hostElPosition.left + hostElPosition.width - targetElement.offsetWidth;\n        break;\n      case 'center':\n        if (placementPrimary === 'top' || placementPrimary === 'bottom') {\n          leftPosition = hostElPosition.left + hostElPosition.width / 2 - targetElement.offsetWidth / 2;\n        } else {\n          topPosition = hostElPosition.top + hostElPosition.height / 2 - targetElement.offsetHeight / 2;\n        }\n        break;\n    }\n    /// The translate3d/gpu acceleration render a blurry text on chrome, the next line is commented until a browser fix\n    // targetElement.style.transform = `translate3d(${Math.round(leftPosition)}px, ${Math.floor(topPosition)}px, 0px)`;\n    targetElement.style.transform = \"translate(\" + Math.round(leftPosition) + \"px, \" + Math.round(topPosition) + \"px)\";\n    // Check if the targetElement is inside the viewport\n    var targetElBCR = targetElement.getBoundingClientRect();\n    var html = document.documentElement;\n    var windowHeight = window.innerHeight || html.clientHeight;\n    var windowWidth = window.innerWidth || html.clientWidth;\n    return targetElBCR.left >= 0 && targetElBCR.top >= 0 && targetElBCR.right <= windowWidth && targetElBCR.bottom <= windowHeight;\n  };\n  return Positioning;\n}();\nexport { Positioning };\nvar placementSeparator = /\\s+/;\nvar positionService = new Positioning();\n/*\n * Accept the placement array and applies the appropriate placement dependent on the viewport.\n * Returns the applied placement.\n * In case of auto placement, placements are selected in order\n *   'top', 'bottom', 'left', 'right',\n *   'top-left', 'top-right',\n *   'bottom-left', 'bottom-right',\n *   'left-top', 'left-bottom',\n *   'right-top', 'right-bottom'.\n * */\nexport function positionElements(hostElement, targetElement, placement, appendToBody, baseClass) {\n  var placementVals = Array.isArray(placement) ? placement : placement.split(placementSeparator);\n  var allowedPlacements = ['top', 'bottom', 'left', 'right', 'top-left', 'top-right', 'bottom-left', 'bottom-right', 'left-top', 'left-bottom', 'right-top', 'right-bottom'];\n  var classList = targetElement.classList;\n  var addClassesToTarget = function (targetPlacement) {\n    var _a = targetPlacement.split('-'),\n      primary = _a[0],\n      secondary = _a[1];\n    var classes = [];\n    if (baseClass) {\n      classes.push(baseClass + \"-\" + primary);\n      if (secondary) {\n        classes.push(baseClass + \"-\" + primary + \"-\" + secondary);\n      }\n      classes.forEach(function (classname) {\n        classList.add(classname);\n      });\n    }\n    return classes;\n  };\n  // Remove old placement classes to avoid issues\n  if (baseClass) {\n    allowedPlacements.forEach(function (placementToRemove) {\n      classList.remove(baseClass + \"-\" + placementToRemove);\n    });\n  }\n  // replace auto placement with other placements\n  var hasAuto = placementVals.findIndex(function (val) {\n    return val === 'auto';\n  });\n  if (hasAuto >= 0) {\n    allowedPlacements.forEach(function (obj) {\n      if (placementVals.find(function (val) {\n        return val.search('^' + obj) !== -1;\n      }) == null) {\n        placementVals.splice(hasAuto++, 1, obj);\n      }\n    });\n  }\n  // coordinates where to position\n  // Required for transform:\n  var style = targetElement.style;\n  style.position = 'absolute';\n  style.top = '0';\n  style.left = '0';\n  style['will-change'] = 'transform';\n  var testPlacement;\n  var isInViewport = false;\n  for (var _i = 0, placementVals_1 = placementVals; _i < placementVals_1.length; _i++) {\n    testPlacement = placementVals_1[_i];\n    var addedClasses = addClassesToTarget(testPlacement);\n    if (positionService.positionElements(hostElement, targetElement, testPlacement, appendToBody)) {\n      isInViewport = true;\n      break;\n    }\n    // Remove the baseClasses for further calculation\n    if (baseClass) {\n      addedClasses.forEach(function (classname) {\n        classList.remove(classname);\n      });\n    }\n  }\n  if (!isInViewport) {\n    // If nothing match, the first placement is the default one\n    testPlacement = placementVals[0];\n    addClassesToTarget(testPlacement);\n    positionService.positionElements(hostElement, targetElement, testPlacement, appendToBody);\n  }\n  return testPlacement;\n}\n//# sourceMappingURL=positioning.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}