{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbCheckboxModule, NbSelectModule, NbOptionModule, NbInputModule } from '@nebular/theme';\nimport { GetRequirement } from 'src/services/api/models';\nimport { StepNavigatorComponent } from '../step-navigator/step-navigator.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/requirement.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction RequestItemImportComponent_div_8_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"span\", 37);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 38);\n    i0.ɵɵtext(5, \"\\u8F09\\u5165\\u9700\\u6C42\\u9805\\u76EE\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RequestItemImportComponent_div_8_div_28_div_1_div_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const requirement_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u5099\\u8A3B: \", requirement_r4.CRemark, \"\");\n  }\n}\nfunction RequestItemImportComponent_div_8_div_28_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"nb-checkbox\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_8_div_28_div_1_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const requirement_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      i0.ɵɵtwoWayBindingSet(requirement_r4.selected, $event) || (requirement_r4.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function RequestItemImportComponent_div_8_div_28_div_1_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onRequirementItemChange());\n    });\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45)(4, \"span\", 46);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 47);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 48);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 49);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, RequestItemImportComponent_div_8_div_28_div_1_div_1_span_13_Template, 2, 1, \"span\", 50);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const requirement_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", requirement_r4.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(requirement_r4.CRequirement || \"\\u672A\\u547D\\u540D\\u9700\\u6C42\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u4F4D\\u7F6E: \", requirement_r4.CLocation || \"\\u672A\\u6307\\u5B9A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u4F4D: \", requirement_r4.CUnit || \"\\u672A\\u6307\\u5B9A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u50F9: \", i0.ɵɵpipeBind4(12, 6, requirement_r4.CUnitPrice, \"TWD\", \"symbol-narrow\", \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", requirement_r4.CRemark);\n  }\n}\nfunction RequestItemImportComponent_div_8_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, RequestItemImportComponent_div_8_div_28_div_1_div_1_Template, 14, 11, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.requirements);\n  }\n}\nfunction RequestItemImportComponent_div_8_div_28_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2, \" \\u6C92\\u6709\\u53EF\\u532F\\u5165\\u7684\\u9700\\u6C42\\u9805\\u76EE\\uFF0C\\u8ACB\\u8ABF\\u6574\\u641C\\u5C0B\\u689D\\u4EF6\\u6216\\u806F\\u7E6B\\u7BA1\\u7406\\u54E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequestItemImportComponent_div_8_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, RequestItemImportComponent_div_8_div_28_div_1_Template, 2, 1, \"div\", 40)(2, RequestItemImportComponent_div_8_div_28_ng_template_2_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noRequirements_r5 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.requirements.length > 0)(\"ngIfElse\", noRequirements_r5);\n  }\n}\nfunction RequestItemImportComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17);\n    i0.ɵɵelement(3, \"i\", 18);\n    i0.ɵɵtext(4, \"\\u641C\\u5C0B\\u689D\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 19)(6, \"div\", 20)(7, \"div\", 21)(8, \"label\", 22);\n    i0.ɵɵtext(9, \"\\u5340\\u57DF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 23);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_8_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CLocation, $event) || (ctx_r1.searchRequest.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 21)(12, \"label\", 24);\n    i0.ɵɵtext(13, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_8_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CRequirement, $event) || (ctx_r1.searchRequest.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 20)(16, \"div\", 26)(17, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_div_8_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resetSearch());\n    });\n    i0.ɵɵelement(18, \"i\", 28);\n    i0.ɵɵtext(19, \" \\u91CD\\u7F6E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_div_8_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelement(21, \"i\", 30);\n    i0.ɵɵtext(22, \" \\u641C\\u5C0B \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(23, \"div\", 31)(24, \"div\", 17);\n    i0.ɵɵelement(25, \"i\", 32);\n    i0.ɵɵtext(26, \"\\u9078\\u64C7\\u9700\\u6C42\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, RequestItemImportComponent_div_8_div_27_Template, 6, 0, \"div\", 33)(28, RequestItemImportComponent_div_8_div_28_Template, 4, 2, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CRequirement);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction RequestItemImportComponent_div_9_div_12_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5099\\u8A3B: \", item_r6.CRemark, \" \");\n  }\n}\nfunction RequestItemImportComponent_div_9_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"h6\", 62);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 63)(5, \"div\", 64);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 65);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 66);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, RequestItemImportComponent_div_9_div_12_div_12_Template, 2, 1, \"div\", 67);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i_r7 + 1, \". \", item_r6.CRequirement || \"\\u672A\\u547D\\u540D\\u9700\\u6C42\", \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u4F4D\\u7F6E: \", item_r6.CLocation || \"\\u672A\\u6307\\u5B9A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u4F4D: \", item_r6.CUnit || \"\\u672A\\u6307\\u5B9A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u50F9: \", i0.ɵɵpipeBind4(11, 6, item_r6.CUnitPrice, \"TWD\", \"symbol-narrow\", \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r6.CRemark);\n  }\n}\nfunction RequestItemImportComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 54)(2, \"div\", 17);\n    i0.ɵɵelement(3, \"i\", 55);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u532F\\u5165\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56)(6, \"div\", 57);\n    i0.ɵɵtext(7, \" \\u5C07\\u532F\\u5165 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" \\u500B\\u9700\\u6C42\\u9805\\u76EE\\u5230\\u76EE\\u524D\\u7684\\u5EFA\\u6848\\u4E2D \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 58);\n    i0.ɵɵtemplate(12, RequestItemImportComponent_div_9_div_12_Template, 13, 11, \"div\", 59);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.getSelectedCount());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectedItems());\n  }\n}\nfunction RequestItemImportComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵelement(1, \"i\", 69);\n    i0.ɵɵtext(2, \" \\u4E0A\\u4E00\\u6B65 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequestItemImportComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(1, \" \\u4E0B\\u4E00\\u6B65 \");\n    i0.ɵɵelement(2, \"i\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canProceed());\n  }\n}\nfunction RequestItemImportComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.importRequirements());\n    });\n    i0.ɵɵelement(1, \"i\", 73);\n    i0.ɵɵtext(2, \" \\u78BA\\u8A8D\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RequestItemImportComponent {\n  constructor(requirementService, dialogRef) {\n    this.requirementService = requirementService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = 0;\n    this.houseType = [];\n    this.itemsImported = new EventEmitter();\n    this.currentStep = 1;\n    this.requirements = [];\n    this.loading = false;\n    // 步驟配置\n    this.steps = [{\n      id: 1,\n      label: '選擇項目',\n      icon: 'layers-outline'\n    }, {\n      id: 2,\n      label: '確認匯入',\n      icon: 'checkmark-circle-outline'\n    }];\n    // 搜尋相關屬性\n    this.searchRequest = {};\n  }\n  ngOnInit() {\n    this.initializeSearchForm();\n    this.loadRequirementsFromAPI();\n  }\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目\n    this.searchRequest.CIsShow = null; // 預設只顯示需要顯示的項目\n    this.searchRequest.CIsSimple = null;\n    this.searchRequest.CRequirement = '';\n    this.searchRequest.CLocation = '';\n    // 使用外部傳入的參數\n    this.searchRequest.CBuildCaseID = this.buildCaseId;\n    this.searchRequest.CHouseType = this.houseType;\n  }\n  // 搜尋事件\n  onSearch() {\n    this.loadRequirementsFromAPI();\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    this.loadRequirementsFromAPI();\n  }\n  loadRequirementsFromAPI() {\n    if (!this.searchRequest.CBuildCaseID) {\n      return;\n    }\n    this.loading = true;\n    // 準備API請求參數\n    const getRequirementListArgs = {\n      CBuildCaseID: this.searchRequest.CBuildCaseID,\n      CHouseType: this.searchRequest.CHouseType,\n      CLocation: this.searchRequest.CLocation || null,\n      CRequirement: this.searchRequest.CRequirement || null,\n      CStatus: this.searchRequest.CStatus,\n      CIsShow: this.searchRequest.CIsShow,\n      CIsSimple: this.searchRequest.CIsSimple,\n      PageIndex: 1,\n      PageSize: 100\n    };\n    this.requirementService.apiRequirementGetRequestListForTemplatePost$Json({\n      body: getRequirementListArgs\n    }).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.StatusCode === 0 && response.Entries) {\n          this.requirements = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          this.requirements = [];\n        }\n      },\n      error: () => {\n        this.loading = false;\n        this.requirements = [];\n      }\n    });\n  }\n  onRequirementItemChange() {\n    // 當需求項目選擇變更時的處理\n  }\n  getSelectedItems() {\n    return this.requirements.filter(item => item.selected);\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要匯入的需求項目',\n      2: '確認匯入詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  importRequirements() {\n    const config = {\n      buildCaseId: this.searchRequest.CBuildCaseID || 0,\n      buildCaseName: '',\n      selectedItems: this.getSelectedItems(),\n      totalItems: this.getSelectedItems().length,\n      searchCriteria: {\n        CHouseType: this.searchRequest.CHouseType || undefined,\n        CLocation: this.searchRequest.CLocation || undefined,\n        CRequirement: this.searchRequest.CRequirement || undefined\n      }\n    };\n    this.itemsImported.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    this.requirements.forEach(requirement => {\n      requirement.selected = false;\n    });\n  }\n  selectAll() {\n    const allSelected = this.requirements.every(item => item.selected);\n    this.requirements.forEach(item => {\n      item.selected = !allSelected;\n    });\n  }\n  getSelectedCount() {\n    return this.getSelectedItems().length;\n  }\n  getTotalCount() {\n    return this.requirements.length;\n  }\n  static {\n    this.ɵfac = function RequestItemImportComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequestItemImportComponent)(i0.ɵɵdirectiveInject(i1.RequirementService), i0.ɵɵdirectiveInject(i2.NbDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequestItemImportComponent,\n      selectors: [[\"app-request-item-import\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        houseType: \"houseType\"\n      },\n      outputs: {\n        itemsImported: \"itemsImported\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 17,\n      vars: 9,\n      consts: [[\"noRequirements\", \"\"], [1, \"request-item-import-dialog\"], [1, \"request-item-import-header\"], [1, \"request-item-import-title\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"request-item-import-body\"], [3, \"steps\", \"currentStep\", \"allowClickNavigation\", \"showStepNumber\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"request-item-import-footer\"], [1, \"step-buttons\"], [\"nbButton\", \"\", \"ghost\", \"\", \"class\", \"mr-2\", 3, \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"mr-2\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"search-section\", \"mb-4\"], [1, \"section-title\"], [1, \"fas\", \"fa-search\", \"mr-2\"], [1, \"search-form\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-6\"], [\"for\", \"location\", 1, \"label\", \"mb-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"location\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5340\\u57DF\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"requirement\", 1, \"label\", \"mb-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-redo\", \"mr-1\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"requirement-selection\"], [1, \"fas\", \"fa-layer-group\", \"mr-2\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"requirement-list\", 4, \"ngIf\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"sr-only\"], [1, \"mt-2\"], [1, \"requirement-list\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"requirement-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"requirement-item\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"requirement-info\"], [1, \"requirement-info-row\", 2, \"display\", \"flex\", \"flex-wrap\", \"wrap\", \"align-items\", \"center\", \"gap\", \"16px\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"item-status\"], [1, \"item-type\"], [\"class\", \"item-remark\", 4, \"ngIf\"], [1, \"item-remark\"], [1, \"no-requirements\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [1, \"confirmation-area\"], [1, \"fas\", \"fa-check-circle\", \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"selected-requirements-details\"], [\"class\", \"requirement-detail-section\", 4, \"ngFor\", \"ngForOf\"], [1, \"requirement-detail-section\"], [1, \"requirement-detail-header\"], [1, \"requirement-name\"], [1, \"requirement-meta\"], [1, \"requirement-location\"], [1, \"requirement-unit\"], [1, \"requirement-price\"], [\"class\", \"requirement-remark\", 4, \"ngIf\"], [1, \"requirement-remark\"], [1, \"fas\", \"fa-arrow-left\", \"mr-1\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-arrow-right\", \"ml-1\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\"], [1, \"fas\", \"fa-download\", \"mr-1\"]],\n      template: function RequestItemImportComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\", 2)(2, \"div\", 3);\n          i0.ɵɵtext(3, \"\\u9700\\u6C42\\u9805\\u76EE\\u532F\\u5165\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function RequestItemImportComponent_Template_button_click_4_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 6);\n          i0.ɵɵelement(7, \"app-step-navigator\", 7);\n          i0.ɵɵtemplate(8, RequestItemImportComponent_div_8_Template, 29, 4, \"div\", 8)(9, RequestItemImportComponent_div_9_Template, 13, 2, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"nb-card-footer\", 9)(11, \"div\", 10);\n          i0.ɵɵtemplate(12, RequestItemImportComponent_button_12_Template, 3, 0, \"button\", 11);\n          i0.ɵɵelementStart(13, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function RequestItemImportComponent_Template_button_click_13_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(14, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, RequestItemImportComponent_button_15_Template, 3, 1, \"button\", 13)(16, RequestItemImportComponent_button_16_Template, 3, 0, \"button\", 14);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"steps\", ctx.steps)(\"currentStep\", ctx.currentStep)(\"allowClickNavigation\", false)(\"showStepNumber\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.CurrencyPipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbCheckboxModule, i2.NbCheckboxComponent, NbSelectModule, NbOptionModule, NbInputModule, i2.NbInputDirective, StepNavigatorComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n.badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.25em 0.4em;\\n  font-size: 75%;\\n  font-weight: 700;\\n  line-height: 1;\\n  text-align: center;\\n  white-space: nowrap;\\n  vertical-align: baseline;\\n  border-radius: 0.25rem;\\n}\\n.badge.badge-primary[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  background-color: #B8A676;\\n}\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  background-color: #28A745;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  background-color: #17A2B8;\\n}\\n.badge.badge-secondary[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  background-color: #5A5A5A;\\n}\\n\\n\\n\\n.request-item-import-dialog[_ngcontent-%COMP%] {\\n  min-width: 600px;\\n  max-width: 800px;\\n  min-height: 500px;\\n  max-height: 80vh;\\n  border-radius: 0.5rem;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.25rem 1.5rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  background-color: #FFFFFF;\\n  border-radius: 0.5rem 0.5rem 0 0;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .request-item-import-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  padding: 0.25rem;\\n  min-width: auto;\\n  border: none;\\n  background: transparent;\\n  color: #ADB5BD;\\n  transition: 0.3s ease;\\n  border-radius: 0.25rem;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #2C3E50;\\n  background-color: rgba(184, 166, 118, 0.05);\\n  transform: scale(1.05);\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  overflow-y: auto;\\n  max-height: 60vh;\\n  background-color: #FFFFFF;\\n}\\n\\n\\n\\n.step-content[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  \\n\\n  \\n\\n  \\n\\n}\\n.step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 1rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #B8A676;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n  background: rgba(184, 166, 118, 0.03);\\n  padding: 1.5rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid rgba(184, 166, 118, 0.3);\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2C3E50;\\n  display: block;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], \\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-color: rgba(184, 166, 118, 0.3);\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]:focus, \\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #B8A676;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  transition: 0.3s ease;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: #FFFFFF;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  transform: translateY(-1px);\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #F8F9FA;\\n  border-color: #CDCDCD;\\n  color: #5A5A5A;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #F5F5F5;\\n  border-color: #ADB5BD;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  transition: 0.3s ease;\\n  background-color: #FFFFFF;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover {\\n  border-color: rgba(184, 166, 118, 0.3);\\n  background: rgba(184, 166, 118, 0.05);\\n  transform: translateY(-1px);\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:has(nb-checkbox input:checked) {\\n  border-color: #B8A676;\\n  background: rgba(184, 166, 118, 0.15);\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  flex: 1;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n  margin-bottom: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n  margin-bottom: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #A69660;\\n  font-weight: 500;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #17A2B8;\\n  font-style: italic;\\n  margin-top: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #5A5A5A;\\n  background: rgba(184, 166, 118, 0.03);\\n  border-radius: 0.375rem;\\n  border: 1px solid rgba(184, 166, 118, 0.3);\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #B8A676;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border: 1px solid #B8A676;\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  font-weight: 500;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  font-weight: 700;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%] {\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  margin-bottom: 1rem;\\n  overflow: hidden;\\n  background-color: #FFFFFF;\\n  transition: 0.3s ease;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F8F6F0 0%, #F0EDE5 100%);\\n  padding: 1rem;\\n  border-bottom: 1px solid #E9ECEF;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-name[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%]   .requirement-location[_ngcontent-%COMP%], \\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%]   .requirement-unit[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%]   .requirement-price[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #A69660;\\n  font-weight: 600;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-remark[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #17A2B8;\\n  font-style: italic;\\n  margin-top: 0.5rem;\\n}\\n\\n\\n\\n.step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 2rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  padding-bottom: 1rem;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  margin: 0 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  transition: 0.3s ease;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background-color: #28A745;\\n  color: #FFFFFF;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #F8F9FA;\\n  color: #ADB5BD;\\n  border: 1px solid #E9ECEF;\\n}\\n\\n\\n\\n.request-item-import-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid #E9ECEF;\\n  background: rgba(184, 166, 118, 0.03);\\n  border-radius: 0 0 0.5rem 0.5rem;\\n}\\n.request-item-import-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .request-item-import-dialog[_ngcontent-%COMP%] {\\n    min-width: 95vw;\\n    max-width: 95vw;\\n    margin: 0.5rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.4rem 0.8rem;\\n    margin: 0 0.25rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n    margin-bottom: 0.75rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%], \\n   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n    align-items: stretch;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n\\n\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%] {\\n  background-color: #1A1A1A;\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .request-item-import-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .request-item-import-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #FFFFFF;\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%] {\\n  background-color: #1A1A1A;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover {\\n  background-color: #3a3a3a;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:has(nb-checkbox input:checked), .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:has(nb-checkbox input:checked) {\\n  border-color: #B8A676;\\n  background-color: rgba(184, 166, 118, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #A69660 0%, #9B8A5A 100%);\\n  border-color: #B8A676;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(184, 166, 118, 0.2) 0%, rgba(184, 166, 118, 0.1) 100%);\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(184, 166, 118, 0.05) 0%, rgba(184, 166, 118, 0.02) 100%);\\n  border-top-color: #404040;\\n}\\n\\n\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n\\n\\nnb-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  width: 100%;\\n}\\nnb-checkbox[_ngcontent-%COMP%]   .customised-control-input[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  margin-top: 0.125rem;\\n}\\n\\n\\n\\n.mr-1[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbCheckboxModule", "NbSelectModule", "NbOptionModule", "NbInputModule", "GetRequirement", "StepNavigatorComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "requirement_r4", "CRemark", "ɵɵtwoWayListener", "RequestItemImportComponent_div_8_div_28_div_1_div_1_Template_nb_checkbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "ctx_r1", "ɵɵnextContext", "onRequirementItemChange", "ɵɵtemplate", "RequestItemImportComponent_div_8_div_28_div_1_div_1_span_13_Template", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CRequirement", "CLocation", "CUnit", "ɵɵpipeBind4", "CUnitPrice", "ɵɵproperty", "RequestItemImportComponent_div_8_div_28_div_1_div_1_Template", "requirements", "ɵɵelement", "RequestItemImportComponent_div_8_div_28_div_1_Template", "RequestItemImportComponent_div_8_div_28_ng_template_2_Template", "ɵɵtemplateRefExtractor", "length", "noRequirements_r5", "RequestItemImportComponent_div_8_Template_input_ngModelChange_10_listener", "_r1", "searchRequest", "RequestItemImportComponent_div_8_Template_input_ngModelChange_14_listener", "RequestItemImportComponent_div_8_Template_button_click_17_listener", "resetSearch", "RequestItemImportComponent_div_8_Template_button_click_20_listener", "onSearch", "RequestItemImportComponent_div_8_div_27_Template", "RequestItemImportComponent_div_8_div_28_Template", "loading", "item_r6", "RequestItemImportComponent_div_9_div_12_div_12_Template", "ɵɵtextInterpolate2", "i_r7", "RequestItemImportComponent_div_9_div_12_Template", "getSelectedCount", "getSelectedItems", "RequestItemImportComponent_button_12_Template_button_click_0_listener", "_r8", "previousStep", "RequestItemImportComponent_button_15_Template_button_click_0_listener", "_r9", "nextStep", "canProceed", "RequestItemImportComponent_button_16_Template_button_click_0_listener", "_r10", "importRequirements", "RequestItemImportComponent", "constructor", "requirementService", "dialogRef", "buildCaseId", "houseType", "itemsImported", "currentStep", "steps", "id", "label", "icon", "ngOnInit", "initializeSearchForm", "loadRequirementsFromAPI", "CStatus", "CIsShow", "CIsSimple", "CBuildCaseID", "CHouseType", "getRequirementListArgs", "PageIndex", "PageSize", "apiRequirementGetRequestListForTemplatePost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "error", "filter", "getProgressText", "progressTexts", "config", "buildCaseName", "selectedItems", "totalItems", "searchCriteria", "undefined", "emit", "close", "resetSelections", "for<PERSON>ach", "requirement", "selectAll", "allSelected", "every", "getTotalCount", "ɵɵdirectiveInject", "i1", "RequirementService", "i2", "NbDialogRef", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequestItemImportComponent_Template", "rf", "ctx", "RequestItemImportComponent_Template_button_click_4_listener", "RequestItemImportComponent_div_8_Template", "RequestItemImportComponent_div_9_Template", "RequestItemImportComponent_button_12_Template", "RequestItemImportComponent_Template_button_click_13_listener", "RequestItemImportComponent_button_15_Template", "RequestItemImportComponent_button_16_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "C<PERSON><PERSON>cyPipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbCheckboxComponent", "NbInputDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbCheckboxModule,\r\n  NbSelectModule,\r\n  NbOptionModule,\r\n  NbInputModule,\r\n  NbDialogRef\r\n} from '@nebular/theme';\r\nimport { RequirementService } from 'src/services/api/services/requirement.service';\r\nimport { GetListRequirementRequest, GetRequirement, GetRequirementListResponseBase } from 'src/services/api/models';\r\nimport { StepNavigatorComponent } from '../step-navigator/step-navigator.component';\r\nimport { StepConfig } from '../../interfaces/step-config.interface';\r\n\r\nexport interface ExtendedRequirementItem extends GetRequirement {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface RequestItemImportConfig {\r\n  buildCaseId: number;\r\n  buildCaseName?: string;\r\n  selectedItems: ExtendedRequirementItem[];\r\n  totalItems: number;\r\n  searchCriteria?: {\r\n    CHouseType?: number[];\r\n    CLocation?: string;\r\n    CRequirement?: string;\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-request-item-import',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbCheckboxModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NbInputModule,\r\n    StepNavigatorComponent\r\n  ],\r\n  templateUrl: './request-item-import.component.html',\r\n  styleUrls: ['./request-item-import.component.scss']\r\n})\r\nexport class RequestItemImportComponent implements OnInit {\r\n  @Input() buildCaseId: number = 0;\r\n  @Input() houseType: number[] = [];\r\n  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();\r\n\r\n  currentStep: number = 1;\r\n  requirements: ExtendedRequirementItem[] = [];\r\n  loading: boolean = false;\r\n\r\n  // 步驟配置\r\n  steps: StepConfig[] = [\r\n    { id: 1, label: '選擇項目', icon: 'layers-outline' },\r\n    { id: 2, label: '確認匯入', icon: 'checkmark-circle-outline' }\r\n  ];\r\n\r\n  // 搜尋相關屬性\r\n  searchRequest: GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null } = {};\r\n\r\n  constructor(\r\n    private requirementService: RequirementService,\r\n    private dialogRef: NbDialogRef<RequestItemImportComponent>\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.initializeSearchForm();\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目\r\n    this.searchRequest.CIsShow = null; // 預設只顯示需要顯示的項目\r\n    this.searchRequest.CIsSimple = null;\r\n    this.searchRequest.CRequirement = '';\r\n    this.searchRequest.CLocation = '';\r\n    // 使用外部傳入的參數\r\n    this.searchRequest.CBuildCaseID = this.buildCaseId;\r\n    this.searchRequest.CHouseType = this.houseType;\r\n  }\r\n\r\n\r\n\r\n  // 搜尋事件\r\n  onSearch() {\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  loadRequirementsFromAPI() {\r\n    if (!this.searchRequest.CBuildCaseID) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n\r\n    // 準備API請求參數\r\n    const getRequirementListArgs: GetListRequirementRequest = {\r\n      CBuildCaseID: this.searchRequest.CBuildCaseID,\r\n      CHouseType: this.searchRequest.CHouseType,\r\n      CLocation: this.searchRequest.CLocation || null,\r\n      CRequirement: this.searchRequest.CRequirement || null,\r\n      CStatus: this.searchRequest.CStatus,\r\n      CIsShow: this.searchRequest.CIsShow,\r\n      CIsSimple: this.searchRequest.CIsSimple,\r\n      PageIndex: 1,\r\n      PageSize: 100\r\n    };\r\n\r\n    this.requirementService.apiRequirementGetRequestListForTemplatePost$Json({\r\n      body: getRequirementListArgs\r\n    }).subscribe({\r\n      next: (response: GetRequirementListResponseBase) => {\r\n        this.loading = false;\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.requirements = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          this.requirements = [];\r\n        }\r\n      },\r\n      error: () => {\r\n        this.loading = false;\r\n        this.requirements = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onRequirementItemChange() {\r\n    // 當需求項目選擇變更時的處理\r\n  }\r\n\r\n  getSelectedItems(): ExtendedRequirementItem[] {\r\n    return this.requirements.filter(item => item.selected);\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要匯入的需求項目',\r\n      2: '確認匯入詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  importRequirements() {\r\n    const config: RequestItemImportConfig = {\r\n      buildCaseId: this.searchRequest.CBuildCaseID || 0,\r\n      buildCaseName: '',\r\n      selectedItems: this.getSelectedItems(),\r\n      totalItems: this.getSelectedItems().length,\r\n      searchCriteria: {\r\n        CHouseType: this.searchRequest.CHouseType || undefined,\r\n        CLocation: this.searchRequest.CLocation || undefined,\r\n        CRequirement: this.searchRequest.CRequirement || undefined\r\n      }\r\n    };\r\n\r\n    this.itemsImported.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    this.requirements.forEach(requirement => {\r\n      requirement.selected = false;\r\n    });\r\n  }\r\n\r\n  selectAll() {\r\n    const allSelected = this.requirements.every(item => item.selected);\r\n    this.requirements.forEach(item => {\r\n      item.selected = !allSelected;\r\n    });\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.getSelectedItems().length;\r\n  }\r\n\r\n  getTotalCount(): number {\r\n    return this.requirements.length;\r\n  }\r\n}\r\n", "<nb-card class=\"request-item-import-dialog\">\r\n  <nb-card-header class=\"request-item-import-header\">\r\n    <div class=\"request-item-import-title\">需求項目匯入</div>\r\n    <button class=\"close-btn\" nbButton ghost (click)=\"close()\">\r\n      <i class=\"fas fa-times\"></i>\r\n    </button>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body class=\"request-item-import-body\">\r\n    <!-- 步驟導航 -->\r\n    <app-step-navigator [steps]=\"steps\" [currentStep]=\"currentStep\" [allowClickNavigation]=\"false\"\r\n      [showStepNumber]=\"true\">\r\n    </app-step-navigator>\r\n    <!-- 步驟1: 選擇需求項目 -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <!-- 搜尋區塊 -->\r\n      <div class=\"search-section mb-4\">\r\n        <div class=\"section-title\">\r\n          <i class=\"fas fa-search mr-2\"></i>搜尋條件\r\n        </div>\r\n        <div class=\"search-form\">\r\n          <div class=\"row\">\r\n            <div class=\"form-group col-12 col-md-6\">\r\n              <label for=\"location\" class=\"label mb-2\">區域</label>\r\n              <input type=\"text\" nbInput id=\"location\" placeholder=\"請輸入區域\" [(ngModel)]=\"searchRequest.CLocation\">\r\n            </div>\r\n            <div class=\"form-group col-12 col-md-6\">\r\n              <label for=\"requirement\" class=\"label mb-2\">工程項目</label>\r\n              <input type=\"text\" nbInput id=\"requirement\" placeholder=\"請輸入工程項目\"\r\n                [(ngModel)]=\"searchRequest.CRequirement\">\r\n            </div>\r\n          </div>\r\n          <div class=\"row\">\r\n            <div class=\"col-12 text-right\">\r\n              <button class=\"btn btn-secondary me-2\" (click)=\"resetSearch()\">\r\n                <i class=\"fas fa-redo mr-1\"></i>\r\n                重置\r\n              </button>\r\n              <button class=\"btn btn-primary\" (click)=\"onSearch()\">\r\n                <i class=\"fas fa-search mr-1\"></i>\r\n                搜尋\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 需求項目選擇區域 -->\r\n      <div class=\"requirement-selection\">\r\n        <div class=\"section-title\">\r\n          <i class=\"fas fa-layer-group mr-2\"></i>選擇需求項目\r\n        </div>\r\n\r\n        <!-- 載入中 -->\r\n        <div *ngIf=\"loading\" class=\"text-center py-4\">\r\n          <div class=\"spinner-border text-primary\" role=\"status\">\r\n            <span class=\"sr-only\">載入中...</span>\r\n          </div>\r\n          <div class=\"mt-2\">載入需求項目中...</div>\r\n        </div>\r\n\r\n        <!-- 需求項目列表 -->\r\n        <div *ngIf=\"!loading\" class=\"requirement-list\">\r\n          <div *ngIf=\"requirements.length > 0; else noRequirements\">\r\n            <div *ngFor=\"let requirement of requirements\" class=\"requirement-item\">\r\n              <nb-checkbox [(ngModel)]=\"requirement.selected\" (ngModelChange)=\"onRequirementItemChange()\">\r\n                <div class=\"requirement-info\">\r\n                  <div class=\"requirement-info-row\"\r\n                    style=\"display: flex; flex-wrap: wrap; align-items: center; gap: 16px;\">\r\n                    <span class=\"item-name\">{{ requirement.CRequirement || '未命名需求' }}</span>\r\n                    <span class=\"item-code\">位置: {{ requirement.CLocation || '未指定' }}</span>\r\n                    <span class=\"item-status\">單位: {{ requirement.CUnit || '未指定' }}</span>\r\n                    <span class=\"item-type\">單價: {{ requirement.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0'\r\n                      }}</span>\r\n                    <span *ngIf=\"requirement.CRemark\" class=\"item-remark\">備註: {{ requirement.CRemark }}</span>\r\n                  </div>\r\n                </div>\r\n              </nb-checkbox>\r\n            </div>\r\n          </div>\r\n          <ng-template #noRequirements>\r\n            <div class=\"no-requirements\">\r\n              <i class=\"fas fa-info-circle mr-2\"></i>\r\n              沒有可匯入的需求項目，請調整搜尋條件或聯繫管理員\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 步驟2: 確認匯入 -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <div class=\"confirmation-area\">\r\n        <div class=\"section-title\">\r\n          <i class=\"fas fa-check-circle mr-2\"></i>確認匯入詳情\r\n        </div>\r\n\r\n        <div class=\"selected-summary\">\r\n          <div class=\"summary-text\">\r\n            將匯入 <strong>{{ getSelectedCount() }}</strong> 個需求項目到目前的建案中\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"selected-requirements-details\">\r\n          <div *ngFor=\"let item of getSelectedItems(); let i = index\" class=\"requirement-detail-section\">\r\n            <div class=\"requirement-detail-header\">\r\n              <h6 class=\"requirement-name\">{{ i + 1 }}. {{ item.CRequirement || '未命名需求' }}</h6>\r\n              <div class=\"requirement-meta\">\r\n                <div class=\"requirement-location\">位置: {{ item.CLocation || '未指定' }}</div>\r\n                <div class=\"requirement-unit\">單位: {{ item.CUnit || '未指定' }}</div>\r\n                <div class=\"requirement-price\">單價: {{ item.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0' }}</div>\r\n              </div>\r\n              <div *ngIf=\"item.CRemark\" class=\"requirement-remark\">\r\n                備註: {{ item.CRemark }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-footer class=\"request-item-import-footer\">\r\n    <div class=\"step-buttons\">\r\n      <button *ngIf=\"currentStep > 1\" nbButton ghost (click)=\"previousStep()\" class=\"mr-2\">\r\n        <i class=\"fas fa-arrow-left mr-1\"></i>\r\n        上一步\r\n      </button>\r\n\r\n      <button nbButton ghost (click)=\"close()\" class=\"mr-2\">\r\n        取消\r\n      </button>\r\n\r\n      <button *ngIf=\"currentStep < 2\" nbButton status=\"primary\" [disabled]=\"!canProceed()\" (click)=\"nextStep()\">\r\n        下一步\r\n        <i class=\"fas fa-arrow-right ml-1\"></i>\r\n      </button>\r\n\r\n      <button *ngIf=\"currentStep === 2\" nbButton status=\"success\" (click)=\"importRequirements()\">\r\n        <i class=\"fas fa-download mr-1\"></i>\r\n        確認匯入\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,aAAa,QAER,gBAAgB;AAEvB,SAAoCC,cAAc,QAAwC,yBAAyB;AACnH,SAASC,sBAAsB,QAAQ,4CAA4C;;;;;;;;IC0CvEC,EAFJ,CAAAC,cAAA,cAA8C,cACW,eAC/B;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAC9BF,EAD8B,CAAAG,YAAA,EAAM,EAC9B;;;;;IAeMH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApCH,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAK,kBAAA,mBAAAC,cAAA,CAAAC,OAAA,KAA6B;;;;;;IATzFP,EADF,CAAAC,cAAA,cAAuE,sBACuB;IAA/ED,EAAA,CAAAQ,gBAAA,2BAAAC,kGAAAC,MAAA;MAAA,MAAAJ,cAAA,GAAAN,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAb,EAAA,CAAAc,kBAAA,CAAAR,cAAA,CAAAS,QAAA,EAAAL,MAAA,MAAAJ,cAAA,CAAAS,QAAA,GAAAL,MAAA;MAAA,OAAAV,EAAA,CAAAgB,WAAA,CAAAN,MAAA;IAAA,EAAkC;IAACV,EAAA,CAAAiB,UAAA,2BAAAR,kGAAA;MAAAT,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAAiBE,MAAA,CAAAE,uBAAA,EAAyB;IAAA,EAAC;IAIrFpB,EAHJ,CAAAC,cAAA,cAA8B,cAE8C,eAChD;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvEH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IACpB;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAqB,UAAA,KAAAC,oEAAA,mBAAsD;IAI9DtB,EAHM,CAAAG,YAAA,EAAM,EACF,EACM,EACV;;;;IAbSH,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAuB,gBAAA,YAAAjB,cAAA,CAAAS,QAAA,CAAkC;IAIjBf,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAwB,iBAAA,CAAAlB,cAAA,CAAAmB,YAAA,qCAAyC;IACzCzB,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAK,kBAAA,mBAAAC,cAAA,CAAAoB,SAAA,6BAAwC;IACtC1B,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,kBAAA,mBAAAC,cAAA,CAAAqB,KAAA,6BAAoC;IACtC3B,EAAA,CAAAI,SAAA,GACpB;IADoBJ,EAAA,CAAAK,kBAAA,mBAAAL,EAAA,CAAA4B,WAAA,QAAAtB,cAAA,CAAAuB,UAAA,uCACpB;IACG7B,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAA8B,UAAA,SAAAxB,cAAA,CAAAC,OAAA,CAAyB;;;;;IAX1CP,EAAA,CAAAC,cAAA,UAA0D;IACxDD,EAAA,CAAAqB,UAAA,IAAAU,4DAAA,oBAAuE;IAezE/B,EAAA,CAAAG,YAAA,EAAM;;;;IAfyBH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAA8B,UAAA,YAAAZ,MAAA,CAAAc,YAAA,CAAe;;;;;IAiB5ChC,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAiC,SAAA,YAAuC;IACvCjC,EAAA,CAAAE,MAAA,yJACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAtBVH,EAAA,CAAAC,cAAA,cAA+C;IAkB7CD,EAjBA,CAAAqB,UAAA,IAAAa,sDAAA,kBAA0D,IAAAC,8DAAA,gCAAAnC,EAAA,CAAAoC,sBAAA,CAiB7B;IAM/BpC,EAAA,CAAAG,YAAA,EAAM;;;;;IAvBEH,EAAA,CAAAI,SAAA,EAA+B;IAAAJ,EAA/B,CAAA8B,UAAA,SAAAZ,MAAA,CAAAc,YAAA,CAAAK,MAAA,KAA+B,aAAAC,iBAAA,CAAmB;;;;;;IA9C1DtC,EAHJ,CAAAC,cAAA,cAAoD,cAEjB,cACJ;IACzBD,EAAA,CAAAiC,SAAA,YAAkC;IAAAjC,EAAA,CAAAE,MAAA,gCACpC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIAH,EAHN,CAAAC,cAAA,cAAyB,cACN,cACyB,gBACG;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAC,cAAA,iBAAmG;IAAtCD,EAAA,CAAAQ,gBAAA,2BAAA+B,0EAAA7B,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA6B,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAc,kBAAA,CAAAI,MAAA,CAAAuB,aAAA,CAAAf,SAAA,EAAAhB,MAAA,MAAAQ,MAAA,CAAAuB,aAAA,CAAAf,SAAA,GAAAhB,MAAA;MAAA,OAAAV,EAAA,CAAAgB,WAAA,CAAAN,MAAA;IAAA,EAAqC;IACpGV,EADE,CAAAG,YAAA,EAAmG,EAC/F;IAEJH,EADF,CAAAC,cAAA,eAAwC,iBACM;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAAC,cAAA,iBAC2C;IAAzCD,EAAA,CAAAQ,gBAAA,2BAAAkC,0EAAAhC,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA6B,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAc,kBAAA,CAAAI,MAAA,CAAAuB,aAAA,CAAAhB,YAAA,EAAAf,MAAA,MAAAQ,MAAA,CAAAuB,aAAA,CAAAhB,YAAA,GAAAf,MAAA;MAAA,OAAAV,EAAA,CAAAgB,WAAA,CAAAN,MAAA;IAAA,EAAwC;IAE9CV,EAHI,CAAAG,YAAA,EAC2C,EACvC,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAiB,eACgB,kBACkC;IAAxBD,EAAA,CAAAiB,UAAA,mBAAA0B,mEAAA;MAAA3C,EAAA,CAAAW,aAAA,CAAA6B,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAASE,MAAA,CAAA0B,WAAA,EAAa;IAAA,EAAC;IAC5D5C,EAAA,CAAAiC,SAAA,aAAgC;IAChCjC,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAqD;IAArBD,EAAA,CAAAiB,UAAA,mBAAA4B,mEAAA;MAAA7C,EAAA,CAAAW,aAAA,CAAA6B,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAASE,MAAA,CAAA4B,QAAA,EAAU;IAAA,EAAC;IAClD9C,EAAA,CAAAiC,SAAA,aAAkC;IAClCjC,EAAA,CAAAE,MAAA,sBACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;IAIJH,EADF,CAAAC,cAAA,eAAmC,eACN;IACzBD,EAAA,CAAAiC,SAAA,aAAuC;IAAAjC,EAAA,CAAAE,MAAA,6CACzC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAWNH,EARA,CAAAqB,UAAA,KAAA0B,gDAAA,kBAA8C,KAAAC,gDAAA,kBAQC;IA0BnDhD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAhEiEH,EAAA,CAAAI,SAAA,IAAqC;IAArCJ,EAAA,CAAAuB,gBAAA,YAAAL,MAAA,CAAAuB,aAAA,CAAAf,SAAA,CAAqC;IAKhG1B,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAuB,gBAAA,YAAAL,MAAA,CAAAuB,aAAA,CAAAhB,YAAA,CAAwC;IAyB1CzB,EAAA,CAAAI,SAAA,IAAa;IAAbJ,EAAA,CAAA8B,UAAA,SAAAZ,MAAA,CAAA+B,OAAA,CAAa;IAQbjD,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAA8B,UAAA,UAAAZ,MAAA,CAAA+B,OAAA,CAAc;;;;;IAkDdjD,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,oBAAA6C,OAAA,CAAA3C,OAAA,MACF;;;;;IARAP,EAFJ,CAAAC,cAAA,cAA+F,cACtD,aACR;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE/EH,EADF,CAAAC,cAAA,cAA8B,cACM;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzEH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjEH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAkE;;IACnGF,EADmG,CAAAG,YAAA,EAAM,EACnG;IACNH,EAAA,CAAAqB,UAAA,KAAA8B,uDAAA,kBAAqD;IAIzDnD,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAV2BH,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAoD,kBAAA,KAAAC,IAAA,YAAAH,OAAA,CAAAzB,YAAA,yCAA+C;IAExCzB,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,kBAAA,mBAAA6C,OAAA,CAAAxB,SAAA,6BAAiC;IACrC1B,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,kBAAA,mBAAA6C,OAAA,CAAAvB,KAAA,6BAA6B;IAC5B3B,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAAK,kBAAA,mBAAAL,EAAA,CAAA4B,WAAA,QAAAsB,OAAA,CAAArB,UAAA,uCAAkE;IAE7F7B,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA8B,UAAA,SAAAoB,OAAA,CAAA3C,OAAA,CAAkB;;;;;IAnB9BP,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAAiC,SAAA,YAAwC;IAAAjC,EAAA,CAAAE,MAAA,4CAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,kFAChD;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAENH,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAAqB,UAAA,KAAAiC,gDAAA,oBAA+F;IAerGtD,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IApBcH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAwB,iBAAA,CAAAN,MAAA,CAAAqC,gBAAA,GAAwB;IAKhBvD,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA8B,UAAA,YAAAZ,MAAA,CAAAsC,gBAAA,GAAuB;;;;;;IAoBjDxD,EAAA,CAAAC,cAAA,iBAAqF;IAAtCD,EAAA,CAAAiB,UAAA,mBAAAwC,sEAAA;MAAAzD,EAAA,CAAAW,aAAA,CAAA+C,GAAA;MAAA,MAAAxC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAASE,MAAA,CAAAyC,YAAA,EAAc;IAAA,EAAC;IACrE3D,EAAA,CAAAiC,SAAA,YAAsC;IACtCjC,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAMTH,EAAA,CAAAC,cAAA,iBAA0G;IAArBD,EAAA,CAAAiB,UAAA,mBAAA2C,sEAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAkD,GAAA;MAAA,MAAA3C,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAASE,MAAA,CAAA4C,QAAA,EAAU;IAAA,EAAC;IACvG9D,EAAA,CAAAE,MAAA,2BACA;IAAAF,EAAA,CAAAiC,SAAA,YAAuC;IACzCjC,EAAA,CAAAG,YAAA,EAAS;;;;IAHiDH,EAAA,CAAA8B,UAAA,cAAAZ,MAAA,CAAA6C,UAAA,GAA0B;;;;;;IAKpF/D,EAAA,CAAAC,cAAA,iBAA2F;IAA/BD,EAAA,CAAAiB,UAAA,mBAAA+C,sEAAA;MAAAhE,EAAA,CAAAW,aAAA,CAAAsD,IAAA;MAAA,MAAA/C,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAASE,MAAA,CAAAgD,kBAAA,EAAoB;IAAA,EAAC;IACxFlE,EAAA,CAAAiC,SAAA,YAAoC;IACpCjC,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;AD3Ff,OAAM,MAAOgE,0BAA0B;EAkBrCC,YACUC,kBAAsC,EACtCC,SAAkD;IADlD,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,SAAS,GAATA,SAAS;IAnBV,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,SAAS,GAAa,EAAE;IACvB,KAAAC,aAAa,GAAG,IAAIpF,YAAY,EAA2B;IAErE,KAAAqF,WAAW,GAAW,CAAC;IACvB,KAAA1C,YAAY,GAA8B,EAAE;IAC5C,KAAAiB,OAAO,GAAY,KAAK;IAExB;IACA,KAAA0B,KAAK,GAAiB,CACpB;MAAEC,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAgB,CAAE,EAChD;MAAEF,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAA0B,CAAE,CAC3D;IAED;IACA,KAAArC,aAAa,GAAyF,EAAE;EAKpG;EAEJsC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEA;EACAD,oBAAoBA,CAAA;IAClB,IAAI,CAACvC,aAAa,CAACyC,OAAO,GAAG,CAAC,CAAC,CAAC;IAChC,IAAI,CAACzC,aAAa,CAAC0C,OAAO,GAAG,IAAI,CAAC,CAAC;IACnC,IAAI,CAAC1C,aAAa,CAAC2C,SAAS,GAAG,IAAI;IACnC,IAAI,CAAC3C,aAAa,CAAChB,YAAY,GAAG,EAAE;IACpC,IAAI,CAACgB,aAAa,CAACf,SAAS,GAAG,EAAE;IACjC;IACA,IAAI,CAACe,aAAa,CAAC4C,YAAY,GAAG,IAAI,CAACd,WAAW;IAClD,IAAI,CAAC9B,aAAa,CAAC6C,UAAU,GAAG,IAAI,CAACd,SAAS;EAChD;EAIA;EACA1B,QAAQA,CAAA;IACN,IAAI,CAACmC,uBAAuB,EAAE;EAChC;EAEA;EACArC,WAAWA,CAAA;IACT,IAAI,CAACoC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACxC,aAAa,CAAC4C,YAAY,EAAE;MACpC;IACF;IAEA,IAAI,CAACpC,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMsC,sBAAsB,GAA8B;MACxDF,YAAY,EAAE,IAAI,CAAC5C,aAAa,CAAC4C,YAAY;MAC7CC,UAAU,EAAE,IAAI,CAAC7C,aAAa,CAAC6C,UAAU;MACzC5D,SAAS,EAAE,IAAI,CAACe,aAAa,CAACf,SAAS,IAAI,IAAI;MAC/CD,YAAY,EAAE,IAAI,CAACgB,aAAa,CAAChB,YAAY,IAAI,IAAI;MACrDyD,OAAO,EAAE,IAAI,CAACzC,aAAa,CAACyC,OAAO;MACnCC,OAAO,EAAE,IAAI,CAAC1C,aAAa,CAAC0C,OAAO;MACnCC,SAAS,EAAE,IAAI,CAAC3C,aAAa,CAAC2C,SAAS;MACvCI,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAACpB,kBAAkB,CAACqB,gDAAgD,CAAC;MACvEC,IAAI,EAAEJ;KACP,CAAC,CAACK,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAwC,IAAI;QACjD,IAAI,CAAC7C,OAAO,GAAG,KAAK;QACpB,IAAI6C,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAAChE,YAAY,GAAG8D,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAChD,GAAGA,IAAI;YACPnF,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL,IAAI,CAACiB,YAAY,GAAG,EAAE;QACxB;MACF,CAAC;MACDmE,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAClD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACjB,YAAY,GAAG,EAAE;MACxB;KACD,CAAC;EACJ;EAEAZ,uBAAuBA,CAAA;IACrB;EAAA;EAGFoC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACxB,YAAY,CAACoE,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACnF,QAAQ,CAAC;EACxD;EAEAgD,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACW,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAAClB,gBAAgB,EAAE,CAACnB,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAyB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACW,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAf,YAAYA,CAAA;IACV,IAAI,IAAI,CAACe,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEA2B,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAAC5B,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAR,kBAAkBA,CAAA;IAChB,MAAMqC,MAAM,GAA4B;MACtChC,WAAW,EAAE,IAAI,CAAC9B,aAAa,CAAC4C,YAAY,IAAI,CAAC;MACjDmB,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,IAAI,CAACjD,gBAAgB,EAAE;MACtCkD,UAAU,EAAE,IAAI,CAAClD,gBAAgB,EAAE,CAACnB,MAAM;MAC1CsE,cAAc,EAAE;QACdrB,UAAU,EAAE,IAAI,CAAC7C,aAAa,CAAC6C,UAAU,IAAIsB,SAAS;QACtDlF,SAAS,EAAE,IAAI,CAACe,aAAa,CAACf,SAAS,IAAIkF,SAAS;QACpDnF,YAAY,EAAE,IAAI,CAACgB,aAAa,CAAChB,YAAY,IAAImF;;KAEpD;IAED,IAAI,CAACnC,aAAa,CAACoC,IAAI,CAACN,MAAM,CAAC;IAC/B,IAAI,CAACO,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACzC,SAAS,CAACwC,KAAK,EAAE;EACxB;EAEQC,eAAeA,CAAA;IACrB,IAAI,CAACrC,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC1C,YAAY,CAACgF,OAAO,CAACC,WAAW,IAAG;MACtCA,WAAW,CAAClG,QAAQ,GAAG,KAAK;IAC9B,CAAC,CAAC;EACJ;EAEAmG,SAASA,CAAA;IACP,MAAMC,WAAW,GAAG,IAAI,CAACnF,YAAY,CAACoF,KAAK,CAAClB,IAAI,IAAIA,IAAI,CAACnF,QAAQ,CAAC;IAClE,IAAI,CAACiB,YAAY,CAACgF,OAAO,CAACd,IAAI,IAAG;MAC/BA,IAAI,CAACnF,QAAQ,GAAG,CAACoG,WAAW;IAC9B,CAAC,CAAC;EACJ;EAEA5D,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACC,gBAAgB,EAAE,CAACnB,MAAM;EACvC;EAEAgF,aAAaA,CAAA;IACX,OAAO,IAAI,CAACrF,YAAY,CAACK,MAAM;EACjC;;;uCA/KW8B,0BAA0B,EAAAnE,EAAA,CAAAsH,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAxH,EAAA,CAAAsH,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA1BvD,0BAA0B;MAAAwD,SAAA;MAAAC,MAAA;QAAArD,WAAA;QAAAC,SAAA;MAAA;MAAAqD,OAAA;QAAApD,aAAA;MAAA;MAAAqD,UAAA;MAAAC,QAAA,GAAA/H,EAAA,CAAAgI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChDnCtI,EAFJ,CAAAC,cAAA,iBAA4C,wBACS,aACV;UAAAD,EAAA,CAAAE,MAAA,2CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnDH,EAAA,CAAAC,cAAA,gBAA2D;UAAlBD,EAAA,CAAAiB,UAAA,mBAAAuH,4DAAA;YAAA,OAASD,GAAA,CAAAzB,KAAA,EAAO;UAAA,EAAC;UACxD9G,EAAA,CAAAiC,SAAA,WAA4B;UAEhCjC,EADE,CAAAG,YAAA,EAAS,EACM;UAEjBH,EAAA,CAAAC,cAAA,sBAA+C;UAE7CD,EAAA,CAAAiC,SAAA,4BAEqB;UA+ErBjC,EA7EA,CAAAqB,UAAA,IAAAoH,yCAAA,kBAAoD,IAAAC,yCAAA,kBA6EA;UA6BtD1I,EAAA,CAAAG,YAAA,EAAe;UAGbH,EADF,CAAAC,cAAA,yBAAmD,eACvB;UACxBD,EAAA,CAAAqB,UAAA,KAAAsH,6CAAA,qBAAqF;UAKrF3I,EAAA,CAAAC,cAAA,kBAAsD;UAA/BD,EAAA,CAAAiB,UAAA,mBAAA2H,6DAAA;YAAA,OAASL,GAAA,CAAAzB,KAAA,EAAO;UAAA,EAAC;UACtC9G,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOTH,EALA,CAAAqB,UAAA,KAAAwH,6CAAA,qBAA0G,KAAAC,6CAAA,qBAKf;UAMjG9I,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;UAtIcH,EAAA,CAAAI,SAAA,GAAe;UACjCJ,EADkB,CAAA8B,UAAA,UAAAyG,GAAA,CAAA5D,KAAA,CAAe,gBAAA4D,GAAA,CAAA7D,WAAA,CAA4B,+BAA+B,wBACrE;UAGnB1E,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAA8B,UAAA,SAAAyG,GAAA,CAAA7D,WAAA,OAAuB;UA6EvB1E,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAA8B,UAAA,SAAAyG,GAAA,CAAA7D,WAAA,OAAuB;UAiClB1E,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAA8B,UAAA,SAAAyG,GAAA,CAAA7D,WAAA,KAAqB;UASrB1E,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAA8B,UAAA,SAAAyG,GAAA,CAAA7D,WAAA,KAAqB;UAKrB1E,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAA8B,UAAA,SAAAyG,GAAA,CAAA7D,WAAA,OAAuB;;;qBDrGlCpF,YAAY,EAAAyJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,YAAA,EACZ3J,WAAW,EAAA4J,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX9J,YAAY,EAAAiI,EAAA,CAAA8B,eAAA,EAAA9B,EAAA,CAAA+B,mBAAA,EAAA/B,EAAA,CAAAgC,qBAAA,EAAAhC,EAAA,CAAAiC,qBAAA,EACZjK,cAAc,EAAAgI,EAAA,CAAAkC,iBAAA,EACdjK,gBAAgB,EAAA+H,EAAA,CAAAmC,mBAAA,EAChBjK,cAAc,EACdC,cAAc,EACdC,aAAa,EAAA4H,EAAA,CAAAoC,gBAAA,EACb9J,sBAAsB;MAAA+J,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}