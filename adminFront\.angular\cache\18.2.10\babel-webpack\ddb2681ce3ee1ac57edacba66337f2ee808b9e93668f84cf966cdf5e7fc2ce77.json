{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nlet TemplateViewerComponent = class TemplateViewerComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.templateType = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 公開Math對象供模板使用\n    this.Math = Math;\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = []; // 保留用於向後相容\n    this.selectedTemplate = null;\n    // 新的詳情資料管理\n    this.currentTemplateDetailsData = [];\n    this.detailSearchKeyword = '';\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 分頁功能 - 模板列表\n    this.templatePagination = {\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedTemplates = [];\n    // 分頁功能 - 模板詳情\n    this.detailPagination = {\n      currentPage: 1,\n      pageSize: 5,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedDetails = [];\n  }\n  ngOnInit() {\n    this.loadTemplates();\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 載入模板列表 - 使用真實的 API 調用\n  loadTemplates() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: this.templateType,\n      // 1=客變需求\n      PageIndex: 1,\n      // 暫時載入第一頁\n      PageSize: 100,\n      // 暫時載入較多資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateList API\n    this.templateService.apiTemplateGetTemplateListPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // API 調用成功\n          // 轉換 API 回應為內部格式\n          this.templates = response.Entries.map(item => ({\n            TemplateID: item.CTemplateId,\n            TemplateName: item.CTemplateName || '',\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\n          }));\n          // 更新過濾列表\n          this.updateFilteredTemplates();\n          // 清空詳情資料，改為按需載入\n          this.templateDetails = [];\n          this.currentTemplateDetailsData = [];\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n          this.templateDetails = [];\n          this.updateFilteredTemplates();\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n        this.templates = [];\n        this.templateDetails = [];\n        this.updateFilteredTemplates();\n      }\n    });\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n    this.updateTemplatePagination();\n  }\n  // 更新模板分頁\n  updateTemplatePagination() {\n    this.templatePagination.totalItems = this.filteredTemplates.length;\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\n    // 確保當前頁面不超過總頁數\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\n    }\n    this.updatePaginatedTemplates();\n  }\n  // 更新分頁後的模板列表\n  updatePaginatedTemplates() {\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\n    const endIndex = startIndex + this.templatePagination.pageSize;\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\n  }\n  // 模板分頁導航\n  goToTemplatePage(page) {\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = page;\n      this.updatePaginatedTemplates();\n    }\n  }\n  // 獲取模板分頁頁碼數組\n  getTemplatePageNumbers() {\n    const pages = [];\n    const totalPages = this.templatePagination.totalPages;\n    const currentPage = this.templatePagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n    // 按需載入模板詳情\n    if (template.TemplateID) {\n      this.loadTemplateDetails(template.TemplateID);\n    }\n    this.updateDetailPagination();\n  }\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\n  loadTemplateDetails(templateId, pageIndex = 1, searchKeyword) {\n    const args = {\n      templateId: templateId\n    };\n    // 調用真實的 GetTemplateDetailById API\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\n          let allDetails = response.Entries.map(item => ({\n            CTemplateDetailId: item.CTemplateDetailId || 0,\n            CTemplateId: item.CTemplateId || templateId,\n            CReleateId: item.CReleateId || 0,\n            CReleateName: item.CReleateName || '',\n            CGroupName: item.CGroupName || '',\n            // 新增群組名稱欄位\n            CSort: undefined,\n            CRemark: undefined,\n            CCreateDt: new Date().toISOString(),\n            CCreator: '系統',\n            CCategory: undefined,\n            CUnitPrice: undefined,\n            CQuantity: undefined,\n            CUnit: undefined\n          }));\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\n          if (searchKeyword && searchKeyword.trim()) {\n            allDetails = allDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()));\n          }\n          // 前端分頁處理 (因為 API 不支援分頁參數)\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n          const endIndex = startIndex + this.detailPagination.pageSize;\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\n          // 更新詳情資料和分頁資訊\n          this.currentTemplateDetailsData = pagedDetails;\n          this.detailPagination.totalItems = allDetails.length;\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\n          this.detailPagination.currentPage = pageIndex;\n        } else {\n          this.currentTemplateDetailsData = [];\n          this.detailPagination.totalItems = 0;\n          this.detailPagination.totalPages = 0;\n          this.detailPagination.currentPage = 1;\n        }\n      },\n      error: () => {\n        this.currentTemplateDetailsData = [];\n        this.detailPagination.totalItems = 0;\n        this.detailPagination.totalPages = 0;\n        this.detailPagination.currentPage = 1;\n        // 如果 API 失敗，回退到模擬資料\n        this.loadTemplateDetailsMock(templateId, pageIndex, searchKeyword);\n      }\n    });\n  }\n  // 模擬載入模板詳情 (暫時使用，等待後端 API)\n  loadTemplateDetailsMock(templateId, pageIndex = 1, searchKeyword) {\n    // 模擬 API 延遲\n    setTimeout(() => {\n      // 生成模擬詳情資料\n      const mockDetails = [];\n      const itemsPerTemplate = 8; // 每個模板8個項目\n      for (let i = 1; i <= itemsPerTemplate; i++) {\n        const detail = {\n          CTemplateDetailId: templateId * 100 + i,\n          CTemplateId: templateId,\n          CReleateId: templateId * 1000 + i,\n          CReleateName: `工程項目${String.fromCharCode(64 + templateId % 26 + 1)}-${i}`,\n          CGroupName: i % 4 === 0 ? '結構工程' : i % 4 === 1 ? '機電工程' : i % 4 === 2 ? '裝修工程' : '其他工程',\n          // 新增群組名稱\n          CSort: i,\n          CRemark: i % 3 === 0 ? `備註說明 ${i}` : undefined,\n          CCreateDt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\n          CCreator: '系統管理員',\n          CCategory: i % 2 === 0 ? '基礎工程' : '進階工程',\n          CUnitPrice: Math.floor(Math.random() * 10000) + 1000,\n          CQuantity: Math.floor(Math.random() * 10) + 1,\n          CUnit: i % 3 === 0 ? '式' : i % 3 === 1 ? '個' : 'm²'\n        };\n        mockDetails.push(detail);\n      }\n      // 搜尋篩選\n      let filteredDetails = mockDetails;\n      if (searchKeyword && searchKeyword.trim()) {\n        filteredDetails = mockDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CRemark && detail.CRemark.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CCategory && detail.CCategory.toLowerCase().includes(searchKeyword.toLowerCase()));\n      }\n      // 分頁處理\n      const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n      const endIndex = startIndex + this.detailPagination.pageSize;\n      const pagedDetails = filteredDetails.slice(startIndex, endIndex);\n      // 更新資料\n      this.currentTemplateDetailsData = pagedDetails;\n      this.detailPagination.totalItems = filteredDetails.length;\n      this.detailPagination.totalPages = Math.ceil(filteredDetails.length / this.detailPagination.pageSize);\n      this.detailPagination.currentPage = pageIndex;\n    }, 300);\n  }\n  // 搜尋模板詳情\n  searchTemplateDetails(keyword) {\n    this.detailSearchKeyword = keyword;\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\n    }\n  }\n  // 更新詳情分頁 (保留向後相容)\n  updateDetailPagination() {\n    // 如果使用新的詳情資料，直接返回\n    if (this.currentTemplateDetailsData.length > 0) {\n      return;\n    }\n    // 向後相容：使用舊的詳情資料\n    const details = this.currentTemplateDetails;\n    this.detailPagination.totalItems = details.length;\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\n    this.detailPagination.currentPage = 1; // 重置到第一頁\n    this.updatePaginatedDetails();\n  }\n  // 更新分頁後的詳情列表\n  updatePaginatedDetails() {\n    const details = this.currentTemplateDetails;\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\n    const endIndex = startIndex + this.detailPagination.pageSize;\n    this.paginatedDetails = details.slice(startIndex, endIndex);\n  }\n  // 詳情分頁導航\n  goToDetailPage(page) {\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\n      // 如果使用新的詳情資料，重新載入\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\n      } else {\n        // 向後相容：使用舊的分頁邏輯\n        this.detailPagination.currentPage = page;\n        this.updatePaginatedDetails();\n      }\n    }\n  }\n  // 獲取詳情分頁頁碼數組\n  getDetailPageNumbers() {\n    const pages = [];\n    const totalPages = this.detailPagination.totalPages;\n    const currentPage = this.detailPagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // 刪除模板 - 使用真實的 API 調用\n  deleteTemplateById(templateID) {\n    // 準備 API 請求參數\n    const deleteTemplateArgs = {\n      CTemplateId: templateID\n    };\n    // 調用 DeleteTemplate API\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\n      body: deleteTemplateArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0) {\n          // API 調用成功\n          // 重新載入模板列表\n          this.loadTemplates();\n          // 如果當前查看的模板被刪除，關閉詳情\n          if (this.selectedTemplate?.TemplateID === templateID) {\n            this.selectedTemplate = null;\n          }\n        } else {\n          // API 返回錯誤\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n      }\n    });\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/Template/GetTemplateList\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/Template/SaveTemplate\n   *    請求體: {\n   *      CTemplateName: string,\n   *      CTemplateType: number,  // 1=客變需求\n   *      CStatus: number,\n   *      Details: [{\n   *        CReleateId: number,        // 關聯主檔ID\n   *        CReleateName: string       // 關聯名稱\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    POST /api/Template/DeleteTemplate\n   *    請求體: { CTemplateId: number }\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n};\n__decorate([Input()], TemplateViewerComponent.prototype, \"templateType\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"selectTemplate\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"close\", void 0);\nTemplateViewerComponent = __decorate([Component({\n  selector: 'app-template-viewer',\n  templateUrl: './template-viewer.component.html',\n  styleUrls: ['./template-viewer.component.scss'],\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule]\n})], TemplateViewerComponent);\nexport { TemplateViewerComponent };\n// 注意：GetTemplateDetailById API 使用的是 GetTemplateDetailByIdArgs 和 TemplateDetailItemListResponseBase\n// 這些 interface 已經在 API models 中定義，不需要重複定義", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "TemplateViewerComponent", "constructor", "templateService", "templateType", "selectTemplate", "close", "Math", "templates", "templateDetails", "selectedTemplate", "currentTemplateDetailsData", "detailSearchKeyword", "searchKeyword", "filteredTemplates", "templatePagination", "currentPage", "pageSize", "totalItems", "totalPages", "paginatedTemplates", "detailPagination", "paginatedDetails", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "TemplateID", "CTemplateId", "TemplateName", "Description", "CCreateDt", "Date", "toLocaleDateString", "error", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "updateTemplatePagination", "length", "ceil", "max", "updatePaginatedTemplates", "startIndex", "endIndex", "slice", "goToTemplatePage", "page", "getTemplatePageNumbers", "pages", "startPage", "endPage", "min", "i", "push", "onSearch", "clearSearch", "onSelectTemplate", "emit", "loadTemplateDetails", "updateDetailPagination", "templateId", "pageIndex", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "allDetails", "CTemplateDetailId", "CReleateId", "CReleateName", "CGroupName", "CSort", "undefined", "CRemark", "toISOString", "CCreator", "CCategory", "CUnitPrice", "CQuantity", "CUnit", "detail", "pagedDetails", "loadTemplateDetailsMock", "setTimeout", "mockDetails", "itemsPerTemplate", "String", "fromCharCode", "now", "random", "floor", "filteredDetails", "searchTemplateDetails", "details", "currentTemplateDetails", "updatePaginatedDetails", "goToDetailPage", "getDetailPageNumbers", "onClose", "onDeleteTemplate", "templateID", "confirm", "deleteTemplateById", "deleteTemplateArgs", "apiTemplateDeleteTemplatePost$Json", "closeTemplateDetail", "d", "trackByTemplateId", "index", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, GetTemplateByIdArgs, GetTemplateDetailByIdArgs } from 'src/services/api/models';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() templateType: number = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 公開Math對象供模板使用\r\n  Math = Math;\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = []; // 保留用於向後相容\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 新的詳情資料管理\r\n  currentTemplateDetailsData: TemplateDetailItem[] = [];\r\n  detailSearchKeyword = '';\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板列表\r\n  templatePagination = {\r\n    currentPage: 1,\r\n    pageSize: 10,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板詳情\r\n  detailPagination = {\r\n    currentPage: 1,\r\n    pageSize: 5,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedDetails: TemplateDetail[] = [];\r\n\r\n\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    this.loadTemplates();\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 載入模板列表 - 使用真實的 API 調用\r\n  loadTemplates() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.templateType, // 1=客變需求\r\n      PageIndex: 1, // 暫時載入第一頁\r\n      PageSize: 100, // 暫時載入較多資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateList API\r\n    this.templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // API 調用成功\r\n\r\n          // 轉換 API 回應為內部格式\r\n          this.templates = response.Entries.map(item => ({\r\n            TemplateID: item.CTemplateId,\r\n            TemplateName: item.CTemplateName || '',\r\n            Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\r\n          }));\r\n\r\n          // 更新過濾列表\r\n          this.updateFilteredTemplates();\r\n\r\n          // 清空詳情資料，改為按需載入\r\n          this.templateDetails = [];\r\n          this.currentTemplateDetailsData = [];\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n          this.templateDetails = [];\r\n          this.updateFilteredTemplates();\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n        this.templateDetails = [];\r\n        this.updateFilteredTemplates();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n    this.updateTemplatePagination();\r\n  }\r\n\r\n  // 更新模板分頁\r\n  updateTemplatePagination() {\r\n    this.templatePagination.totalItems = this.filteredTemplates.length;\r\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\r\n\r\n    // 確保當前頁面不超過總頁數\r\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\r\n    }\r\n\r\n    this.updatePaginatedTemplates();\r\n  }\r\n\r\n  // 更新分頁後的模板列表\r\n  updatePaginatedTemplates() {\r\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\r\n    const endIndex = startIndex + this.templatePagination.pageSize;\r\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 模板分頁導航\r\n  goToTemplatePage(page: number) {\r\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = page;\r\n      this.updatePaginatedTemplates();\r\n    }\r\n  }\r\n\r\n  // 獲取模板分頁頁碼數組\r\n  getTemplatePageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.templatePagination.totalPages;\r\n    const currentPage = this.templatePagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n\r\n    // 按需載入模板詳情\r\n    if (template.TemplateID) {\r\n      this.loadTemplateDetails(template.TemplateID);\r\n    }\r\n\r\n    this.updateDetailPagination();\r\n  }\r\n\r\n  // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\r\n  loadTemplateDetails(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    // 調用真實的 GetTemplateDetailById API\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n\r\n          // 轉換 API 回應為內部格式，並處理搜尋和分頁\r\n          let allDetails = response.Entries.map(item => ({\r\n            CTemplateDetailId: item.CTemplateDetailId || 0,\r\n            CTemplateId: item.CTemplateId || templateId,\r\n            CReleateId: item.CReleateId || 0,\r\n            CReleateName: item.CReleateName || '',\r\n            CGroupName: item.CGroupName || '', // 新增群組名稱欄位\r\n            CSort: undefined,\r\n            CRemark: undefined,\r\n            CCreateDt: new Date().toISOString(),\r\n            CCreator: '系統',\r\n            CCategory: undefined,\r\n            CUnitPrice: undefined,\r\n            CQuantity: undefined,\r\n            CUnit: undefined\r\n          } as TemplateDetailItem));\r\n\r\n          // 前端搜尋篩選 (因為 API 不支援搜尋參數)\r\n          if (searchKeyword && searchKeyword.trim()) {\r\n            allDetails = allDetails.filter(detail =>\r\n              detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n              (detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n            );\r\n          }\r\n\r\n          // 前端分頁處理 (因為 API 不支援分頁參數)\r\n          const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n          const endIndex = startIndex + this.detailPagination.pageSize;\r\n          const pagedDetails = allDetails.slice(startIndex, endIndex);\r\n\r\n          // 更新詳情資料和分頁資訊\r\n          this.currentTemplateDetailsData = pagedDetails;\r\n          this.detailPagination.totalItems = allDetails.length;\r\n          this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\r\n          this.detailPagination.currentPage = pageIndex;\r\n        } else {\r\n          this.currentTemplateDetailsData = [];\r\n          this.detailPagination.totalItems = 0;\r\n          this.detailPagination.totalPages = 0;\r\n          this.detailPagination.currentPage = 1;\r\n        }\r\n      },\r\n      error: () => {\r\n        this.currentTemplateDetailsData = [];\r\n        this.detailPagination.totalItems = 0;\r\n        this.detailPagination.totalPages = 0;\r\n        this.detailPagination.currentPage = 1;\r\n\r\n        // 如果 API 失敗，回退到模擬資料\r\n        this.loadTemplateDetailsMock(templateId, pageIndex, searchKeyword);\r\n      }\r\n    });\r\n  }\r\n\r\n  // 模擬載入模板詳情 (暫時使用，等待後端 API)\r\n  private loadTemplateDetailsMock(templateId: number, pageIndex: number = 1, searchKeyword?: string) {\r\n    // 模擬 API 延遲\r\n    setTimeout(() => {\r\n      // 生成模擬詳情資料\r\n      const mockDetails: TemplateDetailItem[] = [];\r\n      const itemsPerTemplate = 8; // 每個模板8個項目\r\n\r\n      for (let i = 1; i <= itemsPerTemplate; i++) {\r\n        const detail: TemplateDetailItem = {\r\n          CTemplateDetailId: templateId * 100 + i,\r\n          CTemplateId: templateId,\r\n          CReleateId: templateId * 1000 + i,\r\n          CReleateName: `工程項目${String.fromCharCode(64 + (templateId % 26) + 1)}-${i}`,\r\n          CGroupName: i % 4 === 0 ? '結構工程' : (i % 4 === 1 ? '機電工程' : (i % 4 === 2 ? '裝修工程' : '其他工程')), // 新增群組名稱\r\n          CSort: i,\r\n          CRemark: i % 3 === 0 ? `備註說明 ${i}` : undefined,\r\n          CCreateDt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\r\n          CCreator: '系統管理員',\r\n          CCategory: i % 2 === 0 ? '基礎工程' : '進階工程',\r\n          CUnitPrice: Math.floor(Math.random() * 10000) + 1000,\r\n          CQuantity: Math.floor(Math.random() * 10) + 1,\r\n          CUnit: i % 3 === 0 ? '式' : (i % 3 === 1 ? '個' : 'm²')\r\n        };\r\n        mockDetails.push(detail);\r\n      }\r\n\r\n      // 搜尋篩選\r\n      let filteredDetails = mockDetails;\r\n      if (searchKeyword && searchKeyword.trim()) {\r\n        filteredDetails = mockDetails.filter(detail =>\r\n          detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) ||\r\n          (detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase())) ||\r\n          (detail.CRemark && detail.CRemark.toLowerCase().includes(searchKeyword.toLowerCase())) ||\r\n          (detail.CCategory && detail.CCategory.toLowerCase().includes(searchKeyword.toLowerCase()))\r\n        );\r\n      }\r\n\r\n      // 分頁處理\r\n      const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\r\n      const endIndex = startIndex + this.detailPagination.pageSize;\r\n      const pagedDetails = filteredDetails.slice(startIndex, endIndex);\r\n\r\n      // 更新資料\r\n      this.currentTemplateDetailsData = pagedDetails;\r\n      this.detailPagination.totalItems = filteredDetails.length;\r\n      this.detailPagination.totalPages = Math.ceil(filteredDetails.length / this.detailPagination.pageSize);\r\n      this.detailPagination.currentPage = pageIndex;\r\n    }, 300);\r\n  }\r\n\r\n  // 搜尋模板詳情\r\n  searchTemplateDetails(keyword: string) {\r\n    this.detailSearchKeyword = keyword;\r\n    if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\r\n      this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\r\n    }\r\n  }\r\n\r\n  // 更新詳情分頁 (保留向後相容)\r\n  updateDetailPagination() {\r\n    // 如果使用新的詳情資料，直接返回\r\n    if (this.currentTemplateDetailsData.length > 0) {\r\n      return;\r\n    }\r\n\r\n    // 向後相容：使用舊的詳情資料\r\n    const details = this.currentTemplateDetails;\r\n    this.detailPagination.totalItems = details.length;\r\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\r\n    this.detailPagination.currentPage = 1; // 重置到第一頁\r\n    this.updatePaginatedDetails();\r\n  }\r\n\r\n  // 更新分頁後的詳情列表\r\n  updatePaginatedDetails() {\r\n    const details = this.currentTemplateDetails;\r\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\r\n    const endIndex = startIndex + this.detailPagination.pageSize;\r\n    this.paginatedDetails = details.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 詳情分頁導航\r\n  goToDetailPage(page: number) {\r\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\r\n      // 如果使用新的詳情資料，重新載入\r\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\r\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\r\n      } else {\r\n        // 向後相容：使用舊的分頁邏輯\r\n        this.detailPagination.currentPage = page;\r\n        this.updatePaginatedDetails();\r\n      }\r\n    }\r\n  }\r\n\r\n  // 獲取詳情分頁頁碼數組\r\n  getDetailPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.detailPagination.totalPages;\r\n    const currentPage = this.detailPagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // 刪除模板 - 使用真實的 API 調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 準備 API 請求參數\r\n    const deleteTemplateArgs: GetTemplateByIdArgs = {\r\n      CTemplateId: templateID\r\n    };\r\n\r\n    // 調用 DeleteTemplate API\r\n    this.templateService.apiTemplateDeleteTemplatePost$Json({\r\n      body: deleteTemplateArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0) {\r\n          // API 調用成功\r\n          // 重新載入模板列表\r\n          this.loadTemplates();\r\n\r\n          // 如果當前查看的模板被刪除，關閉詳情\r\n          if (this.selectedTemplate?.TemplateID === templateID) {\r\n            this.selectedTemplate = null;\r\n          }\r\n        } else {\r\n          // API 返回錯誤\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n      }\r\n    });\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/Template/GetTemplateList\r\n   *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/Template/SaveTemplate\r\n   *    請求體: {\r\n   *      CTemplateName: string,\r\n   *      CTemplateType: number,  // 1=客變需求\r\n   *      CStatus: number,\r\n   *      Details: [{\r\n   *        CReleateId: number,        // 關聯主檔ID\r\n   *        CReleateName: string       // 關聯名稱\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    POST /api/Template/DeleteTemplate\r\n   *    請求體: { CTemplateId: number }\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\r\n   * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\r\n   * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\r\n   * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  CTemplateType: number; // 模板類型，1=客變需求\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n\r\n// 擴展的詳情項目結構 (基於 API 的 TemplateDetailItem 擴展)\r\nexport interface TemplateDetailItem {\r\n  CTemplateDetailId: number;\r\n  CTemplateId: number;\r\n  CReleateId: number;            // 關聯主檔ID\r\n  CReleateName: string;          // 關聯名稱\r\n  CGroupName?: string;           // 群組名稱 (API 新增欄位)\r\n  CSort?: number;                // 排序順序\r\n  CRemark?: string;              // 備註\r\n  CCreateDt: string;             // 建立時間\r\n  CCreator: string;              // 建立者\r\n  CUpdateDt?: string;            // 更新時間\r\n  CUpdator?: string;             // 更新者\r\n\r\n  // 業務相關欄位 (依需求添加，部分由 API 提供)\r\n  CUnitPrice?: number;           // 單價\r\n  CQuantity?: number;            // 數量\r\n  CUnit?: string;                // 單位\r\n  CCategory?: string;            // 分類\r\n}\r\n\r\n// 注意：GetTemplateDetailById API 使用的是 GetTemplateDetailByIdArgs 和 TemplateDetailItemListResponseBase\r\n// 這些 interface 已經在 API models 中定義，不需要重複定義\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAA2B,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;AAWtD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAyClCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAxC1B,KAAAC,YAAY,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAC,cAAc,GAAG,IAAIX,YAAY,EAAY;IAC7C,KAAAY,KAAK,GAAG,IAAIZ,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAAa,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE,CAAC,CAAC;IACxC,KAAAC,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAC,0BAA0B,GAAyB,EAAE;IACrD,KAAAC,mBAAmB,GAAG,EAAE;IAExB;IACA,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAC,kBAAkB,GAAG;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE;KACb;IACD,KAAAC,kBAAkB,GAAe,EAAE;IAEnC;IACA,KAAAC,gBAAgB,GAAG;MACjBL,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE;KACb;IACD,KAAAG,gBAAgB,GAAqB,EAAE;EAIiB;EAExDC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACA,MAAMG,mBAAmB,GAAwB;MAC/CC,aAAa,EAAE,IAAI,CAACxB,YAAY;MAAE;MAClCyB,SAAS,EAAE,CAAC;MAAE;MACdC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAAC5B,eAAe,CAAC6B,mCAAmC,CAAC;MACvDC,IAAI,EAAEN;KACP,CAAC,CAACO,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UAEA;UACA,IAAI,CAAC9B,SAAS,GAAG4B,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7CC,UAAU,EAAED,IAAI,CAACE,WAAW;YAC5BC,YAAY,EAAEH,IAAI,CAACT,aAAa,IAAI,EAAE;YACtCa,WAAW,EAAE,SAASJ,IAAI,CAACK,SAAS,GAAG,IAAIC,IAAI,CAACN,IAAI,CAACK,SAAS,CAAC,CAACE,kBAAkB,EAAE,GAAG,IAAI;WAC5F,CAAC,CAAC;UAEH;UACA,IAAI,CAACtB,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAAChB,eAAe,GAAG,EAAE;UACzB,IAAI,CAACE,0BAA0B,GAAG,EAAE;QACtC,CAAC,MAAM;UACL;UACA,IAAI,CAACH,SAAS,GAAG,EAAE;UACnB,IAAI,CAACC,eAAe,GAAG,EAAE;UACzB,IAAI,CAACgB,uBAAuB,EAAE;QAChC;MACF,CAAC;MACDuB,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACxC,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACgB,uBAAuB,EAAE;MAChC;KACD,CAAC;EACJ;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACZ,aAAa,CAACoC,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACnC,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACN,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAM0C,OAAO,GAAG,IAAI,CAACrC,aAAa,CAACsC,WAAW,EAAE;MAChD,IAAI,CAACrC,iBAAiB,GAAG,IAAI,CAACN,SAAS,CAAC4C,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAACV,YAAY,CAACQ,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAACT,WAAW,IAAIS,QAAQ,CAACT,WAAW,CAACO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;IACA,IAAI,CAACK,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,IAAI,CAACxC,kBAAkB,CAACG,UAAU,GAAG,IAAI,CAACJ,iBAAiB,CAAC0C,MAAM;IAClE,IAAI,CAACzC,kBAAkB,CAACI,UAAU,GAAGZ,IAAI,CAACkD,IAAI,CAAC,IAAI,CAAC1C,kBAAkB,CAACG,UAAU,GAAG,IAAI,CAACH,kBAAkB,CAACE,QAAQ,CAAC;IAErH;IACA,IAAI,IAAI,CAACF,kBAAkB,CAACC,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACI,UAAU,EAAE;MAC5E,IAAI,CAACJ,kBAAkB,CAACC,WAAW,GAAGT,IAAI,CAACmD,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC3C,kBAAkB,CAACI,UAAU,CAAC;IACvF;IAEA,IAAI,CAACwC,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAAC7C,kBAAkB,CAACC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,kBAAkB,CAACE,QAAQ;IAC/F,MAAM4C,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC7C,kBAAkB,CAACE,QAAQ;IAC9D,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAACN,iBAAiB,CAACgD,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACAE,gBAAgBA,CAACC,IAAY;IAC3B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACjD,kBAAkB,CAACI,UAAU,EAAE;MAC3D,IAAI,CAACJ,kBAAkB,CAACC,WAAW,GAAGgD,IAAI;MAC1C,IAAI,CAACL,wBAAwB,EAAE;IACjC;EACF;EAEA;EACAM,sBAAsBA,CAAA;IACpB,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAM/C,UAAU,GAAG,IAAI,CAACJ,kBAAkB,CAACI,UAAU;IACrD,MAAMH,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACC,WAAW;IAEvD;IACA,MAAMmD,SAAS,GAAG5D,IAAI,CAACmD,GAAG,CAAC,CAAC,EAAE1C,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMoD,OAAO,GAAG7D,IAAI,CAAC8D,GAAG,CAAClD,UAAU,EAAEH,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIsD,CAAC,GAAGH,SAAS,EAAEG,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCJ,KAAK,CAACK,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOJ,KAAK;EACd;EAEA;EACAM,QAAQA,CAAA;IACN,IAAI,CAAC/C,uBAAuB,EAAE;EAChC;EAEA;EACAgD,WAAWA,CAAA;IACT,IAAI,CAAC5D,aAAa,GAAG,EAAE;IACvB,IAAI,CAACY,uBAAuB,EAAE;EAChC;EAQA;EACAiD,gBAAgBA,CAACrB,QAAkB;IACjC,IAAI,CAAC3C,gBAAgB,GAAG2C,QAAQ;IAChC,IAAI,CAAChD,cAAc,CAACsE,IAAI,CAACtB,QAAQ,CAAC;IAElC;IACA,IAAIA,QAAQ,CAACZ,UAAU,EAAE;MACvB,IAAI,CAACmC,mBAAmB,CAACvB,QAAQ,CAACZ,UAAU,CAAC;IAC/C;IAEA,IAAI,CAACoC,sBAAsB,EAAE;EAC/B;EAEA;EACAD,mBAAmBA,CAACE,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAElE,aAAsB;IACnF,MAAMmE,IAAI,GAA8B;MACtCF,UAAU,EAAEA;KACb;IAED;IACA,IAAI,CAAC3E,eAAe,CAAC8E,yCAAyC,CAAC;MAC7DhD,IAAI,EAAE+C;KACP,CAAC,CAAC9C,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UAEjD;UACA,IAAI4C,UAAU,GAAG9C,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C2C,iBAAiB,EAAE3C,IAAI,CAAC2C,iBAAiB,IAAI,CAAC;YAC9CzC,WAAW,EAAEF,IAAI,CAACE,WAAW,IAAIoC,UAAU;YAC3CM,UAAU,EAAE5C,IAAI,CAAC4C,UAAU,IAAI,CAAC;YAChCC,YAAY,EAAE7C,IAAI,CAAC6C,YAAY,IAAI,EAAE;YACrCC,UAAU,EAAE9C,IAAI,CAAC8C,UAAU,IAAI,EAAE;YAAE;YACnCC,KAAK,EAAEC,SAAS;YAChBC,OAAO,EAAED,SAAS;YAClB3C,SAAS,EAAE,IAAIC,IAAI,EAAE,CAAC4C,WAAW,EAAE;YACnCC,QAAQ,EAAE,IAAI;YACdC,SAAS,EAAEJ,SAAS;YACpBK,UAAU,EAAEL,SAAS;YACrBM,SAAS,EAAEN,SAAS;YACpBO,KAAK,EAAEP;WACe,EAAC;UAEzB;UACA,IAAI3E,aAAa,IAAIA,aAAa,CAACoC,IAAI,EAAE,EAAE;YACzCiC,UAAU,GAAGA,UAAU,CAAC9B,MAAM,CAAC4C,MAAM,IACnCA,MAAM,CAACX,YAAY,CAAClC,WAAW,EAAE,CAACG,QAAQ,CAACzC,aAAa,CAACsC,WAAW,EAAE,CAAC,IACtE6C,MAAM,CAACV,UAAU,IAAIU,MAAM,CAACV,UAAU,CAACnC,WAAW,EAAE,CAACG,QAAQ,CAACzC,aAAa,CAACsC,WAAW,EAAE,CAAE,CAC7F;UACH;UAEA;UACA,MAAMS,UAAU,GAAG,CAACmB,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC1D,gBAAgB,CAACJ,QAAQ;UACnE,MAAM4C,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACvC,gBAAgB,CAACJ,QAAQ;UAC5D,MAAMgF,YAAY,GAAGf,UAAU,CAACpB,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;UAE3D;UACA,IAAI,CAAClD,0BAA0B,GAAGsF,YAAY;UAC9C,IAAI,CAAC5E,gBAAgB,CAACH,UAAU,GAAGgE,UAAU,CAAC1B,MAAM;UACpD,IAAI,CAACnC,gBAAgB,CAACF,UAAU,GAAGZ,IAAI,CAACkD,IAAI,CAACyB,UAAU,CAAC1B,MAAM,GAAG,IAAI,CAACnC,gBAAgB,CAACJ,QAAQ,CAAC;UAChG,IAAI,CAACI,gBAAgB,CAACL,WAAW,GAAG+D,SAAS;QAC/C,CAAC,MAAM;UACL,IAAI,CAACpE,0BAA0B,GAAG,EAAE;UACpC,IAAI,CAACU,gBAAgB,CAACH,UAAU,GAAG,CAAC;UACpC,IAAI,CAACG,gBAAgB,CAACF,UAAU,GAAG,CAAC;UACpC,IAAI,CAACE,gBAAgB,CAACL,WAAW,GAAG,CAAC;QACvC;MACF,CAAC;MACDgC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACrC,0BAA0B,GAAG,EAAE;QACpC,IAAI,CAACU,gBAAgB,CAACH,UAAU,GAAG,CAAC;QACpC,IAAI,CAACG,gBAAgB,CAACF,UAAU,GAAG,CAAC;QACpC,IAAI,CAACE,gBAAgB,CAACL,WAAW,GAAG,CAAC;QAErC;QACA,IAAI,CAACkF,uBAAuB,CAACpB,UAAU,EAAEC,SAAS,EAAElE,aAAa,CAAC;MACpE;KACD,CAAC;EACJ;EAEA;EACQqF,uBAAuBA,CAACpB,UAAkB,EAAEC,SAAA,GAAoB,CAAC,EAAElE,aAAsB;IAC/F;IACAsF,UAAU,CAAC,MAAK;MACd;MACA,MAAMC,WAAW,GAAyB,EAAE;MAC5C,MAAMC,gBAAgB,GAAG,CAAC,CAAC,CAAC;MAE5B,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI+B,gBAAgB,EAAE/B,CAAC,EAAE,EAAE;QAC1C,MAAM0B,MAAM,GAAuB;UACjCb,iBAAiB,EAAEL,UAAU,GAAG,GAAG,GAAGR,CAAC;UACvC5B,WAAW,EAAEoC,UAAU;UACvBM,UAAU,EAAEN,UAAU,GAAG,IAAI,GAAGR,CAAC;UACjCe,YAAY,EAAE,OAAOiB,MAAM,CAACC,YAAY,CAAC,EAAE,GAAIzB,UAAU,GAAG,EAAG,GAAG,CAAC,CAAC,IAAIR,CAAC,EAAE;UAC3EgB,UAAU,EAAEhB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,MAAQ;UAAE;UAC7FiB,KAAK,EAAEjB,CAAC;UACRmB,OAAO,EAAEnB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQA,CAAC,EAAE,GAAGkB,SAAS;UAC9C3C,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAAC0D,GAAG,EAAE,GAAGjG,IAAI,CAACkG,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACf,WAAW,EAAE;UACxFC,QAAQ,EAAE,OAAO;UACjBC,SAAS,EAAEtB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM;UACxCuB,UAAU,EAAEtF,IAAI,CAACmG,KAAK,CAACnG,IAAI,CAACkG,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI;UACpDX,SAAS,EAAEvF,IAAI,CAACmG,KAAK,CAACnG,IAAI,CAACkG,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;UAC7CV,KAAK,EAAEzB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG;SACjD;QACD8B,WAAW,CAAC7B,IAAI,CAACyB,MAAM,CAAC;MAC1B;MAEA;MACA,IAAIW,eAAe,GAAGP,WAAW;MACjC,IAAIvF,aAAa,IAAIA,aAAa,CAACoC,IAAI,EAAE,EAAE;QACzC0D,eAAe,GAAGP,WAAW,CAAChD,MAAM,CAAC4C,MAAM,IACzCA,MAAM,CAACX,YAAY,CAAClC,WAAW,EAAE,CAACG,QAAQ,CAACzC,aAAa,CAACsC,WAAW,EAAE,CAAC,IACtE6C,MAAM,CAACV,UAAU,IAAIU,MAAM,CAACV,UAAU,CAACnC,WAAW,EAAE,CAACG,QAAQ,CAACzC,aAAa,CAACsC,WAAW,EAAE,CAAE,IAC3F6C,MAAM,CAACP,OAAO,IAAIO,MAAM,CAACP,OAAO,CAACtC,WAAW,EAAE,CAACG,QAAQ,CAACzC,aAAa,CAACsC,WAAW,EAAE,CAAE,IACrF6C,MAAM,CAACJ,SAAS,IAAII,MAAM,CAACJ,SAAS,CAACzC,WAAW,EAAE,CAACG,QAAQ,CAACzC,aAAa,CAACsC,WAAW,EAAE,CAAE,CAC3F;MACH;MAEA;MACA,MAAMS,UAAU,GAAG,CAACmB,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC1D,gBAAgB,CAACJ,QAAQ;MACnE,MAAM4C,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACvC,gBAAgB,CAACJ,QAAQ;MAC5D,MAAMgF,YAAY,GAAGU,eAAe,CAAC7C,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;MAEhE;MACA,IAAI,CAAClD,0BAA0B,GAAGsF,YAAY;MAC9C,IAAI,CAAC5E,gBAAgB,CAACH,UAAU,GAAGyF,eAAe,CAACnD,MAAM;MACzD,IAAI,CAACnC,gBAAgB,CAACF,UAAU,GAAGZ,IAAI,CAACkD,IAAI,CAACkD,eAAe,CAACnD,MAAM,GAAG,IAAI,CAACnC,gBAAgB,CAACJ,QAAQ,CAAC;MACrG,IAAI,CAACI,gBAAgB,CAACL,WAAW,GAAG+D,SAAS;IAC/C,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA6B,qBAAqBA,CAAC1D,OAAe;IACnC,IAAI,CAACtC,mBAAmB,GAAGsC,OAAO;IAClC,IAAI,IAAI,CAACxC,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC+B,UAAU,EAAE;MAC7D,IAAI,CAACmC,mBAAmB,CAAC,IAAI,CAAClE,gBAAgB,CAAC+B,UAAU,EAAE,CAAC,EAAES,OAAO,CAAC;IACxE;EACF;EAEA;EACA2B,sBAAsBA,CAAA;IACpB;IACA,IAAI,IAAI,CAAClE,0BAA0B,CAAC6C,MAAM,GAAG,CAAC,EAAE;MAC9C;IACF;IAEA;IACA,MAAMqD,OAAO,GAAG,IAAI,CAACC,sBAAsB;IAC3C,IAAI,CAACzF,gBAAgB,CAACH,UAAU,GAAG2F,OAAO,CAACrD,MAAM;IACjD,IAAI,CAACnC,gBAAgB,CAACF,UAAU,GAAGZ,IAAI,CAACkD,IAAI,CAAC,IAAI,CAACpC,gBAAgB,CAACH,UAAU,GAAG,IAAI,CAACG,gBAAgB,CAACJ,QAAQ,CAAC;IAC/G,IAAI,CAACI,gBAAgB,CAACL,WAAW,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC+F,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMF,OAAO,GAAG,IAAI,CAACC,sBAAsB;IAC3C,MAAMlD,UAAU,GAAG,CAAC,IAAI,CAACvC,gBAAgB,CAACL,WAAW,GAAG,CAAC,IAAI,IAAI,CAACK,gBAAgB,CAACJ,QAAQ;IAC3F,MAAM4C,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACvC,gBAAgB,CAACJ,QAAQ;IAC5D,IAAI,CAACK,gBAAgB,GAAGuF,OAAO,CAAC/C,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC7D;EAEA;EACAmD,cAAcA,CAAChD,IAAY;IACzB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC3C,gBAAgB,CAACF,UAAU,EAAE;MACzD;MACA,IAAI,IAAI,CAACT,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC+B,UAAU,IAAI,IAAI,CAAC9B,0BAA0B,CAAC6C,MAAM,GAAG,CAAC,EAAE;QAC3G,IAAI,CAACoB,mBAAmB,CAAC,IAAI,CAAClE,gBAAgB,CAAC+B,UAAU,EAAEuB,IAAI,EAAE,IAAI,CAACpD,mBAAmB,CAAC;MAC5F,CAAC,MAAM;QACL;QACA,IAAI,CAACS,gBAAgB,CAACL,WAAW,GAAGgD,IAAI;QACxC,IAAI,CAAC+C,sBAAsB,EAAE;MAC/B;IACF;EACF;EAEA;EACAE,oBAAoBA,CAAA;IAClB,MAAM/C,KAAK,GAAa,EAAE;IAC1B,MAAM/C,UAAU,GAAG,IAAI,CAACE,gBAAgB,CAACF,UAAU;IACnD,MAAMH,WAAW,GAAG,IAAI,CAACK,gBAAgB,CAACL,WAAW;IAErD;IACA,MAAMmD,SAAS,GAAG5D,IAAI,CAACmD,GAAG,CAAC,CAAC,EAAE1C,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAMoD,OAAO,GAAG7D,IAAI,CAAC8D,GAAG,CAAClD,UAAU,EAAEH,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIsD,CAAC,GAAGH,SAAS,EAAEG,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCJ,KAAK,CAACK,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOJ,KAAK;EACd;EAEA;EACAgD,OAAOA,CAAA;IACL,IAAI,CAAC5G,KAAK,CAACqE,IAAI,EAAE;EACnB;EAEA;EACAwC,gBAAgBA,CAACC,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACA,MAAMG,kBAAkB,GAAwB;MAC9C7E,WAAW,EAAE0E;KACd;IAED;IACA,IAAI,CAACjH,eAAe,CAACqH,kCAAkC,CAAC;MACtDvF,IAAI,EAAEsF;KACP,CAAC,CAACrF,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B;UACA;UACA,IAAI,CAACb,aAAa,EAAE;UAEpB;UACA,IAAI,IAAI,CAACd,gBAAgB,EAAE+B,UAAU,KAAK2E,UAAU,EAAE;YACpD,IAAI,CAAC1G,gBAAgB,GAAG,IAAI;UAC9B;QACF,CAAC,MAAM;UACL;QAAA;MAEJ,CAAC;MACDsC,KAAK,EAAEA,CAAA,KAAK;QACV;MAAA;KAEH,CAAC;EACJ;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA;EACAyE,mBAAmBA,CAAA;IACjB,IAAI,CAAC/G,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAIoG,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACpG,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACD,eAAe,CAAC2C,MAAM,CAACsE,CAAC,IAAIA,CAAC,CAACjF,UAAU,KAAK,IAAI,CAAC/B,gBAAiB,CAAC+B,UAAU,CAAC;EAC7F;EAEA;EACAkF,iBAAiBA,CAACC,KAAa,EAAEvE,QAAkB;IACjD,OAAOA,QAAQ,CAACZ,UAAU,IAAImF,KAAK;EACrC;CACD;AA3cUC,UAAA,EAARlI,KAAK,EAAE,C,4DAA0B;AACxBkI,UAAA,EAATjI,MAAM,EAAE,C,8DAA+C;AAC9CiI,UAAA,EAATjI,MAAM,EAAE,C,qDAAkC;AAHhCK,uBAAuB,GAAA4H,UAAA,EAPnCpI,SAAS,CAAC;EACTqI,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC,CAAC;EAC/CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACrI,YAAY,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc;CAClE,CAAC,C,EACWC,uBAAuB,CA4cnC;;AAsCD;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}