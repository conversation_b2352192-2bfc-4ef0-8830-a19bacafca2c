{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { PaginationComponent } from 'src/app/pages/components/pagination/pagination.component';\nimport { tap } from 'rxjs/operators';\nlet SpacePickerComponent = class SpacePickerComponent {\n  constructor(spaceService) {\n    this.spaceService = spaceService;\n    this.selectedItems = [];\n    this.multiple = true;\n    this.placeholder = '請選擇空間';\n    this.selectionChange = new EventEmitter();\n    // 搜尋相關屬性\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.isLoading = false;\n    // 分頁相關屬性\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.totalRecords = 0;\n    // 資料相關屬性\n    this.availableSpaces = [];\n    this.allSelected = false;\n  }\n  ngOnInit() {\n    this.loadAvailableSpaces();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    this.isLoading = true;\n    const request = {\n      CPart: this.searchKeyword || null,\n      CLocation: this.searchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this.spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n        this.updateAllSelectedState();\n      }\n      this.isLoading = false;\n    })).subscribe({\n      next: () => {},\n      error: () => {\n        this.isLoading = false;\n      }\n    });\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 重置搜尋\n  onReset() {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 切換空間選擇\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n        if (this.multiple) {\n          this.selectedItems.push({\n            ...space\n          });\n        } else {\n          this.selectedItems = [{\n            ...space\n          }];\n          // 單選模式下，取消其他選項\n          this.availableSpaces.forEach(s => {\n            if (s.CSpaceID !== space.CSpaceID) {\n              s.selected = false;\n            }\n          });\n        }\n      }\n    } else {\n      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 全選/取消全選\n  toggleAllSpaces() {\n    this.allSelected = !this.allSelected;\n    this.availableSpaces.forEach(space => {\n      if (this.allSelected && !space.selected) {\n        space.selected = true;\n        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedItems.push({\n            ...space\n          });\n        }\n      } else if (!this.allSelected && space.selected) {\n        space.selected = false;\n        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 移除已選空間\n  removeSelectedSpace(space) {\n    this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    // 更新可用列表中的選中狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n  // 更新全選狀態\n  updateAllSelectedState() {\n    this.allSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  // 分頁變更 - 與 ngx-pagination 組件兼容\n  onPageChange(page) {\n    this.pageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  // 計算總頁數\n  get totalPages() {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n};\n__decorate([Input()], SpacePickerComponent.prototype, \"selectedItems\", void 0);\n__decorate([Input()], SpacePickerComponent.prototype, \"multiple\", void 0);\n__decorate([Input()], SpacePickerComponent.prototype, \"placeholder\", void 0);\n__decorate([Output()], SpacePickerComponent.prototype, \"selectionChange\", void 0);\nSpacePickerComponent = __decorate([Component({\n  selector: 'app-space-picker',\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule, PaginationComponent],\n  templateUrl: './space-picker.component.html',\n  styleUrls: ['./space-picker.component.scss']\n})], SpacePickerComponent);\nexport { SpacePickerComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "CommonModule", "FormsModule", "NbFormFieldModule", "NbInputModule", "NbButtonModule", "NbIconModule", "PaginationComponent", "tap", "SpacePickerComponent", "constructor", "spaceService", "selectedItems", "multiple", "placeholder", "selectionChange", "searchKeyword", "searchLocation", "isLoading", "pageIndex", "pageSize", "totalRecords", "availableSpaces", "allSelected", "ngOnInit", "loadAvailableSpaces", "request", "<PERSON>art", "CLocation", "CStatus", "PageIndex", "PageSize", "apiSpaceGetSpaceListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CSpaceID", "selected", "some", "s", "TotalItems", "updateAllSelectedState", "subscribe", "next", "error", "onSearch", "onReset", "toggleSpaceSelection", "space", "push", "for<PERSON>ach", "filter", "emit", "toggleAllSpaces", "removeSelectedSpace", "availableSpace", "find", "length", "every", "onPageChange", "page", "totalPages", "Math", "ceil", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-picker\\space-picker.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { PaginationComponent } from 'src/app/pages/components/pagination/pagination.component';\nimport { SpaceService } from 'src/services/api/services/space.service';\nimport { GetSpaceListResponse } from 'src/services/api/models';\nimport { tap } from 'rxjs/operators';\n\nexport interface SpacePickerItem {\n  CSpaceID: number;\n  CPart: string;\n  CLocation?: string | null;\n  selected?: boolean;\n}\n\n@Component({\n  selector: 'app-space-picker',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    NbFormFieldModule,\n    NbInputModule,\n    NbButtonModule,\n    NbIconModule,\n    PaginationComponent\n  ],\n  templateUrl: './space-picker.component.html',\n  styleUrls: ['./space-picker.component.scss']\n})\nexport class SpacePickerComponent implements OnInit {\n  @Input() selectedItems: SpacePickerItem[] = [];\n  @Input() multiple: boolean = true;\n  @Input() placeholder: string = '請選擇空間';\n  @Output() selectionChange = new EventEmitter<SpacePickerItem[]>();\n\n  // 搜尋相關屬性\n  searchKeyword: string = '';\n  searchLocation: string = '';\n  isLoading: boolean = false;\n\n  // 分頁相關屬性\n  pageIndex = 1;\n  pageSize = 10;\n  totalRecords = 0;\n\n  // 資料相關屬性\n  availableSpaces: SpacePickerItem[] = [];\n  allSelected = false;\n\n  constructor(private spaceService: SpaceService) { }\n\n  ngOnInit(): void {\n    this.loadAvailableSpaces();\n  }\n\n  // 載入可用空間列表\n  loadAvailableSpaces(): void {\n    this.isLoading = true;\n    const request = {\n      CPart: this.searchKeyword || null,\n      CLocation: this.searchLocation || null,\n      CStatus: 1, // 只顯示啟用的空間\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n\n    this.spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\n      tap(response => {\n        if (response.StatusCode === 0) {\n          this.availableSpaces = response.Entries?.map(item => ({\n            CSpaceID: item.CSpaceID!,\n            CPart: item.CPart!,\n            CLocation: item.CLocation,\n            selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)\n          })) || [];\n          this.totalRecords = response.TotalItems || 0;\n          this.updateAllSelectedState();\n        }\n        this.isLoading = false;\n      })\n    ).subscribe({\n      next: () => {},\n      error: () => {\n        this.isLoading = false;\n      }\n    });\n  }\n\n  // 搜尋功能\n  onSearch(): void {\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n\n  // 重置搜尋\n  onReset(): void {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.pageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n\n  // 切換空間選擇\n  toggleSpaceSelection(space: SpacePickerItem): void {\n    space.selected = !space.selected;\n\n    if (space.selected) {\n      if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n        if (this.multiple) {\n          this.selectedItems.push({ ...space });\n        } else {\n          this.selectedItems = [{ ...space }];\n          // 單選模式下，取消其他選項\n          this.availableSpaces.forEach(s => {\n            if (s.CSpaceID !== space.CSpaceID) {\n              s.selected = false;\n            }\n          });\n        }\n      }\n    } else {\n      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 全選/取消全選\n  toggleAllSpaces(): void {\n    this.allSelected = !this.allSelected;\n\n    this.availableSpaces.forEach(space => {\n      if (this.allSelected && !space.selected) {\n        space.selected = true;\n        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedItems.push({ ...space });\n        }\n      } else if (!this.allSelected && space.selected) {\n        space.selected = false;\n        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 移除已選空間\n  removeSelectedSpace(space: SpacePickerItem): void {\n    this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);\n\n    // 更新可用列表中的選中狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n\n    this.updateAllSelectedState();\n    this.selectionChange.emit([...this.selectedItems]);\n  }\n\n  // 更新全選狀態\n  updateAllSelectedState(): void {\n    this.allSelected = this.availableSpaces.length > 0 &&\n      this.availableSpaces.every(space => space.selected);\n  }\n\n  // 分頁變更 - 與 ngx-pagination 組件兼容\n  onPageChange(page: number): void {\n    this.pageIndex = page;\n    this.loadAvailableSpaces();\n  }\n\n  // 計算總頁數\n  get totalPages(): number {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;AAC/F,SAASC,mBAAmB,QAAQ,0DAA0D;AAG9F,SAASC,GAAG,QAAQ,gBAAgB;AAwB7B,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAoB/BC,YAAoBC,YAA0B;IAA1B,KAAAA,YAAY,GAAZA,YAAY;IAnBvB,KAAAC,aAAa,GAAsB,EAAE;IACrC,KAAAC,QAAQ,GAAY,IAAI;IACxB,KAAAC,WAAW,GAAW,OAAO;IAC5B,KAAAC,eAAe,GAAG,IAAIf,YAAY,EAAqB;IAEjE;IACA,KAAAgB,aAAa,GAAW,EAAE;IAC1B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,SAAS,GAAY,KAAK;IAE1B;IACA,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,YAAY,GAAG,CAAC;IAEhB;IACA,KAAAC,eAAe,GAAsB,EAAE;IACvC,KAAAC,WAAW,GAAG,KAAK;EAE+B;EAElDC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAA,mBAAmBA,CAAA;IACjB,IAAI,CAACP,SAAS,GAAG,IAAI;IACrB,MAAMQ,OAAO,GAAG;MACdC,KAAK,EAAE,IAAI,CAACX,aAAa,IAAI,IAAI;MACjCY,SAAS,EAAE,IAAI,CAACX,cAAc,IAAI,IAAI;MACtCY,OAAO,EAAE,CAAC;MAAE;MACZC,SAAS,EAAE,IAAI,CAACX,SAAS;MACzBY,QAAQ,EAAE,IAAI,CAACX;KAChB;IAED,IAAI,CAACT,YAAY,CAACqB,6BAA6B,CAAC;MAAEC,IAAI,EAAEP;IAAO,CAAE,CAAC,CAACQ,IAAI,CACrE1B,GAAG,CAAC2B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACd,eAAe,GAAGa,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDC,QAAQ,EAAED,IAAI,CAACC,QAAS;UACxBb,KAAK,EAAEY,IAAI,CAACZ,KAAM;UAClBC,SAAS,EAAEW,IAAI,CAACX,SAAS;UACzBa,QAAQ,EAAE,IAAI,CAAC7B,aAAa,CAAC8B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKD,IAAI,CAACC,QAAQ;SACpE,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAACnB,YAAY,GAAGc,QAAQ,CAACS,UAAU,IAAI,CAAC;QAC5C,IAAI,CAACC,sBAAsB,EAAE;MAC/B;MACA,IAAI,CAAC3B,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH,CAAC4B,SAAS,CAAC;MACVC,IAAI,EAAEA,CAAA,KAAK,CAAE,CAAC;MACdC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC9B,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;EACA+B,QAAQA,CAAA;IACN,IAAI,CAAC9B,SAAS,GAAG,CAAC;IAClB,IAAI,CAACM,mBAAmB,EAAE;EAC5B;EAEA;EACAyB,OAAOA,CAAA;IACL,IAAI,CAAClC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACE,SAAS,GAAG,CAAC;IAClB,IAAI,CAACM,mBAAmB,EAAE;EAC5B;EAEA;EACA0B,oBAAoBA,CAACC,KAAsB;IACzCA,KAAK,CAACX,QAAQ,GAAG,CAACW,KAAK,CAACX,QAAQ;IAEhC,IAAIW,KAAK,CAACX,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAAC7B,aAAa,CAAC8B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKY,KAAK,CAACZ,QAAQ,CAAC,EAAE;QAChE,IAAI,IAAI,CAAC3B,QAAQ,EAAE;UACjB,IAAI,CAACD,aAAa,CAACyC,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACvC,CAAC,MAAM;UACL,IAAI,CAACxC,aAAa,GAAG,CAAC;YAAE,GAAGwC;UAAK,CAAE,CAAC;UACnC;UACA,IAAI,CAAC9B,eAAe,CAACgC,OAAO,CAACX,CAAC,IAAG;YAC/B,IAAIA,CAAC,CAACH,QAAQ,KAAKY,KAAK,CAACZ,QAAQ,EAAE;cACjCG,CAAC,CAACF,QAAQ,GAAG,KAAK;YACpB;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC,MAAM;MACL,IAAI,CAAC7B,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC2C,MAAM,CAACZ,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKY,KAAK,CAACZ,QAAQ,CAAC;IACpF;IAEA,IAAI,CAACK,sBAAsB,EAAE;IAC7B,IAAI,CAAC9B,eAAe,CAACyC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC5C,aAAa,CAAC,CAAC;EACpD;EAEA;EACA6C,eAAeA,CAAA;IACb,IAAI,CAAClC,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,CAACD,eAAe,CAACgC,OAAO,CAACF,KAAK,IAAG;MACnC,IAAI,IAAI,CAAC7B,WAAW,IAAI,CAAC6B,KAAK,CAACX,QAAQ,EAAE;QACvCW,KAAK,CAACX,QAAQ,GAAG,IAAI;QACrB,IAAI,CAAC,IAAI,CAAC7B,aAAa,CAAC8B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKY,KAAK,CAACZ,QAAQ,CAAC,EAAE;UAChE,IAAI,CAAC5B,aAAa,CAACyC,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACvC;MACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC7B,WAAW,IAAI6B,KAAK,CAACX,QAAQ,EAAE;QAC9CW,KAAK,CAACX,QAAQ,GAAG,KAAK;QACtB,IAAI,CAAC7B,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC2C,MAAM,CAACZ,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKY,KAAK,CAACZ,QAAQ,CAAC;MACpF;IACF,CAAC,CAAC;IAEF,IAAI,CAACzB,eAAe,CAACyC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC5C,aAAa,CAAC,CAAC;EACpD;EAEA;EACA8C,mBAAmBA,CAACN,KAAsB;IACxC,IAAI,CAACxC,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC2C,MAAM,CAACZ,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKY,KAAK,CAACZ,QAAQ,CAAC;IAElF;IACA,MAAMmB,cAAc,GAAG,IAAI,CAACrC,eAAe,CAACsC,IAAI,CAACjB,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKY,KAAK,CAACZ,QAAQ,CAAC;IACpF,IAAImB,cAAc,EAAE;MAClBA,cAAc,CAAClB,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAACI,sBAAsB,EAAE;IAC7B,IAAI,CAAC9B,eAAe,CAACyC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC5C,aAAa,CAAC,CAAC;EACpD;EAEA;EACAiC,sBAAsBA,CAAA;IACpB,IAAI,CAACtB,WAAW,GAAG,IAAI,CAACD,eAAe,CAACuC,MAAM,GAAG,CAAC,IAChD,IAAI,CAACvC,eAAe,CAACwC,KAAK,CAACV,KAAK,IAAIA,KAAK,CAACX,QAAQ,CAAC;EACvD;EAEA;EACAsB,YAAYA,CAACC,IAAY;IACvB,IAAI,CAAC7C,SAAS,GAAG6C,IAAI;IACrB,IAAI,CAACvC,mBAAmB,EAAE;EAC5B;EAEA;EACA,IAAIwC,UAAUA,CAAA;IACZ,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC9C,YAAY,GAAG,IAAI,CAACD,QAAQ,CAAC;EACrD;CACD;AAnJUgD,UAAA,EAARtE,KAAK,EAAE,C,0DAAuC;AACtCsE,UAAA,EAARtE,KAAK,EAAE,C,qDAA0B;AACzBsE,UAAA,EAARtE,KAAK,EAAE,C,wDAA+B;AAC7BsE,UAAA,EAATrE,MAAM,EAAE,C,4DAAyD;AAJvDU,oBAAoB,GAAA2D,UAAA,EAfhCvE,SAAS,CAAC;EACTwE,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPtE,YAAY,EACZC,WAAW,EACXC,iBAAiB,EACjBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,mBAAmB,CACpB;EACDiE,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,+BAA+B;CAC5C,CAAC,C,EACWhE,oBAAoB,CAoJhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}