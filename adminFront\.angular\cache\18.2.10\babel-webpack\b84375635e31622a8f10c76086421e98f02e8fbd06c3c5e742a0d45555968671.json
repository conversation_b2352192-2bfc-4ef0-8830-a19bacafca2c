{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiRegularChangeItemCheckRegularChangePost$Json } from '../fn/regular-change-item/api-regular-change-item-check-regular-change-post-json';\nimport { apiRegularChangeItemCheckRegularChangePost$Plain } from '../fn/regular-change-item/api-regular-change-item-check-regular-change-post-plain';\nimport { apiRegularChangeItemGetBuildingSampleSelectionPost$Json } from '../fn/regular-change-item/api-regular-change-item-get-building-sample-selection-post-json';\nimport { apiRegularChangeItemGetBuildingSampleSelectionPost$Plain } from '../fn/regular-change-item/api-regular-change-item-get-building-sample-selection-post-plain';\nimport { apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Json } from '../fn/regular-change-item/api-regular-change-item-get-list-regular-change-detail-by-item-id-post-json';\nimport { apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Plain } from '../fn/regular-change-item/api-regular-change-item-get-list-regular-change-detail-by-item-id-post-plain';\nimport { apiRegularChangeItemGetListRegularChangeItemPost$Json } from '../fn/regular-change-item/api-regular-change-item-get-list-regular-change-item-post-json';\nimport { apiRegularChangeItemGetListRegularChangeItemPost$Plain } from '../fn/regular-change-item/api-regular-change-item-get-list-regular-change-item-post-plain';\nimport { apiRegularChangeItemGetSumaryRegularChangeItemPost$Json } from '../fn/regular-change-item/api-regular-change-item-get-sumary-regular-change-item-post-json';\nimport { apiRegularChangeItemGetSumaryRegularChangeItemPost$Plain } from '../fn/regular-change-item/api-regular-change-item-get-sumary-regular-change-item-post-plain';\nimport { apiRegularChangeItemSaveRegularChangeDetailPost$Json } from '../fn/regular-change-item/api-regular-change-item-save-regular-change-detail-post-json';\nimport { apiRegularChangeItemSaveRegularChangeDetailPost$Plain } from '../fn/regular-change-item/api-regular-change-item-save-regular-change-detail-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let RegularChangeItemService = /*#__PURE__*/(() => {\n  class RegularChangeItemService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiRegularChangeItemGetListRegularChangeItemPost()` */\n    static {\n      this.ApiRegularChangeItemGetListRegularChangeItemPostPath = '/api/RegularChangeItem/GetListRegularChangeItem';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemGetListRegularChangeItemPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemGetListRegularChangeItemPost$Plain$Response(params, context) {\n      return apiRegularChangeItemGetListRegularChangeItemPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemGetListRegularChangeItemPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemGetListRegularChangeItemPost$Plain(params, context) {\n      return this.apiRegularChangeItemGetListRegularChangeItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemGetListRegularChangeItemPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemGetListRegularChangeItemPost$Json$Response(params, context) {\n      return apiRegularChangeItemGetListRegularChangeItemPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemGetListRegularChangeItemPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemGetListRegularChangeItemPost$Json(params, context) {\n      return this.apiRegularChangeItemGetListRegularChangeItemPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRegularChangeItemGetListRegularChangeDetailByItemIdPost()` */\n    static {\n      this.ApiRegularChangeItemGetListRegularChangeDetailByItemIdPostPath = '/api/RegularChangeItem/GetListRegularChangeDetailByItemId';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Plain$Response(params, context) {\n      return apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Plain(params, context) {\n      return this.apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Json$Response(params, context) {\n      return apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Json(params, context) {\n      return this.apiRegularChangeItemGetListRegularChangeDetailByItemIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRegularChangeItemSaveRegularChangeDetailPost()` */\n    static {\n      this.ApiRegularChangeItemSaveRegularChangeDetailPostPath = '/api/RegularChangeItem/SaveRegularChangeDetail';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemSaveRegularChangeDetailPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemSaveRegularChangeDetailPost$Plain$Response(params, context) {\n      return apiRegularChangeItemSaveRegularChangeDetailPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemSaveRegularChangeDetailPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemSaveRegularChangeDetailPost$Plain(params, context) {\n      return this.apiRegularChangeItemSaveRegularChangeDetailPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemSaveRegularChangeDetailPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemSaveRegularChangeDetailPost$Json$Response(params, context) {\n      return apiRegularChangeItemSaveRegularChangeDetailPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemSaveRegularChangeDetailPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemSaveRegularChangeDetailPost$Json(params, context) {\n      return this.apiRegularChangeItemSaveRegularChangeDetailPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRegularChangeItemGetBuildingSampleSelectionPost()` */\n    static {\n      this.ApiRegularChangeItemGetBuildingSampleSelectionPostPath = '/api/RegularChangeItem/GetBuildingSampleSelection';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemGetBuildingSampleSelectionPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemGetBuildingSampleSelectionPost$Plain$Response(params, context) {\n      return apiRegularChangeItemGetBuildingSampleSelectionPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemGetBuildingSampleSelectionPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemGetBuildingSampleSelectionPost$Plain(params, context) {\n      return this.apiRegularChangeItemGetBuildingSampleSelectionPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemGetBuildingSampleSelectionPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemGetBuildingSampleSelectionPost$Json$Response(params, context) {\n      return apiRegularChangeItemGetBuildingSampleSelectionPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemGetBuildingSampleSelectionPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularChangeItemGetBuildingSampleSelectionPost$Json(params, context) {\n      return this.apiRegularChangeItemGetBuildingSampleSelectionPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRegularChangeItemCheckRegularChangePost()` */\n    static {\n      this.ApiRegularChangeItemCheckRegularChangePostPath = '/api/RegularChangeItem/CheckRegularChange';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemCheckRegularChangePost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemCheckRegularChangePost$Plain$Response(params, context) {\n      return apiRegularChangeItemCheckRegularChangePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemCheckRegularChangePost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemCheckRegularChangePost$Plain(params, context) {\n      return this.apiRegularChangeItemCheckRegularChangePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemCheckRegularChangePost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemCheckRegularChangePost$Json$Response(params, context) {\n      return apiRegularChangeItemCheckRegularChangePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemCheckRegularChangePost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemCheckRegularChangePost$Json(params, context) {\n      return this.apiRegularChangeItemCheckRegularChangePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRegularChangeItemGetSumaryRegularChangeItemPost()` */\n    static {\n      this.ApiRegularChangeItemGetSumaryRegularChangeItemPostPath = '/api/RegularChangeItem/GetSumaryRegularChangeItem';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemGetSumaryRegularChangeItemPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemGetSumaryRegularChangeItemPost$Plain$Response(params, context) {\n      return apiRegularChangeItemGetSumaryRegularChangeItemPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemGetSumaryRegularChangeItemPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemGetSumaryRegularChangeItemPost$Plain(params, context) {\n      return this.apiRegularChangeItemGetSumaryRegularChangeItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularChangeItemGetSumaryRegularChangeItemPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemGetSumaryRegularChangeItemPost$Json$Response(params, context) {\n      return apiRegularChangeItemGetSumaryRegularChangeItemPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularChangeItemGetSumaryRegularChangeItemPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiRegularChangeItemGetSumaryRegularChangeItemPost$Json(params, context) {\n      return this.apiRegularChangeItemGetSumaryRegularChangeItemPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function RegularChangeItemService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RegularChangeItemService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RegularChangeItemService,\n        factory: RegularChangeItemService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RegularChangeItemService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}