{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class Track {}\nexport let PlayerService = /*#__PURE__*/(() => {\n  class PlayerService {\n    constructor() {\n      this.playlist = [{\n        name: 'Don\\'t Wanna Fight',\n        artist: 'Alabama Shakes',\n        url: 'https://p.scdn.co/mp3-preview/6156cdbca425a894972c02fca9d76c0b70e001af',\n        cover: 'assets/images/cover1.jpg'\n      }, {\n        name: '<PERSON><PERSON>',\n        artist: 'Daft Punk',\n        url: 'https://p.scdn.co/mp3-preview/92a04c7c0e96bf93a1b1b1cae7dfff1921969a7b',\n        cover: 'assets/images/cover2.jpg'\n      }, {\n        name: 'Come Together',\n        artist: 'Beatles',\n        url: 'https://p.scdn.co/mp3-preview/83090a4db6899eaca689ae35f69126dbe65d94c9',\n        cover: 'assets/images/cover3.jpg'\n      }];\n    }\n    random() {\n      this.current = Math.floor(Math.random() * this.playlist.length);\n      return this.playlist[this.current];\n    }\n    next() {\n      return this.getNextTrack();\n    }\n    prev() {\n      return this.getPrevTrack();\n    }\n    getNextTrack() {\n      if (this.current === this.playlist.length - 1) {\n        this.current = 0;\n      } else {\n        this.current++;\n      }\n      return this.playlist[this.current];\n    }\n    getPrevTrack() {\n      if (this.current === 0) {\n        this.current = this.playlist.length - 1;\n      } else {\n        this.current--;\n      }\n      return this.playlist[this.current];\n    }\n    static {\n      this.ɵfac = function PlayerService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PlayerService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PlayerService,\n        factory: PlayerService.ɵfac\n      });\n    }\n  }\n  return PlayerService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}