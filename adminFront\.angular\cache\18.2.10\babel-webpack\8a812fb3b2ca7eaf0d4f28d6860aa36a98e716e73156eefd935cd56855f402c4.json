{"ast": null, "code": "import { CryptoService } from \"./crypto.service\";\nimport * as i0 from \"@angular/core\";\nexport let LocalStorageService = /*#__PURE__*/(() => {\n  class LocalStorageService {\n    static AddLocalStorage(key, value) {\n      localStorage.setItem(key, this._Add(value));\n    }\n    static RemoveLocalStorage(key) {\n      localStorage.removeItem(key);\n    }\n    static GetLocalStorage(key) {\n      let resultStr = this._Get(localStorage.getItem(key) || \"\");\n      return resultStr;\n    }\n    static ClearLocalStorage() {\n      localStorage.clear();\n    }\n    static AddSessionStorage(key, value) {\n      sessionStorage.setItem(key, this._Add(value));\n    }\n    static RemoveSessionStorage(key) {\n      sessionStorage.removeItem(key);\n    }\n    static GetSessionStorage(key) {\n      let resultStr = this._Get(sessionStorage.getItem(key) || \"\");\n      return resultStr;\n    }\n    static _Add(value) {\n      let result = CryptoService.EncryptUsingAES256(value);\n      return result;\n    }\n    static _Get(value) {\n      let result = CryptoService.DecryptUsingAES256(value);\n      result.split(\"\\\\\").join(\"\");\n      result.slice(0, 1);\n      result.slice(result.length - 1, 1);\n      return result == \"\" ? \"\" : JSON.parse(result);\n    }\n    static {\n      this.ɵfac = function LocalStorageService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || LocalStorageService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: LocalStorageService,\n        factory: LocalStorageService.ɵfac,\n        providedIn: \"root\"\n      });\n    }\n  }\n  return LocalStorageService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}