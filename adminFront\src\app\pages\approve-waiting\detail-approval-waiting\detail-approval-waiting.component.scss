/* 檔案項目樣式 */
.file-item {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  background: white;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
  }
}

.file-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 8px;
  transition: background-color 0.3s ease;

  &:hover {
    background: #e5e7eb;
  }
}

.file-info {
  flex-grow: 1;
  min-width: 0;
}

.file-name {
  color: #1d4ed8;
  font-weight: 500;
  transition: color 0.3s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:hover {
    color: #1e40af;
  }
}

.file-type-badge {
  padding: 4px 8px;
  font-size: 12px;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 999px;
  margin-left: 8px;
}

.file-action-hint {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
}

.file-action-icon {
  color: #9ca3af;
  transition: color 0.3s ease;

  &:hover {
    color: #6b7280;
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .file-item {
    padding: 8px;
    margin-bottom: 8px;
  }

  .file-icon {
    width: 40px;
    height: 40px;
  }

  .file-name {
    font-size: 14px;
  }

  .file-action-hint {
    font-size: 12px;
  }
}

/* 無檔案狀態樣式 */
.no-files {
  color: #6b7280;
  font-style: italic;
  padding: 20px;
  text-align: center;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #d1d5db;
}