{"ast": null, "code": "export var STORAGE_KEY = /*#__PURE__*/function (STORAGE_KEY) {\n  STORAGE_KEY[\"TOKEN\"] = \"cdp_token\";\n  STORAGE_KEY[\"ALLOW\"] = \"cdp_allow\";\n  STORAGE_KEY[\"BUID\"] = \"cdp_buId\";\n  STORAGE_KEY[\"HOUSE_SEARCH\"] = \"HOUSE_SEARCH\";\n  STORAGE_KEY[\"REVIEW_SEARCH\"] = \"REVIEW_SEARCH\";\n  return STORAGE_KEY;\n}(STORAGE_KEY || {});\nexport const DEFAULT_DATE = {\n  START_DATE: \"01/01/1990\",\n  END_DATE: \"12/31/2100\"\n};\nexport class FormBodyBuilder {\n  static BuildBodyContent(value) {\n    var _bodyContent = {};\n    if (value !== null && value !== undefined) {\n      const formData = new FormData();\n      for (const key of Object.keys(value)) {\n        var val = value[key];\n        if (val instanceof Array) {\n          var count = 0;\n          for (const v of val) {\n            if (v instanceof Object) {\n              for (const objName of Object.keys(v)) {\n                const toAppend = v[objName];\n                var keyName = `${key}[${count}].${objName}`;\n                if (toAppend !== null) {\n                  formData.append(keyName, toAppend);\n                }\n              }\n            } else {\n              for (const v of val) {\n                const toAppend = this._formDataValue(v);\n                if (toAppend !== null) {\n                  formData.append(key, toAppend);\n                  val = val.filter(x => x != v);\n                }\n              }\n            }\n            count++;\n          }\n        } else {\n          const toAppend = this._formDataValue(val);\n          if (toAppend !== null) {\n            formData.set(key, toAppend);\n          }\n        }\n      }\n      console.log(_bodyContent);\n      _bodyContent = formData;\n    }\n    return _bodyContent;\n  }\n  static _formDataValue(value) {\n    if (value === null || value === undefined) {\n      return null;\n    }\n    if (value instanceof Blob) {\n      return value;\n    }\n    if (typeof value === 'object') {\n      return JSON.stringify(value);\n    }\n    return String(value);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}