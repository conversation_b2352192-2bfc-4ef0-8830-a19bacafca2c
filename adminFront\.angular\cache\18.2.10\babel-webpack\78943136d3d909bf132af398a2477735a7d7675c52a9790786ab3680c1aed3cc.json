{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let Base64ImagePipe = /*#__PURE__*/(() => {\n  class Base64ImagePipe {\n    transform(value, imageType = 'png') {\n      if (!value) {\n        return '';\n      }\n      // 如果已經包含前綴，直接返回\n      if (value.startsWith('data:image/')) {\n        return value;\n      }\n      // 如果是 HTTP/HTTPS URL，直接返回\n      if (value.startsWith('http://') || value.startsWith('https://')) {\n        return value;\n      }\n      // 添加 Base64 前綴\n      return `data:image/${imageType};base64,${value}`;\n    }\n    static {\n      this.ɵfac = function Base64ImagePipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || Base64ImagePipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"base64Image\",\n        type: Base64ImagePipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return Base64ImagePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}