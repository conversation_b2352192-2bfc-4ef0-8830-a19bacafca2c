.image-table {
  width: 50px;
  height: 50px;
  cursor: pointer;
}

.empty-image {
  color: red;
}

.fit-size {
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
}

// 圖片綁定功能樣式
.image-grid-item {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &.selected {
    border-color: #3366ff;
    background-color: #f0f7ff;
    box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.2);
  }
}

.image-checkbox {
  border-color: #ccc;
  background-color: white;
  transition: all 0.2s ease;

  &.checked {
    background-color: #3366ff;
    border-color: #3366ff;
  }
}

.image-thumbnail {
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.image-preview-container {
  max-height: 480px;
  overflow-y: auto !important;
  overflow-x: hidden;

  /* 自定義滑軌樣式 */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }

  /* 確保在各種瀏覽器中都能滑動 */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease-in-out;

  &:focus {
    outline: none;
    border-color: #3366ff;
    box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.1);
  }
}

.btn-image-action {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
  }

  &.btn-xs {
    padding: 2px 6px;
    font-size: 10px;
  }
}

/* 確保 Flexbox 正確運作 */
.d-flex.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
  min-height: 0;
}

/* 圖片綁定對話框特定樣式 */
nb-card.w-\[900px\] {
  .nb-card-body {
    height: calc(700px - 120px) !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
  }
}

/* 確保滑動容器可以正常運作 */
.flex-shrink-0 {
  flex-shrink: 0;
}

/* 強制滑動容器樣式 */
.image-preview-container.flex-1 {
  flex: 1 1 auto;
  min-height: 0;
  height: auto;
  overflow-y: scroll !important;
  overflow-x: hidden !important;
}

/* Grid 容器確保內容能夠被滑動 */
.grid.grid-cols-4 {
  min-height: min-content;
}

/* 確保在 Angular 材質設計中正常運作 */
::ng-deep nb-card-body {
  .image-preview-container {
    max-height: none !important;
    height: 100% !important;
  }
}

/* Picklist 特定樣式 */
.picklist-container {
  display: flex;
  gap: 1rem;
  height: 100%;
  min-height: 400px;
}

.picklist-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  background-color: #fafafa;

  h6 {
    margin-bottom: 0.75rem;
    color: #333;
    font-weight: 600;
  }
}

.picklist-controls {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 80px;
  gap: 0.5rem;

  .btn {
    width: 100%;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  hr {
    width: 100%;
    margin: 0.5rem 0;
    border-color: #ddd;
  }
}

.image-grid-item {
  &.hover\:bg-gray-50:hover {
    background-color: #f9f9f9;
  }

  &.border-success {
    border-color: #28a745 !important;
    background-color: #f8fff9;
  }

  &.bg-green-50 {
    background-color: #f0fff4;
  }
}

.badge {
  &.badge-success {
    background-color: #28a745;
    color: white;
  }

  &.badge-info {
    background-color: #17a2b8;
    color: white;
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .picklist-container {
    flex-direction: column;
    gap: 0.5rem;
  }

  .picklist-controls {
    flex-direction: row;
    width: 100%;
    height: auto;

    .btn {
      width: auto;
      min-width: 60px;
    }
  }

  .grid.grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}