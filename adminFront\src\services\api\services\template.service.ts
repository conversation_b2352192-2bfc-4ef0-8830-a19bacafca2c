/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { apiTemplateDeleteTemplatePost$Json } from '../fn/template/api-template-delete-template-post-json';
import { ApiTemplateDeleteTemplatePost$Json$Params } from '../fn/template/api-template-delete-template-post-json';
import { apiTemplateDeleteTemplatePost$Plain } from '../fn/template/api-template-delete-template-post-plain';
import { ApiTemplateDeleteTemplatePost$Plain$Params } from '../fn/template/api-template-delete-template-post-plain';
import { apiTemplateGetTemplateByIdPost$Json } from '../fn/template/api-template-get-template-by-id-post-json';
import { ApiTemplateGetTemplateByIdPost$Json$Params } from '../fn/template/api-template-get-template-by-id-post-json';
import { apiTemplateGetTemplateByIdPost$Plain } from '../fn/template/api-template-get-template-by-id-post-plain';
import { ApiTemplateGetTemplateByIdPost$Plain$Params } from '../fn/template/api-template-get-template-by-id-post-plain';
import { apiTemplateGetTemplateDetailByIdPost$Json } from '../fn/template/api-template-get-template-detail-by-id-post-json';
import { ApiTemplateGetTemplateDetailByIdPost$Json$Params } from '../fn/template/api-template-get-template-detail-by-id-post-json';
import { apiTemplateGetTemplateDetailByIdPost$Plain } from '../fn/template/api-template-get-template-detail-by-id-post-plain';
import { ApiTemplateGetTemplateDetailByIdPost$Plain$Params } from '../fn/template/api-template-get-template-detail-by-id-post-plain';
import { apiTemplateGetTemplateListForCommonPost$Json } from '../fn/template/api-template-get-template-list-for-common-post-json';
import { ApiTemplateGetTemplateListForCommonPost$Json$Params } from '../fn/template/api-template-get-template-list-for-common-post-json';
import { apiTemplateGetTemplateListForCommonPost$Plain } from '../fn/template/api-template-get-template-list-for-common-post-plain';
import { ApiTemplateGetTemplateListForCommonPost$Plain$Params } from '../fn/template/api-template-get-template-list-for-common-post-plain';
import { apiTemplateGetTemplateListPost$Json } from '../fn/template/api-template-get-template-list-post-json';
import { ApiTemplateGetTemplateListPost$Json$Params } from '../fn/template/api-template-get-template-list-post-json';
import { apiTemplateGetTemplateListPost$Plain } from '../fn/template/api-template-get-template-list-post-plain';
import { ApiTemplateGetTemplateListPost$Plain$Params } from '../fn/template/api-template-get-template-list-post-plain';
import { apiTemplateSaveTemplatePost$Json } from '../fn/template/api-template-save-template-post-json';
import { ApiTemplateSaveTemplatePost$Json$Params } from '../fn/template/api-template-save-template-post-json';
import { apiTemplateSaveTemplatePost$Plain } from '../fn/template/api-template-save-template-post-plain';
import { ApiTemplateSaveTemplatePost$Plain$Params } from '../fn/template/api-template-save-template-post-plain';
import { StringResponseBase } from '../models/string-response-base';
import { TemplateDetailItemListResponseBase } from '../models/template-detail-item-list-response-base';
import { TemplateGetListResponseListResponseBase } from '../models/template-get-list-response-list-response-base';
import { TemplateGetListResponseResponseBase } from '../models/template-get-list-response-response-base';

@Injectable({ providedIn: 'root' })
export class TemplateService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `apiTemplateGetTemplateListPost()` */
  static readonly ApiTemplateGetTemplateListPostPath = '/api/Template/GetTemplateList';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateGetTemplateListPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateListPost$Plain$Response(params?: ApiTemplateGetTemplateListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseListResponseBase>> {
    return apiTemplateGetTemplateListPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateGetTemplateListPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateListPost$Plain(params?: ApiTemplateGetTemplateListPost$Plain$Params, context?: HttpContext): Observable<TemplateGetListResponseListResponseBase> {
    return this.apiTemplateGetTemplateListPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<TemplateGetListResponseListResponseBase>): TemplateGetListResponseListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateGetTemplateListPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateListPost$Json$Response(params?: ApiTemplateGetTemplateListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseListResponseBase>> {
    return apiTemplateGetTemplateListPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateGetTemplateListPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateListPost$Json(params?: ApiTemplateGetTemplateListPost$Json$Params, context?: HttpContext): Observable<TemplateGetListResponseListResponseBase> {
    return this.apiTemplateGetTemplateListPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<TemplateGetListResponseListResponseBase>): TemplateGetListResponseListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiTemplateGetTemplateByIdPost()` */
  static readonly ApiTemplateGetTemplateByIdPostPath = '/api/Template/GetTemplateById';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateGetTemplateByIdPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateByIdPost$Plain$Response(params?: ApiTemplateGetTemplateByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseResponseBase>> {
    return apiTemplateGetTemplateByIdPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateGetTemplateByIdPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateByIdPost$Plain(params?: ApiTemplateGetTemplateByIdPost$Plain$Params, context?: HttpContext): Observable<TemplateGetListResponseResponseBase> {
    return this.apiTemplateGetTemplateByIdPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<TemplateGetListResponseResponseBase>): TemplateGetListResponseResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateGetTemplateByIdPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateByIdPost$Json$Response(params?: ApiTemplateGetTemplateByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseResponseBase>> {
    return apiTemplateGetTemplateByIdPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateGetTemplateByIdPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateByIdPost$Json(params?: ApiTemplateGetTemplateByIdPost$Json$Params, context?: HttpContext): Observable<TemplateGetListResponseResponseBase> {
    return this.apiTemplateGetTemplateByIdPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<TemplateGetListResponseResponseBase>): TemplateGetListResponseResponseBase => r.body)
    );
  }

  /** Path part for operation `apiTemplateDeleteTemplatePost()` */
  static readonly ApiTemplateDeleteTemplatePostPath = '/api/Template/DeleteTemplate';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateDeleteTemplatePost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateDeleteTemplatePost$Plain$Response(params?: ApiTemplateDeleteTemplatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiTemplateDeleteTemplatePost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateDeleteTemplatePost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateDeleteTemplatePost$Plain(params?: ApiTemplateDeleteTemplatePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiTemplateDeleteTemplatePost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateDeleteTemplatePost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateDeleteTemplatePost$Json$Response(params?: ApiTemplateDeleteTemplatePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiTemplateDeleteTemplatePost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateDeleteTemplatePost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateDeleteTemplatePost$Json(params?: ApiTemplateDeleteTemplatePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiTemplateDeleteTemplatePost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /** Path part for operation `apiTemplateSaveTemplatePost()` */
  static readonly ApiTemplateSaveTemplatePostPath = '/api/Template/SaveTemplate';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateSaveTemplatePost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateSaveTemplatePost$Plain$Response(params?: ApiTemplateSaveTemplatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiTemplateSaveTemplatePost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateSaveTemplatePost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateSaveTemplatePost$Plain(params?: ApiTemplateSaveTemplatePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiTemplateSaveTemplatePost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateSaveTemplatePost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateSaveTemplatePost$Json$Response(params?: ApiTemplateSaveTemplatePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiTemplateSaveTemplatePost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateSaveTemplatePost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateSaveTemplatePost$Json(params?: ApiTemplateSaveTemplatePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiTemplateSaveTemplatePost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /** Path part for operation `apiTemplateGetTemplateDetailByIdPost()` */
  static readonly ApiTemplateGetTemplateDetailByIdPostPath = '/api/Template/GetTemplateDetailById';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateGetTemplateDetailByIdPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateDetailByIdPost$Plain$Response(params?: ApiTemplateGetTemplateDetailByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateDetailItemListResponseBase>> {
    return apiTemplateGetTemplateDetailByIdPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateGetTemplateDetailByIdPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateDetailByIdPost$Plain(params?: ApiTemplateGetTemplateDetailByIdPost$Plain$Params, context?: HttpContext): Observable<TemplateDetailItemListResponseBase> {
    return this.apiTemplateGetTemplateDetailByIdPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<TemplateDetailItemListResponseBase>): TemplateDetailItemListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateGetTemplateDetailByIdPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateDetailByIdPost$Json$Response(params?: ApiTemplateGetTemplateDetailByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateDetailItemListResponseBase>> {
    return apiTemplateGetTemplateDetailByIdPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateGetTemplateDetailByIdPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateDetailByIdPost$Json(params?: ApiTemplateGetTemplateDetailByIdPost$Json$Params, context?: HttpContext): Observable<TemplateDetailItemListResponseBase> {
    return this.apiTemplateGetTemplateDetailByIdPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<TemplateDetailItemListResponseBase>): TemplateDetailItemListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiTemplateGetTemplateListForCommonPost()` */
  static readonly ApiTemplateGetTemplateListForCommonPostPath = '/api/Template/GetTemplateListForCommon';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateGetTemplateListForCommonPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateListForCommonPost$Plain$Response(params?: ApiTemplateGetTemplateListForCommonPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseListResponseBase>> {
    return apiTemplateGetTemplateListForCommonPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateGetTemplateListForCommonPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateListForCommonPost$Plain(params?: ApiTemplateGetTemplateListForCommonPost$Plain$Params, context?: HttpContext): Observable<TemplateGetListResponseListResponseBase> {
    return this.apiTemplateGetTemplateListForCommonPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<TemplateGetListResponseListResponseBase>): TemplateGetListResponseListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiTemplateGetTemplateListForCommonPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateListForCommonPost$Json$Response(params?: ApiTemplateGetTemplateListForCommonPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseListResponseBase>> {
    return apiTemplateGetTemplateListForCommonPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiTemplateGetTemplateListForCommonPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiTemplateGetTemplateListForCommonPost$Json(params?: ApiTemplateGetTemplateListForCommonPost$Json$Params, context?: HttpContext): Observable<TemplateGetListResponseListResponseBase> {
    return this.apiTemplateGetTemplateListForCommonPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<TemplateGetListResponseListResponseBase>): TemplateGetListResponseListResponseBase => r.body)
    );
  }

}
