{"ast": null, "code": "import { DestroyRef, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { forkJoin, map, switchMap, tap } from 'rxjs';\nimport { CalendarModule } from 'primeng/calendar';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nimport * as i12 from \"../../../@theme/pipes/BooleanString.pipe\";\nimport * as i13 from \"../../../@theme/pipes/timing.pipe\";\nimport * as i14 from \"primeng/calendar\";\nfunction PreOrderComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r2.CBuildCaseName);\n  }\n}\nfunction PreOrderComponent_tr_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 22);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 23);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 23);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 23);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 23);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"hour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 29);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 25);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"booleanString\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 23);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 25)(24, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function PreOrderComponent_tr_58_Template_button_click_24_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      const dialog_r3 = i0.ɵɵreference(62);\n      return i0.ɵɵresetView(ctx_r5.edit(dialog_r3, item_r5));\n    });\n    i0.ɵɵelement(25, \"i\", 31);\n    i0.ɵɵtext(26, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PreOrderComponent_tr_58_Template_button_click_27_listener() {\n      const ctx_r6 = i0.ɵɵrestoreView(_r4);\n      const item_r5 = ctx_r6.$implicit;\n      const i_r8 = ctx_r6.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.deleteAppointment(item_r5, i_r8));\n    });\n    i0.ɵɵelement(28, \"i\", 33);\n    i0.ɵɵtext(29, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CCustomerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CPhone);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, item_r5.CPreOrderDate, \"yyyy/MM/dd\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 12, item_r5.CHour));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r5.CPeoples);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 14, item_r5.CHasDesigner));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(22, 16, item_r5.CCreateDT, \"yyyy/MM/dd\"));\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.isNewAppointment)(\"value\", item_r10.CHouseHold);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CHouseHold);\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_select_14_nb_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r12 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"value\", floor_r12)(\"disabled\", !ctx_r5.isNewAppointment);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(floor_r12);\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_select_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-select\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_nb_select_14_Template_nb_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CFloor, $event) || (ctx_r5.appointment.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(1, PreOrderComponent_ng_template_61_nb_select_14_nb_option_1_Template, 2, 3, \"nb-option\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.floorList);\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r13.CDate);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, item_r13.CDate, \"yyyy/MM/dd\"));\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_select_23_nb_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"hour\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hour_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", hour_r15);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, hour_r15));\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_select_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-select\", 62);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_nb_select_23_Template_nb_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CHour, $event) || (ctx_r5.appointment.CHour = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(1, PreOrderComponent_ng_template_61_nb_select_23_nb_option_1_Template, 3, 4, \"nb-option\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CHour);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.hourList);\n  }\n}\nfunction PreOrderComponent_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 34)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"p\", 35);\n    i0.ɵɵtext(5, \" \\u60A8\\u53EF\\u65BC\\u6B64\\u70BA\\u5BA2\\u6236\\u5EFA\\u7ACB\\u4E00\\u7B46\\u9810\\u7D04\\uFF0C\\u4F46\\u50C5\\u9650\\u65BC\\u5728\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5\\u5167\\u4E4B\\u5BA2\\u6236\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 5)(7, \"label\", 36);\n    i0.ɵɵtext(8, \"\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"nb-select\", 37);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_nb_select_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CHouseHold, $event) || (ctx_r5.appointment.CHouseHold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function PreOrderComponent_ng_template_61_Template_nb_select_selectedChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onHouseHoldChange($event));\n    });\n    i0.ɵɵtemplate(10, PreOrderComponent_ng_template_61_nb_option_10_Template, 2, 3, \"nb-option\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 5)(12, \"label\", 39);\n    i0.ɵɵtext(13, \"\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, PreOrderComponent_ng_template_61_nb_select_14_Template, 2, 2, \"nb-select\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 5)(16, \"label\", 41);\n    i0.ɵɵtext(17, \"\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-select\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_nb_select_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CPreOrderDate, $event) || (ctx_r5.appointment.CPreOrderDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function PreOrderComponent_ng_template_61_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onDateTimeChange($event));\n    });\n    i0.ɵɵtemplate(19, PreOrderComponent_ng_template_61_nb_option_19_Template, 3, 5, \"nb-option\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 5)(21, \"label\", 43);\n    i0.ɵɵtext(22, \"\\u6642\\u6BB5\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, PreOrderComponent_ng_template_61_nb_select_23_Template, 2, 2, \"nb-select\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 5)(25, \"label\", 45);\n    i0.ɵɵtext(26, \"\\u51FA\\u5E2D\\u4EBA\\u6578\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_input_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CPeoples, $event) || (ctx_r5.appointment.CPeoples = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 5)(29, \"label\", 47);\n    i0.ɵɵtext(30, \"\\u8A2D\\u8A08\\u5E2B\\u51FA\\u5E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\")(32, \"input\", 48);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_input_ngModelChange_32_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CHasDesigner, $event) || (ctx_r5.appointment.CHasDesigner = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"label\", 49);\n    i0.ɵɵtext(34, \"\\u662F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"input\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_input_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CHasDesigner, $event) || (ctx_r5.appointment.CHasDesigner = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"label\", 51);\n    i0.ɵɵtext(37, \"\\u5426\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 5)(39, \"label\", 52);\n    i0.ɵɵtext(40, \"\\u5099\\u8A3B\\u4E8B\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"textarea\", 53);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_textarea_ngModelChange_41_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CRemark, $event) || (ctx_r5.appointment.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 5)(43, \"label\", 52);\n    i0.ɵɵtext(44, \"\\u8B8A\\u66F4\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"textarea\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_textarea_ngModelChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CHouseRequirement, $event) || (ctx_r5.appointment.CHouseRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"nb-card-footer\", 55)(47, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function PreOrderComponent_ng_template_61_Template_button_click_47_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r9).dialogRef;\n      return i0.ɵɵresetView(ref_r16.close());\n    });\n    i0.ɵɵtext(48, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function PreOrderComponent_ng_template_61_Template_button_click_49_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.save(ref_r16));\n    });\n    i0.ɵɵtext(50, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.isNewAppointment ? \"\\u65B0\\u589E\" : \"\\u7DE8\\u8F2F\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"hidden\", !ctx_r5.isNewAppointment);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CHouseHold);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.houseHoldList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.floorList.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CPreOrderDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.dateTimeList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.hourList && ctx_r5.hourList.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CPeoples);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CHasDesigner);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", false);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CHasDesigner);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CRemark);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CHouseRequirement);\n  }\n}\nexport let PreOrderComponent = /*#__PURE__*/(() => {\n  class PreOrderComponent extends BaseComponent {\n    constructor(allow, dialogService, houseService, buildCaseService, valid, message, _ultilityService) {\n      super(allow);\n      this.allow = allow;\n      this.dialogService = dialogService;\n      this.houseService = houseService;\n      this.buildCaseService = buildCaseService;\n      this.valid = valid;\n      this.message = message;\n      this._ultilityService = _ultilityService;\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      this.buildCaseList = [];\n      this.allBuildCaseList = [];\n      this.caseList = [];\n      this.houseHoldList = [];\n      this.newHouseHightestList = [];\n      this.dateTimeList = [];\n      this.appointment = {};\n      this.searchAppointment = {};\n      this.floorList = [];\n      this.hourList = [];\n      this.destroy = inject(DestroyRef);\n    }\n    ngOnInit() {\n      this.isNewAppointment = false;\n      this.initialList();\n    }\n    initialList() {\n      this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(switchMap(res => {\n        this.caseList = res.Entries ?? [];\n        this.currentBuildCase = res.Entries?.[0]?.cID;\n        let listAppointmentRequest = {\n          body: {\n            CBuildCaseID: this.currentBuildCase\n          }\n        };\n        this.getHourListAppointment();\n        this.getHouseAndFloorByBuildCaseId();\n        return this.houseService.apiHouseGetListAppointmentsPost$Json(listAppointmentRequest);\n      }), takeUntilDestroyed(this.destroy)).subscribe(res => {\n        this.allBuildCaseList = res.Entries ?? [];\n        if (res.TotalItems || res.TotalItems === 0) {\n          this.controllPagination(res.TotalItems);\n        }\n      });\n    }\n    controllPagination(totalItems) {\n      this.pageFirst = (this.pageIndex - 1) * this.pageSize + 1;\n      this.totalRecords = totalItems;\n      let lastIndex = this.totalRecords < this.pageIndex * this.pageSize ? this.totalRecords + 1 : this.pageIndex * this.pageSize;\n      this.buildCaseList = this.allBuildCaseList.slice(this.pageFirst - 1, lastIndex);\n    }\n    getHouseAndFloorByBuildCaseId() {\n      return this.buildCaseService.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json({\n        buildCaseId: this.currentBuildCase\n      }).pipe(tap(res => {\n        if (res.Entries && res.Entries.length) {\n          this.houseHoldList = res.Entries;\n          console.log('getHouseAndFloorByBuildCaseId');\n        }\n      }), takeUntilDestroyed(this.destroy));\n    }\n    groupByCDate(data) {\n      const groupedData = data.reduce((acc, entry) => {\n        if (!acc[entry.CDate]) {\n          acc[entry.CDate] = [];\n        }\n        acc[entry.CDate].push(entry.CHour);\n        return acc;\n      }, {});\n      return Object.keys(groupedData).map(CDate => ({\n        CDate,\n        CHour: groupedData[CDate]\n      }));\n    }\n    getHourListAppointment() {\n      const req = {\n        CBuildCaseID: this.currentBuildCase\n      };\n      return this.houseService.apiHouseGetHourListAppointmentPost$Json({\n        body: req\n      }).pipe(map(res => {\n        res.Entries?.forEach(i => {\n          i.CDate = i.CDate?.split('T')[0];\n          i.CHour = i.CHour;\n        });\n        res.Entries = res.Entries?.filter(i => i.CHour >= 9 && i.CHour <= 21);\n        return res;\n      }), tap(res => {\n        if (res.Entries && res.Entries.length) {\n          this.listCurrent = res.Entries;\n          this.dateTimeList = this.groupByCDate(res.Entries);\n        }\n      }), takeUntilDestroyed(this.destroy));\n    }\n    filterOrders(listAppointmentsByCustomer, selectedData) {\n      let result;\n      if (selectedData && selectedData?.CHour && selectedData.CDate) {\n        result = selectedData.CHour ? [...selectedData.CHour] : [];\n        // Bước 1: Lọc mảng A với các phần tử có CPreOrderDate trùng với B.CDate\n        const filteredOrders = listAppointmentsByCustomer.filter(order => order.CPreOrderDate.startsWith(selectedData.CDate));\n        // Bước 2: Loại bỏ các phần tử B.CHour trùng với các giá trị CHour trong mảng filteredOrders\n        filteredOrders.forEach(order => {\n          const hourIndex = result.indexOf(order.CHour);\n          if (hourIndex !== -1) {\n            result.splice(hourIndex, 1); // Xóa phần tử trùng khỏi B.CHour\n          }\n        });\n        if (this.selectedAppointment.CPreOrderDate.startsWith(selectedData.CDate) && this.selectedAppointment.CHour) {\n          result = [...result, this.selectedAppointment.CHour];\n        }\n      }\n      return result;\n    }\n    onDateTimeChange(selectedDate) {\n      const selectedData = this.dateTimeList.find(item => item.CDate === selectedDate);\n      this.hourList = selectedData ? selectedData.CHour.sort((a, b) => a - b) : [];\n      this.hourList = this.filterOrders(this.listAppointmentsByCustomer, selectedData);\n      this.appointment.CHour = undefined;\n    }\n    getUserBuildcaseList() {\n      let request = {};\n      request.body = {};\n      this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n        this.caseList = res.Entries ?? [];\n      });\n    }\n    getPreorderList(params) {\n      let request = {\n        body: {\n          CBuildCaseID: params.body.CBuildCaseID,\n          CCustomerName: params.body.CCustomerName,\n          CPhone: params.body && params.body.CPhone,\n          CPreOrderDate: params.body && params.body.CPreOrderDate?.split(\"T\")[0]\n        }\n      };\n      this.houseService.apiHouseGetListAppointmentsPost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.allBuildCaseList = res.Entries ?? [];\n          if (res.TotalItems || res.TotalItems === 0) {\n            this.controllPagination(res.TotalItems);\n          }\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    }\n    search() {\n      this.searchAppointment = {\n        body: {\n          CBuildCaseID: this.currentBuildCase,\n          CCustomerName: this.currentCustomerName,\n          CPhone: this.currentCustomerPhone !== undefined ? this.currentCustomerPhone : undefined,\n          CPreOrderDate: this.currentSelectDate ? this.formatDate(this.currentSelectDate) : undefined //=== '全部' ? undefined : this.currentSelectDate\n        }\n      };\n      this.getPreorderList(this.searchAppointment);\n    }\n    onHouseHoldChange(selectedHouseHold) {\n      const selectedData = this.houseHoldList.find(item => item.CHouseHold === selectedHouseHold);\n      this.floorList = selectedData ? selectedData.Floors : [];\n      this.appointment.CFloor = undefined; // Reset selected floor\n    }\n    edit(ref, item) {\n      forkJoin({\n        hourList: this.getHourListAppointment(),\n        houseAndFloor: this.getHouseAndFloorByBuildCaseId()\n      }).subscribe({\n        next: () => {\n          this.selectedAppointment = item;\n          this.isNewAppointment = false;\n          this.editChangepreoderID = item.CChangePreOrderID;\n          let selectedHouseHold = this.houseHoldList.find(i => i.CHouseHold === item.CHouseHold);\n          this.appointment.CHouseHold = selectedHouseHold?.CHouseHold;\n          this.floorList = selectedHouseHold?.Floors;\n          // let currentTemp = this.listCurrent.find((i: any) => {\n          //   return (i.CDate === item.CPreOrderDate.split(\"T\")[0] && i.CHour === item.CHour)\n          // });\n          // if (!currentTemp) {\n          this.dateTimeList = this.groupByCDate([...this.listCurrent]); //, { CDate: item.CPreOrderDate.split(\"T\")[0], CHour: item.CHour }])\n          // }\n          this.listAppointmentsByCustomer = this.allBuildCaseList.filter(o => o.CCustomerName == item.CCustomerName);\n          this.onDateTimeChange(item.CPreOrderDate.split(\"T\")[0]);\n          this.appointment = {\n            CFloor: item.CFloor,\n            CHasDesigner: item.CHasDesigner,\n            CHour: item.CHour,\n            CHouseHold: item.CHouseHold,\n            CPeoples: item.CPeoples,\n            CPreOrderDate: item.CPreOrderDate.split(\"T\")[0],\n            CRemark: item.CRemark,\n            CNeedMail: item.CNeedMail,\n            CHouseRequirement: item.CHouseRequirement\n          };\n          this.dialogService.open(ref);\n        }\n      });\n    }\n    addNewAppointment(ref) {\n      forkJoin({\n        hourList: this.getHourListAppointment(),\n        houseAndFloor: this.getHouseAndFloorByBuildCaseId()\n      }).subscribe({\n        next: () => {\n          this.isNewAppointment = true;\n          this.appointment = {};\n          this.dialogService.open(ref);\n        }\n      });\n    }\n    checkAppointmentExists() {\n      const formattedPreOrderDate = this.appointment.CPreOrderDate + 'T00:00:00';\n      return this.listAppointmentsByCustomer.some(appointment => appointment.CPreOrderDate === formattedPreOrderDate && appointment.CHour === this.appointment.CHour);\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required(`戶別`, this.appointment.CHouseHold);\n      this.valid.required(`樓層`, this.appointment.CFloor);\n      this.valid.required(`日期`, this.appointment.CPreOrderDate);\n      this.valid.required(`時段`, this.appointment.CHour);\n      this.valid.required(`出席人數`, this.appointment.CPeoples);\n      this.valid.required(`設計師出席`, this.appointment.CHasDesigner);\n      // console.log('this.listAppointmentsByCustomer', this.listAppointmentsByCustomer.filter((item: any)=> item.CHouseHold ==this.appointment.CHouseHold ));\n      // console.log('this.appointment', this.appointment);\n      // if (this.listAppointmentsByCustomer && this.listAppointmentsByCustomer.length) {\n      //   if (this.checkAppointmentExists()) {\n      //     this.valid.addErrorMessage('無法選擇尚未被加入可 預約時段的日期及時段');\n      //   }\n      // }\n    }\n    deleteAppointment(item, i) {\n      if (window.confirm(`確定要刪除【項目${this.pageFirst + i}】?`)) {\n        this.houseService.apiHouseDeleteAppointmentPost$Json({\n          body: item.CChangePreOrderID\n        }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG(\"執行成功\");\n            this.getPreorderList({\n              body: {\n                CBuildCaseID: this.currentBuildCase\n              }\n            });\n          }\n        });\n      }\n    }\n    saveEdit(ref) {\n      let request = {\n        body: {\n          CID: this.editChangepreoderID,\n          CHasDesigner: this.appointment.CHasDesigner,\n          CHour: this.appointment.CHour,\n          CPeoples: this.appointment.CPeoples,\n          CPreOrderDate: this.appointment.CPreOrderDate,\n          CRemark: this.appointment.CRemark,\n          CStatus: this.selectedAppointment.CStatus\n        }\n      };\n      this.houseService.apiHouseEditAppointmentPost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getPreorderList({\n            body: {\n              CBuildCaseID: this.currentBuildCase\n            }\n          });\n          ref.close();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n    save(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      let request;\n      request = {\n        body: {\n          ...this.appointment,\n          CBuildcaseID: this.currentBuildCase\n        }\n      };\n      if (this.isNewAppointment) {\n        this.houseService.apiHouseCreateAppointmentPost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG(`執行成功`);\n            this.getPreorderList({\n              body: {\n                CBuildCaseID: this.currentBuildCase\n              }\n            });\n            ref.close();\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        });\n      } else {\n        this.saveEdit(ref);\n      }\n    }\n    exportExcel() {\n      this.houseService.apiHouseExportExcelListAppointmentsPost$Json({\n        body: {\n          CBuildCaseID: this.currentBuildCase,\n          CCustomerName: this.currentCustomerName,\n          CPhone: this.currentCustomerPhone !== undefined ? this.currentCustomerPhone : undefined,\n          CPreOrderDate: this.currentSelectDate ? this.formatDate(this.currentSelectDate) : undefined //this.currentSelectDate === '全部' ? undefined : this.currentSelectDate\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this._ultilityService.downloadExcelFile(res.Entries?.FileByte, '預約');\n        }\n      })).subscribe();\n    }\n    static {\n      this.ɵfac = function PreOrderComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PreOrderComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.HouseService), i0.ɵɵdirectiveInject(i3.BuildCaseService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.UtilityService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PreOrderComponent,\n        selectors: [[\"app-pre-order\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 63,\n        vars: 13,\n        consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"mb-4\", 2, \"color\", \"#818181\", \"font-weight\", \"700\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\"], [3, \"ngModelChange\", \"selectedChange\", \"placeholder\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"name\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5BA2\\u6236\\u59D3\\u540D\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"preOrderDate\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"inputId\", \"icondisplay\", \"dateFormat\", \"yy/mm/dd\", 1, \"!w-full\", 3, \"ngModelChange\", \"appendTo\", \"iconDisplay\", \"showIcon\", \"ngModel\"], [\"for\", \"phone\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5BA2\\u6236\\u96FB\\u8A71\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"justify-end\"], [1, \"btn\", \"btn-info\", \"mr-3\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"mr-3\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [2, \"min-width\", \"5%\"], [2, \"min-width\", \"10%\"], [2, \"min-width\", \"15%\"], [1, \"text-center\", 2, \"min-width\", \"15%\"], [4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [3, \"value\"], [1, \"text-center\", 2, \"min-width\", \"10%\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [2, \"max-height\", \"95vh\", \"min-width\", \"400px\"], [1, \"mb-4\", 2, \"color\", \"#818181\", \"font-weight\", \"700\", 3, \"hidden\"], [\"for\", \"distinguish\", \"baseLabel\", \"\", 1, \"required-field\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"disabled\", \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"floor\", \"baseLabel\", \"\", 1, \"required-field\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6A13\\u5C64\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"for\", \"date\", \"baseLabel\", \"\", 1, \"required-field\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u65E5\\u671F\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"for\", \"timeline\", \"baseLabel\", \"\", 1, \"required-field\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6642\\u6BB5\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"for\", \"headcount\", \"baseLabel\", \"\", 1, \"required-field\"], [\"type\", \"number\", \"nbInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"designer\", \"baseLabel\", \"\", 1, \"required-field\"], [\"type\", \"radio\", \"id\", \"designerAttend\", \"name\", \"attend\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"designerAttend\", 1, \"label\", \"ml-1\"], [\"type\", \"radio\", \"id\", \"designerAbsent\", \"name\", \"attend\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"designerAbsent\", 1, \"label\", \"ml-1\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"align-self-start\"], [\"name\", \"remark\", \"id\", \"remark\", \"rows\", \"5\", \"nbInput\", \"\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"remark\", \"id\", \"remark\", \"rows\", \"5\", \"nbInput\", \"\", \"disabled\", \"true\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"], [3, \"disabled\", \"value\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6A13\\u5C64\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", \"disabled\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\", \"disabled\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6642\\u6BB5\", 3, \"ngModelChange\", \"ngModel\"]],\n        template: function PreOrderComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"p\", 2);\n            i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u65BC\\u6B64\\u6AA2\\u8996/\\u7DE8\\u8F2F\\u76EE\\u524D\\u4E4B\\u9810\\u7D04\\u8CC7\\u6599\\uFF0C\\u4EA6\\u53EF\\u70BA\\u5BA2\\u6236\\u65B0\\u589E\\u4E00\\u7B46\\u8CC7\\u6599\\uFF08\\u4F46\\u6BCF\\u500B\\u6236\\u5225\\u53EA\\u80FD\\u6709\\u4E00\\u7B46\\u9810\\u7D04\\u8CC7\\u6599\\uFF09\\u3002\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 5)(9, \"label\", 6);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.currentBuildCase, $event) || (ctx.currentBuildCase = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function PreOrderComponent_Template_nb_select_selectedChange_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.search());\n            });\n            i0.ɵɵtemplate(12, PreOrderComponent_nb_option_12_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 5)(14, \"label\", 9);\n            i0.ɵɵtext(15, \"\\u5BA2\\u6236\\u59D3\\u540D\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"input\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_Template_input_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.currentCustomerName, $event) || (ctx.currentCustomerName = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 4)(18, \"div\", 5)(19, \"label\", 11);\n            i0.ɵɵtext(20, \"\\u9810\\u7D04\\u65E5\\u671F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"p-calendar\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_Template_p_calendar_ngModelChange_21_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.currentSelectDate, $event) || (ctx.currentSelectDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 5)(23, \"label\", 13);\n            i0.ɵɵtext(24, \"\\u5BA2\\u6236\\u96FB\\u8A71\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"input\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_Template_input_ngModelChange_25_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.currentCustomerPhone, $event) || (ctx.currentCustomerPhone = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(26, \"div\", 15)(27, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function PreOrderComponent_Template_button_click_27_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.search());\n            });\n            i0.ɵɵtext(28, \"\\u67E5\\u8A62\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function PreOrderComponent_Template_button_click_29_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const dialog_r3 = i0.ɵɵreference(62);\n              return i0.ɵɵresetView(ctx.addNewAppointment(dialog_r3));\n            });\n            i0.ɵɵtext(30, \"\\u65B0\\u589E\\u9810\\u7D04\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function PreOrderComponent_Template_button_click_31_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.exportExcel());\n            });\n            i0.ɵɵtext(32, \"\\u532F\\u51FA\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(33, \"div\", 19)(34, \"table\", 20)(35, \"thead\")(36, \"tr\", 21)(37, \"th\", 22);\n            i0.ɵɵtext(38, \"\\u6236\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"th\", 22);\n            i0.ɵɵtext(40, \"\\u6A13\\u5C64\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"th\", 23);\n            i0.ɵɵtext(42, \"\\u5BA2\\u6236\\u59D3\\u540D\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"th\", 23);\n            i0.ɵɵtext(44, \"\\u5BA2\\u6236\\u96FB\\u8A71\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"th\", 23);\n            i0.ɵɵtext(46, \"\\u9810\\u7D04\\u65E5\\u671F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"th\", 23);\n            i0.ɵɵtext(48, \"\\u9810\\u7D04\\u6642\\u6BB5\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"th\", 23);\n            i0.ɵɵtext(50, \"\\u51FA\\u5E2D\\u4EBA\\u6578\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"th\", 24);\n            i0.ɵɵtext(52, \"\\u8A2D\\u8A08\\u5E2B\\u51FA\\u5E2D\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"th\", 23);\n            i0.ɵɵtext(54, \"\\u586B\\u55AE\\u6642\\u9593\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"th\", 25);\n            i0.ɵɵtext(56, \"\\u64CD\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(57, \"tbody\");\n            i0.ɵɵtemplate(58, PreOrderComponent_tr_58_Template, 30, 19, \"tr\", 26);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(59, \"nb-card-footer\")(60, \"ngx-pagination\", 27);\n            i0.ɵɵtwoWayListener(\"PageChange\", function PreOrderComponent_Template_ngx_pagination_PageChange_60_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function PreOrderComponent_Template_ngx_pagination_PageChange_60_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.controllPagination(ctx.totalRecords));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(61, PreOrderComponent_ng_template_61_Template, 51, 15, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\");\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.currentBuildCase);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.caseList);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.currentCustomerName);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"appendTo\", \"body\")(\"iconDisplay\", \"input\")(\"showIcon\", true);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.currentSelectDate);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.currentCustomerPhone);\n            i0.ɵɵadvance(33);\n            i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          }\n        },\n        dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DatePipe, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.RadioControlValueAccessor, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent, i11.BaseLabelDirective, i12.BooleanStringPipe, i13.FormatHourPipe, CalendarModule, i14.Calendar],\n        styles: [\"label[_ngcontent-%COMP%]{min-width:75px;margin:0}input[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%], nb-select[_ngcontent-%COMP%]{flex:1}  p-calendar>span{width:100%;max-width:20rem}\"]\n      });\n    }\n  }\n  return PreOrderComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}