{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./@core/utils/analytics.service\";\nimport * as i2 from \"./@core/utils/seo.service\";\nimport * as i3 from \"@angular/router\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(analytics, seoService) {\n      this.analytics = analytics;\n      this.seoService = seoService;\n    }\n    ngOnInit() {\n      this.analytics.trackPageViews();\n      this.seoService.trackCanonicalChanges();\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.AnalyticsService), i0.ɵɵdirectiveInject(i2.SeoService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"ngx-app\"]],\n        decls: 1,\n        vars: 0,\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"router-outlet\");\n          }\n        },\n        dependencies: [i3.RouterOutlet],\n        encapsulation: 2\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}