// 表單樣式
.template-form {
  .form-section {
    border: 1px solid #e4e9f0;
    border-radius: 8px;
    padding: 1.5rem;
    background-color: #fafbfc;

    .section-title {
      color: #2c3e50;
      font-weight: 600;
      margin-bottom: 1rem;

      i {
        color: #3498db;
      }
    }
  }

  .input-group {
    margin-bottom: 1rem;

    .input-label {
      display: block;
      font-weight: 500;
      color: #2c3e50;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;

      .required {
        color: #e74c3c;
        margin-left: 2px;
      }
    }

    .input-field {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 0.9rem;
      transition: border-color 0.3s ease;

      &:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      }

      &.is-invalid {
        border-color: #e74c3c;
        box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
      }

      &:disabled {
        background-color: #f8f9fa;
        cursor: not-allowed;
      }
    }

    .invalid-feedback {
      color: #e74c3c;
      font-size: 0.8rem;
      margin-top: 0.25rem;
      display: block;
    }
  }
}

// 選擇摘要
.selection-summary {
  .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

// 全選控制
.select-all-control {
  padding: 0.75rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;

  .select-all-label {
    display: flex;
    align-items: center;
    margin: 0;
    cursor: pointer;
    font-weight: 500;

    .select-all-checkbox {
      margin-right: 0.5rem;
      transform: scale(1.1);
    }

    .select-all-text {
      color: #495057;
    }

    &:hover {
      .select-all-text {
        color: #007bff;
      }
    }
  }
}

// 項目選擇器
.items-selector {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background-color: white;

  &.has-error {
    border-color: #e74c3c;
  }

  .empty-items {
    text-align: center;
    padding: 2rem;
    color: #6c757d;

    i {
      font-size: 2rem;
      margin-bottom: 0.5rem;
      display: block;
    }
  }

  .item-option {
    border-bottom: 1px solid #f1f3f4;

    &:last-child {
      border-bottom: none;
    }

    .item-label {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      margin: 0;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
      }

      .item-checkbox {
        margin-right: 0.75rem;
        transform: scale(1.1);

        &:disabled {
          cursor: not-allowed;
        }
      }

      .item-content {
        flex: 1;

        .item-title {
          font-weight: 500;
          color: #2c3e50;
          margin-bottom: 0.25rem;
        }

        .item-desc {
          font-size: 0.85rem;
          color: #6c757d;
        }
      }
    }
  }
}

// 表單操作按鈕
.form-actions {
  gap: 0.5rem;

  .btn {
    min-width: 100px;
    font-weight: 500;

    i {
      margin-right: 0.25rem;
    }

    &:disabled {
      cursor: not-allowed;
    }
  }
}

// 響應式設計
@media (max-width: 768px) {
  .template-form {
    .form-section {
      padding: 1rem;
    }
  }

  .items-selector {
    max-height: 250px;
  }

  .form-actions {
    flex-direction: column;

    .btn {
      width: 100%;
      margin-bottom: 0.5rem;
    }
  }
}

// 滾動條樣式
.items-selector {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// 動畫效果
.item-option {
  transition: all 0.2s ease;
}

.form-section {
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}