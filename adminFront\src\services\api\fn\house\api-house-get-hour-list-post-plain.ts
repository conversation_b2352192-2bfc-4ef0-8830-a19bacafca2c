/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetHourListResponeListResponseBase } from '../../models/get-hour-list-respone-list-response-base';
import { HouseGetHourListArgs } from '../../models/house-get-hour-list-args';

export interface ApiHouseGetHourListPost$Plain$Params {
      body?: HouseGetHourListArgs
}

export function apiHouseGetHourListPost$Plain(http: HttpClient, rootUrl: string, params?: ApiHouseGetHourListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHourListResponeListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiHouseGetHourListPost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetHourListResponeListResponseBase>;
    })
  );
}

apiHouseGetHourListPost$Plain.PATH = '/api/House/GetHourList';
