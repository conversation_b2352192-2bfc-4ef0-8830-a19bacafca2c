{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { UserService } from './users.service';\nimport { ElectricityService } from './electricity.service';\nimport { SmartTableService } from './smart-table.service';\nimport { UserActivityService } from './user-activity.service';\nimport { OrdersChartService } from './orders-chart.service';\nimport { ProfitChartService } from './profit-chart.service';\nimport { TrafficListService } from './traffic-list.service';\nimport { PeriodsService } from './periods.service';\n//import { EarningService } from './earning.service';\nimport { OrdersProfitChartService } from './orders-profit-chart.service';\nimport { TrafficBarService } from './traffic-bar.service';\nimport { ProfitBarAnimationChartService } from './profit-bar-animation-chart.service';\nimport { TemperatureHumidityService } from './temperature-humidity.service';\nimport { SolarService } from './solar.service';\nimport { TrafficChartService } from './traffic-chart.service';\nimport { StatsBarService } from './stats-bar.service';\nimport { CountryOrderService } from './country-order.service';\nimport { StatsProgressBarService } from './stats-progress-bar.service';\nimport { VisitorsAnalyticsService } from './visitors-analytics.service';\nimport { SecurityCamerasService } from './security-cameras.service';\nimport * as i0 from \"@angular/core\";\nconst SERVICES = [UserService, ElectricityService, SmartTableService, UserActivityService, OrdersChartService, ProfitChartService, TrafficListService, PeriodsService,\n//EarningService,\nOrdersProfitChartService, TrafficBarService, ProfitBarAnimationChartService, TemperatureHumidityService, SolarService, TrafficChartService, StatsBarService, CountryOrderService, StatsProgressBarService, VisitorsAnalyticsService, SecurityCamerasService];\nexport let MockDataModule = /*#__PURE__*/(() => {\n  class MockDataModule {\n    static forRoot() {\n      return {\n        ngModule: MockDataModule,\n        providers: [...SERVICES]\n      };\n    }\n    static {\n      this.ɵfac = function MockDataModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || MockDataModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: MockDataModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [...SERVICES],\n        imports: [CommonModule]\n      });\n    }\n  }\n  return MockDataModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}