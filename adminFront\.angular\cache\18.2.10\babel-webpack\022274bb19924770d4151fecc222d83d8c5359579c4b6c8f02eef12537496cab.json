{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { UserData } from '../data/users';\nimport * as i0 from \"@angular/core\";\nexport let UserService = /*#__PURE__*/(() => {\n  class UserService extends UserData {\n    constructor() {\n      super(...arguments);\n      this.time = new Date();\n      this.users = {\n        nick: {\n          name: '<PERSON>',\n          picture: 'assets/images/nick.png'\n        },\n        eva: {\n          name: '<PERSON>',\n          picture: 'assets/images/eva.png'\n        },\n        jack: {\n          name: '<PERSON>',\n          picture: 'assets/images/jack.png'\n        },\n        lee: {\n          name: '<PERSON>',\n          picture: 'assets/images/lee.png'\n        },\n        alan: {\n          name: '<PERSON>',\n          picture: 'assets/images/alan.png'\n        },\n        kate: {\n          name: '<PERSON>',\n          picture: 'assets/images/kate.png'\n        }\n      };\n      this.types = {\n        mobile: 'mobile',\n        home: 'home',\n        work: 'work'\n      };\n      this.contacts = [{\n        user: this.users.nick,\n        type: this.types.mobile\n      }, {\n        user: this.users.eva,\n        type: this.types.home\n      }, {\n        user: this.users.jack,\n        type: this.types.mobile\n      }, {\n        user: this.users.lee,\n        type: this.types.mobile\n      }, {\n        user: this.users.alan,\n        type: this.types.home\n      }, {\n        user: this.users.kate,\n        type: this.types.work\n      }];\n      this.recentUsers = [{\n        user: this.users.alan,\n        type: this.types.home,\n        time: this.time.setHours(21, 12)\n      }, {\n        user: this.users.eva,\n        type: this.types.home,\n        time: this.time.setHours(17, 45)\n      }, {\n        user: this.users.nick,\n        type: this.types.mobile,\n        time: this.time.setHours(5, 29)\n      }, {\n        user: this.users.lee,\n        type: this.types.mobile,\n        time: this.time.setHours(11, 24)\n      }, {\n        user: this.users.jack,\n        type: this.types.mobile,\n        time: this.time.setHours(10, 45)\n      }, {\n        user: this.users.kate,\n        type: this.types.work,\n        time: this.time.setHours(9, 42)\n      }, {\n        user: this.users.kate,\n        type: this.types.work,\n        time: this.time.setHours(9, 31)\n      }, {\n        user: this.users.jack,\n        type: this.types.mobile,\n        time: this.time.setHours(8, 0)\n      }];\n    }\n    getUsers() {\n      return observableOf(this.users);\n    }\n    getContacts() {\n      return observableOf(this.contacts);\n    }\n    getRecentUsers() {\n      return observableOf(this.recentUsers);\n    }\n    static {\n      this.ɵfac = /*@__PURE__*/(() => {\n        let ɵUserService_BaseFactory;\n        return function UserService_Factory(__ngFactoryType__) {\n          return (ɵUserService_BaseFactory || (ɵUserService_BaseFactory = i0.ɵɵgetInheritedFactory(UserService)))(__ngFactoryType__ || UserService);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: UserService,\n        factory: UserService.ɵfac\n      });\n    }\n  }\n  return UserService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}