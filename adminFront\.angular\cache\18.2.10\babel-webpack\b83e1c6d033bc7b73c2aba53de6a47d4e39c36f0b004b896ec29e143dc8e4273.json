{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nexport let AnalyticsService = /*#__PURE__*/(() => {\n  class AnalyticsService {\n    constructor(location, router) {\n      this.location = location;\n      this.router = router;\n      this.enabled = false;\n    }\n    trackPageViews() {\n      if (this.enabled) {\n        this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n          ga('send', {\n            hitType: 'pageview',\n            page: this.location.path()\n          });\n        });\n      }\n    }\n    trackEvent(eventName) {\n      if (this.enabled) {\n        ga('send', 'event', eventName);\n      }\n    }\n    static {\n      this.ɵfac = function AnalyticsService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || AnalyticsService)(i0.ɵɵinject(i1.Location), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AnalyticsService,\n        factory: AnalyticsService.ɵfac\n      });\n    }\n  }\n  return AnalyticsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}