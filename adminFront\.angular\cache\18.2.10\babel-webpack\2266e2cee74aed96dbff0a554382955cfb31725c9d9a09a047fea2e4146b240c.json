{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let PluralPipe = /*#__PURE__*/(() => {\n  class PluralPipe {\n    transform(input, label, pluralLabel = '') {\n      input = input || 0;\n      return input === 1 ? `${input} ${label}` : pluralLabel ? `${input} ${pluralLabel}` : `${input} ${label}s`;\n    }\n    static {\n      this.ɵfac = function PluralPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PluralPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"ngxPlural\",\n        type: PluralPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return PluralPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}