{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nfunction SettingTimePeriodComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_container_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i_r3 + 1, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_td_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 43);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_tr_55_td_3_span_3_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const itm_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      const dialog_r7 = i0.ɵɵreference(59);\n      return i0.ɵɵresetView(ctx_r5.openModel(dialog_r7, itm_r5));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"getSettingTimeStatus\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itm_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(itm_r5.CHouseId ? \"cursor-pointer text-blue-800 block min-w-[100px]\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 3, itm_r5.CChangeStartDate, itm_r5.CChangeEndDate, ctx_r5.isStatus), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_td_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_td_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_tr_55_td_3_span_3_Template, 3, 7, \"span\", 41)(4, SettingTimePeriodComponent_tr_55_td_3_span_4_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itm_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", itm_r5.CHouseHold, \"-\", itm_r5.CFloor, \"F\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", itm_r5.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !itm_r5.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_tr_55_td_3_Template, 5, 4, \"td\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", item_r8[0].CFloor, \"F\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", item_r8);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 44);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 45);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 44)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 46);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 47)(7, \"div\", 14)(8, \"label\", 15);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 48);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 16);\n    i0.ɵɵelement(13, \"nb-icon\", 17);\n    i0.ɵɵelementStart(14, \"input\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_58_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r5.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 19, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 50);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 17);\n    i0.ɵɵelementStart(21, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_58_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r5.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 19, 5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 45)(25, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_58_Template_button_click_25_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onClose(ref_r10));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_58_Template_button_click_27_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onSubmit(ref_r10));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r11 = i0.ɵɵreference(16);\n    const changeEndDate_r12 = i0.ɵɵreference(23);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r5.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r5.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r12);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport let SettingTimePeriodComponent = /*#__PURE__*/(() => {\n  class SettingTimePeriodComponent extends BaseComponent {\n    constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._houseService = _houseService;\n      this._buildCaseService = _buildCaseService;\n      this.router = router;\n      this._eventService = _eventService;\n      this.tempBuildCaseId = -1;\n      this.buildCaseOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      this.isStatus = true;\n      this.selectedHouseChangeDate = {\n        CChangeStartDate: '',\n        CChangeEndDate: '',\n        CFloor: undefined,\n        CHouseHold: '',\n        CHouseId: undefined\n      };\n      this._eventService.receive().pipe(tap(res => {\n        if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n          this.tempBuildCaseId = res.payload;\n        }\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        CBuildingNameSelected: this.buildCaseOptions[0],\n        CChangeStartDate: undefined,\n        CChangeEndDate: undefined\n      };\n      this.getUserBuildCase();\n    }\n    openModel(ref, item) {\n      if (item.CHouseId) {\n        this.selectedHouseChangeDate = {\n          ...item,\n          CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n          CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n        };\n        this.dialogService.open(ref);\n      }\n    }\n    formatDate(CChangeDate) {\n      if (CChangeDate) {\n        return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n      }\n      return '';\n    }\n    onSubmit(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      const param = {\n        CHouseId: this.selectedHouseChangeDate.CHouseId,\n        CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n        CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n      };\n      this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n        body: [param]\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getHouseChangeDate();\n          ref.close();\n        }\n      });\n    }\n    getUserBuildCase() {\n      this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n        body: {}\n      }).pipe(tap(res => {\n        const entries = res.Entries ?? []; // entries not undefined and not null\n        if (entries.length && res.StatusCode === 0) {\n          this.userBuildCaseOptions = entries.map(entry => ({\n            CBuildCaseName: entry.CBuildCaseName,\n            cID: entry.cID\n          }));\n          if (this.tempBuildCaseId != -1) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n          }\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n          if (selectedCID) {\n            this.getHouseChangeDate();\n          }\n        }\n      })).subscribe();\n    }\n    convertHouseholdArrayOptimized(arr) {\n      const floorDict = {}; // Initialize dictionary to group elements by CFloor\n      arr.forEach(household => {\n        household.CHouses.forEach(house => {\n          const floor = house.CFloor;\n          if (!floorDict[floor]) {\n            // If CFloor is not in the dictionary, initialize an empty list\n            floorDict[floor] = [];\n          }\n          floorDict[floor].push({\n            CHouseHold: household.CHouseHold,\n            CHouseId: house.CHouseId,\n            CFloor: house.CFloor,\n            CChangeStartDate: house.CChangeStartDate,\n            CChangeEndDate: house.CChangeEndDate\n          });\n        });\n      });\n      // Arrange floors in ascending order\n      this.floors.sort((a, b) => b - a);\n      const result = this.floors.map(floor => {\n        return this.households.map(household => {\n          const house = floorDict[floor].find(h => h.CHouseHold === household);\n          return house || {\n            CHouseHold: household,\n            CHouseId: null,\n            CFloor: floor,\n            CChangeStartDate: null,\n            CChangeEndDate: null\n          };\n        });\n      });\n      return result;\n    }\n    getFloorsAndHouseholds(arr) {\n      const floorsSet = new Set();\n      const householdsSet = new Set();\n      arr.forEach(household => {\n        householdsSet.add(household.CHouseHold);\n        household.CHouses.forEach(house => {\n          floorsSet.add(house.CFloor);\n        });\n      });\n      this.floors = Array.from(floorsSet);\n      this.households = Array.from(householdsSet);\n      return {\n        floors: Array.from(floorsSet),\n        households: Array.from(householdsSet)\n      };\n    }\n    validationDate() {\n      if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n        const startDate = new Date(this.searchQuery.CChangeStartDate);\n        const endDate = new Date(this.searchQuery.CChangeEndDate);\n        if (startDate && endDate && startDate > endDate) {\n          this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n        }\n      }\n    }\n    getHouseChangeDate() {\n      this.validationDate();\n      this._houseService.apiHouseGetHouseChangeDatePost$Json({\n        body: {\n          CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n          CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n          CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseChangeDates = res.Entries ? res.Entries : [];\n          if (res.Entries) {\n            this.houseChangeDates = [...res.Entries];\n            this.getFloorsAndHouseholds(res.Entries);\n            this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          }\n        }\n      });\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n      this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n      this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n    }\n    onNavigateWithId() {\n      this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n    }\n    static {\n      this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SettingTimePeriodComponent,\n        selectors: [[\"ngx-setting-time-period\"]],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 60,\n        vars: 12,\n        consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-md-7\", \"max-sm:col-md-12\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [1, \"form-check\", \"mx-2\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault1\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault1\", 1, \"form-check-label\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault2\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault2\", 1, \"form-check-label\"], [1, \"col-md-5\", \"max-sm:col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"text-center\"], [2, \"display\", \"block\", \"min-width\", \"100px\"], [\"style\", \"display: block; min-width: 100px\", 3, \"class\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"display\", \"block\", \"min-width\", \"100px\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"text-red-600\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n        template: function SettingTimePeriodComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 6)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 7);\n            i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 8)(7, \"div\", 9)(8, \"div\", 10)(9, \"label\", 11);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getHouseChangeDate());\n            });\n            i0.ɵɵtemplate(12, SettingTimePeriodComponent_nb_option_12_Template, 2, 2, \"nb-option\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 14)(15, \"label\", 15);\n            i0.ɵɵtext(16, \"\\u958B\\u653E\\u65E5\\u671F \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"nb-form-field\", 16);\n            i0.ɵɵelement(18, \"nb-icon\", 17);\n            i0.ɵɵelementStart(19, \"input\", 18);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_19_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(20, \"nb-datepicker\", 19, 0);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"label\", 20);\n            i0.ɵɵtext(23, \" ~ \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"nb-form-field\");\n            i0.ɵɵelement(25, \"nb-icon\", 17);\n            i0.ɵɵelementStart(26, \"input\", 21);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_26_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(27, \"nb-datepicker\", 19, 1);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(29, \"div\", 22)(30, \"div\", 10)(31, \"label\", 23);\n            i0.ɵɵtext(32, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"div\", 24)(34, \"input\", 25);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_34_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"label\", 26);\n            i0.ɵɵtext(36, \" \\u4F9D\\u958B\\u653E\\u6642\\u6BB5\\u986F\\u793A \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"div\", 24)(38, \"input\", 27);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_38_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"label\", 28);\n            i0.ɵɵtext(40, \" \\u4F9D\\u958B\\u653E\\u72C0\\u614B\\u986F\\u793A \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(41, \"div\", 29)(42, \"div\", 30)(43, \"button\", 31);\n            i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_43_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getHouseChangeDate());\n            });\n            i0.ɵɵtext(44, \" \\u67E5\\u8A62 \");\n            i0.ɵɵelement(45, \"i\", 32);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"button\", 33);\n            i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_46_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onNavigateWithId());\n            });\n            i0.ɵɵtext(47, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(48, \"div\", 34)(49, \"table\", 35)(50, \"thead\")(51, \"tr\", 36);\n            i0.ɵɵelement(52, \"th\");\n            i0.ɵɵtemplate(53, SettingTimePeriodComponent_ng_container_53_Template, 3, 1, \"ng-container\", 37);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(54, \"tbody\");\n            i0.ɵɵtemplate(55, SettingTimePeriodComponent_tr_55_Template, 4, 2, \"tr\", 37);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(56, SettingTimePeriodComponent_ng_template_56_Template, 4, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(58, SettingTimePeriodComponent_ng_template_58_Template, 29, 6, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            const StartDate_r13 = i0.ɵɵreference(21);\n            const EndDate_r14 = i0.ɵɵreference(28);\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"nbDatepicker\", StartDate_r13);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"nbDatepicker\", EndDate_r14);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"value\", true);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"value\", false);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseChangeDates);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.convertedHouseArray);\n          }\n        },\n        dependencies: [CommonModule, i8.NgForOf, i8.NgIf, SharedModule, i9.DefaultValueAccessor, i9.RadioControlValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, RadioButtonModule, NbDatepickerModule, NbDateFnsDateModule, SettingTimeStatusPipe]\n      });\n    }\n  }\n  return SettingTimePeriodComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}