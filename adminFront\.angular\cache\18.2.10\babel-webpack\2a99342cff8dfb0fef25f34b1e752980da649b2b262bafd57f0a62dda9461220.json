{"ast": null, "code": "/**\n * @license Angular v18.2.9\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, ɵRuntimeError, ɵgetOutputDestroyRef, Injector, effect, untracked, assertNotInReactiveContext, signal, computed } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @developerPreview\n */\nfunction takeUntilDestroyed(destroyRef) {\n  if (!destroyRef) {\n    assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n  const destroyed$ = new Observable(observer => {\n    const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n    return unregisterFn;\n  });\n  return source => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n  constructor(source) {\n    this.source = source;\n    this.destroyed = false;\n    this.destroyRef = inject(DestroyRef);\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  subscribe(callbackFn) {\n    if (this.destroyed) {\n      throw new ɵRuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    // Stop yielding more values when the directive/component is already destroyed.\n    const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: value => callbackFn(value)\n    });\n    return {\n      unsubscribe: () => subscription.unsubscribe()\n    };\n  }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @developerPreview\n */\nfunction outputFromObservable(observable, opts) {\n  ngDevMode && assertInInjectionContext(outputFromObservable);\n  return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @developerPreview\n */\nfunction outputToObservable(ref) {\n  const destroyRef = ɵgetOutputDestroyRef(ref);\n  return new Observable(observer => {\n    // Complete the observable upon directive/component destroy.\n    // Note: May be `undefined` if an `EventEmitter` is declared outside\n    // of an injection context.\n    destroyRef?.onDestroy(() => observer.complete());\n    const subscription = ref.subscribe(v => observer.next(v));\n    return () => subscription.unsubscribe();\n  });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = effect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n  ngDevMode && assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' + 'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n  const requiresCleanup = !options?.manualCleanup;\n  requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n  const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n  const equal = makeToSignalEqual(options?.equal);\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({\n      kind: 0 /* StateKind.NoValue */\n    }, {\n      equal\n    });\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal({\n      kind: 1 /* StateKind.Value */,\n      value: options?.initialValue\n    }, {\n      equal\n    });\n  }\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: value => state.set({\n      kind: 1 /* StateKind.Value */,\n      value\n    }),\n    error: error => {\n      if (options?.rejectErrors) {\n        // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n        // the error to end up as an uncaught exception.\n        throw error;\n      }\n      state.set({\n        kind: 2 /* StateKind.Error */,\n        error\n      });\n    }\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n  if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n    throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n  }\n  // Unsubscribe when the current context is destroyed, if requested.\n  cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(() => {\n    const current = state();\n    switch (current.kind) {\n      case 1 /* StateKind.Value */:\n        return current.value;\n      case 2 /* StateKind.Error */:\n        throw current.error;\n      case 0 /* StateKind.NoValue */:\n        // This shouldn't really happen because the error is thrown on creation.\n        throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n  }, {\n    equal: options?.equal\n  });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n  return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { outputFromObservable, outputToObservable, takeUntilDestroyed, toObservable, toSignal };\n//# sourceMappingURL=rxjs-interop.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}