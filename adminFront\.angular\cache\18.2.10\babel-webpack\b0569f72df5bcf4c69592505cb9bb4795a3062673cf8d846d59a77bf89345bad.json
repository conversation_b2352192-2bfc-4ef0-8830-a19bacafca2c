{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiUserAddDataPost$Json } from '../fn/user/api-user-add-data-post-json';\nimport { apiUserAddDataPost$Plain } from '../fn/user/api-user-add-data-post-plain';\nimport { apiUserGetDataPost$Json } from '../fn/user/api-user-get-data-post-json';\nimport { apiUserGetDataPost$Plain } from '../fn/user/api-user-get-data-post-plain';\nimport { apiUserGetListPost$Json } from '../fn/user/api-user-get-list-post-json';\nimport { apiUserGetListPost$Plain } from '../fn/user/api-user-get-list-post-plain';\nimport { apiUserGetMenuPost$Json } from '../fn/user/api-user-get-menu-post-json';\nimport { apiUserGetMenuPost$Plain } from '../fn/user/api-user-get-menu-post-plain';\nimport { apiUserGetUserLogPost$Json } from '../fn/user/api-user-get-user-log-post-json';\nimport { apiUserGetUserLogPost$Plain } from '../fn/user/api-user-get-user-log-post-plain';\nimport { apiUserRemoveUserPost$Json } from '../fn/user/api-user-remove-user-post-json';\nimport { apiUserRemoveUserPost$Plain } from '../fn/user/api-user-remove-user-post-plain';\nimport { apiUserSaveDataPost$Json } from '../fn/user/api-user-save-data-post-json';\nimport { apiUserSaveDataPost$Plain } from '../fn/user/api-user-save-data-post-plain';\nimport { apiUserUserLoginPost$Json } from '../fn/user/api-user-user-login-post-json';\nimport { apiUserUserLoginPost$Plain } from '../fn/user/api-user-user-login-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let UserService = /*#__PURE__*/(() => {\n  class UserService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiUserUserLoginPost()` */\n    static {\n      this.ApiUserUserLoginPostPath = '/api/User/UserLogin';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserUserLoginPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserUserLoginPost$Plain$Response(params, context) {\n      return apiUserUserLoginPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserUserLoginPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserUserLoginPost$Plain(params, context) {\n      return this.apiUserUserLoginPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserUserLoginPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserUserLoginPost$Json$Response(params, context) {\n      return apiUserUserLoginPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserUserLoginPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserUserLoginPost$Json(params, context) {\n      return this.apiUserUserLoginPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiUserGetMenuPost()` */\n    static {\n      this.ApiUserGetMenuPostPath = '/api/User/GetMenu';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGetMenuPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetMenuPost$Plain$Response(params, context) {\n      return apiUserGetMenuPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGetMenuPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetMenuPost$Plain(params, context) {\n      return this.apiUserGetMenuPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGetMenuPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetMenuPost$Json$Response(params, context) {\n      return apiUserGetMenuPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGetMenuPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetMenuPost$Json(params, context) {\n      return this.apiUserGetMenuPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiUserGetListPost()` */\n    static {\n      this.ApiUserGetListPostPath = '/api/User/GetList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGetListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetListPost$Plain$Response(params, context) {\n      return apiUserGetListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGetListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetListPost$Plain(params, context) {\n      return this.apiUserGetListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGetListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetListPost$Json$Response(params, context) {\n      return apiUserGetListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGetListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetListPost$Json(params, context) {\n      return this.apiUserGetListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiUserGetDataPost()` */\n    static {\n      this.ApiUserGetDataPostPath = '/api/User/GetData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGetDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetDataPost$Plain$Response(params, context) {\n      return apiUserGetDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGetDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetDataPost$Plain(params, context) {\n      return this.apiUserGetDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGetDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetDataPost$Json$Response(params, context) {\n      return apiUserGetDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGetDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetDataPost$Json(params, context) {\n      return this.apiUserGetDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiUserAddDataPost()` */\n    static {\n      this.ApiUserAddDataPostPath = '/api/User/AddData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserAddDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserAddDataPost$Plain$Response(params, context) {\n      return apiUserAddDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserAddDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserAddDataPost$Plain(params, context) {\n      return this.apiUserAddDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserAddDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserAddDataPost$Json$Response(params, context) {\n      return apiUserAddDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserAddDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserAddDataPost$Json(params, context) {\n      return this.apiUserAddDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiUserSaveDataPost()` */\n    static {\n      this.ApiUserSaveDataPostPath = '/api/User/SaveData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserSaveDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserSaveDataPost$Plain$Response(params, context) {\n      return apiUserSaveDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserSaveDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserSaveDataPost$Plain(params, context) {\n      return this.apiUserSaveDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserSaveDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserSaveDataPost$Json$Response(params, context) {\n      return apiUserSaveDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserSaveDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserSaveDataPost$Json(params, context) {\n      return this.apiUserSaveDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiUserRemoveUserPost()` */\n    static {\n      this.ApiUserRemoveUserPostPath = '/api/User/RemoveUser';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserRemoveUserPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserRemoveUserPost$Plain$Response(params, context) {\n      return apiUserRemoveUserPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserRemoveUserPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserRemoveUserPost$Plain(params, context) {\n      return this.apiUserRemoveUserPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserRemoveUserPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserRemoveUserPost$Json$Response(params, context) {\n      return apiUserRemoveUserPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserRemoveUserPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserRemoveUserPost$Json(params, context) {\n      return this.apiUserRemoveUserPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiUserGetUserLogPost()` */\n    static {\n      this.ApiUserGetUserLogPostPath = '/api/User/GetUserLog';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGetUserLogPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetUserLogPost$Plain$Response(params, context) {\n      return apiUserGetUserLogPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGetUserLogPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetUserLogPost$Plain(params, context) {\n      return this.apiUserGetUserLogPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiUserGetUserLogPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetUserLogPost$Json$Response(params, context) {\n      return apiUserGetUserLogPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiUserGetUserLogPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiUserGetUserLogPost$Json(params, context) {\n      return this.apiUserGetUserLogPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function UserService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || UserService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: UserService,\n        factory: UserService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return UserService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}