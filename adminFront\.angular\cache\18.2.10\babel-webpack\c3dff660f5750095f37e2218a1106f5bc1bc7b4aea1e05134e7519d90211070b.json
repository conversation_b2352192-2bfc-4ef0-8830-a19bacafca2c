{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { BaseLabelDirective } from '../../directives/label.directive';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"*\"];\nconst _c1 = a0 => ({\n  \"required-field\": a0\n});\nexport let FormGroupComponent = /*#__PURE__*/(() => {\n  class FormGroupComponent {\n    static {\n      this.ɵfac = function FormGroupComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FormGroupComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FormGroupComponent,\n        selectors: [[\"app-form-group\"]],\n        inputs: {\n          label: \"label\",\n          labelFor: \"labelFor\",\n          isRequired: \"isRequired\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 3,\n        vars: 5,\n        consts: [[\"baseLabel\", \"\", 1, \"col-4\", 3, \"for\", \"ngClass\"]],\n        template: function FormGroupComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelementStart(0, \"label\", 0);\n            i0.ɵɵtext(1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵprojection(2);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"for\", ctx.labelFor)(\"ngClass\", i0.ɵɵpureFunction1(3, _c1, ctx.isRequired));\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate(ctx.label);\n          }\n        },\n        dependencies: [CommonModule, i1.NgClass, FormsModule, BaseLabelDirective],\n        styles: [\"[_nghost-%COMP%]{margin-bottom:1rem;display:flex;align-items:center;width:100%}\"]\n      });\n    }\n  }\n  return FormGroupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}