/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetListQuotationRequest } from '../../models/get-list-quotation-request';
import { GetQuotationListResponseBase } from '../../models/get-quotation-list-response-base';

export interface ApiQuotationGetListPost$Plain$Params {
      body?: GetListQuotationRequest
}

export function apiQuotationGetListPost$Plain(http: HttpClient, rootUrl: string, params?: ApiQuotationGetListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiQuotationGetListPost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetQuotationListResponseBase>;
    })
  );
}

apiQuotationGetListPost$Plain.PATH = '/api/Quotation/GetList';
