// 選中行的樣式
.table-active {
    background-color: #e3f2fd !important;
    border-left: 3px solid #2196f3;
}

// 頁面說明區域樣式
.page-description-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.25rem;
    border-radius: 10px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1rem;

    h6 {
        font-weight: 600;
        color: #007bff;
    }

    p {
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 0.75rem;
    }

    .feature-highlights {
        .badge {
            background-color: #ffffff;
            color: #6c757d;
            border: 1px solid #dee2e6;
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
            font-weight: 500;

            i {
                color: #007bff;
            }
        }
    }
}

// 模板管理按鈕區域樣式
.template-creation-controls {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;

    // 模板操作按鈕組
    .template-action-buttons {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    // 模板狀態信息
    .template-status-info {
        margin-left: 1rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        font-weight: 500;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        &:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 0.75rem;
        }
    }



    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
        transition: all 0.2s ease;

        &:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }
    }

    .text-muted {
        font-size: 0.875rem;
        font-style: italic;
    }

    .text-success {
        font-size: 0.875rem;
        font-weight: 500;
    }
}

// 表格頭部複選框樣式
.table thead th {
    .form-check-input {
        transform: scale(1.1);
        margin: 0;
    }
}

// 表格行複選框樣式
.table tbody td {
    .form-check-input {
        transform: scale(1.1);
        margin: 0;
    }
}

// 選中狀態的行動畫效果
.table tbody tr {
    transition: all 0.2s ease;

    &.table-active {
        animation: selectRow 0.3s ease-out;
    }
}

@keyframes selectRow {
    0% {
        background-color: transparent;
        transform: scale(1);
    }

    50% {
        transform: scale(1.01);
    }

    100% {
        background-color: #e3f2fd;
        transform: scale(1);
    }
}

// 響應式設計
@media (max-width: 768px) {
    .template-creation-controls {
        .d-flex {
            flex-direction: column;
            gap: 0.5rem;

            .d-flex {
                flex-direction: row;
                gap: 0.25rem;
            }
        }

        .btn {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }
    }
}