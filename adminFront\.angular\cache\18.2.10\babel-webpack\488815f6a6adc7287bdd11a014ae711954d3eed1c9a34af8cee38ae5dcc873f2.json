{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isLeapYear\n * @category Year Helpers\n * @summary Is the given date in the leap year?\n *\n * @description\n * Is the given date in the leap year?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in the leap year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 1 September 2012 in the leap year?\n * const result = isLeapYear(new Date(2012, 8, 1))\n * //=> true\n */\nexport default function isLeapYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}