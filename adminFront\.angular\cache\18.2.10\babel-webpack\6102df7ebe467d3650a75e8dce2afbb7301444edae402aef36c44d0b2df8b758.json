{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs/operators';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/event.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@nebular/theme\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nfunction ContentManagementSalesAccountComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r1.CBuildCaseName, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r2.label, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_21_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_21_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleSwitch(ctx_r3.listFormItem.CIsLock));\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵtext(2, \" \\u9396\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_21_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_21_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F\\u5167\\u5BB9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContentManagementSalesAccountComponent_ng_container_21_button_1_Template, 3, 0, \"button\", 20)(2, ContentManagementSalesAccountComponent_ng_container_21_button_2_Template, 2, 0, \"button\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.listFormItem.formItems && ctx_r3.isUpdate);\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_22_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_22_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u770B\\u5167\\u5BB9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContentManagementSalesAccountComponent_ng_container_22_button_1_Template, 2, 0, \"button\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.listFormItem.formItems && ctx_r3.isUpdate);\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_23_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_23_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.navidateDetai());\n    });\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵtext(2, \" \\u65B0\\u589E\\u5167\\u5BB9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContentManagementSalesAccountComponent_ng_container_23_button_1_Template, 3, 0, \"button\", 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isCreate);\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_33_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.$implicit;\n    const ix_r9 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ix_r9 > 0 ? \"\\u3001\" : \"\", \" \", i_r8.CHousehold, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtemplate(4, ContentManagementSalesAccountComponent_tr_33_span_4_Template, 2, 2, \"span\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CItemName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r10.tblFormItemHouseholds);\n  }\n}\nexport let ContentManagementSalesAccountComponent = /*#__PURE__*/(() => {\n  class ContentManagementSalesAccountComponent extends BaseComponent {\n    toggleSwitch(CIsLock) {\n      if (CIsLock) {\n        this.unLock();\n      } else {\n        this.onLock();\n      }\n    }\n    constructor(_allow, router, message, _buildCaseService, _formItemService, _eventService) {\n      super(_allow);\n      this._allow = _allow;\n      this.router = router;\n      this.message = message;\n      this._buildCaseService = _buildCaseService;\n      this._formItemService = _formItemService;\n      this._eventService = _eventService;\n      // 戶型選項\n      this.houseTypeOptions = [{\n        label: '地主戶',\n        value: EnumHouseType.地主戶\n      }, {\n        label: '銷售戶',\n        value: EnumHouseType.銷售戶\n      }];\n      // 選中的戶型\n      this.selectedHouseType = EnumHouseType.銷售戶;\n      this.tempBuildCaseID = -1;\n      this.buildingSelectedOptions = [{\n        value: '',\n        label: '全部'\n      }];\n      this.pageSize = 20;\n      this._eventService.receive().pipe(tap(res => {\n        if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n          this.tempBuildCaseID = res.payload;\n        }\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.cBuildCaseSelected = null;\n      this.getUserBuildCase();\n    }\n    getUserBuildCase() {\n      this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n        body: {\n          CBuildCaseId: this.buildCaseId\n        }\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.userBuildCaseOptions = res.Entries.map(res => {\n            return {\n              CBuildCaseName: res.CBuildCaseName,\n              cID: res.cID\n            };\n          });\n          if (this.tempBuildCaseID != -1) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseID);\n            if (index >= 0) {\n              this.cBuildCaseSelected = this.userBuildCaseOptions[index];\n            } else {\n              this.cBuildCaseSelected = this.userBuildCaseOptions[0];\n            }\n          } else {\n            this.cBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n          if (this.cBuildCaseSelected && this.cBuildCaseSelected.cID && this.cBuildCaseSelected.cID > 0) {\n            this.getListFormItem();\n          }\n        }\n      })).subscribe();\n    }\n    // 移除硬編碼的類型配置，改為動態使用選中的戶型\n    // typeContentManagementSalesAccount = {\n    //   CFormType: 2,\n    // }\n    // 戶型選項變更處理\n    onHouseTypeChange() {\n      // 當戶型改變時，重新載入表單項目\n      if (this.cBuildCaseSelected?.cID) {\n        this.getListFormItem();\n      }\n    }\n    getListFormItem() {\n      this._formItemService.apiFormItemGetListFormItemPost$Json({\n        body: {\n          CBuildCaseId: this.cBuildCaseSelected.cID,\n          CFormType: this.selectedHouseType,\n          // 使用選中的戶型\n          PageIndex: this.pageIndex,\n          PageSize: this.pageSize,\n          CIsPaging: true\n        }\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.formItems = res.Entries.formItems;\n          this.listFormItem = res.Entries;\n          this.totalRecords = res.TotalItems ? res.TotalItems : 0;\n        }\n      })).subscribe();\n    }\n    onSelectionChangeBuildCase() {\n      this.getListFormItem();\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getListFormItem();\n    }\n    onLock() {\n      this._formItemService.apiFormItemLockFormItemPost$Json({\n        body: {\n          CBuildCaseId: this.cBuildCaseSelected.cID,\n          CFormId: this.listFormItem.CFormId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        } else {\n          // this.message.showErrorMSG(res.Message!);\n          this.message.showErrorMSG(\"無資料，不可鎖定\");\n        }\n        this.getListFormItem();\n      });\n    }\n    unLock() {\n      this._formItemService.apiFormItemUnlockFormItemPost$Json({\n        body: {\n          CBuildCaseID: this.cBuildCaseSelected.cID,\n          CFormId: this.listFormItem.CFormId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n        this.getListFormItem();\n      });\n    }\n    navidateDetai() {\n      // 檢查是否有選擇建案\n      if (!this.cBuildCaseSelected || !this.cBuildCaseSelected.cID || this.cBuildCaseSelected.cID <= 0) {\n        this.message.showErrorMSG(\"請先選擇建案\");\n        return;\n      }\n      // 導航到詳細頁面，使用建案ID、戶型和表單ID參數\n      this.router.navigate([`pages/content-management-sales-account/${this.cBuildCaseSelected.cID}`], {\n        queryParams: {\n          houseType: this.selectedHouseType\n        }\n      });\n    }\n    static {\n      this.ɵfac = function ContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i5.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ContentManagementSalesAccountComponent,\n        selectors: [[\"ngx-content-management-sales-account\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 36,\n        vars: 11,\n        consts: [[\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"houseType\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9078\\u64C7\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [\"class\", \"btn btn-danger mx-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"mx-1\", 3, \"click\"], [1, \"fas\", \"fa-lock\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [\"class\", \"btn btn-secondary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"class\", \"btn btn-success\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-success\", 3, \"click\"], [1, \"fas\", \"fa-plus\"]],\n        template: function ContentManagementSalesAccountComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 1);\n            i0.ɵɵtext(5, \" \\u60A8\\u53EF\\u5C07\\u65BC\\u5EFA\\u6750\\u7BA1\\u7406\\u53CA\\u65B9\\u6848\\u7BA1\\u7406\\u8A2D\\u5B9A\\u597D\\u7684\\u65B9\\u6848\\u53CA\\u6750\\u6599\\uFF0C\\u65BC\\u6B64\\u7D44\\u5408\\u6210\\u9078\\u6A23\\u5167\\u5BB9\\uFF0C\\u4E26\\u53EF\\u8A2D\\u5B9A\\u5404\\u65B9\\u6848\\u3001\\u6750\\u6599\\u53EF\\u9078\\u64C7\\u4E4B\\u6236\\u578B\\u3002 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"div\", 4)(9, \"label\", 5);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 6);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.cBuildCaseSelected, $event) || (ctx.cBuildCaseSelected = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectedChange\", function ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_11_listener() {\n              return ctx.onSelectionChangeBuildCase();\n            });\n            i0.ɵɵtemplate(12, ContentManagementSalesAccountComponent_nb_option_12_Template, 2, 2, \"nb-option\", 7);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 3)(14, \"div\", 4)(15, \"label\", 8);\n            i0.ɵɵtext(16, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"nb-select\", 9);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_17_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedHouseType, $event) || (ctx.selectedHouseType = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectedChange\", function ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_17_listener() {\n              return ctx.onHouseTypeChange();\n            });\n            i0.ɵɵtemplate(18, ContentManagementSalesAccountComponent_nb_option_18_Template, 2, 2, \"nb-option\", 7);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"div\", 3)(20, \"div\", 10);\n            i0.ɵɵtemplate(21, ContentManagementSalesAccountComponent_ng_container_21_Template, 3, 2, \"ng-container\", 11)(22, ContentManagementSalesAccountComponent_ng_container_22_Template, 2, 1, \"ng-container\", 11)(23, ContentManagementSalesAccountComponent_ng_container_23_Template, 2, 1, \"ng-container\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(24, \"div\", 12)(25, \"table\", 13)(26, \"thead\")(27, \"tr\", 14)(28, \"th\", 15);\n            i0.ɵɵtext(29, \"\\u65B9\\u6848\\u540D\\u7A31/\\u5EFA\\u6750\\u4F4D\\u7F6E\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"th\", 15);\n            i0.ɵɵtext(31, \"\\u9069\\u7528\\u6236\\u5225 \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(32, \"tbody\");\n            i0.ɵɵtemplate(33, ContentManagementSalesAccountComponent_tr_33_Template, 5, 2, \"tr\", 16);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(34, \"nb-card-footer\", 17)(35, \"ngb-pagination\", 18);\n            i0.ɵɵtwoWayListener(\"pageChange\", function ContentManagementSalesAccountComponent_Template_ngb_pagination_pageChange_35_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"pageChange\", function ContentManagementSalesAccountComponent_Template_ngb_pagination_pageChange_35_listener($event) {\n              return ctx.pageChanged($event);\n            });\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.cBuildCaseSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseType);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.formItems && ctx.formItems.length > 0 && ctx.listFormItem && !ctx.listFormItem.CIsLock && ctx.cBuildCaseSelected && ctx.cBuildCaseSelected.cID > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.formItems && ctx.formItems.length > 0 && ctx.listFormItem && ctx.listFormItem.CIsLock && ctx.cBuildCaseSelected && ctx.cBuildCaseSelected.cID > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (!ctx.formItems || ctx.formItems.length === 0) && ctx.cBuildCaseSelected && ctx.cBuildCaseSelected.cID > 0);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngForOf\", ctx.formItems);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i6.NgForOf, i6.NgIf, SharedModule, i7.NgControlStatus, i7.NgModel, i8.NbCardComponent, i8.NbCardBodyComponent, i8.NbCardFooterComponent, i8.NbCardHeaderComponent, i8.NbSelectComponent, i8.NbOptionComponent, i9.NgbPagination, i10.BreadcrumbComponent]\n      });\n    }\n  }\n  return ContentManagementSalesAccountComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}