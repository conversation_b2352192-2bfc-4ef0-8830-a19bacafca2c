export enum EnumTemplateType {
  SpaceTemplate = 1, // 空間模板
  ItemTemplate = 2   // 項目模板
}

export class EnumTemplateTypeHelper {
  static getDisplayName(templateType: EnumTemplateType): string {
    switch (templateType) {
      case EnumTemplateType.SpaceTemplate:
        return '空間模板';
      case EnumTemplateType.ItemTemplate:
        return '項目模板';
      default:
        return '未知';
    }
  }

  static getTemplateTypeList(): Array<{ value: EnumTemplateType; label: string }> {
    return [
      { value: EnumTemplateType.SpaceTemplate, label: '空間模板' },
      { value: EnumTemplateType.ItemTemplate, label: '項目模板' }
    ];
  }
}
