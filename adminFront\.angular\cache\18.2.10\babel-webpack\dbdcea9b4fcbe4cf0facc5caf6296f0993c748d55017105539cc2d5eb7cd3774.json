{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Optional, Inject, Directive, Input, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { map, takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nconst NB_SECURITY_OPTIONS_TOKEN = new InjectionToken('Nebular Security Options');\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nconst shallowObjectClone = o => Object.assign({}, o);\nconst shallowArrayClone = a => Object.assign([], a);\nconst popParent = abilities => {\n  const parent = abilities.parent;\n  delete abilities.parent;\n  return parent;\n};\n/**\n * Common acl service.\n */\nlet NbAclService = /*#__PURE__*/(() => {\n  class NbAclService {\n    static {\n      this.ANY_RESOURCE = '*';\n    }\n    constructor(settings = {}) {\n      this.settings = settings;\n      this.state = {};\n      if (settings.accessControl) {\n        this.setAccessControl(settings.accessControl);\n      }\n    }\n    /**\n     * Set/Reset ACL list\n     * @param {NbAccessControl} list\n     */\n    setAccessControl(list) {\n      for (const [role, value] of Object.entries(list)) {\n        const abilities = shallowObjectClone(value);\n        const parent = popParent(abilities);\n        this.register(role, parent, abilities);\n      }\n    }\n    /**\n     * Register a new role with a list of abilities (permission/resources combinations)\n     * @param {string} role\n     * @param {string} parent\n     * @param {[permission: string]: string|string[]} abilities\n     */\n    register(role, parent = null, abilities = {}) {\n      this.validateRole(role);\n      this.state[role] = {\n        parent: parent\n      };\n      for (const [permission, value] of Object.entries(abilities)) {\n        const resources = typeof value === 'string' ? [value] : value;\n        this.allow(role, permission, shallowArrayClone(resources));\n      }\n    }\n    /**\n     * Allow a permission for specific resources to a role\n     * @param {string} role\n     * @param {string} permission\n     * @param {string | string[]} resource\n     */\n    allow(role, permission, resource) {\n      this.validateRole(role);\n      if (!this.getRole(role)) {\n        this.register(role, null, {});\n      }\n      resource = typeof resource === 'string' ? [resource] : resource;\n      let resources = shallowArrayClone(this.getRoleResources(role, permission));\n      resources = resources.concat(resource);\n      this.state[role][permission] = resources.filter((item, pos) => resources.indexOf(item) === pos);\n    }\n    /**\n     * Check whether the role has a permission to a resource\n     * @param {string} role\n     * @param {string} permission\n     * @param {string} resource\n     * @returns {boolean}\n     */\n    can(role, permission, resource) {\n      this.validateResource(resource);\n      const parentRole = this.getRoleParent(role);\n      const parentCan = parentRole && this.can(this.getRoleParent(role), permission, resource);\n      return parentCan || this.exactCan(role, permission, resource);\n    }\n    getRole(role) {\n      return this.state[role];\n    }\n    validateRole(role) {\n      if (!role) {\n        throw new Error('NbAclService: role name cannot be empty');\n      }\n    }\n    validateResource(resource) {\n      if (!resource || [NbAclService.ANY_RESOURCE].includes(resource)) {\n        throw new Error(`NbAclService: cannot use empty or bulk '*' resource placeholder with 'can' method`);\n      }\n    }\n    exactCan(role, permission, resource) {\n      const resources = this.getRoleResources(role, permission);\n      return resources.includes(resource) || resources.includes(NbAclService.ANY_RESOURCE);\n    }\n    getRoleResources(role, permission) {\n      return this.getRoleAbilities(role)[permission] || [];\n    }\n    getRoleAbilities(role) {\n      const abilities = shallowObjectClone(this.state[role] || {});\n      popParent(shallowObjectClone(this.state[role] || {}));\n      return abilities;\n    }\n    getRoleParent(role) {\n      return this.state[role] ? this.state[role].parent : null;\n    }\n    static {\n      this.ɵfac = function NbAclService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbAclService)(i0.ɵɵinject(NB_SECURITY_OPTIONS_TOKEN, 8));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbAclService,\n        factory: NbAclService.ɵfac\n      });\n    }\n  }\n  return NbAclService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass NbRoleProvider {}\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * Access checker service.\n *\n * Injects `NbRoleProvider` to determine current user role, and checks access permissions using `NbAclService`\n */\nlet NbAccessChecker = /*#__PURE__*/(() => {\n  class NbAccessChecker {\n    constructor(roleProvider, acl) {\n      this.roleProvider = roleProvider;\n      this.acl = acl;\n    }\n    /**\n     * Checks whether access is granted or not\n     *\n     * @param {string} permission\n     * @param {string} resource\n     * @returns {Observable<boolean>}\n     */\n    isGranted(permission, resource) {\n      return this.roleProvider.getRole().pipe(map(role => Array.isArray(role) ? role : [role]), map(roles => {\n        return roles.some(role => this.acl.can(role, permission, resource));\n      }));\n    }\n    static {\n      this.ɵfac = function NbAccessChecker_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbAccessChecker)(i0.ɵɵinject(NbRoleProvider), i0.ɵɵinject(NbAclService));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbAccessChecker,\n        factory: NbAccessChecker.ɵfac\n      });\n    }\n  }\n  return NbAccessChecker;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet NbIsGrantedDirective = /*#__PURE__*/(() => {\n  class NbIsGrantedDirective {\n    constructor(templateRef, viewContainer, accessChecker) {\n      this.templateRef = templateRef;\n      this.viewContainer = viewContainer;\n      this.accessChecker = accessChecker;\n      this.destroy$ = new Subject();\n      this.hasView = false;\n    }\n    set nbIsGranted([permission, resource]) {\n      this.accessChecker.isGranted(permission, resource).pipe(takeUntil(this.destroy$)).subscribe(can => {\n        if (can && !this.hasView) {\n          this.viewContainer.createEmbeddedView(this.templateRef);\n          this.hasView = true;\n        } else if (!can && this.hasView) {\n          this.viewContainer.clear();\n          this.hasView = false;\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    static {\n      this.ɵfac = function NbIsGrantedDirective_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbIsGrantedDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(NbAccessChecker));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: NbIsGrantedDirective,\n        selectors: [[\"\", \"nbIsGranted\", \"\"]],\n        inputs: {\n          nbIsGranted: \"nbIsGranted\"\n        }\n      });\n    }\n  }\n  return NbIsGrantedDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet NbSecurityModule = /*#__PURE__*/(() => {\n  class NbSecurityModule {\n    static forRoot(nbSecurityOptions) {\n      return {\n        ngModule: NbSecurityModule,\n        providers: [{\n          provide: NB_SECURITY_OPTIONS_TOKEN,\n          useValue: nbSecurityOptions\n        }, NbAclService, NbAccessChecker]\n      };\n    }\n    static {\n      this.ɵfac = function NbSecurityModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbSecurityModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: NbSecurityModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [CommonModule]\n      });\n    }\n  }\n  return NbSecurityModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NB_SECURITY_OPTIONS_TOKEN, NbAccessChecker, NbAclService, NbIsGrantedDirective, NbRoleProvider, NbSecurityModule };\n//# sourceMappingURL=nebular-security.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}