{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function find(predicate, thisArg) {\n  return operate(createFind(predicate, thisArg, 'value'));\n}\nexport function createFind(predicate, thisArg, emit) {\n  const findIndex = emit === 'index';\n  return (source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const i = index++;\n      if (predicate.call(thisArg, value, i, source)) {\n        subscriber.next(findIndex ? i : value);\n        subscriber.complete();\n      }\n    }, () => {\n      subscriber.next(findIndex ? -1 : undefined);\n      subscriber.complete();\n    }));\n  };\n}\n//# sourceMappingURL=find.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}