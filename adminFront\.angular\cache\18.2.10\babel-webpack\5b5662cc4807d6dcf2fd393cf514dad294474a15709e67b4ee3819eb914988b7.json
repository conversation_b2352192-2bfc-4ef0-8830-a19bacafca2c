{"ast": null, "code": "import { ServiceBase } from './api/services/service-base';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let FileService = /*#__PURE__*/(() => {\n  class FileService extends ServiceBase {\n    constructor(http) {\n      super(http);\n      this.baseUrl = `${this.apiBaseUrl}/File`;\n    }\n    getFile(relativePath, fileName) {\n      const url = `${this.baseUrl}/GetFile`;\n      const params = {\n        relativePath: relativePath,\n        fileName: fileName\n      };\n      return this.http.get(url, {\n        params: params,\n        responseType: 'blob'\n      });\n    }\n    static {\n      this.ɵfac = function FileService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FileService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: FileService,\n        factory: FileService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FileService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}