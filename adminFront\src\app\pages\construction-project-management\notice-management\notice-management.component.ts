import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { NbDialogService } from '@nebular/theme';
import { MessageService } from 'src/app/shared/services/message.service';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { BuildCaseService, FileService, HouseService, RegularNoticeFileService, SpecialNoticeFileService } from 'src/services/api/services';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { GetListHouseHoldRes, GetSpecialNoticeFileListRes, HouseSpecialNoticeFile, SpecialNoticeFileList, TblExamineLog } from 'src/services/api/models';
import { tap } from 'rxjs';
import { SharedModule } from '../../components/shared.module';
import { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from '../../components/base/baseComponent';
import { EnumFileType } from 'src/app/shared/enum/enumFileType';
import { NoticeServiceCustom } from 'src/app/@core/service/notice.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { environment } from 'src/environments/environment';
import { DateFormatHourPipe } from 'src/app/@theme/pipes';
import { FileUploadComponent, FileUploadConfig, FileUploadResult } from '../../components/file-upload/file-upload.component';
import { HouseholdItem, BuildingData } from 'src/app/shared/components/household-binding/household-binding.component';

export interface HouseList {
  CFloor?: number | null;
  CHouseHold?: string | null;
  CHouseID?: number;
  CID?: number;
  CIsSelect?: boolean | null;
  CHouseType?: number | null;
  CIsEnable?: boolean | null;
}
export interface SaveSpecialNoticeFileCus {
  CFileUrl?: string
  CNoticeType?: number
  CBuildCaseId?: number
  CFile?: Blob
  CHouse?: Array<string>
  CSpecialNoticeFileId?: number
  CIsSelectAll?: boolean
  selectedCNoticeType?: any
  CExamineNote?: string | null;
  tblExamineLogs?: TblExamineLog[],
  tblSpecialNoticeFileHouses: any
  CExamineStauts?: number;
}

@Component({
  selector: 'ngx-notice-management',
  templateUrl: './notice-management.component.html',
  styleUrls: ['./notice-management.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule, AppSharedModule, DatePipe, DateFormatHourPipe, FileUploadComponent],
})

export class NoticeManagementComponent extends BaseComponent implements OnInit {
  constructor(
    private _allow: AllowHelper,
    private dialogService: NbDialogService,
    private message: MessageService,
    private valid: ValidationHelper,
    private _utilityService: UtilityService,
    private _buildCaseService: BuildCaseService,
    private _specialNoticeFileService: SpecialNoticeFileService,
    private _regularNoticeFileService: RegularNoticeFileService,
    private _houseService: HouseService,
    private _specialChangeCustomService: NoticeServiceCustom,
    private _fileService: FileService,
    private _houseService2: HouseService
  ) { super(_allow) }

  override ngOnInit(): void {
    this.getUserBuildCase()
  }

  selectedCBuildCase: any

  override pageFirst = 1;
  override pageSize = 10;
  override pageIndex = 1;
  override totalRecords = 0;
  pageFirstSales = 1;
  pageSizeSales = 10;
  pageIndexSales = 1;
  totalRecordsSales = 0;


  saveSpecialNoticeFile: SaveSpecialNoticeFileCus

  buildCaseOptions: any[] = [{ label: '全部', value: '' }]

  cNoticeTypeOptions: any[] = [
    { label: '地主戶', value: 1 },
    { label: '銷售戶', value: 2 }
  ]
  seletectedNoticeType: any

  typeContentManagementLandowner = {
    CFormType: 1,
    CNoticeType: 1
  }
  houseList2D: HouseList[][]
  userBuildCaseOptions: any
  // 新增：新戶別選擇器相關屬性
  buildingData: BuildingData = {} // 存放建築物戶別資料
  selectedHouseholds: number[] = [] // 選中的戶別ID (使用 houseId)
  isBuildingDataLoading: boolean = false // 新增：建築物資料載入狀態

  pageChanged(newPage: number) {
    this.pageIndex = newPage;
  }
  pageChangedSales(newPage: number) {
    this.pageIndexSales = newPage;
  }

  // 檔案上傳配置
  fileUploadConfig: FileUploadConfig = {
    acceptedTypes: ['application/pdf'],
    acceptedFileRegex: /pdf/i,
    acceptAttribute: 'application/pdf',
    label: '上傳檔案',
    helpText: '*請上傳PDF格式',
    required: false,
    disabled: false,
    autoFillName: false,
    buttonText: '上傳',
    buttonIcon: 'fa-solid fa-cloud-arrow-up',
    maxFileSize: 10,
    multiple: false,
    showPreview: false
  };

  selectedFile: FileUploadResult | null = null;

  // 檔案選擇事件處理
  onFileUpload(fileResult: FileUploadResult) {
    this.selectedFile = fileResult;
  }
  // 檔案清除事件處理
  onFileClear() {
    this.selectedFile = null;
  }

  userBuildCaseSelected: any
  onChangeBuildCase() {
    if (this.selectedCBuildCase.value) {
      this.getSpecialNoticeFileHouseHoldList()
      this.getSpecialNoticeFileList()
      // 新增：載入建築物戶別資料
      this.loadBuildingDataFromAPI()
    }
  }

  getUserBuildCase() {
    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.userBuildCaseOptions = res.Entries.map(res => {
            return {
              label: res.CBuildCaseName,
              value: res.cID
            }
          })
          this.selectedCBuildCase = this.userBuildCaseOptions[0]
          if (this.selectedCBuildCase.value) {
            this.getSpecialNoticeFileHouseHoldList()
            this.getSpecialNoticeFileList()
            // 新增：載入建築物戶別資料
            this.loadBuildingDataFromAPI()
          }
        }
      }),
    ).subscribe()
  }

  groupByFloor(customerData: HouseList[]): HouseList[][] {

    const groupedData: HouseList[][] = [];
    const uniqueFloors = Array.from(new Set(
      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]
    ));
    for (const floor of uniqueFloors) {
      groupedData.push([]);
    }
    for (const customer of customerData) {
      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number);
      if (floorIndex !== -1) {
        groupedData[floorIndex].push({
          CIsSelect: customer?.CIsSelect || false,
          CHouseID: customer.CID,
          CHouseType: customer.CHouseType,
          CFloor: customer.CFloor,
          CHouseHold: customer.CHouseHold,
          CIsEnable: customer.CIsEnable,
        });
      }
    }
    return groupedData;
  }

  listSpecialNoticeFile: GetSpecialNoticeFileListRes

  getSpecialNoticeFileList() {
    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({
      body: {
        CBuildCaseId: this.selectedCBuildCase.value,
        PageIndexLandLord: this.pageIndex,
        PageIndexSales: this.pageIndexSales,
        PageSizeLandLord: this.pageSize,
        PageSizeSales: this.pageSizeSales,
      }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.listSpecialNoticeFile = res.Entries
        this.totalRecords = res.Entries.TotalListLandLords || 0
        this.totalRecordsSales = res.Entries.TotalListSales || 0
      }
    })
  }
  listSpecialNoticeFileHouseHold: GetListHouseHoldRes[]
  houseHoldList: string[];

  onStatusChange(newStatus: any) {
    // 觸發建築物資料重新篩選
    this.onNoticeTypeChange();
  }

  getSpecialNoticeFileHouseHoldList() {
    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({
      body: this.selectedCBuildCase.value
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.listSpecialNoticeFileHouseHold = res.Entries
      }
    })
  }

  createArrayObjectFromArray(a: string[], b: { CSpecialNoticeFileHouseholdId: number, CSpecialNoticeFileId: number, CHousehold: string, CIsSelected: boolean }[] | any[]): { [key: string]: boolean } {
    const c: { [key: string]: boolean } = {};
    for (const item of a) {
      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);
      c[item] = !!matchingItem;
    }
    return c;
  }


  getItemByValue(value: any, options: any[]) {
    for (const item of options) {
      if (item.value === value) {
        return item;
      }
    }
    return null;
  }

  cExamineStatusOption = ['待審核', '已通過', '已駁回']

  updateCIsEnable(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {
    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));
    return houseList2D.map(floorArray => {
      return floorArray.map(item => {
        const key = `${item.CHouseHold}-${item.CFloor}`;
        if (selectedHouses.has(key)) {
          item.CIsSelect = true;
        } else {
          item.CIsSelect = false;
        }
        return item;
      });
    });
  }

  addCIsSelectToA(A: any[], B: any[]): any[] {
    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));
    return A.map(item => {
      const key = `${item.CHouseHold}-${item.CFloor}`;
      return {
        ...item,
        CIsSelect: mapB.has(key) ? mapB.get(key) : false
      };
    });
  }

  getSpecialNoticeFileById(item: any, ref: any) {
    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({
      body: item.CSpecialNoticeFileId
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        const data = res.Entries
        this.saveSpecialNoticeFile = {
          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,
          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,
          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,
          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,
          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],
          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,
          CExamineNote: data.CExamineNote,
          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter((i: any) => i.CIsSelect),
          CExamineStauts: data.CExamineStauts
        }
        // 新增：初始化選中的戶別ID
        this.selectedHouseholds = this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses?.map((house: any) => house.CHouseID) || []

        // 新增：根據載入的通知類型重新篩選建築物資料
        this.loadBuildingDataFromAPI()

        this.dialogService.open(ref)
      }
    })
  }

  isNew = true;

  openPdfInNewTab(file: SpecialNoticeFileList) {
    // 檢查檔案資訊是否存在
    if (!file.CFile || !file.CFileName) {
      this.message.showErrorMSG('檔案資訊不完整');
      return;
    }

    // 使用 FileService 下載檔案
    this._fileService.getFile(file.CFile, file.CFileName).subscribe({
      next: (blob: Blob) => {
        // 建立 Blob URL
        const blobUrl = URL.createObjectURL(blob);

        // 在新頁籤開啟 PDF
        const newWindow = window.open(blobUrl, '_blank');

        if (!newWindow) {
          this.message.showErrorMSG('無法開啟新視窗，請檢查瀏覽器設定');
          URL.revokeObjectURL(blobUrl); // 清理 URL
          return;
        }

        // 當新視窗關閉時清理 Blob URL
        newWindow.addEventListener('beforeunload', () => {
          URL.revokeObjectURL(blobUrl);
        });
      },
      error: (error) => {
        console.error('下載檔案失敗:', error);
        this.message.showErrorMSG('檔案下載失敗，請稍後再試');
      }
    });
  } openModel(ref: any, item?: any) {
    this.isNew = true
    this.onFileClear() // 替換 clearImage()
    this.selectedHouseholds = [] // 新增：清空選中的戶別
    this.saveSpecialNoticeFile = {
      CNoticeType: 1,
      CBuildCaseId: undefined,
      CFile: undefined,
      CHouse: [],
      CSpecialNoticeFileId: undefined,
      CIsSelectAll: false,
      selectedCNoticeType: this.cNoticeTypeOptions[0],
      CExamineNote: '',
      tblSpecialNoticeFileHouses: undefined
    }

    if (item) {
      this.isNew = false
      this.getSpecialNoticeFileById(item, ref)
    } else {
      this.isNew = true
      this.dialogService.open(ref)
      // 新增：在對話框開啟後載入建築物資料（確保預設通知類型已設置）
      setTimeout(() => {
        this.loadBuildingDataFromAPI()
      }, 0);
    }
  }

  removeBase64Prefix(base64String: any) {
    const prefixIndex = base64String.indexOf(",");
    if (prefixIndex !== -1) {
      return base64String.substring(prefixIndex + 1);
    }
    return base64String;
  }

  onDelete(item: any) {
    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {
      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({
        body: item.CSpecialNoticeFileId
      }).subscribe(res => {
        if (res.StatusCode === 0) {
          this.message.showSucessMSG("執行成功");
          this.onChangeBuildCase()
        }
      })
    }
    return item
  }

  getTrueKeys(inputDict: { [key: string]: boolean }): string[] {
    const trueKeys: string[] = [];
    for (const key in inputDict) {
      if (inputDict[key]) {
        trueKeys.push(key);
      }
    }
    return trueKeys;
  }
  flattenAndFilter(data: any[][]): HouseSpecialNoticeFile[] {
    const flattened: HouseSpecialNoticeFile[] = [];
    for (const floorData of data) {
      for (const house of floorData) {
        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {
          flattened.push({
            CHouseID: house.CHouseID,
            CIsSelect: house.CIsSelect,
          });
        }
      }
    }
    return flattened;
  }

  // 新增：從新戶別選擇器的選擇結果轉換為原有格式
  convertSelectedHouseholdsToHouseSpecialNoticeFile(): HouseSpecialNoticeFile[] {
    const result: HouseSpecialNoticeFile[] = [];

    // 使用 buildingData 和 selectedHouseholds 來產生結果
    if (this.selectedHouseholds && this.selectedHouseholds.length > 0 && this.buildingData) {
      // 從 buildingData 中找到對應的戶別資訊
      Object.values(this.buildingData).forEach(buildings => {
        buildings.forEach(house => {
          if (house.houseId &&
            this.selectedHouseholds.includes(house.houseId) &&
            !house.isDisabled) {
            result.push({
              CHouseID: house.houseId,
              CIsSelect: true,
            });
          }
        });
      });
    }

    return result;
  } onSaveSpecialNoticeFile(ref: any) {
    const selectedHouses = this.convertSelectedHouseholdsToHouseSpecialNoticeFile();

    // 除錯資訊
    console.log('選中的戶別ID:', this.selectedHouseholds);
    console.log('轉換後的戶別資料:', selectedHouses);
    console.log('建築物資料 keys:', Object.keys(this.buildingData));
    console.log('建築物資料總戶數:', Object.values(this.buildingData).reduce((total, houses) => total + houses.length, 0));
    console.log('通知類型:', this.saveSpecialNoticeFile.selectedCNoticeType);

    const param = {
      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,
      CBuildCaseId: this.selectedCBuildCase.value,
      CFile: this.selectedFile ? this.selectedFile.CFileUpload : undefined,
      CHouse: selectedHouses, // 使用新的轉換方法
      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,
      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,
      CExamineNote: this.saveSpecialNoticeFile.CExamineNote
    }

    this.validation(param.CHouse)

    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return
    } this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {
      if (res && res.body! && res.body.StatusCode! === 0) {
        this.message.showSucessMSG("執行成功");
        this.onFileClear() // 替換 clearImage()
        this.getSpecialNoticeFileList()
        ref.close();
      } else {
        this.message.showErrorMSG(res && res.body && res.body.Message!);
      }
    })
  }


  onClose(ref: any) {
    ref.close();
  }

  validation(CHouse: any[]) {
    this.valid.clear();
    if (this.isNew && !this.selectedFile) {
      this.valid.required('[檔案]', '')
    }
    if (!(CHouse.length > 0)) {
      this.valid.required('[適用戶別]', '')
    }
    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote)
  }

  getActionName(actionID: number | undefined) {
    let textR = "";
    if (actionID != undefined) {
      switch (actionID) {
        case 1:
          textR = "傳送";
          break;
        case 2:
          textR = "通過";
          break;
        case 3:
          textR = "駁回";
          break;
        default:
          break;
      }
    }
    return textR;
  }


  updateCIsClick(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {
    const selectedHouses = houseSpecialNoticeFile.map(item => item.CHouseID);
    return houseList2D.map(floorArray => {
      return floorArray.map(item => {
        if (selectedHouses.includes(item.CHouseID)) {
          item.CIsSelect = true;
        } else {
          item.CIsSelect = false;
        }
        return item;
      });
    });
  }  // 新增：載入建築物戶別資料 (使用 GetDropDown API)
  private loadBuildingDataFromAPI(): void {
    if (!this.selectedCBuildCase?.value) return;

    this.isBuildingDataLoading = true;
    this.buildingData = {}; // 清空舊資料

    this._houseService2.apiHouseGetDropDownPost$Json({
      buildCaseId: this.selectedCBuildCase.value
    }).subscribe({
      next: (response) => {
        this.isBuildingDataLoading = false;
        if (response.Entries) {
          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);
        }
      },
      error: (error) => {
        this.isBuildingDataLoading = false;
        console.error('Error loading building data from API:', error);
        // 清空建築物資料，避免使用過時的資料
        this.buildingData = {};
      }
    });
  }  // 新增：將 API 回應轉換為建築物資料格式
  private convertApiResponseToBuildingData(entries: any): BuildingData {
    const buildingData: BuildingData = {};

    Object.entries(entries).forEach(([building, houses]: [string, any]) => {
      // 不在父元件中預先篩選，將所有戶別資料傳給子元件
      // 篩選邏輯由戶別綁定元件根據 preFilterHouseType 參數處理
      buildingData[building] = houses.map((house: any) => ({
        houseName: house.HouseName,
        building: house.Building,
        floor: house.Floor,
        houseId: house.HouseId,
        houseType: house.HouseType, // 保留戶別類型資訊供子元件篩選使用
        isSelected: false,
        isDisabled: false
      }));
    });

    return buildingData;
  }
  // 新增：將現有的 houseList2D 轉換為新戶別選擇器的格式
  convertHouseList2DToBuildingData(): BuildingData {
    const buildingData: BuildingData = {};

    if (!this.houseList2D || this.houseList2D.length === 0) {
      return buildingData;
    }

    this.houseList2D.forEach(row => {
      row.forEach(house => {
        if (!house.CHouseHold) return;

        // 不在父元件中預先篩選，將所有戶別資料傳給子元件
        // 篩選邏輯由戶別綁定元件根據 preFilterHouseType 參數處理

        // 嘗試從戶別名稱中提取建築物代碼（假設格式為 A001, B002 等）
        const buildingMatch = house.CHouseHold.match(/^([A-Z]+)/);
        const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';

        if (!buildingData[building]) {
          buildingData[building] = [];
        } buildingData[building].push({
          houseName: house.CHouseHold,
          building: building,
          floor: house.CFloor ? `${house.CFloor}F` : undefined,
          houseId: house.CHouseID,
          houseType: house.CHouseType || undefined, // 新增：戶別類型
          isSelected: house.CIsSelect || false,
          isDisabled: !house.CIsEnable ||
            !(this.saveSpecialNoticeFile?.selectedCNoticeType?.value === house.CHouseType) ||
            this.saveSpecialNoticeFile?.CExamineStauts === 0
        });
      });
    });

    return buildingData;
  }  // 新增：處理戶別選擇變更
  onHouseholdSelectionChange(selectedItems: HouseholdItem[]) {
    // 更新 selectedHouseholds (使用 houseId)
    this.selectedHouseholds = selectedItems.map(item => item.houseId).filter(id => id !== undefined) as number[];
  }
  // 新增：處理戶別ID變更事件
  onHouseholdIdChange(selectedIds: number[]) {
    this.selectedHouseholds = selectedIds;
  }
  // 新增：處理通知類型變更
  onNoticeTypeChange() {
    // 清空當前選擇的戶別
    this.selectedHouseholds = [];

    // 不需要重新載入資料，因為篩選邏輯已移至戶別綁定元件內部
    // 戶別綁定元件會根據 preFilterHouseType 參數自動篩選顯示的戶別
  }

  // 新增：取得戶別選擇的提醒文案
  getHouseholdReminderText(): string {
    if (!this.saveSpecialNoticeFile?.selectedCNoticeType) {
      return '請先選擇檔案類型，系統將根據類型篩選對應的戶別';
    }

    const noticeType = this.saveSpecialNoticeFile.selectedCNoticeType;
    if (noticeType.value === 1) {
      return '目前篩選顯示：地主戶，只會顯示地主戶相關的戶別選項';
    } else if (noticeType.value === 2) {
      return '目前篩選顯示：銷售戶，只會顯示銷售戶相關的戶別選項';
    }

    return '';
  }
}

