{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfQuarter\n * @category Quarter Helpers\n * @summary Return the end of a year quarter for the given date.\n *\n * @description\n * Return the end of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of a quarter\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of a quarter for 2 September 2014 11:55:00:\n * const result = endOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 23:59:59.999\n */\nexport default function endOfQuarter(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var currentMonth = date.getMonth();\n  var month = currentMonth - currentMonth % 3 + 3;\n  date.setMonth(month, 0);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}