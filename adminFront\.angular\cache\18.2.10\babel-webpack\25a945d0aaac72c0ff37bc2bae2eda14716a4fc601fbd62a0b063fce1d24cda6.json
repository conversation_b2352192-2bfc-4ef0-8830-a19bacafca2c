{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule } from '@nebular/theme';\nimport { TemplateGetListResponse } from 'src/services/api/models';\nimport { EnumTemplateType } from 'src/app/shared/enum/enumTemplateType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"nb-checkbox\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const template_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(template_r2.selected, $event) || (template_r2.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onTemplateItemChange());\n    });\n    i0.ɵɵelementStart(2, \"div\", 27)(3, \"div\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 30);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 31);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const template_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", template_r2.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r2.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", template_r2.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u72C0\\u614B: \", template_r2.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u985E\\u578B: \", template_r2.CTemplateType, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template, 11, 5, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templates);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"nb-icon\", 33);\n    i0.ɵɵtext(2, \" \\u66AB\\u7121\\u53EF\\u7528\\u7684\\u6A21\\u677F\\u9805\\u76EE\\uFF0C\\u8ACB\\u7A0D\\u5F8C\\u518D\\u8A66\\u6216\\u806F\\u7E6B\\u7BA1\\u7406\\u54E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"nb-icon\", 21);\n    i0.ɵɵtext(4, \"\\u9078\\u64C7\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 22);\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_12_div_6_Template, 2, 1, \"div\", 23)(7, SpaceTemplateSelectorComponent_div_12_ng_template_7_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const noTemplates_r4 = i0.ɵɵreference(8);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templates.length > 0)(\"ngIfElse\", noTemplates_r4);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u4F4D\\u7F6E: \", detail_r5.CLocation, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵelement(1, \"i\", 66);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u55AE\\u50F9: NT$ \", i0.ɵɵpipeBind1(3, 1, detail_r5.CUnitPrice), \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 67);\n    i0.ɵɵelement(1, \"i\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u55AE\\u4F4D: \", detail_r5.CUnit, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62);\n    i0.ɵɵtemplate(2, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_2_Template, 4, 3, \"span\", 63)(3, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_3_Template, 3, 1, \"span\", 64);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r5.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r5.CUnit);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"div\", 57);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template, 2, 1, \"div\", 58)(7, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_Template, 4, 2, \"div\", 59);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r6 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r5.CPart);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r5.CLocation);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate && (detail_r5.CUnitPrice || detail_r5.CUnit));\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"span\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 52);\n    i0.ɵɵtemplate(5, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template, 8, 4, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5305\\u542B \", ctx_r2.getTemplateDetails(item_r7.CTemplateId).length, \" \\u500B\\u660E\\u7D30\\u9805\\u76EE\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getTemplateDetails(item_r7.CTemplateId));\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"nb-icon\", 33);\n    i0.ɵɵtext(2, \" \\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u660E\\u7D30\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"h5\", 43);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 44)(5, \"span\", 45);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 46);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 47);\n    i0.ɵɵtemplate(10, SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template, 6, 2, \"div\", 48)(11, SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template, 3, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const noDetails_r8 = i0.ɵɵreference(12);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r7.CTemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", item_r7.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTemplateDetails(item_r7.CTemplateId).length > 0)(\"ngIfElse\", noDetails_r8);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"strong\");\n    i0.ɵɵelement(3, \"nb-icon\", 72);\n    i0.ɵɵtext(4, \"\\u885D\\u7A81\\u6AA2\\u6E2C\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6AA2\\u6E2C\\u5230 \", ctx_r2.getConflictCount(), \" \\u500B\\u9805\\u76EE\\u53EF\\u80FD\\u8207\\u73FE\\u6709\\u9700\\u6C42\\u91CD\\u8907\\uFF0C\\u7CFB\\u7D71\\u5C07\\u81EA\\u52D5\\u8655\\u7406\\u885D\\u7A81\\u9805\\u76EE\\u3002 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 34)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"nb-icon\", 35);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u5957\\u7528\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36)(6, \"div\", 37);\n    i0.ɵɵtext(7, \" \\u5C07\\u5957\\u7528 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9, \"\\u901A\\u7528\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_13_div_12_Template, 13, 5, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SpaceTemplateSelectorComponent_div_13_div_13_Template, 6, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\\uFF1A\", ctx_r2.getSelectedItems().length, \"\\u500B\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSelectedItems());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasConflicts());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.previousStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0B\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canProceed());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyTemplate());\n    });\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u5957\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.getSelectedItems().length === 0);\n  }\n}\nexport class SpaceTemplateSelectorComponent {\n  constructor(templateService, dialogRef) {\n    this.templateService = templateService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = '';\n    this.CTemplateType = EnumTemplateType.SpaceTemplate;\n    this.templateApplied = new EventEmitter();\n    // 暴露枚舉給模板使用\n    this.EnumTemplateType = EnumTemplateType;\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templates = []; // 直接使用 API 資料\n    this.selectedTemplateDetails = new Map(); // 存儲已載入的模板詳情\n  }\n  ngOnInit() {\n    // 組件初始化時載入模板\n    this.loadTemplatesFromAPI();\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: this.CTemplateType,\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 直接使用 API 資料，只添加 selected 屬性\n          this.templates = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        this.templates = [];\n      }\n    });\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  getSelectedItems() {\n    return this.templates.filter(item => item.selected);\n  }\n  getSelectedTotalPrice() {\n    // 由於 API 沒有價格資訊，返回 0\n    return 0;\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      if (this.currentStep === 1) {\n        // 進入步驟2前，載入選中模板的詳情\n        this.loadSelectedTemplateDetails();\n      }\n      this.currentStep++;\n    }\n  }\n  loadSelectedTemplateDetails() {\n    const selectedItems = this.getSelectedItems();\n    selectedItems.forEach(item => {\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\n        // 只載入尚未載入過的模板詳情\n        this.loadTemplateDetailById(item.CTemplateId);\n      }\n    });\n  }\n  loadTemplateDetailById(templateId) {\n    const args = {\n      templateId: templateId\n    };\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          this.selectedTemplateDetails.set(templateId, response.Entries);\n        }\n      },\n      error: () => {\n        // 錯誤處理：設置空陣列\n        this.selectedTemplateDetails.set(templateId, []);\n      }\n    });\n  }\n  getTemplateDetails(templateId) {\n    return this.selectedTemplateDetails.get(templateId) || [];\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要套用的模板項目',\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: '通用模板',\n      selectedItems: this.getSelectedItems(),\n      templateDetails: new Map(this.selectedTemplateDetails),\n      // 傳遞已載入的模板明細\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  // 移除不需要的方法\n  // onBackdropClick 由 NbDialog 自動處理\n  reset() {\n    this.currentStep = 1;\n    this.templates = [];\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    // 保留 templates 資料，只重置選擇狀態\n    this.templates.forEach(template => {\n      template.selected = false;\n    });\n    // 清空詳情快取\n    this.selectedTemplateDetails.clear();\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService), i0.ɵɵdirectiveInject(i2.NbDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceTemplateSelectorComponent,\n      selectors: [[\"app-space-template-selector\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        CTemplateType: \"CTemplateType\"\n      },\n      outputs: {\n        templateApplied: \"templateApplied\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 24,\n      vars: 16,\n      consts: [[\"noTemplates\", \"\"], [\"noDetails\", \"\"], [1, \"space-template-dialog\"], [1, \"space-template-header\"], [1, \"space-template-title\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"close-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"space-template-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"space-template-footer\"], [1, \"progress-info\"], [1, \"step-buttons\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"template-selection\"], [1, \"section-title\"], [\"icon\", \"layers-outline\", 1, \"mr-2\"], [1, \"template-list\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-item\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"template-info\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"item-status\"], [1, \"item-type\"], [1, \"no-templates\"], [\"icon\", \"info-outline\", 1, \"mr-2\"], [1, \"confirmation-area\"], [\"icon\", \"checkmark-circle-outline\", 1, \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"selected-templates-details\"], [\"class\", \"template-detail-section\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"conflict-warning\", 4, \"ngIf\"], [1, \"template-detail-section\"], [1, \"template-detail-header\"], [1, \"template-name\"], [1, \"template-meta\"], [1, \"template-id\"], [1, \"template-status\"], [1, \"template-detail-content\"], [\"class\", \"detail-items\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-items\"], [1, \"detail-items-header\"], [1, \"detail-count\"], [1, \"detail-items-list\"], [\"class\", \"detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\"], [1, \"detail-index\"], [1, \"detail-info\"], [1, \"detail-part\"], [\"class\", \"detail-location\", 4, \"ngIf\"], [\"class\", \"detail-pricing\", 4, \"ngIf\"], [1, \"detail-location\"], [1, \"detail-pricing\"], [1, \"pricing-info\"], [\"class\", \"price-item\", 4, \"ngIf\"], [\"class\", \"unit-item\", 4, \"ngIf\"], [1, \"price-item\"], [1, \"fas\", \"fa-dollar-sign\"], [1, \"unit-item\"], [1, \"fas\", \"fa-ruler\"], [1, \"no-details\"], [1, \"conflict-warning\"], [1, \"warning-text\"], [\"icon\", \"alert-triangle-outline\", 1, \"mr-1\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\", \"disabled\"]],\n      template: function SpaceTemplateSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\", 3)(2, \"div\", 4);\n          i0.ɵɵtext(3, \"\\u7A7A\\u9593\\u6A21\\u677F\\u9078\\u64C7\\u5668\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_4_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(5, \"nb-icon\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 7)(7, \"div\", 8)(8, \"div\", 9);\n          i0.ɵɵtext(9, \"1. \\u9078\\u64C7\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 9);\n          i0.ɵɵtext(11, \"2. \\u78BA\\u8A8D\\u5957\\u7528\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_12_Template, 9, 2, \"div\", 10)(13, SpaceTemplateSelectorComponent_div_13_Template, 14, 3, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"nb-card-footer\", 11)(15, \"div\", 12)(16, \"span\");\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_19_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(20, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(21, SpaceTemplateSelectorComponent_button_21_Template, 2, 0, \"button\", 15)(22, SpaceTemplateSelectorComponent_button_22_Template, 2, 1, \"button\", 16)(23, SpaceTemplateSelectorComponent_button_23_Template, 2, 1, \"button\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(8, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(12, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.getProgressText());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.DecimalPipe, FormsModule, i4.NgControlStatus, i4.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbIconModule, i2.NbIconComponent, NbCheckboxModule, i2.NbCheckboxComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n\\n\\n.space-template-dialog[_ngcontent-%COMP%] {\\n  min-width: 600px;\\n  max-width: 800px;\\n  min-height: 500px;\\n  max-height: 80vh;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.25rem 1.5rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  background-color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  padding: 0.25rem;\\n  min-width: auto;\\n  border: none;\\n  background: transparent;\\n  color: #ADB5BD;\\n  transition: 0.15s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #2C3E50;\\n  background-color: rgba(184, 166, 118, 0.05);\\n  border-radius: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  overflow-y: auto;\\n  max-height: 60vh;\\n  background-color: #FFFFFF;\\n  \\n\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 2rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  padding-bottom: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  margin: 0 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  transition: 0.3s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background-color: #28A745;\\n  color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #F8F9FA;\\n  color: #ADB5BD;\\n  border: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  \\n\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #AE9B66;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  transition: 0.3s ease;\\n  background-color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  border-color: #AE9B66;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n  background-color: rgba(184, 166, 118, 0.03);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  flex: 1;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #ADB5BD;\\n  background-color: #F8F9FA;\\n  border-radius: 0.375rem;\\n  border: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #17A2B8;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F0EDE5 0%, #E8E2D5 100%);\\n  border: 1px solid rgba(184, 166, 118, 0.3);\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%] {\\n  color: #2C3E50;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #A69660;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%] {\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  margin-bottom: 1rem;\\n  overflow: hidden;\\n  background-color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F0EDE5 0%, #E8E2D5 100%);\\n  padding: 1rem;\\n  border-bottom: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .template-id[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .template-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #28A745;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-header[_ngcontent-%COMP%]   .detail-count[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  padding: 0.5rem 0;\\n  border-bottom: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n  border-radius: 50%;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  margin-right: 0.75rem;\\n  flex-shrink: 0;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-part[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2C3E50;\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-location[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #ADB5BD;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n  padding-top: 0.5rem;\\n  border-top: 1px solid #E9ECEF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-item[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n  width: 12px;\\n  text-align: center;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-item.price-item[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-item.price-item[_ngcontent-%COMP%] {\\n  color: #28A745;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-item.price-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-item.price-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #1E7E34;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-item.unit-item[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-item.unit-item[_ngcontent-%COMP%] {\\n  color: #17A2B8;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .price-item.unit-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-pricing[_ngcontent-%COMP%]   .pricing-info[_ngcontent-%COMP%]   .unit-item.unit-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #138496;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .no-details[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 1rem;\\n  color: #ADB5BD;\\n  background-color: #F8F9FA;\\n  border-radius: 0.375rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .no-details[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #17A2B8;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%] {\\n  background-color: #FFF3CD;\\n  border: 1px solid #FFC107;\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-top: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%] {\\n  color: #E0A800;\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n  color: #FFC107;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid #E9ECEF;\\n  background-color: #F8F9FA;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  color: #ADB5BD;\\n  font-size: 0.875rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  transition: 0.15s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=primary][_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #AE9B66;\\n  color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=primary][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=success][_ngcontent-%COMP%] {\\n  background-color: #28A745;\\n  border-color: #28A745;\\n  color: #FFFFFF;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=success][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1E7E34;\\n  border-color: #1E7E34;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=basic][_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border-color: #CDCDCD;\\n  color: #5A5A5A;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[nbButton][status=basic][_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  border-color: #5A5A5A;\\n  color: #2C3E50;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .space-template-dialog[_ngcontent-%COMP%] {\\n    min-width: 95vw;\\n    max-width: 95vw;\\n    margin: 0.5rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.4rem 0.8rem;\\n    margin: 0 0.25rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n    align-items: stretch;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n\\n\\nnb-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  width: 100%;\\n}\\nnb-checkbox[_ngcontent-%COMP%]   .customised-control-input[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  margin-top: 0.125rem;\\n}\\n\\n\\n\\n.mr-1[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n\\n\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%] {\\n  background-color: #1A1A1A;\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #FFFFFF;\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n  background-color: #1A1A1A;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-top-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "TemplateGetListResponse", "EnumTemplateType", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener", "$event", "template_r2", "ɵɵrestoreView", "_r1", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "ctx_r2", "ɵɵnextContext", "onTemplateItemChange", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CTemplateName", "ɵɵtextInterpolate1", "CTemplateId", "CStatus", "CTemplateType", "ɵɵtemplate", "SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template", "ɵɵproperty", "templates", "ɵɵelement", "SpaceTemplateSelectorComponent_div_12_div_6_Template", "SpaceTemplateSelectorComponent_div_12_ng_template_7_Template", "ɵɵtemplateRefExtractor", "length", "noTemplates_r4", "detail_r5", "CLocation", "ɵɵpipeBind1", "CUnitPrice", "CUnit", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_2_Template", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_span_3_Template", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_7_Template", "i_r6", "<PERSON>art", "ItemTemplate", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template", "getTemplateDetails", "item_r7", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template", "SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template", "noDetails_r8", "getConflictCount", "SpaceTemplateSelectorComponent_div_13_div_12_Template", "SpaceTemplateSelectorComponent_div_13_div_13_Template", "getSelectedItems", "hasConflicts", "SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener", "_r9", "previousStep", "SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener", "_r10", "nextStep", "canProceed", "SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener", "_r11", "applyTemplate", "SpaceTemplateSelectorComponent", "constructor", "templateService", "dialogRef", "buildCaseId", "SpaceTemplate", "templateApplied", "currentStep", "selectedTemplateDetails", "Map", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "PageIndex", "PageSize", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "error", "filter", "getSelectedTotalPrice", "loadSelectedTemplateDetails", "selectedItems", "for<PERSON>ach", "has", "loadTemplateDetailById", "templateId", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "set", "get", "getProgressText", "progressTexts", "config", "spaceId", "spaceName", "templateDetails", "totalPrice", "emit", "close", "resetSelections", "reset", "template", "clear", "ɵɵdirectiveInject", "i1", "TemplateService", "i2", "NbDialogRef", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "SpaceTemplateSelectorComponent_Template", "rf", "ctx", "SpaceTemplateSelectorComponent_Template_button_click_4_listener", "SpaceTemplateSelectorComponent_div_12_Template", "SpaceTemplateSelectorComponent_div_13_Template", "SpaceTemplateSelectorComponent_Template_button_click_19_listener", "SpaceTemplateSelectorComponent_button_21_Template", "SpaceTemplateSelectorComponent_button_22_Template", "SpaceTemplateSelectorComponent_button_23_Template", "ɵɵpureFunction3", "_c0", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i4", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbIconComponent", "NbCheckboxComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbIconModule,\r\n  NbCheckboxModule,\r\n  NbDialogRef\r\n} from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, TemplateGetListResponse, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';\r\nimport { EnumTemplateType } from 'src/app/shared/enum/enumTemplateType';\r\n\r\n// 擴展 API 模型以支援前端選擇功能\r\nexport interface ExtendedTemplateItem extends TemplateGetListResponse {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: ExtendedTemplateItem[];\r\n  templateDetails: Map<number, TemplateDetailItem[]>; // 新增：包含所有模板的明細\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule\r\n  ],\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() buildCaseId: string = '';\r\n  @Input() CTemplateType: number = EnumTemplateType.SpaceTemplate;\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n\r\n  // 暴露枚舉給模板使用\r\n  EnumTemplateType = EnumTemplateType;\r\n\r\n  currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n  templates: ExtendedTemplateItem[] = []; // 直接使用 API 資料\r\n  selectedTemplateDetails: Map<number, TemplateDetailItem[]> = new Map(); // 存儲已載入的模板詳情\r\n\r\n  constructor(\r\n    private templateService: TemplateService,\r\n    private dialogRef: NbDialogRef<SpaceTemplateSelectorComponent>\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    // 組件初始化時載入模板\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  loadTemplatesFromAPI() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: this.CTemplateType,\r\n      PageIndex: 1,\r\n      PageSize: 100, // 載入足夠的資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateListForCommon API\r\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // 直接使用 API 資料，只添加 selected 屬性\r\n          this.templates = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  getSelectedItems(): ExtendedTemplateItem[] {\r\n    return this.templates.filter(item => item.selected);\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    // 由於 API 沒有價格資訊，返回 0\r\n    return 0;\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      if (this.currentStep === 1) {\r\n        // 進入步驟2前，載入選中模板的詳情\r\n        this.loadSelectedTemplateDetails();\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  loadSelectedTemplateDetails() {\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    selectedItems.forEach(item => {\r\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\r\n        // 只載入尚未載入過的模板詳情\r\n        this.loadTemplateDetailById(item.CTemplateId);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTemplateDetailById(templateId: number) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.selectedTemplateDetails.set(templateId, response.Entries);\r\n        }\r\n      },\r\n      error: () => {\r\n        // 錯誤處理：設置空陣列\r\n        this.selectedTemplateDetails.set(templateId, []);\r\n      }\r\n    });\r\n  }\r\n\r\n  getTemplateDetails(templateId: number): TemplateDetailItem[] {\r\n    return this.selectedTemplateDetails.get(templateId) || [];\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要套用的模板項目',\r\n      2: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: 'common', // 通用模板，不特定空間\r\n      spaceName: '通用模板',\r\n      selectedItems: this.getSelectedItems(),\r\n      templateDetails: new Map(this.selectedTemplateDetails), // 傳遞已載入的模板明細\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  // 移除不需要的方法\r\n  // onBackdropClick 由 NbDialog 自動處理\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.templates = [];\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    // 保留 templates 資料，只重置選擇狀態\r\n    this.templates.forEach(template => {\r\n      template.selected = false;\r\n    });\r\n    // 清空詳情快取\r\n    this.selectedTemplateDetails.clear();\r\n  }\r\n}\r\n", "<!-- 空間模板選擇器共用元件 - 使用 nb-dialog -->\r\n<nb-card class=\"space-template-dialog\">\r\n  <nb-card-header class=\"space-template-header\">\r\n    <div class=\"space-template-title\">空間模板選擇器</div>\r\n    <button class=\"close-btn\" nbButton ghost (click)=\"close()\">\r\n      <nb-icon icon=\"close-outline\"></nb-icon>\r\n    </button>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body class=\"space-template-body\">\r\n    <!-- 步驟導航 -->\r\n    <div class=\"step-nav\">\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 1,\r\n        'completed': currentStep > 1,\r\n        'pending': currentStep < 1\r\n      }\">1. 選擇模板</div>\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 2,\r\n        'completed': currentStep > 2,\r\n        'pending': currentStep < 2\r\n      }\">2. 確認套用</div>\r\n    </div>\r\n\r\n    <!-- 步驟1: 選擇模板 -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <div class=\"template-selection\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"layers-outline\" class=\"mr-2\"></nb-icon>選擇模板項目\r\n        </div>\r\n        <div class=\"template-list\">\r\n          <div *ngIf=\"templates.length > 0; else noTemplates\">\r\n            <div *ngFor=\"let template of templates\" class=\"template-item\">\r\n              <nb-checkbox [(ngModel)]=\"template.selected\" (ngModelChange)=\"onTemplateItemChange()\">\r\n                <div class=\"template-info\">\r\n                  <div class=\"item-name\">{{ template.CTemplateName }}</div>\r\n                  <div class=\"item-code\">ID: {{ template.CTemplateId }}</div>\r\n                  <div class=\"item-status\">\r\n                    狀態: {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n                  </div>\r\n                  <div class=\"item-type\">\r\n                    類型: {{ template.CTemplateType }}\r\n                  </div>\r\n                </div>\r\n              </nb-checkbox>\r\n            </div>\r\n          </div>\r\n          <ng-template #noTemplates>\r\n            <div class=\"no-templates\">\r\n              <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n              暫無可用的模板項目，請稍後再試或聯繫管理員\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 步驟2: 確認套用 -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <div class=\"confirmation-area\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"checkmark-circle-outline\" class=\"mr-2\"></nb-icon>確認套用詳情\r\n        </div>\r\n\r\n        <div class=\"selected-summary\">\r\n          <div class=\"summary-text\">\r\n            將套用 <strong>通用模板</strong>：{{ getSelectedItems().length }}個模板項目\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 選中的模板詳情展開 -->\r\n        <div class=\"selected-templates-details\">\r\n          <div *ngFor=\"let item of getSelectedItems()\" class=\"template-detail-section\">\r\n            <div class=\"template-detail-header\">\r\n              <h5 class=\"template-name\">{{ item.CTemplateName }}</h5>\r\n              <div class=\"template-meta\">\r\n                <span class=\"template-id\">ID: {{ item.CTemplateId }}</span>\r\n                <span class=\"template-status\">{{ item.CStatus === 1 ? '啟用' : '停用' }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"template-detail-content\">\r\n              <div *ngIf=\"getTemplateDetails(item.CTemplateId!).length > 0; else noDetails\" class=\"detail-items\">\r\n                <div class=\"detail-items-header\">\r\n                  <span class=\"detail-count\">包含 {{ getTemplateDetails(item.CTemplateId!).length }} 個明細項目：</span>\r\n                </div>\r\n                <div class=\"detail-items-list\">\r\n                  <div *ngFor=\"let detail of getTemplateDetails(item.CTemplateId!); let i = index\" class=\"detail-item\">\r\n                    <div class=\"detail-index\">{{ i + 1 }}</div>\r\n                    <div class=\"detail-info\">\r\n                      <div class=\"detail-part\">{{ detail.CPart }}</div>\r\n                      <div class=\"detail-location\" *ngIf=\"detail.CLocation\">\r\n                        位置: {{ detail.CLocation }}\r\n                      </div>\r\n                      <!-- 項目模板顯示單位和單價資訊 -->\r\n                      <div\r\n                        *ngIf=\"CTemplateType === EnumTemplateType.ItemTemplate && (detail.CUnitPrice || detail.CUnit)\"\r\n                        class=\"detail-pricing\">\r\n                        <div class=\"pricing-info\">\r\n                          <span class=\"price-item\" *ngIf=\"detail.CUnitPrice\">\r\n                            <i class=\"fas fa-dollar-sign\"></i>\r\n                            單價: NT$ {{ detail.CUnitPrice | number }}\r\n                          </span>\r\n                          <span class=\"unit-item\" *ngIf=\"detail.CUnit\">\r\n                            <i class=\"fas fa-ruler\"></i>\r\n                            單位: {{ detail.CUnit }}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <ng-template #noDetails>\r\n                <div class=\"no-details\">\r\n                  <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n                  此模板暫無明細項目\r\n                </div>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"hasConflicts()\" class=\"conflict-warning\">\r\n          <div class=\"warning-text\">\r\n            <strong><nb-icon icon=\"alert-triangle-outline\" class=\"mr-1\"></nb-icon>衝突檢測：</strong>\r\n            檢測到 {{ getConflictCount() }} 個項目可能與現有需求重複，系統將自動處理衝突項目。\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-footer class=\"space-template-footer\">\r\n    <div class=\"progress-info\">\r\n      <span>{{ getProgressText() }}</span>\r\n    </div>\r\n    <div class=\"step-buttons\">\r\n      <button nbButton status=\"basic\" (click)=\"close()\">取消</button>\r\n      <button *ngIf=\"currentStep > 1\" nbButton status=\"basic\" (click)=\"previousStep()\">上一步</button>\r\n      <button *ngIf=\"currentStep < 2\" nbButton status=\"primary\" [disabled]=\"!canProceed()\"\r\n        (click)=\"nextStep()\">下一步</button>\r\n      <button *ngIf=\"currentStep === 2\" nbButton status=\"success\" [disabled]=\"getSelectedItems().length === 0\"\r\n        (click)=\"applyTemplate()\">確認套用</button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,QAEX,gBAAgB;AAEvB,SAA8BC,uBAAuB,QAAuD,yBAAyB;AACrI,SAASC,gBAAgB,QAAQ,sCAAsC;;;;;;;;;;;;;;ICqBzDC,EADF,CAAAC,cAAA,cAA8D,sBAC0B;IAAzED,EAAA,CAAAE,gBAAA,2BAAAC,gGAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAJ,WAAA,CAAAK,QAAA,EAAAN,MAAA,MAAAC,WAAA,CAAAK,QAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAACJ,EAAA,CAAAY,UAAA,2BAAAT,gGAAA;MAAAH,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAAiBE,MAAA,CAAAE,oBAAA,EAAsB;IAAA,EAAC;IAEjFf,EADF,CAAAC,cAAA,cAA2B,cACF;IAAAD,EAAA,CAAAgB,MAAA,GAA4B;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACzDjB,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAgB,MAAA,GAA8B;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAC3DjB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAgB,MAAA,IACF;IAGNhB,EAHM,CAAAiB,YAAA,EAAM,EACF,EACM,EACV;;;;IAZSjB,EAAA,CAAAkB,SAAA,EAA+B;IAA/BlB,EAAA,CAAAmB,gBAAA,YAAAd,WAAA,CAAAK,QAAA,CAA+B;IAEjBV,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAAoB,iBAAA,CAAAf,WAAA,CAAAgB,aAAA,CAA4B;IAC5BrB,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAAsB,kBAAA,SAAAjB,WAAA,CAAAkB,WAAA,KAA8B;IAEnDvB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAjB,WAAA,CAAAmB,OAAA,8CACF;IAEExB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAjB,WAAA,CAAAoB,aAAA,MACF;;;;;IAXRzB,EAAA,CAAAC,cAAA,UAAoD;IAClDD,EAAA,CAAA0B,UAAA,IAAAC,0DAAA,mBAA8D;IAchE3B,EAAA,CAAAiB,YAAA,EAAM;;;;IAdsBjB,EAAA,CAAAkB,SAAA,EAAY;IAAZlB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAAgB,SAAA,CAAY;;;;;IAgBtC7B,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA8B,SAAA,kBAAoD;IACpD9B,EAAA,CAAAgB,MAAA,uIACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;;IAxBVjB,EAFJ,CAAAC,cAAA,cAAoD,cAClB,cACH;IACzBD,EAAA,CAAA8B,SAAA,kBAAsD;IAAA9B,EAAA,CAAAgB,MAAA,4CACxD;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,cAA2B;IAiBzBD,EAhBA,CAAA0B,UAAA,IAAAK,oDAAA,kBAAoD,IAAAC,4DAAA,gCAAAhC,EAAA,CAAAiC,sBAAA,CAgB1B;IAQhCjC,EAFI,CAAAiB,YAAA,EAAM,EACF,EACF;;;;;IAxBMjB,EAAA,CAAAkB,SAAA,GAA4B;IAAAlB,EAA5B,CAAA4B,UAAA,SAAAf,MAAA,CAAAgB,SAAA,CAAAK,MAAA,KAA4B,aAAAC,cAAA,CAAgB;;;;;IA4DtCnC,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;IADJjB,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAc,SAAA,CAAAC,SAAA,MACF;;;;;IAMIrC,EAAA,CAAAC,cAAA,eAAmD;IACjDD,EAAA,CAAA8B,SAAA,YAAkC;IAClC9B,EAAA,CAAAgB,MAAA,GACF;;IAAAhB,EAAA,CAAAiB,YAAA,EAAO;;;;IADLjB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,wBAAAtB,EAAA,CAAAsC,WAAA,OAAAF,SAAA,CAAAG,UAAA,OACF;;;;;IACAvC,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAA8B,SAAA,YAA4B;IAC5B9B,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAO;;;;IADLjB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAc,SAAA,CAAAI,KAAA,MACF;;;;;IARFxC,EAHF,CAAAC,cAAA,cAEyB,cACG;IAKxBD,EAJA,CAAA0B,UAAA,IAAAe,+EAAA,mBAAmD,IAAAC,+EAAA,mBAIN;IAKjD1C,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IATwBjB,EAAA,CAAAkB,SAAA,GAAuB;IAAvBlB,EAAA,CAAA4B,UAAA,SAAAQ,SAAA,CAAAG,UAAA,CAAuB;IAIxBvC,EAAA,CAAAkB,SAAA,EAAkB;IAAlBlB,EAAA,CAAA4B,UAAA,SAAAQ,SAAA,CAAAI,KAAA,CAAkB;;;;;IAfjDxC,EADF,CAAAC,cAAA,cAAqG,cACzE;IAAAD,EAAA,CAAAgB,MAAA,GAAW;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAEzCjB,EADF,CAAAC,cAAA,cAAyB,cACE;IAAAD,EAAA,CAAAgB,MAAA,GAAkB;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAKjDjB,EAJA,CAAA0B,UAAA,IAAAiB,wEAAA,kBAAsD,IAAAC,wEAAA,kBAM7B;IAa7B5C,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;;IAtBsBjB,EAAA,CAAAkB,SAAA,GAAW;IAAXlB,EAAA,CAAAoB,iBAAA,CAAAyB,IAAA,KAAW;IAEV7C,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAAoB,iBAAA,CAAAgB,SAAA,CAAAU,KAAA,CAAkB;IACb9C,EAAA,CAAAkB,SAAA,EAAsB;IAAtBlB,EAAA,CAAA4B,UAAA,SAAAQ,SAAA,CAAAC,SAAA,CAAsB;IAKjDrC,EAAA,CAAAkB,SAAA,EAA4F;IAA5FlB,EAAA,CAAA4B,UAAA,SAAAf,MAAA,CAAAY,aAAA,KAAAZ,MAAA,CAAAd,gBAAA,CAAAgD,YAAA,KAAAX,SAAA,CAAAG,UAAA,IAAAH,SAAA,CAAAI,KAAA,EAA4F;;;;;IAZnGxC,EAFJ,CAAAC,cAAA,cAAmG,cAChE,eACJ;IAAAD,EAAA,CAAAgB,MAAA,GAA4D;IACzFhB,EADyF,CAAAiB,YAAA,EAAO,EAC1F;IACNjB,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA0B,UAAA,IAAAsB,kEAAA,kBAAqG;IAyBzGhD,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IA5ByBjB,EAAA,CAAAkB,SAAA,GAA4D;IAA5DlB,EAAA,CAAAsB,kBAAA,kBAAAT,MAAA,CAAAoC,kBAAA,CAAAC,OAAA,CAAA3B,WAAA,EAAAW,MAAA,0CAA4D;IAG/DlC,EAAA,CAAAkB,SAAA,GAA0C;IAA1ClB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAAoC,kBAAA,CAAAC,OAAA,CAAA3B,WAAA,EAA0C;;;;;IA4BpEvB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA8B,SAAA,kBAAoD;IACpD9B,EAAA,CAAAgB,MAAA,+DACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;;IA5CRjB,EAFJ,CAAAC,cAAA,cAA6E,cACvC,aACR;IAAAD,EAAA,CAAAgB,MAAA,GAAwB;IAAAhB,EAAA,CAAAiB,YAAA,EAAK;IAErDjB,EADF,CAAAC,cAAA,cAA2B,eACC;IAAAD,EAAA,CAAAgB,MAAA,GAA0B;IAAAhB,EAAA,CAAAiB,YAAA,EAAO;IAC3DjB,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAgB,MAAA,GAAsC;IAExEhB,EAFwE,CAAAiB,YAAA,EAAO,EACvE,EACF;IAENjB,EAAA,CAAAC,cAAA,cAAqC;IAiCnCD,EAhCA,CAAA0B,UAAA,KAAAyB,4DAAA,kBAAmG,KAAAC,oEAAA,gCAAApD,EAAA,CAAAiC,sBAAA,CAgC3E;IAO5BjC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;;IA/CwBjB,EAAA,CAAAkB,SAAA,GAAwB;IAAxBlB,EAAA,CAAAoB,iBAAA,CAAA8B,OAAA,CAAA7B,aAAA,CAAwB;IAEtBrB,EAAA,CAAAkB,SAAA,GAA0B;IAA1BlB,EAAA,CAAAsB,kBAAA,SAAA4B,OAAA,CAAA3B,WAAA,KAA0B;IACtBvB,EAAA,CAAAkB,SAAA,GAAsC;IAAtClB,EAAA,CAAAoB,iBAAA,CAAA8B,OAAA,CAAA1B,OAAA,yCAAsC;IAKhExB,EAAA,CAAAkB,SAAA,GAAwD;IAAAlB,EAAxD,CAAA4B,UAAA,SAAAf,MAAA,CAAAoC,kBAAA,CAAAC,OAAA,CAAA3B,WAAA,EAAAW,MAAA,KAAwD,aAAAmB,YAAA,CAAc;;;;;IA4C9ErD,EAFJ,CAAAC,cAAA,cAAqD,cACzB,aAChB;IAAAD,EAAA,CAAA8B,SAAA,kBAA8D;IAAA9B,EAAA,CAAAgB,MAAA,qCAAK;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IACpFjB,EAAA,CAAAgB,MAAA,GACF;IACFhB,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IAFFjB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,yBAAAT,MAAA,CAAAyC,gBAAA,+JACF;;;;;IApEFtD,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAA8B,SAAA,kBAAgE;IAAA9B,EAAA,CAAAgB,MAAA,4CAClE;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAGJjB,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAgB,MAAA,2BAAI;IAAAhB,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IAAAjB,EAAA,CAAAgB,MAAA,IAC3B;IACFhB,EADE,CAAAiB,YAAA,EAAM,EACF;IAGNjB,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAA0B,UAAA,KAAA6B,qDAAA,mBAA6E;IAkD/EvD,EAAA,CAAAiB,YAAA,EAAM;IAENjB,EAAA,CAAA0B,UAAA,KAAA8B,qDAAA,kBAAqD;IAOzDxD,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IAjE2BjB,EAAA,CAAAkB,SAAA,IAC3B;IAD2BlB,EAAA,CAAAsB,kBAAA,WAAAT,MAAA,CAAA4C,gBAAA,GAAAvB,MAAA,oCAC3B;IAKsBlC,EAAA,CAAAkB,SAAA,GAAqB;IAArBlB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAA4C,gBAAA,GAAqB;IAoDvCzD,EAAA,CAAAkB,SAAA,EAAoB;IAApBlB,EAAA,CAAA4B,UAAA,SAAAf,MAAA,CAAA6C,YAAA,GAAoB;;;;;;IAgB5B1D,EAAA,CAAAC,cAAA,iBAAiF;IAAzBD,EAAA,CAAAY,UAAA,mBAAA+C,0EAAA;MAAA3D,EAAA,CAAAM,aAAA,CAAAsD,GAAA;MAAA,MAAA/C,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAAgD,YAAA,EAAc;IAAA,EAAC;IAAC7D,EAAA,CAAAgB,MAAA,yBAAG;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;;;IAC7FjB,EAAA,CAAAC,cAAA,iBACuB;IAArBD,EAAA,CAAAY,UAAA,mBAAAkD,0EAAA;MAAA9D,EAAA,CAAAM,aAAA,CAAAyD,IAAA;MAAA,MAAAlD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAAmD,QAAA,EAAU;IAAA,EAAC;IAAChE,EAAA,CAAAgB,MAAA,yBAAG;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;IADuBjB,EAAA,CAAA4B,UAAA,cAAAf,MAAA,CAAAoD,UAAA,GAA0B;;;;;;IAEpFjE,EAAA,CAAAC,cAAA,iBAC4B;IAA1BD,EAAA,CAAAY,UAAA,mBAAAsD,0EAAA;MAAAlE,EAAA,CAAAM,aAAA,CAAA6D,IAAA;MAAA,MAAAtD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAAuD,aAAA,EAAe;IAAA,EAAC;IAACpE,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;IADmBjB,EAAA,CAAA4B,UAAA,aAAAf,MAAA,CAAA4C,gBAAA,GAAAvB,MAAA,OAA4C;;;ADtG9G,OAAM,MAAOmC,8BAA8B;EAYzCC,YACUC,eAAgC,EAChCC,SAAsD;IADtD,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IAbV,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAhD,aAAa,GAAW1B,gBAAgB,CAAC2E,aAAa;IACrD,KAAAC,eAAe,GAAG,IAAIpF,YAAY,EAAuB;IAEnE;IACA,KAAAQ,gBAAgB,GAAGA,gBAAgB;IAEnC,KAAA6E,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAA/C,SAAS,GAA2B,EAAE,CAAC,CAAC;IACxC,KAAAgD,uBAAuB,GAAsC,IAAIC,GAAG,EAAE,CAAC,CAAC;EAKpE;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB;IACA,MAAMC,mBAAmB,GAAwB;MAC/CxD,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCyD,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACf9D,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAACkD,eAAe,CAACa,4CAA4C,CAAC;MAChEC,IAAI,EAAEJ;KACP,CAAC,CAACK,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACA,IAAI,CAAC7D,SAAS,GAAG2D,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C,GAAGA,IAAI;YACPlF,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL;UACA,IAAI,CAACmB,SAAS,GAAG,EAAE;QACrB;MACF,CAAC;MACDgE,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAAChE,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEAd,oBAAoBA,CAAA;IAClB;EAAA;EAGF0C,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC5B,SAAS,CAACiE,MAAM,CAACF,IAAI,IAAIA,IAAI,CAAClF,QAAQ,CAAC;EACrD;EAEAqF,qBAAqBA,CAAA;IACnB;IACA,OAAO,CAAC;EACV;EAEA9B,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACW,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACnB,gBAAgB,EAAE,CAACvB,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEA8B,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACW,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B;QACA,IAAI,CAACoB,2BAA2B,EAAE;MACpC;MACA,IAAI,CAACpB,WAAW,EAAE;IACpB;EACF;EAEAoB,2BAA2BA,CAAA;IACzB,MAAMC,aAAa,GAAG,IAAI,CAACxC,gBAAgB,EAAE;IAE7CwC,aAAa,CAACC,OAAO,CAACN,IAAI,IAAG;MAC3B,IAAIA,IAAI,CAACrE,WAAW,IAAI,CAAC,IAAI,CAACsD,uBAAuB,CAACsB,GAAG,CAACP,IAAI,CAACrE,WAAW,CAAC,EAAE;QAC3E;QACA,IAAI,CAAC6E,sBAAsB,CAACR,IAAI,CAACrE,WAAW,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEA6E,sBAAsBA,CAACC,UAAkB;IACvC,MAAMC,IAAI,GAA8B;MACtCD,UAAU,EAAEA;KACb;IAED,IAAI,CAAC9B,eAAe,CAACgC,yCAAyC,CAAC;MAC7DlB,IAAI,EAAEiB;KACP,CAAC,CAAChB,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAACb,uBAAuB,CAAC2B,GAAG,CAACH,UAAU,EAAEb,QAAQ,CAACE,OAAO,CAAC;QAChE;MACF,CAAC;MACDG,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAAChB,uBAAuB,CAAC2B,GAAG,CAACH,UAAU,EAAE,EAAE,CAAC;MAClD;KACD,CAAC;EACJ;EAEApD,kBAAkBA,CAACoD,UAAkB;IACnC,OAAO,IAAI,CAACxB,uBAAuB,CAAC4B,GAAG,CAACJ,UAAU,CAAC,IAAI,EAAE;EAC3D;EAEAxC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACe,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEA8B,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAAC/B,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAlB,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACD,gBAAgB,EAAE,CAACvB,MAAM,GAAG,CAAC;EAC3C;EAEAoB,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACI,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAU,aAAaA,CAAA;IACX,MAAMwC,MAAM,GAAwB;MAClCC,OAAO,EAAE,QAAQ;MAAE;MACnBC,SAAS,EAAE,MAAM;MACjBb,aAAa,EAAE,IAAI,CAACxC,gBAAgB,EAAE;MACtCsD,eAAe,EAAE,IAAIjC,GAAG,CAAC,IAAI,CAACD,uBAAuB,CAAC;MAAE;MACxDmC,UAAU,EAAE,IAAI,CAACjB,qBAAqB;KACvC;IAED,IAAI,CAACpB,eAAe,CAACsC,IAAI,CAACL,MAAM,CAAC;IACjC,IAAI,CAACM,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAAC3C,SAAS,CAAC0C,KAAK,EAAE;EACxB;EAEA;EACA;EAEQE,KAAKA,CAAA;IACX,IAAI,CAACxC,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC/C,SAAS,GAAG,EAAE;EACrB;EAEQsF,eAAeA,CAAA;IACrB,IAAI,CAACvC,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAAC/C,SAAS,CAACqE,OAAO,CAACmB,QAAQ,IAAG;MAChCA,QAAQ,CAAC3G,QAAQ,GAAG,KAAK;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAACmE,uBAAuB,CAACyC,KAAK,EAAE;EACtC;;;uCArLWjD,8BAA8B,EAAArE,EAAA,CAAAuH,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAzH,EAAA,CAAAuH,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA9BtD,8BAA8B;MAAAuD,SAAA;MAAAC,MAAA;QAAApD,WAAA;QAAAhD,aAAA;MAAA;MAAAqG,OAAA;QAAAnD,eAAA;MAAA;MAAAoD,UAAA;MAAAC,QAAA,GAAAhI,EAAA,CAAAiI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAf,QAAA,WAAAgB,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtCvCtI,EAFJ,CAAAC,cAAA,iBAAuC,wBACS,aACV;UAAAD,EAAA,CAAAgB,MAAA,iDAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAC/CjB,EAAA,CAAAC,cAAA,gBAA2D;UAAlBD,EAAA,CAAAY,UAAA,mBAAA4H,gEAAA;YAAA,OAASD,GAAA,CAAArB,KAAA,EAAO;UAAA,EAAC;UACxDlH,EAAA,CAAA8B,SAAA,iBAAwC;UAE5C9B,EADE,CAAAiB,YAAA,EAAS,EACM;UAKbjB,EAHJ,CAAAC,cAAA,sBAA0C,aAElB,aAKjB;UAAAD,EAAA,CAAAgB,MAAA,kCAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAChBjB,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAgB,MAAA,mCAAO;UACZhB,EADY,CAAAiB,YAAA,EAAM,EACZ;UAoCNjB,EAjCA,CAAA0B,UAAA,KAAA+G,8CAAA,kBAAoD,KAAAC,8CAAA,mBAiCA;UA0EtD1I,EAAA,CAAAiB,YAAA,EAAe;UAIXjB,EAFJ,CAAAC,cAAA,0BAA8C,eACjB,YACnB;UAAAD,EAAA,CAAAgB,MAAA,IAAuB;UAC/BhB,EAD+B,CAAAiB,YAAA,EAAO,EAChC;UAEJjB,EADF,CAAAC,cAAA,eAA0B,kBAC0B;UAAlBD,EAAA,CAAAY,UAAA,mBAAA+H,iEAAA;YAAA,OAASJ,GAAA,CAAArB,KAAA,EAAO;UAAA,EAAC;UAAClH,EAAA,CAAAgB,MAAA,oBAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAI7DjB,EAHA,CAAA0B,UAAA,KAAAkH,iDAAA,qBAAiF,KAAAC,iDAAA,qBAE1D,KAAAC,iDAAA,qBAEK;UAGlC9I,EAFI,CAAAiB,YAAA,EAAM,EACS,EACT;;;UAvImBjB,EAAA,CAAAkB,SAAA,GAIrB;UAJqBlB,EAAA,CAAA4B,UAAA,YAAA5B,EAAA,CAAA+I,eAAA,IAAAC,GAAA,EAAAT,GAAA,CAAA3D,WAAA,QAAA2D,GAAA,CAAA3D,WAAA,MAAA2D,GAAA,CAAA3D,WAAA,MAIrB;UACqB5E,EAAA,CAAAkB,SAAA,GAIrB;UAJqBlB,EAAA,CAAA4B,UAAA,YAAA5B,EAAA,CAAA+I,eAAA,KAAAC,GAAA,EAAAT,GAAA,CAAA3D,WAAA,QAAA2D,GAAA,CAAA3D,WAAA,MAAA2D,GAAA,CAAA3D,WAAA,MAIrB;UAIE5E,EAAA,CAAAkB,SAAA,GAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAA2G,GAAA,CAAA3D,WAAA,OAAuB;UAiCvB5E,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAA2G,GAAA,CAAA3D,WAAA,OAAuB;UA8ErB5E,EAAA,CAAAkB,SAAA,GAAuB;UAAvBlB,EAAA,CAAAoB,iBAAA,CAAAmH,GAAA,CAAA7B,eAAA,GAAuB;UAIpB1G,EAAA,CAAAkB,SAAA,GAAqB;UAArBlB,EAAA,CAAA4B,UAAA,SAAA2G,GAAA,CAAA3D,WAAA,KAAqB;UACrB5E,EAAA,CAAAkB,SAAA,EAAqB;UAArBlB,EAAA,CAAA4B,UAAA,SAAA2G,GAAA,CAAA3D,WAAA,KAAqB;UAErB5E,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAA2G,GAAA,CAAA3D,WAAA,OAAuB;;;qBDhHlCpF,YAAY,EAAAyJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,WAAA,EACZ5J,WAAW,EAAA6J,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EACX9J,YAAY,EAAAgI,EAAA,CAAA+B,eAAA,EAAA/B,EAAA,CAAAgC,mBAAA,EAAAhC,EAAA,CAAAiC,qBAAA,EAAAjC,EAAA,CAAAkC,qBAAA,EACZjK,cAAc,EAAA+H,EAAA,CAAAmC,iBAAA,EACdjK,YAAY,EAAA8H,EAAA,CAAAoC,eAAA,EACZjK,gBAAgB,EAAA6H,EAAA,CAAAqC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}