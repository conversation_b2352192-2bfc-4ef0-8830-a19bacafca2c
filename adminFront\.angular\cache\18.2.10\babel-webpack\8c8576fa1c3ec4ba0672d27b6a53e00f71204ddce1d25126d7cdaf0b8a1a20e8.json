{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let CapitalizePipe = /*#__PURE__*/(() => {\n  class CapitalizePipe {\n    transform(input) {\n      return input && input.length ? input.charAt(0).toUpperCase() + input.slice(1).toLowerCase() : input;\n    }\n    static {\n      this.ɵfac = function CapitalizePipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || CapitalizePipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"ngxCapitalize\",\n        type: CapitalizePipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return CapitalizePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}