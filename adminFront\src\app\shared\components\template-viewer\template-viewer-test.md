# Template Viewer GetTemplateDetailById API 測試指南

## 測試目標
驗證 GetTemplateDetailById API 整合是否正常運作，包括：
1. API 調用成功時的資料顯示
2. API 調用失敗時的錯誤處理
3. 前端搜尋功能
4. 前端分頁功能

## 測試步驟

### 1. 準備測試環境
確保後端 API 服務正在運行：
```bash
# 確認 API 服務運行在 http://localhost:9453
curl http://localhost:9453/swagger/v1/swagger.json
```

### 2. 測試 API 調用成功情況
1. 開啟瀏覽器開發者工具 (F12)
2. 在 template-viewer 組件中選擇一個模板
3. 觀察 Console 輸出：
   - 應該看到 "GetTemplateDetailById API 調用:" 的日誌
   - 應該看到 "模板詳情載入成功:" 的日誌
4. 檢查 Network 標籤：
   - 應該有對 `/api/Template/GetTemplateDetailById` 的 POST 請求
   - 請求體應包含 `{ templateId: [選中的模板ID] }`

### 3. 測試 API 調用失敗情況
1. 停止後端 API 服務或修改 API URL 使其無效
2. 選擇一個模板
3. 觀察 Console 輸出：
   - 應該看到 "GetTemplateDetailById API 調用失敗:" 的錯誤日誌
4. 確認模板詳情顯示為空（無資料狀態）

### 4. 測試前端搜尋功能
1. 選擇一個有詳情的模板
2. 在搜尋框中輸入關鍵字
3. 按 Enter 或點擊搜尋
4. 確認：
   - 只顯示包含關鍵字的項目
   - 搜尋不區分大小寫
   - 清空搜尋框會顯示所有項目

### 5. 測試前端分頁功能
1. 選擇一個有多個詳情項目的模板
2. 確認分頁控制項正確顯示
3. 點擊不同頁碼，確認：
   - 顯示對應頁面的項目
   - 頁碼高亮正確
   - 總頁數計算正確

## 預期結果

### API 成功回應格式
```json
{
  "StatusCode": 0,
  "Message": "成功",
  "TotalItems": 5,
  "Entries": [
    {
      "CTemplateDetailId": 1,
      "CTemplateId": 123,
      "CReleateId": 456,
      "CReleateName": "項目名稱"
    }
  ]
}
```

### 轉換後的內部格式
```typescript
{
  CTemplateDetailId: 1,
  CTemplateId: 123,
  CReleateId: 456,
  CReleateName: "項目名稱",
  CSort: undefined,
  CRemark: undefined,
  CCreateDt: "2024-01-01T00:00:00.000Z",
  CCreator: "系統",
  CCategory: undefined,
  CUnitPrice: undefined,
  CQuantity: undefined,
  CUnit: undefined
}
```

## 故障排除

### 常見問題
1. **API 調用失敗**
   - 檢查後端服務是否運行
   - 檢查 API URL 是否正確
   - 檢查 CORS 設定

2. **資料不顯示**
   - 檢查 API 回應格式是否符合預期
   - 檢查 StatusCode 是否為 0
   - 檢查 Entries 陣列是否有資料

3. **搜尋不工作**
   - 確認搜尋是基於 CReleateName 欄位
   - 檢查資料是否有 CReleateName 值

4. **分頁不正確**
   - 檢查 TotalItems 是否正確設定
   - 確認 pageSize 設定正確

## 效能考量
- API 不支援伺服器端分頁，所有資料會一次載入到前端
- 對於大量資料，建議後端 API 增加分頁支援
- 搜尋功能在前端執行，對於大量資料可能影響效能
