{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSaturday\n * @category Weekday Helpers\n * @summary Is the given date Saturday?\n *\n * @description\n * Is the given date Saturday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Saturday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 27 September 2014 Saturday?\n * const result = isSaturday(new Date(2014, 8, 27))\n * //=> true\n */\nexport default function isSaturday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 6;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}