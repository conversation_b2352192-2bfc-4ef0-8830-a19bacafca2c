{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbActionsModule, NbLayoutModule, NbMenuModule, NbSearchModule, NbSidebarModule, NbUserModule, NbContextMenuModule, NbButtonModule, NbSelectModule, NbIconModule, NbThemeModule } from '@nebular/theme';\nimport { NbEvaIconsModule } from '@nebular/eva-icons';\nimport { NbSecurityModule } from '@nebular/security';\nimport { FooterComponent, HeaderComponent, SearchInputComponent, TinyMCEComponent } from './components';\nimport { CapitalizePipe, PluralPipe, RoundPipe, TimingPipe, NumberWithCommasPipe, MomentPipe, BooleanStringPipe, FormatHourPipe, BaseFilePipe, DateFormatPipe, FileNamePipe, HtmlPipe, FilterListItemsPipe } from './pipes';\nimport { OneColumnLayoutComponent, ThreeColumnsLayoutComponent, TwoColumnsLayoutComponent } from './layouts';\nimport { DEFAULT_THEME } from './styles/theme.default';\nimport { COSMIC_THEME } from './styles/theme.cosmic';\nimport { CORPORATE_THEME } from './styles/theme.corporate';\nimport { DARK_THEME } from './styles/theme.dark';\nimport { ApprovalWaitingPipe, DefaultKeyPipe, DocumentStatusPipe, PlanUsePipe, StatusMailPipe, StatusPipe, TaskLogStatusPipe, TaskStatusPipe, TypeMailPipe } from './pipes/mapping.pipe';\nimport { ApproveStatusPipe } from './pipes/approveStatus.pipe';\nimport * as i0 from \"@angular/core\";\nconst NB_MODULES = [NbLayoutModule, NbMenuModule, NbUserModule, NbActionsModule, NbSearchModule, NbSidebarModule, NbContextMenuModule, NbSecurityModule, NbButtonModule, NbSelectModule, NbIconModule, NbEvaIconsModule];\nconst COMPONENTS = [HeaderComponent, FooterComponent, SearchInputComponent, TinyMCEComponent, OneColumnLayoutComponent, ThreeColumnsLayoutComponent, TwoColumnsLayoutComponent];\nconst PIPES = [CapitalizePipe, PluralPipe, RoundPipe, TimingPipe, NumberWithCommasPipe, MomentPipe, StatusPipe, StatusMailPipe, TypeMailPipe, DefaultKeyPipe, TaskStatusPipe, TaskLogStatusPipe, BooleanStringPipe, FormatHourPipe, ApproveStatusPipe, ApprovalWaitingPipe, BaseFilePipe, DateFormatPipe, FileNamePipe, HtmlPipe, PlanUsePipe, FilterListItemsPipe, DocumentStatusPipe];\nexport let ThemeModule = /*#__PURE__*/(() => {\n  class ThemeModule {\n    static forRoot() {\n      return {\n        ngModule: ThemeModule,\n        providers: [...NbThemeModule.forRoot({\n          name: 'default'\n        }, [DEFAULT_THEME, COSMIC_THEME, CORPORATE_THEME, DARK_THEME]).providers]\n      };\n    }\n    static {\n      this.ɵfac = function ThemeModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ThemeModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ThemeModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, NB_MODULES, HeaderComponent, OneColumnLayoutComponent, ThreeColumnsLayoutComponent, TwoColumnsLayoutComponent, CommonModule]\n      });\n    }\n  }\n  return ThemeModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}