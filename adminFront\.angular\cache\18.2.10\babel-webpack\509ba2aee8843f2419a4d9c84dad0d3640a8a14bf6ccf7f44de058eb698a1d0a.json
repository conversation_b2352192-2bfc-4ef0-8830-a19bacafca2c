{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let ApproveStatusPipe = /*#__PURE__*/(() => {\n  class ApproveStatusPipe {\n    transform(value) {\n      switch (value) {\n        case true:\n          return '已通過';\n        case false:\n          return '已駁回';\n        case null:\n          return '待審核';\n        case undefined:\n          return '待審核';\n        default:\n          return '待審核';\n      }\n    }\n    static {\n      this.ɵfac = function ApproveStatusPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ApproveStatusPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"approveStatus\",\n        type: ApproveStatusPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return ApproveStatusPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}